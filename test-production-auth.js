// Test authentication in production
const BASE_URL = 'https://pharmastock.ma';

async function testProductionAuth() {
  try {
    console.log('🧪 Testing production authentication...\n');

    // Test 1: Check demo status endpoint
    console.log('1. Testing demo status endpoint...');
    const demoStatusResponse = await fetch(`${BASE_URL}/api/admin/demo-status`);
    const demoStatusData = await demoStatusResponse.json();
    
    console.log(`Status: ${demoStatusResponse.status}`);
    console.log('Response:', JSON.stringify(demoStatusData, null, 2));

    // Test 2: Check debug auth endpoint (should return 401 without session)
    console.log('\n2. Testing debug auth endpoint...');
    const debugResponse = await fetch(`${BASE_URL}/api/debug/auth`);
    const debugData = await debugResponse.json();
    
    console.log(`Status: ${debugResponse.status}`);
    console.log('Response:', JSON.stringify(debugData, null, 2));

    // Test 3: Check network API (should return 401 without session)
    console.log('\n3. Testing network API...');
    const networkResponse = await fetch(`${BASE_URL}/api/network`);
    const networkData = await networkResponse.json();
    
    console.log(`Status: ${networkResponse.status}`);
    console.log('Response:', JSON.stringify(networkData, null, 2));

    // Test 4: Check urgent requests API
    console.log('\n4. Testing urgent requests API...');
    const urgentResponse = await fetch(`${BASE_URL}/api/urgent-requests`);
    const urgentData = await urgentResponse.json();
    
    console.log(`Status: ${urgentResponse.status}`);
    console.log('Response:', JSON.stringify(urgentData, null, 2));

    console.log('\n📋 Summary:');
    console.log('- All endpoints should return 401 without authentication');
    console.log('- The debug details should show what\'s missing');
    console.log('- After logging in as demo user, these should work');
    
    console.log('\n💡 Next steps:');
    console.log('1. Deploy the middleware fix');
    console.log('2. <NAME_EMAIL>');
    console.log('3. Test /network page again');
    console.log('4. Check browser console for any errors');

  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

testProductionAuth();
