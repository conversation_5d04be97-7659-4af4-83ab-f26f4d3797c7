// Simple test script to verify demo account authentication
// Run with: node test-demo-auth.js

const BASE_URL = 'https://pharmastock.ma'; // Change to localhost:3000 for local testing

async function testDemoAuth() {
  try {
    console.log('🧪 Testing demo account authentication...\n');

    // Test 1: Debug auth endpoint
    console.log('1. Testing debug auth endpoint...');
    const debugResponse = await fetch(`${BASE_URL}/api/debug/auth`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        // In a real test, you'd need to include the session cookie
        // For now, this will show the 401 error details
      }
    });

    const debugData = await debugResponse.json();
    console.log('Debug response:', JSON.stringify(debugData, null, 2));

    // Test 2: Network API
    console.log('\n2. Testing network API...');
    const networkResponse = await fetch(`${BASE_URL}/api/network`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const networkData = await networkResponse.json();
    console.log('Network response:', JSON.stringify(networkData, null, 2));

    // Test 3: Urgent requests API
    console.log('\n3. Testing urgent requests API...');
    const urgentResponse = await fetch(`${BASE_URL}/api/urgent-requests`, {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
      }
    });

    const urgentData = await urgentResponse.json();
    console.log('Urgent requests response:', JSON.stringify(urgentData, null, 2));

    console.log('\n📋 Summary:');
    console.log('- All endpoints should return 401 without proper authentication');
    console.log('- Debug endpoint should show detailed error information');
    console.log('- Check the debug details to see what\'s missing');
    console.log('\n💡 To fix:');
    console.log('1. Run: node fix-demo-accounts.js');
    console.log('2. <NAME_EMAIL>');
    console.log('3. Test the /network page again');

  } catch (error) {
    console.error('❌ Test error:', error.message);
  }
}

testDemoAuth();
