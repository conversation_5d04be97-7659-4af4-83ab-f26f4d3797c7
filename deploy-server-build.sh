#!/bin/bash

# Deploy Next.js app with server-side build
# This avoids local disk space issues

set -e

SERVER_IP="**************"
SSH_USER="deploy"
APP_DIR="/var/www/pharmastock-app"
DOMAIN="pharmastock.ma"

echo "🚀 Deploying PharmaStock App with server-side build..."

# Step 1: Upload source files (excluding build artifacts)
echo "📤 Uploading source files..."
rsync -avz --progress --delete \
  --exclude='.git' \
  --exclude='node_modules' \
  --exclude='.next' \
  --exclude='.next/cache' \
  --exclude='build' \
  --exclude='dist' \
  ./ $SSH_USER@$SERVER_IP:$APP_DIR/

# Step 2: Set up production environment on server
echo "🔧 Setting up production environment on server..."
if [ -f ".env.production" ]; then
    # Remove any existing env files and upload production config
    ssh $SSH_USER@$SERVER_IP "cd $APP_DIR && rm -f .env.local .env.development .env"
    # Copy to both .env.local and .env for build-time variables
    scp .env.production $SSH_USER@$SERVER_IP:$APP_DIR/.env.local
    scp .env.production $SSH_USER@$SERVER_IP:$APP_DIR/.env
    echo "✅ Production environment uploaded to both .env and .env.local"
else
    echo "⚠️  No .env.production found, using server's existing environment"
fi

# Step 3: Build on server
echo "🏗️ Building application on server..."
ssh $SSH_USER@$SERVER_IP << 'EOF'
cd /var/www/pharmastock-app

echo "📦 Installing dependencies..."
npm install --production=false

echo "🔍 Verifying environment variables..."
echo "NEXT_PUBLIC_SUPABASE_URL: $(grep NEXT_PUBLIC_SUPABASE_URL .env | head -1)"
echo "Environment files present: $(ls -la .env* 2>/dev/null || echo 'none')"

echo "🔧 Building Next.js application..."
npm run build

echo "⚙️ Restarting PM2 process..."
pm2 restart pharmastock-app || pm2 start npm --name "pharmastock-app" -- start

echo "💾 Saving PM2 configuration..."
pm2 save

echo "✅ Deployment completed successfully!"
pm2 status
EOF

echo ""
echo "🎉 Deployment completed!"
echo "🌐 App should be available at: https://$DOMAIN"
echo "📊 Check status with: ssh $SSH_USER@$SERVER_IP 'pm2 status'"
echo ""
