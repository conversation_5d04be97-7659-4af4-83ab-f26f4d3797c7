"use client";

import { useEffect, useState, useCallback } from "react";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";
import { useNotification } from "@/contexts/notification-context";
import { useAuth } from "@/contexts/auth-context";

interface UrgentRequest {
  id: string;
  pharmacy_id: string;
  product_name: string;
  quantity_needed: number;
  urgency_level: 'low' | 'normal' | 'high' | 'critical';
  description?: string;
  contact_name?: string;
  contact_phone?: string;
  contact_email?: string;
  status: 'active' | 'fulfilled' | 'cancelled' | 'expired';
  expires_at?: string;
  created_at: string;
  updated_at: string;
  pharmacy?: {
    name: string;
    address: string;
    latitude: number;
    longitude: number;
  };
}

interface UrgentRequestResponse {
  id: string;
  urgent_request_id: string;
  responding_pharmacy_id: string;
  quantity_available: number;
  price_per_unit?: number;
  message?: string;
  contact_name?: string;
  contact_phone?: string;
  contact_email?: string;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
  updated_at: string;
}

export function useUrgentRequestsRealtime() {
  const supabase = createClientComponentClient();
  const { showNotification } = useNotification();
  const { user } = useAuth();
  const [nearbyRequests, setNearbyRequests] = useState<UrgentRequest[]>([]);
  const [myRequests, setMyRequests] = useState<UrgentRequest[]>([]);
  const [responses, setResponses] = useState<UrgentRequestResponse[]>([]);

  // Calculate distance between two points (Haversine formula)
  const calculateDistance = useCallback((lat1: number, lon1: number, lat2: number, lon2: number): number => {
    const R = 6371; // Earth's radius in kilometers
    const dLat = (lat2 - lat1) * Math.PI / 180;
    const dLon = (lon2 - lon1) * Math.PI / 180;
    const a = 
      Math.sin(dLat/2) * Math.sin(dLat/2) +
      Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
      Math.sin(dLon/2) * Math.sin(dLon/2);
    const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
    return R * c;
  }, []);

  // Get urgency level display text
  const getUrgencyText = useCallback((level: string): string => {
    switch (level) {
      case 'critical': return 'CRITIQUE';
      case 'high': return 'URGENT';
      case 'normal': return 'Normal';
      case 'low': return 'Faible';
      default: return 'Normal';
    }
  }, []);

  // Get urgency notification priority
  const getNotificationPriority = useCallback((level: string): 'success' | 'error' | 'info' => {
    switch (level) {
      case 'critical': return 'error';
      case 'high': return 'error';
      case 'normal': return 'info';
      case 'low': return 'info';
      default: return 'info';
    }
  }, []);

  // Subscribe to new urgent requests in geographic area
  useEffect(() => {
    if (!user?.pharmacyId) return;

    console.log('🚨 Setting up real-time urgent requests subscription...');

    // Subscribe to new urgent requests
    const urgentRequestsChannel = supabase
      .channel('urgent-requests-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'urgent_requests',
          filter: `status=eq.active`,
        },
        async (payload) => {
          const newRequest = payload.new as UrgentRequest;
          
          // Don't notify about our own requests
          if (newRequest.pharmacy_id === user.pharmacyId) {
            return;
          }

          console.log('🚨 New urgent request received:', newRequest);

          // Get pharmacy location for distance calculation
          try {
            const { data: requestingPharmacy } = await supabase
              .from('pharmacies')
              .select('name, address, latitude, longitude')
              .eq('id', newRequest.pharmacy_id)
              .single();

            const { data: myPharmacy } = await supabase
              .from('pharmacies')
              .select('latitude, longitude')
              .eq('id', user.pharmacyId)
              .single();

            if (requestingPharmacy && myPharmacy) {
              const distance = calculateDistance(
                myPharmacy.latitude,
                myPharmacy.longitude,
                requestingPharmacy.latitude,
                requestingPharmacy.longitude
              );

              // Only show notifications for requests within 50km
              if (distance <= 50) {
                const urgencyText = getUrgencyText(newRequest.urgency_level);
                const priority = getNotificationPriority(newRequest.urgency_level);
                
                showNotification(
                  `🚨 ${urgencyText}: ${newRequest.product_name} (${newRequest.quantity_needed} unités) - ${requestingPharmacy.name} (${distance.toFixed(1)}km)`,
                  priority
                );

                // Add to nearby requests
                setNearbyRequests(prev => [{
                  ...newRequest,
                  pharmacy: requestingPharmacy
                }, ...prev]);
              }
            }
          } catch (error) {
            console.error('Error processing new urgent request:', error);
          }
        }
      )
      .subscribe();

    return () => {
      console.log('🚨 Cleaning up urgent requests subscription');
      supabase.removeChannel(urgentRequestsChannel);
    };
  }, [user?.pharmacyId, supabase, showNotification, calculateDistance, getUrgencyText, getNotificationPriority]);

  // Subscribe to responses to my urgent requests
  useEffect(() => {
    if (!user?.pharmacyId) return;

    console.log('📞 Setting up responses subscription...');

    const responsesChannel = supabase
      .channel('urgent-responses-realtime')
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'urgent_request_responses',
        },
        async (payload) => {
          const newResponse = payload.new as UrgentRequestResponse;
          
          console.log('📞 New response received:', newResponse);

          // Check if this response is for one of my requests
          try {
            const { data: urgentRequest } = await supabase
              .from('urgent_requests')
              .select('pharmacy_id, product_name')
              .eq('id', newResponse.urgent_request_id)
              .single();

            if (urgentRequest?.pharmacy_id === user.pharmacyId) {
              const { data: respondingPharmacy } = await supabase
                .from('pharmacies')
                .select('name')
                .eq('id', newResponse.responding_pharmacy_id)
                .single();

              showNotification(
                `📞 Nouvelle réponse de ${respondingPharmacy?.name || 'Pharmacie'} pour ${urgentRequest.product_name} (${newResponse.quantity_available} unités disponibles)`,
                'success'
              );

              // Add to responses
              setResponses(prev => [newResponse, ...prev]);
            }
          } catch (error) {
            console.error('Error processing new response:', error);
          }
        }
      )
      .subscribe();

    return () => {
      console.log('📞 Cleaning up responses subscription');
      supabase.removeChannel(responsesChannel);
    };
  }, [user?.pharmacyId, supabase, showNotification]);

  // Subscribe to status updates
  useEffect(() => {
    if (!user?.pharmacyId) return;

    console.log('🔄 Setting up status updates subscription...');

    const statusUpdatesChannel = supabase
      .channel('urgent-status-updates')
      .on(
        'postgres_changes',
        {
          event: 'UPDATE',
          schema: 'public',
          table: 'urgent_requests',
        },
        async (payload) => {
          const updatedRequest = payload.new as UrgentRequest;
          
          console.log('🔄 Request status updated:', updatedRequest);

          // Update my requests if this is one of mine
          if (updatedRequest.pharmacy_id === user.pharmacyId) {
            setMyRequests(prev => 
              prev.map(req => 
                req.id === updatedRequest.id ? updatedRequest : req
              )
            );

            // Show notification for status changes
            if (updatedRequest.status === 'fulfilled') {
              showNotification(
                `✅ Votre demande pour ${updatedRequest.product_name} a été satisfaite`,
                'success'
              );
            } else if (updatedRequest.status === 'expired') {
              showNotification(
                `⏰ Votre demande pour ${updatedRequest.product_name} a expiré`,
                'info'
              );
            }
          }

          // Update nearby requests
          setNearbyRequests(prev => 
            prev.map(req => 
              req.id === updatedRequest.id ? updatedRequest : req
            ).filter(req => req.status === 'active') // Remove non-active requests
          );
        }
      )
      .subscribe();

    return () => {
      console.log('🔄 Cleaning up status updates subscription');
      supabase.removeChannel(statusUpdatesChannel);
    };
  }, [user?.pharmacyId, supabase, showNotification]);

  return {
    nearbyRequests,
    myRequests,
    responses,
    setMyRequests,
    setNearbyRequests,
    setResponses,
  };
}
