pharmastock-app/app/admin/analytics/page.tsx
pharmastock-app/app/admin/audit-logs/page.tsx
pharmastock-app/app/admin/categories/page.tsx
pharmastock-app/app/admin/dashboard/page.tsx
pharmastock-app/app/admin/layout.tsx
pharmastock-app/app/admin/marketplace/page.tsx
pharmastock-app/app/admin/messages/page.tsx
pharmastock-app/app/admin/page.tsx
pharmastock-app/app/admin/pharmacies/page.tsx
pharmastock-app/app/admin/reports/page.tsx
pharmastock-app/app/admin/settings/page.tsx
pharmastock-app/app/admin/transactions/page.tsx
pharmastock-app/app/admin/ui-comparison/page.tsx
pharmastock-app/app/admin/urgent-requests/page.tsx
pharmastock-app/app/admin/users/page.tsx
pharmastock-app/app/alerts/page.tsx
pharmastock-app/app/api/admin/messages/route.ts
pharmastock-app/app/api/admin/stats/route.ts
pharmastock-app/app/api/admin/users/route.ts
pharmastock-app/app/api/alerts/route.ts
pharmastock-app/app/api/analyze-image/route.ts
pharmastock-app/app/api/analyze-prescription/route.ts
pharmastock-app/app/api/auth/create-demo-user/route.ts
pharmastock-app/app/api/auth/login/route.ts
pharmastock-app/app/api/auth/logout/route.ts
pharmastock-app/app/api/auth/pharmacy/route.ts
pharmastock-app/app/api/auth/refresh-session/route.ts
pharmastock-app/app/api/auth/register/route.ts
pharmastock-app/app/api/auth/session/route.ts
pharmastock-app/app/api/auth/simple-register/route.ts
pharmastock-app/app/api/dashboard/activity/route.ts
pharmastock-app/app/api/dashboard/stats/route.ts
pharmastock-app/app/api/demo/generate-notifications/route.ts
pharmastock-app/app/api/enhance-prescription/route.ts
pharmastock-app/app/api/marketplace/exchanges/route.ts
pharmastock-app/app/api/marketplace/listings/route.ts
pharmastock-app/app/api/marketplace/reservations/route.ts
pharmastock-app/app/api/medication-lookup/route.ts
pharmastock-app/app/api/network/contact.ts
pharmastock-app/app/api/network/invite.ts
pharmastock-app/app/api/network/route.ts
pharmastock-app/app/api/orders/route.ts
pharmastock-app/app/api/pharmacy/[id]/route.ts
pharmastock-app/app/api/pharmacy/drug-requests/route.ts
pharmastock-app/app/api/stock/route.ts
pharmastock-app/app/api/suppliers/route.ts
pharmastock-app/app/api/urgent-requests/route.ts
pharmastock-app/app/auth/forgot-password/page.tsx
pharmastock-app/app/auth/login/page.tsx
pharmastock-app/app/auth/register/page.tsx
pharmastock-app/app/dashboard/layout.tsx
pharmastock-app/app/dashboard/listings/page.tsx
pharmastock-app/app/dashboard/page.tsx
pharmastock-app/app/history/layout.tsx
pharmastock-app/app/history/page.tsx
pharmastock-app/app/inventory/layout.tsx
pharmastock-app/app/inventory/page.tsx
pharmastock-app/app/layout.tsx
pharmastock-app/app/marketplace/list/page.tsx
pharmastock-app/app/marketplace/page.tsx
pharmastock-app/app/marketplace/reservations/layout.tsx
pharmastock-app/app/network/page.tsx
pharmastock-app/app/orders/layout.tsx
pharmastock-app/app/orders/page.tsx
pharmastock-app/app/page.tsx
pharmastock-app/app/prescription-reader/page.tsx
pharmastock-app/app/profile/layout.tsx
pharmastock-app/app/profile/page.tsx
pharmastock-app/app/providers.tsx
pharmastock-app/app/settings/page.tsx
pharmastock-app/app/supplier/dashboard/page.tsx
pharmastock-app/app/supplier/layout.tsx
pharmastock-app/app/team/layout.tsx
pharmastock-app/app/team/page.tsx
pharmastock-app/app/test-access/page.tsx
pharmastock-app/app/unauthorized/page.tsx
pharmastock-app/app/urgent-requests/layout.tsx
pharmastock-app/app/urgent-requests/page.tsx
pharmastock-app/archive/configuration/page.tsx
pharmastock-app/archive/dashboard-old-design.tsx
pharmastock-app/archive/expiring/page.tsx
pharmastock-app/archive/routes/page.tsx
pharmastock-app/archive/scan/page.tsx
pharmastock-app/archive/settings/notifications/test/page.tsx
pharmastock-app/archive/sharing/page.tsx
pharmastock-app/archive/stocks/page.tsx
pharmastock-app/check-enum-values.js
pharmastock-app/check-existing-users.js
pharmastock-app/check-registration.js
pharmastock-app/check-tables.js
pharmastock-app/check-users.js
pharmastock-app/clear-auth-users.js
pharmastock-app/components/admin/AdminSidebar.tsx
pharmastock-app/components/admin/alert-system.tsx
pharmastock-app/components/admin/moderation-panel.tsx
pharmastock-app/components/admin/pharmacy-management.tsx
pharmastock-app/components/admin/sidebar.tsx
pharmastock-app/components/admin/user-management.tsx
pharmastock-app/components/audit/transaction-log.tsx
pharmastock-app/components/auth/AccessControl.tsx
pharmastock-app/components/dashboard/recent-transactions.tsx
pharmastock-app/components/dashboard/stats-card.tsx
pharmastock-app/components/dashboard/stats/active-listings.tsx
pharmastock-app/components/dashboard/stats/category-distribution.tsx
pharmastock-app/components/dashboard/stats/local-offers.tsx
pharmastock-app/components/dashboard/stats/most-requested-meds.tsx
pharmastock-app/components/dashboard/stats/stock-evolution.tsx
pharmastock-app/components/dashboard/stock-alert-table.tsx
pharmastock-app/components/debug/debug-panel.tsx
pharmastock-app/components/layout/app-shell.tsx
pharmastock-app/components/layout/header.tsx
pharmastock-app/components/layout/navbar.tsx
pharmastock-app/components/layout/root-wrapper.tsx
pharmastock-app/components/layout/sidebar.tsx
pharmastock-app/components/marketplace/advanced-filters.tsx
pharmastock-app/components/marketplace/barcode-scanner.tsx
pharmastock-app/components/marketplace/drug-recognition.tsx
pharmastock-app/components/marketplace/filters.tsx
pharmastock-app/components/marketplace/list-for-sale-form.tsx
pharmastock-app/components/marketplace/list-for-sale-modal.tsx
pharmastock-app/components/marketplace/product-card.tsx
pharmastock-app/components/marketplace/product-detail-modal.tsx
pharmastock-app/components/marketplace/reservations-list.tsx
pharmastock-app/components/marketplace/reserve-product-modal.tsx
pharmastock-app/components/marketplace/sell-product-modal.tsx
pharmastock-app/components/network/contact-pharmacy-modal.tsx
pharmastock-app/components/notifications/notification-analytics.tsx
pharmastock-app/components/notifications/notification-list.tsx
pharmastock-app/components/notifications/notification-preferences.tsx
pharmastock-app/components/notifications/notification-test.tsx
pharmastock-app/components/profile/pharmacy-documents.tsx
pharmastock-app/components/profile/pharmacy-profile-view.tsx
pharmastock-app/components/profile/pharmacy-profile.tsx
pharmastock-app/components/profile/pharmacy-settings.tsx
pharmastock-app/components/providers/MantineProvider.tsx
pharmastock-app/components/pwa/pwa-init.tsx
pharmastock-app/components/share-button.tsx
pharmastock-app/components/sidebar.tsx
pharmastock-app/components/stocks/stock-form.tsx
pharmastock-app/components/stocks/stock-table.tsx
pharmastock-app/components/theme-provider.tsx
pharmastock-app/components/theme-toggle.tsx
pharmastock-app/components/ui/accordion.tsx
pharmastock-app/components/ui/alert-dialog.tsx
pharmastock-app/components/ui/alert.tsx
pharmastock-app/components/ui/aspect-ratio.tsx
pharmastock-app/components/ui/avatar.tsx
pharmastock-app/components/ui/badge.tsx
pharmastock-app/components/ui/breadcrumb.tsx
pharmastock-app/components/ui/button.tsx
pharmastock-app/components/ui/calendar.tsx
pharmastock-app/components/ui/card.tsx
pharmastock-app/components/ui/carousel.tsx
pharmastock-app/components/ui/chart.tsx
pharmastock-app/components/ui/checkbox.tsx
pharmastock-app/components/ui/collapsible.tsx
pharmastock-app/components/ui/command.tsx
pharmastock-app/components/ui/context-menu.tsx
pharmastock-app/components/ui/custom-badge.tsx
pharmastock-app/components/ui/dialog.tsx
pharmastock-app/components/ui/drawer.tsx
pharmastock-app/components/ui/dropdown-menu.tsx
pharmastock-app/components/ui/expiry-progress.tsx
pharmastock-app/components/ui/form.tsx
pharmastock-app/components/ui/hover-card.tsx
pharmastock-app/components/ui/input-otp.tsx
pharmastock-app/components/ui/input.tsx
pharmastock-app/components/ui/label.tsx
pharmastock-app/components/ui/logo.tsx
pharmastock-app/components/ui/menubar.tsx
pharmastock-app/components/ui/navigation-menu.tsx
pharmastock-app/components/ui/pagination.tsx
pharmastock-app/components/ui/popover.tsx
pharmastock-app/components/ui/popup-provider.tsx
pharmastock-app/components/ui/progress.tsx
pharmastock-app/components/ui/radio-group.tsx
pharmastock-app/components/ui/resizable.tsx
pharmastock-app/components/ui/scroll-area.tsx
pharmastock-app/components/ui/select.tsx
pharmastock-app/components/ui/separator.tsx
pharmastock-app/components/ui/sheet.tsx
pharmastock-app/components/ui/skeleton.tsx
pharmastock-app/components/ui/slider.tsx
pharmastock-app/components/ui/sonner.tsx
pharmastock-app/components/ui/switch.tsx
pharmastock-app/components/ui/table.tsx
pharmastock-app/components/ui/tabs.tsx
pharmastock-app/components/ui/textarea.tsx
pharmastock-app/components/ui/toast.tsx
pharmastock-app/components/ui/toaster.tsx
pharmastock-app/components/ui/toggle-group.tsx
pharmastock-app/components/ui/toggle.tsx
pharmastock-app/components/ui/tooltip.tsx
pharmastock-app/components/ui/use-toast.ts
pharmastock-app/components/urgent-requests/urgent-request-form.tsx
pharmastock-app/components/urgent-requests/urgent-request-modal.tsx
pharmastock-app/components/user-nav.tsx
pharmastock-app/contexts/auth-context.tsx
pharmastock-app/contexts/database-context.tsx
pharmastock-app/contexts/notification-context.tsx
pharmastock-app/contexts/supabase-context.tsx
pharmastock-app/create-all-demo-accounts.js
pharmastock-app/create-demo-notifications.js
pharmastock-app/create-demo-users.js
pharmastock-app/create-final-demo-users.js
pharmastock-app/create-production-demo-users.js
pharmastock-app/create-test-user.js
pharmastock-app/create-working-demo-users.js
pharmastock-app/demo-summary.js
pharmastock-app/fix-demo-users-with-existing-ids.js
pharmastock-app/hooks/use-product-interest.ts
pharmastock-app/hooks/use-realtime-notifications.ts
pharmastock-app/hooks/use-team.ts
pharmastock-app/hooks/use-toast.ts
pharmastock-app/hooks/useAccessControl.ts
pharmastock-app/lib/api-utils.ts
pharmastock-app/lib/config.ts
pharmastock-app/lib/db.ts
pharmastock-app/lib/form-utils.ts
pharmastock-app/lib/logger.ts
pharmastock-app/lib/mantine-theme.ts
pharmastock-app/lib/notifications.ts
pharmastock-app/lib/prisma.ts
pharmastock-app/lib/prisma/index.ts
pharmastock-app/lib/pwa.ts
pharmastock-app/lib/supabaseClient.ts
pharmastock-app/lib/trpc/context.ts
pharmastock-app/lib/trpc/routers/auth.ts
pharmastock-app/lib/trpc/server.ts
pharmastock-app/lib/types/auth.ts
pharmastock-app/lib/types/marketplace.ts
pharmastock-app/lib/utils.ts
pharmastock-app/middleware.ts
pharmastock-app/playwright-report/trace/assets/codeMirrorModule-CyuxU5C-.js
pharmastock-app/playwright-report/trace/assets/defaultSettingsView-5nVJRt0A.js
pharmastock-app/playwright-report/trace/index.qVn2ZnpC.js
pharmastock-app/playwright-report/trace/sw.bundle.js
pharmastock-app/playwright-report/trace/uiMode.m4IPRPOd.js
pharmastock-app/playwright.config.ts
pharmastock-app/public/sw.js
pharmastock-app/public/workbox-4754cb34.js
pharmastock-app/reset-demo.js
pharmastock-app/scripts/analyze-code.js
pharmastock-app/scripts/check-db.js
pharmastock-app/scripts/check-db.ts
pharmastock-app/scripts/check-schema.js
pharmastock-app/scripts/check-users.ts
pharmastock-app/scripts/create-auth-users.js
pharmastock-app/scripts/create-pharmacy-owner.js
pharmastock-app/scripts/create-supabase-users.js
pharmastock-app/scripts/create-super-admin.js
pharmastock-app/scripts/create-team-member.js
pharmastock-app/scripts/create-test-user.js
pharmastock-app/scripts/create-test-users-properly.js
pharmastock-app/scripts/create-test-users.ts
pharmastock-app/scripts/create-users-with-api.js
pharmastock-app/scripts/fix-passwords.js
pharmastock-app/scripts/fix-pharma-test-user.js
pharmastock-app/scripts/generate-icons.js
pharmastock-app/scripts/refresh-user-session.js
pharmastock-app/scripts/reset-auth-schema.js
pharmastock-app/scripts/reset-pharmacy-owner-password.js
pharmastock-app/scripts/reset-user-passwords.js
pharmastock-app/scripts/run-tests.ts
pharmastock-app/scripts/setup-database.js
pharmastock-app/scripts/setup-test-environment.js
pharmastock-app/scripts/setup-test-pharmacy.js
pharmastock-app/scripts/setup-test-users-admin.js
pharmastock-app/scripts/setup-test-users-final.js
pharmastock-app/scripts/setup-test-users-simple.js
pharmastock-app/scripts/setup-test-users.js
pharmastock-app/scripts/setup-users-properly.js
pharmastock-app/scripts/test-auth-simple.js
pharmastock-app/scripts/test-session-api.js
pharmastock-app/scripts/test-session-profile-query.js
pharmastock-app/scripts/test-user-roles.js
pharmastock-app/scripts/update-admin-password.js
pharmastock-app/scripts/update-user-passwords.js
pharmastock-app/scripts/utils.js
pharmastock-app/scripts/utils/check-app.ts
pharmastock-app/scripts/utils/test-setup.ts
pharmastock-app/scripts/utils/test-utils.ts
pharmastock-app/seed-pharmacy-demo.js
pharmastock-app/services/mock/admin.ts
pharmastock-app/services/mock/index.ts
pharmastock-app/setup-complete-demo.js
pharmastock-app/setup-demo-pharmacy.js
pharmastock-app/setup-pharmastock-demo.js
pharmastock-app/src/components/PWAInstallPrompt.tsx
pharmastock-app/src/components/SubscriptionStatus.tsx
pharmastock-app/src/components/supplier/SupplierAlerts.tsx
pharmastock-app/src/components/supplier/SupplierAnalytics.tsx
pharmastock-app/src/components/supplier/SupplierDashboard.tsx
pharmastock-app/src/components/supplier/SupplierMainDashboard.tsx
pharmastock-app/src/components/supplier/SupplierNavigation.tsx
pharmastock-app/src/components/supplier/SupplierNetwork.tsx
pharmastock-app/src/components/supplier/SupplierOpportunities.tsx
pharmastock-app/src/components/supplier/SupplierTerritory.tsx
pharmastock-app/src/hooks/useFreemium.ts
pharmastock-app/src/services/pushNotificationService.ts
pharmastock-app/src/services/supabase.ts
pharmastock-app/src/utils/useIsClient.ts
pharmastock-app/supabase/functions/notify-suppliers-excess/index.ts
pharmastock-app/supabase/functions/send-push-notification/index.ts
pharmastock-app/tailwind.config.ts
pharmastock-app/test-auth.js
pharmastock-app/test-demo-accounts.js
pharmastock-app/test-gemini.js
pharmastock-app/test-single-user.js
pharmastock-app/tests/admin/dashboard.spec.ts
pharmastock-app/tests/auth/login.spec.ts
pharmastock-app/tests/example.test.ts
pharmastock-app/tests/global-setup.ts
pharmastock-app/tests/global-teardown.ts
pharmastock-app/tests/prescription-reader.test.js
pharmastock-app/tests/setup/globalSetup.ts
pharmastock-app/tests/setup/testData.ts
pharmastock-app/tests/utils/auth.ts
pharmastock-app/tests/utils/check-app.ts
pharmastock-app/types/index.ts
pharmastock-app/types/supabase.ts
pharmastock-app/update-demo-user-roles.js
pharmastock-app/update-demo-user-to-pro.js
pharmastock-app/website/js/main.js
