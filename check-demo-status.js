import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load production environment
dotenv.config({ path: '.env.production' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkDemoStatus() {
  try {
    console.log('🔍 Checking demo account status in production...\n');
    console.log(`🌐 Supabase URL: ${supabaseUrl}`);

    // Check if demo users exist
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Auth error:', authError.message);
      return;
    }

    const demoUsers = authUsers.users.filter(u => 
      u.email === '<EMAIL>' || 
      u.email === '<EMAIL>'
    );

    console.log(`Found ${demoUsers.length} demo users:`);
    for (const user of demoUsers) {
      console.log(`\n👤 ${user.email}:`);
      console.log(`   ID: ${user.id}`);
      console.log(`   Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
      console.log(`   Role: ${user.user_metadata?.role || 'none'}`);
      console.log(`   Created: ${user.created_at}`);

      // Check profile
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      if (profileError) {
        console.log(`   ❌ Profile: ${profileError.message}`);
      } else if (profile) {
        console.log(`   ✅ Profile: pharmacy_id=${profile.pharmacy_id}, role=${profile.role}`);
      } else {
        console.log(`   ⚠️  Profile: Not found`);
      }

      // Check team membership
      const { data: teamMember, error: teamError } = await supabase
        .from('pharmacy_team_members')
        .select('*, pharmacies(name)')
        .eq('user_id', user.id)
        .single();

      if (teamError) {
        console.log(`   ❌ Team: ${teamError.message}`);
      } else if (teamMember) {
        console.log(`   ✅ Team: ${teamMember.pharmacies?.name} (${teamMember.pharmacy_id}), role=${teamMember.role}, status=${teamMember.status}`);
      } else {
        console.log(`   ⚠️  Team: Not found`);
      }
    }

    // Check demo pharmacy
    console.log('\n🏥 Demo Pharmacy:');
    const { data: pharmacy, error: pharmacyError } = await supabase
      .from('pharmacies')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (pharmacyError) {
      console.log(`❌ ${pharmacyError.message}`);
    } else if (pharmacy) {
      console.log(`✅ ${pharmacy.name} (${pharmacy.id})`);
      console.log(`   Verified: ${pharmacy.is_verified}`);
      console.log(`   City: ${pharmacy.city}`);
    } else {
      console.log(`⚠️  Not found`);
    }

    // Test login
    console.log('\n🔐 Testing Login:');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123!'
    });

    if (loginError) {
      console.log(`❌ Login failed: ${loginError.message}`);
    } else {
      console.log(`✅ Login successful`);
      console.log(`   Session: ${loginData.session ? 'Valid' : 'Invalid'}`);
      console.log(`   User ID: ${loginData.user.id}`);
    }

    console.log('\n📋 Summary:');
    console.log(`- Demo users found: ${demoUsers.length}/2`);
    console.log(`- Users with profiles: ${demoUsers.filter(u => u.user_metadata?.role).length}/2`);
    console.log(`- Pharmacy exists: ${pharmacy ? 'Yes' : 'No'}`);
    console.log(`- Login works: ${loginError ? 'No' : 'Yes'}`);

    if (demoUsers.length === 2 && pharmacy && !loginError) {
      console.log('\n🎉 Demo accounts appear to be set up correctly!');
      console.log('🔍 If you\'re still getting 401 errors, the issue might be:');
      console.log('   1. Session cookies not being sent properly');
      console.log('   2. Middleware authentication logic');
      console.log('   3. API endpoint permission checks');
    } else {
      console.log('\n⚠️  Demo accounts need to be fixed. Run: npm run fix:demo');
    }

  } catch (error) {
    console.error('💥 Check error:', error.message);
  }
}

checkDemoStatus();
