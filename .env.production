# Production Environment Variables
# OpenAI Configuration
OPENAI_API_KEY=********************************************************************************************************************************************************************
GOOGLE_AI_API_KEY=AIzaSyBE7Wgu24ZiSpNDHhO5mRgL88r0cmkNGqE

# Supabase Configuration (Production)
NEXT_PUBLIC_SUPABASE_URL=https://shptfbdcuwaxyosyjsas.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocHRmYmRjdXdheHlvc3lqc2FzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MTgyNDMsImV4cCI6MjA2NjM5NDI0M30.tvoMbGEU3nL0tZvqib_jrITKzRpf1DE3A2CIk9czTd0
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocHRmYmRjdXdheHlvc3lqc2FzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDgxODI0MywiZXhwIjoyMDY2Mzk0MjQzfQ.HBU_zcarbOtM2jHUrSSSpUi-3vfWf7kSRidLMvFPNjI

# Database Configuration (Production)
DATABASE_URL=************************************************************************/postgres
DB_USER=postgres
DB_PASSWORD=Idemsup7!@
DB_HOST=db.shptfbdcuwaxyosyjsas.supabase.co
DB_PORT=5432
DB_NAME=postgres

# JWT Configuration (Production)
JWT_SECRET=ltRHQ5vYgAPAeod5oIrJEp0Vc4kJA94ssS0c5QIVnCE=

# Storage Configuration (Production)
NEXT_PUBLIC_STORAGE_URL=https://shptfbdcuwaxyosyjsas.supabase.co/storage/v1/s3
STORAGE_S3_REGION=eu-west-1

# API URLs (Production)
NEXT_PUBLIC_API_URL=https://shptfbdcuwaxyosyjsas.supabase.co
NEXT_PUBLIC_GRAPHQL_URL=https://shptfbdcuwaxyosyjsas.supabase.co/graphql/v1
NEXT_PUBLIC_STUDIO_URL=https://supabase.com/dashboard/project/shptfbdcuwaxyosyjsas

# Application Configuration (Production)
NEXT_PUBLIC_APP_URL=https://pharmastock.ma
NODE_ENV=production

# WebSocket (Production)
NEXT_PUBLIC_WS_URL=wss://pharmastock.ma

NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1Ijoid3dtcyIsImEiOiJjbHdrYWQ0eXAxNGM1MmptbTd4YXg2NGxqIn0.tAM-9pPFtZHoVAzuDuLkUg
