"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  Users,
  Activity,
  MapPin,
  TrendingUp,
  Download,
  RefreshCw,
  Settings,
} from "lucide-react";
import { AdminAnalyticsMap } from "@/components/maps/admin-analytics-map";

export default function AdminAnalyticsMapPage() {
  const [activeView, setActiveView] = useState("overview");

  // Mock global metrics
  const globalMetrics = {
    totalPharmacies: 357,
    activeUsers: 1247,
    monthlyTransactions: 8934,
    totalRevenue: 2450000,
    growthRate: 23.5,
    systemUptime: 99.8,
    avgResponseTime: 145,
    userSatisfaction: 4.3
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <BarChart3 className="w-8 h-8 text-red-600" />
            Analytics Géographiques
          </h1>
          <p className="text-muted-foreground">
            Vue d'ensemble nationale des performances de la plateforme
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Configuration
          </Button>
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Exporter Rapport
          </Button>
          <Button className="bg-red-600 hover:bg-red-700">
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser Données
          </Button>
        </div>
      </div>

      {/* Global KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Pharmacies Totales</h3>
              <p className="text-2xl font-bold text-blue-800">{globalMetrics.totalPharmacies}</p>
              <p className="text-xs text-blue-600">+{globalMetrics.growthRate}% ce mois</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <Activity className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Utilisateurs Actifs</h3>
              <p className="text-2xl font-bold text-green-800">{globalMetrics.activeUsers.toLocaleString()}</p>
              <p className="text-xs text-green-600">Dernières 24h</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Transactions</h3>
              <p className="text-2xl font-bold text-purple-800">{globalMetrics.monthlyTransactions.toLocaleString()}</p>
              <p className="text-xs text-purple-600">Ce mois</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <MapPin className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Couverture</h3>
              <p className="text-2xl font-bold text-orange-800">12</p>
              <p className="text-xs text-orange-600">Régions actives</p>
            </div>
          </div>
        </Card>
      </div>

      {/* System Health */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-muted-foreground">Disponibilité Système</h4>
              <p className="text-2xl font-bold text-green-600">{globalMetrics.systemUptime}%</p>
            </div>
            <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center">
              <Activity className="w-6 h-6 text-green-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-muted-foreground">Temps Réponse Moyen</h4>
              <p className="text-2xl font-bold text-blue-600">{globalMetrics.avgResponseTime}ms</p>
            </div>
            <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
              <BarChart3 className="w-6 h-6 text-blue-600" />
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium text-muted-foreground">Satisfaction Utilisateur</h4>
              <p className="text-2xl font-bold text-purple-600">{globalMetrics.userSatisfaction}/5</p>
            </div>
            <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center">
              <Users className="w-6 h-6 text-purple-600" />
            </div>
          </div>
        </Card>
      </div>

      {/* Main Analytics Map */}
      <Tabs value={activeView} onValueChange={setActiveView} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Vue d'Ensemble
          </TabsTrigger>
          <TabsTrigger value="performance" className="flex items-center gap-2">
            <Activity className="w-4 h-4" />
            Performance Système
          </TabsTrigger>
          <TabsTrigger value="growth" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Analyse Croissance
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          <AdminAnalyticsMap />
        </TabsContent>

        <TabsContent value="performance" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Activity className="w-5 h-5 text-green-600" />
              Métriques de Performance Système
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              <div className="p-4 bg-green-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Temps de Réponse API</div>
                <div className="text-2xl font-bold text-green-600">142ms</div>
                <div className="text-xs text-green-600">-8ms vs hier</div>
              </div>
              
              <div className="p-4 bg-blue-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Taux d'Erreur</div>
                <div className="text-2xl font-bold text-blue-600">0.3%</div>
                <div className="text-xs text-blue-600">-0.1% vs hier</div>
              </div>
              
              <div className="p-4 bg-purple-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Charge Serveur</div>
                <div className="text-2xl font-bold text-purple-600">67%</div>
                <div className="text-xs text-purple-600">Optimal</div>
              </div>
              
              <div className="p-4 bg-orange-50 rounded-lg">
                <div className="text-sm text-muted-foreground">Connexions Actives</div>
                <div className="text-2xl font-bold text-orange-600">1,247</div>
                <div className="text-xs text-orange-600">+156 vs hier</div>
              </div>
            </div>

            <div className="mt-6">
              <h4 className="font-medium mb-3">Performance par Région</h4>
              <div className="space-y-2">
                {[
                  { region: 'Casablanca-Settat', responseTime: 125, errorRate: 0.2, status: 'excellent' },
                  { region: 'Rabat-Salé-Kénitra', responseTime: 138, errorRate: 0.1, status: 'excellent' },
                  { region: 'Marrakech-Safi', responseTime: 167, errorRate: 0.4, status: 'bon' },
                  { region: 'Fès-Meknès', responseTime: 189, errorRate: 0.6, status: 'moyen' }
                ].map((region, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded">
                    <div>
                      <div className="font-medium">{region.region}</div>
                      <div className="text-sm text-muted-foreground">
                        {region.responseTime}ms • {region.errorRate}% erreurs
                      </div>
                    </div>
                    <div className={`px-2 py-1 rounded text-xs font-medium ${
                      region.status === 'excellent' ? 'bg-green-100 text-green-800' :
                      region.status === 'bon' ? 'bg-blue-100 text-blue-800' :
                      'bg-yellow-100 text-yellow-800'
                    }`}>
                      {region.status}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="growth" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-blue-600" />
              Analyse de Croissance
            </h3>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              <div>
                <h4 className="font-medium mb-3">Croissance par Région</h4>
                <div className="space-y-3">
                  {[
                    { region: 'Marrakech-Safi', growth: 31, pharmacies: 67 },
                    { region: 'Casablanca-Settat', growth: 23, pharmacies: 156 },
                    { region: 'Rabat-Salé-Kénitra', growth: 18, pharmacies: 89 },
                    { region: 'Fès-Meknès', growth: 12, pharmacies: 45 }
                  ].map((region, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <div>
                        <div className="font-medium text-sm">{region.region}</div>
                        <div className="text-xs text-muted-foreground">{region.pharmacies} pharmacies</div>
                      </div>
                      <div className="text-right">
                        <div className="text-green-600 font-bold">+{region.growth}%</div>
                        <div className="text-xs text-muted-foreground">ce mois</div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3">Métriques d'Adoption</h4>
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Marketplace</span>
                    <span className="font-bold text-blue-600">89%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Réseau SOS</span>
                    <span className="font-bold text-green-600">76%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Pharmacy Radar</span>
                    <span className="font-bold text-purple-600">67%</span>
                  </div>
                  <div className="flex items-center justify-between">
                    <span className="text-sm">Demande Saisonnière</span>
                    <span className="font-bold text-orange-600">45%</span>
                  </div>
                </div>
              </div>

              <div>
                <h4 className="font-medium mb-3">Objectifs 2024</h4>
                <div className="space-y-3">
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Pharmacies Actives</span>
                      <span>357/500</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-blue-600 h-2 rounded-full" style={{ width: '71%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Transactions Mensuelles</span>
                      <span>8.9K/15K</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-green-600 h-2 rounded-full" style={{ width: '59%' }}></div>
                    </div>
                  </div>
                  <div>
                    <div className="flex justify-between text-sm mb-1">
                      <span>Couverture Régionale</span>
                      <span>12/16</span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div className="bg-purple-600 h-2 rounded-full" style={{ width: '75%' }}></div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Quick Actions */}
      <Card className="p-6 bg-gradient-to-r from-red-50 to-orange-50 border-red-200">
        <div className="flex items-center justify-between">
          <div>
            <h3 className="font-semibold text-red-900">Actions Rapides</h3>
            <p className="text-red-700">Outils d'administration et de monitoring</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Users className="w-4 h-4 mr-2" />
              Gestion Utilisateurs
            </Button>
            <Button variant="outline">
              <BarChart3 className="w-4 h-4 mr-2" />
              Rapports Détaillés
            </Button>
            <Button className="bg-red-600 hover:bg-red-700">
              <Settings className="w-4 h-4 mr-2" />
              Configuration Système
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
