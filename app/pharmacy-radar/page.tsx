"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Radar,
  Map,
  TrendingUp,
  Bell,
  Settings,
  Info,
  Zap,
  Users,
  Package,
  AlertTriangle,
} from "lucide-react";
import { MapInterface } from "@/components/pharmacy-radar/map-interface";

export default function PharmacyRadarPage() {
  const [activeTab, setActiveTab] = useState("map");
  const [notificationsEnabled, setNotificationsEnabled] = useState(true);

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Radar className="w-8 h-8 text-blue-600" />
            Pharmacy Radar
          </h1>
          <p className="text-muted-foreground">
            Visualisation en temps réel du réseau pharmaceutique
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => setNotificationsEnabled(!notificationsEnabled)}
          >
            <Bell className={`w-4 h-4 mr-2 ${notificationsEnabled ? 'text-blue-600' : 'text-gray-400'}`} />
            Notifications {notificationsEnabled ? 'ON' : 'OFF'}
          </Button>
          <Button variant="outline">
            <Settings className="w-4 h-4 mr-2" />
            Paramètres
          </Button>
        </div>
      </div>

      {/* Feature Overview */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Map className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Carte Interactive</h3>
              <p className="text-sm text-blue-700">Localisation en temps réel</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Indicateurs Anonymes</h3>
              <p className="text-sm text-green-700">Demande et offre agrégées</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <Zap className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Proximité Avancée</h3>
              <p className="text-sm text-orange-700">Calculs optimisés</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Bell className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Notifications Push</h3>
              <p className="text-sm text-purple-700">Opportunités en temps réel</p>
            </div>
          </div>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="map" className="flex items-center gap-2">
            <Map className="w-4 h-4" />
            Carte Interactive
          </TabsTrigger>
          <TabsTrigger value="indicators" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Indicateurs
          </TabsTrigger>
          <TabsTrigger value="notifications" className="flex items-center gap-2">
            <Bell className="w-4 h-4" />
            Notifications
          </TabsTrigger>
          <TabsTrigger value="settings" className="flex items-center gap-2">
            <Settings className="w-4 h-4" />
            Paramètres
          </TabsTrigger>
        </TabsList>

        <TabsContent value="map" className="space-y-4">
          <MapInterface />
        </TabsContent>

        <TabsContent value="indicators" className="space-y-4">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <h3 className="text-lg font-semibold">Indicateurs Anonymes de Demande/Offre</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Demand Indicators */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <AlertTriangle className="w-4 h-4 text-orange-600" />
                  Indicateurs de Demande
                </h4>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <div className="font-medium text-red-900">Zone Critique</div>
                      <div className="text-sm text-red-700">Secteur Maarif</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-red-600">95%</div>
                      <div className="text-xs text-red-500">Demande</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-orange-50 rounded-lg">
                    <div>
                      <div className="font-medium text-orange-900">Zone Forte</div>
                      <div className="text-sm text-orange-700">Centre-ville</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-orange-600">78%</div>
                      <div className="text-xs text-orange-500">Demande</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                    <div>
                      <div className="font-medium text-yellow-900">Zone Moyenne</div>
                      <div className="text-sm text-yellow-700">Hay Hassani</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-yellow-600">45%</div>
                      <div className="text-xs text-yellow-500">Demande</div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Supply Indicators */}
              <div className="space-y-4">
                <h4 className="font-medium flex items-center gap-2">
                  <Package className="w-4 h-4 text-green-600" />
                  Indicateurs d'Offre
                </h4>
                
                <div className="space-y-3">
                  <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                    <div>
                      <div className="font-medium text-green-900">Stock Élevé</div>
                      <div className="text-sm text-green-700">Ain Sebaa</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">87%</div>
                      <div className="text-xs text-green-500">Disponibilité</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                    <div>
                      <div className="font-medium text-blue-900">Stock Moyen</div>
                      <div className="text-sm text-blue-700">Bernoussi</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">62%</div>
                      <div className="text-xs text-blue-500">Disponibilité</div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <div className="font-medium text-red-900">Stock Faible</div>
                      <div className="text-sm text-red-700">Sidi Bernoussi</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-red-600">23%</div>
                      <div className="text-xs text-red-500">Disponibilité</div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            <div className="mt-6 p-4 bg-blue-50 rounded-lg">
              <div className="flex items-start gap-2">
                <Info className="w-4 h-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <div className="font-medium">Confidentialité des Données</div>
                  <div className="mt-1">
                    Tous les indicateurs sont anonymisés et agrégés pour protéger la confidentialité 
                    des pharmacies individuelles. Les données sont mises à jour en temps réel 
                    pour fournir une vue d'ensemble précise du marché.
                  </div>
                </div>
              </div>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="notifications" className="space-y-4">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Bell className="w-5 h-5 text-purple-600" />
              <h3 className="text-lg font-semibold">Notifications Push pour Opportunités</h3>
            </div>
            
            <div className="text-center py-8">
              <Bell className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Système de Notifications</h3>
              <p className="text-muted-foreground mb-4">
                Recevez des alertes en temps réel pour les opportunités à proximité
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Nouvelles annonces dans votre zone</div>
                <div>• Demandes urgentes à proximité</div>
                <div>• Activité du marché local</div>
                <div>• Alertes de stock critique</div>
              </div>
              <Button className="mt-4" disabled>
                <Bell className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="settings" className="space-y-4">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-4">
              <Settings className="w-5 h-5 text-gray-600" />
              <h3 className="text-lg font-semibold">Paramètres du Radar</h3>
            </div>
            
            <div className="text-center py-8">
              <Settings className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Configuration Avancée</h3>
              <p className="text-muted-foreground mb-4">
                Personnalisez votre expérience Pharmacy Radar
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Préférences de notification</div>
                <div>• Rayon de recherche par défaut</div>
                <div>• Anonymisation des données</div>
                <div>• Fréquence de mise à jour</div>
              </div>
              <Button className="mt-4" disabled>
                <Settings className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
