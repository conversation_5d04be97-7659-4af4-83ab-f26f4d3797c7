"use client";

import { useEffect } from 'react';

export default function ForceRedirectPage() {
  useEffect(() => {
    console.log('🚀 Force redirect page loaded');
    console.log('📍 Current URL:', window.location.href);
    
    // Nuclear option: immediate redirect with multiple fallbacks
    const redirect = () => {
      console.log('🎯 Attempting immediate redirect to dashboard');
      
      try {
        // Method 1: Direct assignment
        console.log('Method 1: window.location.href');
        window.location.href = '/dashboard';
        
        // Method 2: Replace (fallback after 500ms)
        setTimeout(() => {
          console.log('Method 2: window.location.replace');
          window.location.replace('/dashboard');
        }, 500);
        
        // Method 3: Assign to different property (fallback after 1s)
        setTimeout(() => {
          console.log('Method 3: window.location.assign');
          window.location.assign('/dashboard');
        }, 1000);
        
        // Method 4: Manual navigation (fallback after 1.5s)
        setTimeout(() => {
          console.log('Method 4: Manual navigation');
          const link = document.createElement('a');
          link.href = '/dashboard';
          link.click();
        }, 1500);
        
        // Method 5: Form submission (fallback after 2s)
        setTimeout(() => {
          console.log('Method 5: Form submission');
          const form = document.createElement('form');
          form.method = 'GET';
          form.action = '/dashboard';
          document.body.appendChild(form);
          form.submit();
        }, 2000);
        
      } catch (error) {
        console.error('❌ All redirect methods failed:', error);
      }
    };
    
    // Start redirect immediately
    redirect();
    
    // Also try after a short delay
    setTimeout(redirect, 100);
    
  }, []);

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      alignItems: 'center',
      justifyContent: 'center',
      minHeight: '100vh',
      backgroundColor: '#f8f9fa',
      fontFamily: 'Arial, sans-serif'
    }}>
      <h1 style={{ color: '#007bff', marginBottom: '20px' }}>
        🚀 Force Redirect
      </h1>
      <p style={{ fontSize: '18px', marginBottom: '20px' }}>
        Attempting multiple redirect methods...
      </p>
      <div style={{
        backgroundColor: 'white',
        padding: '20px',
        borderRadius: '8px',
        boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
        maxWidth: '500px',
        textAlign: 'center'
      }}>
        <p>If you're seeing this page, the automatic redirect failed.</p>
        <button
          onClick={() => {
            console.log('🔄 Manual redirect button clicked');
            window.location.href = '/dashboard';
          }}
          style={{
            backgroundColor: '#007bff',
            color: 'white',
            border: 'none',
            padding: '12px 24px',
            borderRadius: '4px',
            fontSize: '16px',
            cursor: 'pointer',
            marginTop: '10px'
          }}
        >
          Click Here to Go to Dashboard
        </button>
        
        <div style={{ marginTop: '20px', fontSize: '14px', color: '#666' }}>
          <p>Debug info:</p>
          <p>Current URL: {typeof window !== 'undefined' ? window.location.href : 'Loading...'}</p>
        </div>
      </div>
    </div>
  );
}
