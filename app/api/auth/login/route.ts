import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Create Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Attempt login
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return NextResponse.json({
        error: error.message || "Authentication failed"
      }, { status: 401 });
    }

    if (!data.user || !data.session) {
      return NextResponse.json(
        { error: "No session data returned" },
        { status: 401 }
      );
    }

    // Create response with session cookies
    const response = NextResponse.json({
      success: true,
      user: {
        id: data.user.id,
        email: data.user.email,
        role: data.user.user_metadata?.role || 'staff'
      }
    });

    // Manually set session cookies to ensure they're sent
    if (data.session) {
      // Set access token cookie
      response.cookies.set({
        name: 'sb-access-token',
        value: data.session.access_token,
        httpOnly: false, // Needs to be accessible to client
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: data.session.expires_in || 3600
      });

      // Set refresh token cookie
      response.cookies.set({
        name: 'sb-refresh-token',
        value: data.session.refresh_token,
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: 60 * 60 * 24 * 7 // 7 days
      });

      // Set session cookie for server-side auth
      response.cookies.set({
        name: 'sb-session',
        value: JSON.stringify({
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at,
          user: {
            id: data.user.id,
            email: data.user.email,
            role: data.user.user_metadata?.role || 'staff'
          }
        }),
        httpOnly: true,
        secure: process.env.NODE_ENV === 'production',
        sameSite: 'lax',
        path: '/',
        maxAge: data.session.expires_in || 3600
      });
    }

    return response;

  } catch (error: any) {
    return NextResponse.json(
      { error: "Server error occurred" },
      { status: 500 }
    );
  }
}
