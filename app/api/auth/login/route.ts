import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';

export async function POST(request: NextRequest) {
  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    // Create Supabase client
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Attempt login
    const { data, error } = await supabase.auth.signInWithPassword({
      email,
      password,
    });

    if (error) {
      return NextResponse.json({
        error: error.message || "Authentication failed"
      }, { status: 401 });
    }

    if (!data.user || !data.session) {
      return NextResponse.json(
        { error: "No session data returned" },
        { status: 401 }
      );
    }

    // Return success with minimal data
    return NextResponse.json({
      success: true,
      user: {
        id: data.user.id,
        email: data.user.email,
        role: data.user.user_metadata?.role || 'staff'
      }
    });

  } catch (error: any) {
    return NextResponse.json(
      { error: "Server error occurred" },
      { status: 500 }
    );
  }
}
