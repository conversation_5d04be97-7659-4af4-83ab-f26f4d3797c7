import { NextRequest, NextResponse } from "next/server";
import { supabase } from "@/src/services/supabase";
import { createClient } from "@supabase/supabase-js";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/types/auth";

// Service client for admin operations
const serviceSupabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL || "http://127.0.0.1:54351",
  process.env.SUPABASE_SERVICE_ROLE_KEY ||
    "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU",
  {
    auth: {
      autoRefreshToken: false,
      persistSession: false,
    },
  }
);

// Disable edge runtime as it might be causing issues with Supabase auth
export const runtime = "nodejs";

export async function POST(request: NextRequest) {
  logger.debug("Login attempt initiated");

  try {
    const { email, password } = await request.json();

    if (!email || !password) {
      logger.warn("Login attempt missing credentials");
      return NextResponse.json(
        { error: "Email and password are required" },
        { status: 400 }
      );
    }

    logger.debug({ email }, "Attempting to sign in user");

    // Use the simple supabase client for auth

    try {
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        logger.error(
          {
            error: error.message,
            status: error.status,
            code: error.code,
            email,
          },
          "Login failed with Supabase error"
        );

        const errorMessage =
          error.message === "Invalid login credentials"
            ? "Email or password is incorrect"
            : "Authentication failed. Please try again.";

        return NextResponse.json({ error: errorMessage }, { status: 401 });
      }

      if (!data.user || !data.session) {
        logger.error(
          { email },
          "No user or session returned after successful login"
        );
        return NextResponse.json(
          { error: "Authentication failed - no session data" },
          { status: 401 }
        );
      }

      logger.info(
        { userId: data.user.id, email },
        "User logged in successfully"
      );

      // Get additional user data if needed using service client (bypasses RLS)

      let teamMember = null;
      let pharmacy = null;
      let profile = null;

      // Fetch team member data using direct table query
      const { data: teamData, error: teamError } = await serviceSupabase
        .from("pharmacy_team_members")
        .select("pharmacy_id, status, role")
        .eq("user_id", data.user.id)
        .single();

      if (teamError) {
        logger.warn(
          {
            error: teamError.message,
            userId: data.user.id,
          },
          "No team member data found (this is normal for some users)"
        );
      } else if (teamData) {
        teamMember = {
          pharmacy_id: teamData.pharmacy_id,
          status: teamData.status,
          role: teamData.role,
        };
      }

      // Fetch pharmacy info if user is associated with a pharmacy
      if (teamMember?.pharmacy_id) {
        const { data: pharmacyData, error: pharmacyError } =
          await serviceSupabase
            .from("pharmacies")
            .select("*")
            .eq("id", teamMember.pharmacy_id)
            .single();

        if (pharmacyError) {
          logger.warn(
            {
              error: pharmacyError.message,
              pharmacyId: teamMember.pharmacy_id,
            },
            "Error fetching pharmacy info"
          );
        } else if (pharmacyData) {
          pharmacy = pharmacyData;
        }
      }

      // Get user profile from database for accurate role information
      const { data: profileData, error: profileError } =
        await serviceSupabase
          .from("profiles")
          .select("*")
          .eq("id", data.user.id)
          .single();

      if (profileError) {
        logger.warn(
          {
            error: profileError.message,
            userId: data.user.id,
          },
          "Error fetching user profile (this is normal for new users)"
        );
      } else if (profileData) {
        profile = profileData;
      }

      // Determine user role with priority: profile.role > metadata.role > 'staff'
      const metadataRole = data.user.user_metadata?.role as
        | UserRole
        | undefined;
      let userRole: UserRole = "staff";

      if (profile?.role) {
        userRole = profile.role as UserRole;
      } else if (metadataRole === "owner") {
        userRole = metadataRole;
      }

      // Create user data for response and session
      const userData = {
        id: data.user.id,
        email: data.user.email,
        role: userRole,
        // Super admins don't need a specific pharmacy association
        pharmacyId:
          userRole === "super_admin" ? null : teamMember?.pharmacy_id || null,
      };

      // Create the response
      const response = NextResponse.json({
        user: userData,
        session: data.session,
        teamMember: teamMember || null,
        pharmacy,
      });

      // Set the session cookie that middleware expects
      response.cookies.set({
        name: "sb-session",
        value: JSON.stringify({
          user: userData,
          access_token: data.session.access_token,
          refresh_token: data.session.refresh_token,
          expires_at: data.session.expires_at,
        }),
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: data.session.expires_in,
      });

      // Debug: log Set-Cookie header for sb-session
      const setCookieHeader = response.headers.get("Set-Cookie");
      logger.info({ setCookieHeader }, "Set-Cookie header after login");

      // Set additional auth cookies for compatibility
      response.cookies.set({
        name: "sb-access-token",
        value: data.session.access_token,
        httpOnly: false,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: data.session.expires_in,
      });

      response.cookies.set({
        name: "sb-refresh-token",
        value: data.session.refresh_token,
        httpOnly: true,
        secure: process.env.NODE_ENV === "production",
        sameSite: "lax",
        path: "/",
        maxAge: 60 * 60 * 24 * 7, // 7 days
      });

      return response;
    } catch (error: any) {
      logger.error(
        {
          error: error.message,
          stack: error.stack,
          context: "Unhandled error during login process",
        },
        "Unhandled error in POST /api/auth/login"
      );
      return NextResponse.json(
        { error: "Internal server error." },
        { status: 500 }
      );
    }
  } catch (error) {
    logger.error({ error }, "Error processing login request");
    return NextResponse.json(
      { error: "An error occurred while processing your request" },
      { status: 500 }
    );
  }
}
