import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase/admin";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getSupabaseAdmin();

    // Check demo users in auth
    const { data: authUsers, error: authError } = await serviceSupabase.auth.admin.listUsers();
    
    if (authError) {
      return NextResponse.json({ 
        error: "Failed to fetch auth users",
        details: authError.message
      }, { status: 500 });
    }

    const demoUsers = authUsers.users.filter(u => 
      u.email === '<EMAIL>' || 
      u.email === '<EMAIL>'
    );

    // Check profiles for demo users
    const profileChecks = await Promise.all(
      demoUsers.map(async (user) => {
        const { data: profile, error: profileError } = await serviceSupabase
          .from('profiles')
          .select('*')
          .eq('id', user.id)
          .single();

        const { data: teamMember, error: teamError } = await serviceSupabase
          .from('pharmacy_team_members')
          .select('*, pharmacies(name, email)')
          .eq('user_id', user.id)
          .single();

        return {
          email: user.email,
          userId: user.id,
          confirmed: !!user.email_confirmed_at,
          role: user.user_metadata?.role,
          profile: profile || null,
          profileError: profileError?.message || null,
          teamMember: teamMember || null,
          teamError: teamError?.message || null
        };
      })
    );

    // Check demo pharmacy
    const { data: demoPharmacy, error: pharmacyError } = await serviceSupabase
      .from('pharmacies')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    return NextResponse.json({
      success: true,
      demoUsers: profileChecks,
      demoPharmacy: demoPharmacy || null,
      pharmacyError: pharmacyError?.message || null,
      summary: {
        usersFound: demoUsers.length,
        usersWithProfiles: profileChecks.filter(u => u.profile).length,
        usersWithTeamMembership: profileChecks.filter(u => u.teamMember).length,
        pharmacyExists: !!demoPharmacy
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      error: "Demo status check failed",
      details: error.message
    }, { status: 500 });
  }
}
