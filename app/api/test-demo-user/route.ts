import { NextRequest, NextResponse } from "next/server";
import { getSupabaseAdmin } from "@/lib/supabase/admin";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getSupabaseAdmin();

    // Check demo owner user
    const demoEmail = '<EMAIL>';
    
    // Get user from auth
    const { data: authUsers } = await serviceSupabase.auth.admin.listUsers();
    const demoUser = authUsers.users.find(u => u.email === demoEmail);
    
    if (!demoUser) {
      return NextResponse.json({ error: 'Demo user not found' }, { status: 404 });
    }

    // Get profile
    const { data: profile, error: profileError } = await serviceSupabase
      .from('profiles')
      .select('*')
      .eq('id', demoUser.id)
      .single();

    // Get team membership
    const { data: teamMember, error: teamError } = await serviceSupabase
      .from('pharmacy_team_members')
      .select('*, pharmacies(name)')
      .eq('user_id', demoUser.id)
      .single();

    return NextResponse.json({
      success: true,
      demoUser: {
        id: demoUser.id,
        email: demoUser.email,
        role: demoUser.user_metadata?.role,
        confirmed: !!demoUser.email_confirmed_at
      },
      profile: profile || null,
      profileError: profileError?.message || null,
      teamMember: teamMember || null,
      teamError: teamError?.message || null,
      summary: {
        hasProfile: !!profile,
        hasTeamMembership: !!teamMember,
        pharmacyIdFromProfile: profile?.pharmacy_id || null,
        pharmacyIdFromTeam: teamMember?.pharmacy_id || null,
        pharmacyName: teamMember?.pharmacies?.name || null
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      error: 'Test failed',
      details: error.message
    }, { status: 500 });
  }
}
