import { NextRequest, NextResponse } from "next/server";
import { requireAuth, R<PERSON><PERSON> } from "@/lib/auth-helpers";
import { getSupabaseAdmin } from "@/lib/supabase/admin";
import { logger } from "@/lib/logger";

interface PharmacyRadarData {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  distance?: number;
  demandLevel: 'low' | 'medium' | 'high' | 'critical';
  supplyLevel: 'low' | 'medium' | 'high';
  urgentRequests: number;
  activeListings: number;
  lastActivity: string;
  isAnonymous: boolean;
}

interface DemandSupplyIndicator {
  latitude: number;
  longitude: number;
  demandIntensity: number; // 0-100
  supplyIntensity: number; // 0-100
  urgentCount: number;
  listingCount: number;
  radius: number; // Area of influence in km
}

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const radiusKm = parseInt(searchParams.get('radius') || '20'); // Default 20km
    const includeAnonymous = searchParams.get('anonymous') !== 'false'; // Default true
    const viewMode = searchParams.get('mode') || 'both'; // 'pharmacies', 'heatmap', 'both'

    // Validate radius (max 100km for performance)
    if (radiusKm > 100) {
      return NextResponse.json(
        { error: "Rayon maximum autorisé: 100km" },
        { status: 400 }
      );
    }

    // Get current pharmacy location
    const { data: myPharmacy, error: pharmacyError } = await serviceSupabase
      .from("pharmacies")
      .select("latitude, longitude")
      .eq("id", user.pharmacyId)
      .single();

    if (pharmacyError || !myPharmacy) {
      return NextResponse.json(
        { error: "Impossible de localiser votre pharmacie" },
        { status: 400 }
      );
    }

    const pharmacyData: PharmacyRadarData[] = [];
    const indicators: DemandSupplyIndicator[] = [];

    // Fetch nearby pharmacies with activity data
    if (viewMode === 'pharmacies' || viewMode === 'both') {
      const { data: nearbyPharmacies, error: nearbyError } = await serviceSupabase
        .from("pharmacies")
        .select(`
          id,
          name,
          latitude,
          longitude,
          updated_at
        `)
        .neq("id", user.pharmacyId) // Exclude own pharmacy
        .limit(50); // Limit for performance

      if (nearbyError) {
        throw nearbyError;
      }

      if (nearbyPharmacies) {
        // Calculate distances and filter by radius
        for (const pharmacy of nearbyPharmacies) {
          if (!pharmacy.latitude || !pharmacy.longitude) continue;

          // Haversine formula for distance calculation
          const R = 6371; // Earth's radius in kilometers
          const dLat = (pharmacy.latitude - myPharmacy.latitude) * Math.PI / 180;
          const dLon = (pharmacy.longitude - myPharmacy.longitude) * Math.PI / 180;
          const a = 
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(myPharmacy.latitude * Math.PI / 180) * Math.cos(pharmacy.latitude * Math.PI / 180) * 
            Math.sin(dLon/2) * Math.sin(dLon/2);
          const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
          const distance = R * c;

          if (distance <= radiusKm) {
            // Get activity data for this pharmacy
            const [urgentRequestsResult, activeListingsResult] = await Promise.all([
              // Count urgent requests from this pharmacy (last 24 hours)
              serviceSupabase
                .from("urgent_requests")
                .select("count")
                .eq("pharmacy_id", pharmacy.id)
                .eq("status", "active")
                .gte("created_at", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString()),
              
              // Count active marketplace listings from this pharmacy
              serviceSupabase
                .from("marketplace_listings")
                .select("count")
                .eq("pharmacy_id", pharmacy.id)
                .eq("status", "active")
            ]);

            const urgentRequests = urgentRequestsResult.data?.[0]?.count || 0;
            const activeListings = activeListingsResult.data?.[0]?.count || 0;

            // Calculate demand and supply levels based on activity
            const demandLevel = urgentRequests >= 3 ? 'critical' : 
                               urgentRequests >= 2 ? 'high' : 
                               urgentRequests >= 1 ? 'medium' : 'low';
            
            const supplyLevel = activeListings >= 10 ? 'high' : 
                               activeListings >= 5 ? 'medium' : 'low';

            // Determine if pharmacy should be anonymous (privacy setting)
            const isAnonymous = Math.random() > 0.7; // 30% chance to show name (simulate privacy settings)

            pharmacyData.push({
              id: pharmacy.id,
              name: pharmacy.name,
              latitude: pharmacy.latitude,
              longitude: pharmacy.longitude,
              distance: Math.round(distance * 10) / 10,
              demandLevel: demandLevel as any,
              supplyLevel: supplyLevel as any,
              urgentRequests,
              activeListings,
              lastActivity: pharmacy.updated_at,
              isAnonymous
            });
          }
        }
      }
    }

    // Generate demand/supply heat map indicators
    if (viewMode === 'heatmap' || viewMode === 'both') {
      // Create aggregated indicators based on pharmacy clusters
      const clusters = createPharmacyClusters(pharmacyData, 5); // 5km cluster radius
      
      for (const cluster of clusters) {
        const totalDemand = cluster.pharmacies.reduce((sum, p) => {
          const demandScore = p.demandLevel === 'critical' ? 100 : 
                             p.demandLevel === 'high' ? 75 : 
                             p.demandLevel === 'medium' ? 50 : 25;
          return sum + demandScore;
        }, 0);

        const totalSupply = cluster.pharmacies.reduce((sum, p) => {
          const supplyScore = p.supplyLevel === 'high' ? 100 : 
                             p.supplyLevel === 'medium' ? 60 : 30;
          return sum + supplyScore;
        }, 0);

        const avgDemand = cluster.pharmacies.length > 0 ? totalDemand / cluster.pharmacies.length : 0;
        const avgSupply = cluster.pharmacies.length > 0 ? totalSupply / cluster.pharmacies.length : 0;

        indicators.push({
          latitude: cluster.centerLat,
          longitude: cluster.centerLng,
          demandIntensity: Math.min(100, avgDemand),
          supplyIntensity: Math.min(100, avgSupply),
          urgentCount: cluster.pharmacies.reduce((sum, p) => sum + p.urgentRequests, 0),
          listingCount: cluster.pharmacies.reduce((sum, p) => sum + p.activeListings, 0),
          radius: cluster.radius
        });
      }
    }

    // Filter anonymous pharmacies if requested
    const filteredPharmacyData = includeAnonymous ? 
      pharmacyData : 
      pharmacyData.filter(p => !p.isAnonymous);

    // Calculate network statistics
    const networkStats = {
      totalPharmacies: filteredPharmacyData.length,
      urgentRequests: filteredPharmacyData.reduce((sum, p) => sum + p.urgentRequests, 0),
      activeListings: filteredPharmacyData.reduce((sum, p) => sum + p.activeListings, 0),
      highDemandAreas: filteredPharmacyData.filter(p => p.demandLevel === 'high' || p.demandLevel === 'critical').length,
      lowSupplyAreas: filteredPharmacyData.filter(p => p.supplyLevel === 'low').length,
      averageDistance: filteredPharmacyData.length > 0 
        ? Math.round((filteredPharmacyData.reduce((sum, p) => sum + (p.distance || 0), 0) / filteredPharmacyData.length) * 10) / 10
        : 0
    };

    return NextResponse.json({
      pharmacies: filteredPharmacyData,
      indicators,
      networkStats,
      metadata: {
        radius: radiusKm,
        center: { 
          latitude: myPharmacy.latitude, 
          longitude: myPharmacy.longitude 
        },
        includeAnonymous,
        viewMode,
        lastUpdate: new Date().toISOString()
      }
    });

  } catch (error: any) {
    logger.error("Error fetching pharmacy radar data", { error });
    return NextResponse.json(
      { error: "Failed to fetch pharmacy radar data" },
      { status: 500 }
    );
  }
}

// Helper function to create pharmacy clusters for heat map
function createPharmacyClusters(pharmacies: PharmacyRadarData[], clusterRadius: number) {
  const clusters: {
    centerLat: number;
    centerLng: number;
    radius: number;
    pharmacies: PharmacyRadarData[];
  }[] = [];

  const processed = new Set<string>();

  for (const pharmacy of pharmacies) {
    if (processed.has(pharmacy.id)) continue;

    const cluster = {
      centerLat: pharmacy.latitude,
      centerLng: pharmacy.longitude,
      radius: clusterRadius,
      pharmacies: [pharmacy]
    };

    processed.add(pharmacy.id);

    // Find nearby pharmacies to include in this cluster
    for (const otherPharmacy of pharmacies) {
      if (processed.has(otherPharmacy.id)) continue;

      const distance = calculateDistance(
        pharmacy.latitude, pharmacy.longitude,
        otherPharmacy.latitude, otherPharmacy.longitude
      );

      if (distance <= clusterRadius) {
        cluster.pharmacies.push(otherPharmacy);
        processed.add(otherPharmacy.id);
      }
    }

    // Calculate cluster center (centroid)
    if (cluster.pharmacies.length > 1) {
      cluster.centerLat = cluster.pharmacies.reduce((sum, p) => sum + p.latitude, 0) / cluster.pharmacies.length;
      cluster.centerLng = cluster.pharmacies.reduce((sum, p) => sum + p.longitude, 0) / cluster.pharmacies.length;
    }

    clusters.push(cluster);
  }

  return clusters;
}

// Helper function for distance calculation
function calculateDistance(lat1: number, lon1: number, lat2: number, lon2: number): number {
  const R = 6371; // Earth's radius in kilometers
  const dLat = (lat2 - lat1) * Math.PI / 180;
  const dLon = (lon2 - lon1) * Math.PI / 180;
  const a = 
    Math.sin(dLat/2) * Math.sin(dLat/2) +
    Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
    Math.sin(dLon/2) * Math.sin(dLon/2);
  const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
  return R * c;
}
