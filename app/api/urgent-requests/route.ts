import { NextRequest, NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { getSupabaseAdmin } from "@/lib/supabase/admin";
import { UserRole } from "@/lib/types/auth";
import { requireAuth, ROLES } from "@/lib/auth-helpers";

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      logger.error("Urgent requests API auth failed", { authError, userExists: !!user });
      return NextResponse.json({
        error: "Non autorisé",
        details: authError,
        debug: "Check /api/debug/auth for more details"
      }, { status: 401 });
    }
    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();
    const pharmacyId = user.pharmacyId;

    // Get urgent requests from this pharmacy
    const { data: urgentRequests, error } = await serviceSupabase
      .from("urgent_requests")
      .select("*")
      .eq("pharmacy_id", pharmacyId)
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    // Format the data to match the frontend interface
    const formattedRequests =
      urgentRequests?.map((request: any) => ({
        id: request.id,
        medication: request.product_name,
        quantity: request.quantity_needed,
        urgency:
          request.urgency_level === "critical"
            ? "critique"
            : request.urgency_level === "urgent"
            ? "urgent"
            : "normal",
        status:
          request.status === "active"
            ? "en attente"
            : request.status === "fulfilled"
            ? "accepté"
            : "refusé",
        timestamp: request.created_at,
        notes: request.description,
        responses: [], // TODO: Implement responses system
      })) || [];

    return NextResponse.json({ requests: formattedRequests });
  } catch (error: any) {
    logger.error("Error fetching urgent requests", { error });
    return NextResponse.json(
      { error: "Failed to fetch urgent requests" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      logger.error("Urgent requests POST auth failed", { authError, userExists: !!user });
      return NextResponse.json({
        error: "Non autorisé",
        details: authError,
        debug: "Check /api/debug/auth for more details"
      }, { status: 401 });
    }

    if (!user.pharmacyId) {
      logger.error("Urgent requests POST: No pharmacy ID", {
        userId: user.id,
        email: user.email,
        role: user.role
      });
      return NextResponse.json(
        {
          error: "Aucune pharmacie associée à cet utilisateur.",
          debug: {
            userId: user.id,
            email: user.email,
            role: user.role,
            pharmacyId: user.pharmacyId
          }
        },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();
    const pharmacyId = user.pharmacyId;
    const body = await request.json(); // Moved inside the try block

    // Get pharmacy details for contact info
    interface PharmacyDetails {
      name: string | null;
      phone: string | null;
    }
    const { data: pharmacy, error: pharmacyError } = await serviceSupabase
      .from("pharmacies")
      .select("name, phone")
      .eq("id", pharmacyId)
      .single() as { data: PharmacyDetails | null, error: any };
    
    if (pharmacyError) {
      logger.error("urgent_requests_post", "Error fetching pharmacy details", { error: pharmacyError.message });
      return NextResponse.json({ error: "Failed to fetch pharmacy details" }, { status: 500 });
    }

    // Map urgency levels
    const urgencyMapping: { [key: string]: string } = {
      asap: "critical",
      "24h": "urgent",
      normal: "normal",
    };

    // Create urgent request
    const insertPayload = {
      pharmacy_id: pharmacyId,
      product_name: body.medication,
      description: body.details || null, // Use null for empty string if DB expects null
      quantity_needed: parseInt(body.quantity),
      urgency_level: urgencyMapping[body.urgency] || "normal",
      contact_name: pharmacy?.name ?? null, // Use nullish coalescing to ensure string or null
      contact_phone: pharmacy?.phone ?? null, // Use nullish coalescing to ensure string or null
      status: "active",
    };

    const { data: newRequest, error } = await serviceSupabase
      .from("urgent_requests")
      .insert([insertPayload])
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Format response to match frontend interface
    const formattedRequest = {
      id: newRequest.id,
      medication: newRequest.product_name,
      quantity: newRequest.quantity_needed,
      urgency:
        newRequest.urgency_level === "critical"
          ? "critique"
          : newRequest.urgency_level === "urgent"
          ? "urgent"
          : "normal",
      status: "en attente",
      timestamp: newRequest.created_at,
      notes: newRequest.description,
      responses: [],
    };

    return NextResponse.json({ request: formattedRequest });
  } catch (error: any) {
    logger.error("Error creating urgent request", { error });
    return NextResponse.json(
      { error: "Failed to create urgent request" },
      { status: 500 }
    );
  }
}
