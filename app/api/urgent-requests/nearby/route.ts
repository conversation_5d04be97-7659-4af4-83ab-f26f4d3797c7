import { NextRequest, NextResponse } from "next/server";
import { requireAuth, R<PERSON><PERSON> } from "@/lib/auth-helpers";
import { getSupabaseAdmin } from "@/lib/supabase/admin";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const radiusKm = parseInt(searchParams.get('radius') || '20'); // Default 20km
    const urgencyFilter = searchParams.get('urgency'); // 'critical', 'high', 'normal', 'low'
    const limit = parseInt(searchParams.get('limit') || '50'); // Default 50 results

    // Validate radius (max 100km for performance)
    if (radiusKm > 100) {
      return NextResponse.json(
        { error: "Rayon maximum autorisé: 100km" },
        { status: 400 }
      );
    }

    // Get current pharmacy location
    const { data: myPharmacy, error: pharmacyError } = await serviceSupabase
      .from("pharmacies")
      .select("latitude, longitude")
      .eq("id", user.pharmacyId)
      .single();

    if (pharmacyError || !myPharmacy) {
      return NextResponse.json(
        { error: "Impossible de localiser votre pharmacie" },
        { status: 400 }
      );
    }

    // Build the query with PostGIS distance calculation
    let query = serviceSupabase
      .from("urgent_requests")
      .select(`
        *,
        pharmacy:pharmacies!urgent_requests_pharmacy_id_fkey (
          id,
          name,
          address,
          phone,
          latitude,
          longitude
        )
      `)
      .eq("status", "active")
      .neq("pharmacy_id", user.pharmacyId) // Exclude own requests
      .order("created_at", { ascending: false });

    // Add urgency filter if specified
    if (urgencyFilter && ['critical', 'high', 'normal', 'low'].includes(urgencyFilter)) {
      query = query.eq("urgency_level", urgencyFilter);
    }

    // Execute query
    const { data: allRequests, error } = await query.limit(limit * 2); // Get more to filter by distance

    if (error) {
      throw error;
    }

    if (!allRequests || allRequests.length === 0) {
      return NextResponse.json({ 
        requests: [],
        metadata: {
          radius: radiusKm,
          center: { latitude: myPharmacy.latitude, longitude: myPharmacy.longitude },
          total: 0
        }
      });
    }

    // Calculate distances and filter by radius
    const requestsWithDistance = allRequests
      .map((request: any) => {
        if (!request.pharmacy?.latitude || !request.pharmacy?.longitude) {
          return null; // Skip requests without location data
        }

        // Haversine formula for distance calculation
        const R = 6371; // Earth's radius in kilometers
        const dLat = (request.pharmacy.latitude - myPharmacy.latitude) * Math.PI / 180;
        const dLon = (request.pharmacy.longitude - myPharmacy.longitude) * Math.PI / 180;
        const a = 
          Math.sin(dLat/2) * Math.sin(dLat/2) +
          Math.cos(myPharmacy.latitude * Math.PI / 180) * Math.cos(request.pharmacy.latitude * Math.PI / 180) * 
          Math.sin(dLon/2) * Math.sin(dLon/2);
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a));
        const distance = R * c;

        return {
          ...request,
          distance: Math.round(distance * 10) / 10, // Round to 1 decimal place
        };
      })
      .filter((request: any) => request && request.distance <= radiusKm) // Filter by radius
      .sort((a: any, b: any) => {
        // Sort by urgency first, then by distance
        const urgencyOrder = { critical: 4, high: 3, normal: 2, low: 1 };
        const urgencyDiff = (urgencyOrder[b.urgency_level as keyof typeof urgencyOrder] || 0) - 
                           (urgencyOrder[a.urgency_level as keyof typeof urgencyOrder] || 0);
        
        if (urgencyDiff !== 0) return urgencyDiff;
        return a.distance - b.distance; // Then by distance (closest first)
      })
      .slice(0, limit); // Apply final limit

    // Format the response to match frontend interface
    const formattedRequests = requestsWithDistance.map((request: any) => ({
      id: request.id,
      medication: request.product_name,
      quantity: request.quantity_needed,
      urgency: request.urgency_level === "critical" 
        ? "critique" 
        : request.urgency_level === "high" 
        ? "urgent" 
        : request.urgency_level === "low"
        ? "faible"
        : "normal",
      status: request.status === "active" 
        ? "en attente" 
        : request.status === "fulfilled" 
        ? "accepté" 
        : "refusé",
      timestamp: request.created_at,
      notes: request.description,
      distance: request.distance,
      expiresAt: request.expires_at,
      pharmacy: {
        id: request.pharmacy.id,
        name: request.pharmacy.name,
        address: request.pharmacy.address,
        phone: request.pharmacy.phone,
        latitude: request.pharmacy.latitude,
        longitude: request.pharmacy.longitude,
      },
      contact: {
        name: request.contact_name,
        phone: request.contact_phone,
        email: request.contact_email,
      },
    }));

    // Calculate urgency distribution for analytics
    const urgencyStats = formattedRequests.reduce((acc: any, req: any) => {
      acc[req.urgency] = (acc[req.urgency] || 0) + 1;
      return acc;
    }, {});

    return NextResponse.json({
      requests: formattedRequests,
      metadata: {
        radius: radiusKm,
        center: { 
          latitude: myPharmacy.latitude, 
          longitude: myPharmacy.longitude 
        },
        total: formattedRequests.length,
        urgencyDistribution: urgencyStats,
        averageDistance: formattedRequests.length > 0 
          ? Math.round((formattedRequests.reduce((sum: number, req: any) => sum + req.distance, 0) / formattedRequests.length) * 10) / 10
          : 0,
      }
    });

  } catch (error: any) {
    logger.error("Error fetching nearby urgent requests", { error });
    return NextResponse.json(
      { error: "Failed to fetch nearby urgent requests" },
      { status: 500 }
    );
  }
}

// POST endpoint to respond to an urgent request
export async function POST(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const body = await request.json();
    const { 
      urgentRequestId, 
      quantityAvailable, 
      pricePerUnit, 
      message, 
      contactName, 
      contactPhone, 
      contactEmail 
    } = body;

    if (!urgentRequestId || !quantityAvailable) {
      return NextResponse.json(
        { error: "ID de demande et quantité disponible requis" },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();

    // Verify the urgent request exists and is active
    const { data: urgentRequest, error: requestError } = await serviceSupabase
      .from("urgent_requests")
      .select("id, status, pharmacy_id")
      .eq("id", urgentRequestId)
      .single();

    if (requestError || !urgentRequest) {
      return NextResponse.json(
        { error: "Demande urgente introuvable" },
        { status: 404 }
      );
    }

    if (urgentRequest.status !== "active") {
      return NextResponse.json(
        { error: "Cette demande n'est plus active" },
        { status: 400 }
      );
    }

    if (urgentRequest.pharmacy_id === user.pharmacyId) {
      return NextResponse.json(
        { error: "Vous ne pouvez pas répondre à votre propre demande" },
        { status: 400 }
      );
    }

    // Check if we already responded to this request
    const { data: existingResponse } = await serviceSupabase
      .from("urgent_request_responses")
      .select("id")
      .eq("urgent_request_id", urgentRequestId)
      .eq("responding_pharmacy_id", user.pharmacyId)
      .single();

    if (existingResponse) {
      return NextResponse.json(
        { error: "Vous avez déjà répondu à cette demande" },
        { status: 400 }
      );
    }

    // Create the response
    const { data: newResponse, error: responseError } = await serviceSupabase
      .from("urgent_request_responses")
      .insert([{
        urgent_request_id: urgentRequestId,
        responding_pharmacy_id: user.pharmacyId,
        quantity_available: parseInt(quantityAvailable),
        price_per_unit: pricePerUnit ? parseFloat(pricePerUnit) : null,
        message: message || null,
        contact_name: contactName || null,
        contact_phone: contactPhone || null,
        contact_email: contactEmail || null,
        status: "pending",
      }])
      .select()
      .single();

    if (responseError) {
      throw responseError;
    }

    return NextResponse.json({ 
      response: newResponse,
      message: "Réponse envoyée avec succès" 
    });

  } catch (error: any) {
    logger.error("Error responding to urgent request", { error });
    return NextResponse.json(
      { error: "Failed to respond to urgent request" },
      { status: 500 }
    );
  }
}
