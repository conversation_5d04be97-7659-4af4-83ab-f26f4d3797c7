import { NextRequest, NextResponse } from "next/server";
import { cookies } from 'next/headers';
import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';

export async function GET(request: NextRequest) {
  try {
    // Get all cookies
    const cookieStore = await cookies();
    const allCookies = cookieStore.getAll();
    
    // Filter Supabase cookies
    const supabaseCookies = allCookies.filter(c => c.name.startsWith('sb-'));
    
    // Try to get user with Supabase client
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { data: { user }, error: userError } = await supabase.auth.getUser();
    
    // Try to get session
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();

    return NextResponse.json({
      success: true,
      cookies: {
        total: allCookies.length,
        supabaseCount: supabaseCookies.length,
        allCookies: allCookies.map(c => ({
          name: c.name,
          hasValue: !!c.value,
          valueLength: c.value?.length || 0
        })),
        supabaseCookies: supabaseCookies.map(c => ({
          name: c.name,
          hasValue: !!c.value,
          valueLength: c.value?.length || 0
        }))
      },
      auth: {
        hasUser: !!user,
        hasSession: !!session,
        userError: userError?.message || null,
        sessionError: sessionError?.message || null,
        userId: user?.id || null,
        userEmail: user?.email || null,
        sessionValid: session?.expires_at ? new Date(session.expires_at * 1000) > new Date() : false
      },
      headers: {
        userAgent: request.headers.get('user-agent'),
        origin: request.headers.get('origin'),
        referer: request.headers.get('referer'),
        cookie: request.headers.get('cookie') ? 'present' : 'missing'
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      error: 'Cookie test failed',
      details: error.message
    }, { status: 500 });
  }
}
