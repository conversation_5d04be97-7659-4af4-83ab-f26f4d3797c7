import { NextRequest, NextResponse } from "next/server";
import { getAuthenticatedUser } from "@/lib/auth-helpers";
import { getSupabaseAdmin } from "@/lib/supabase/admin";

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    
    if (authError || !user) {
      return NextResponse.json({ 
        error: "Authentication failed",
        details: authError,
        authenticated: false
      }, { status: 401 });
    }

    const serviceSupabase = getSupabaseAdmin();

    // Get detailed user info
    const { data: profile, error: profileError } = await serviceSupabase
      .from("profiles")
      .select("*")
      .eq("id", user.id)
      .single();

    // Get pharmacy team membership
    const { data: teamMember, error: teamError } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("*, pharmacies(name, email)")
      .eq("user_id", user.id)
      .single();

    // Get pharmacy details if pharmacyId exists
    let pharmacy = null;
    if (user.pharmacyId) {
      const { data: pharmacyData, error: pharmacyError } = await serviceSupabase
        .from("pharmacies")
        .select("*")
        .eq("id", user.pharmacyId)
        .single();
      
      if (!pharmacyError) {
        pharmacy = pharmacyData;
      }
    }

    return NextResponse.json({
      authenticated: true,
      user: {
        id: user.id,
        email: user.email,
        role: user.role,
        pharmacyId: user.pharmacyId
      },
      profile: profile || null,
      profileError: profileError?.message || null,
      teamMember: teamMember || null,
      teamError: teamError?.message || null,
      pharmacy: pharmacy || null,
      debug: {
        hasPharmacyId: !!user.pharmacyId,
        profileExists: !!profile,
        teamMemberExists: !!teamMember,
        pharmacyExists: !!pharmacy
      }
    });

  } catch (error: any) {
    return NextResponse.json({
      error: "Debug endpoint failed",
      details: error.message,
      authenticated: false
    }, { status: 500 });
  }
}
