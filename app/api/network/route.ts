import { NextRequest, NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { getSupabaseAdmin } from "@/lib/supabase/admin";
import { UserRole } from "@/lib/types/auth"; // Assuming UserRole is defined here
import type { Database } from "@/types/supabase"; // Temporarily commented out due to type generation issue
import { requireAuth, ROLES } from "@/lib/auth-helpers";

type Pharmacy = Database["public"]["Tables"]["pharmacies"]["Row"];

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      logger.error("Network API auth failed", { authError, userExists: !!user });
      return NextResponse.json({
        error: "Non autorisé",
        details: authError,
        debug: "Check /api/debug/auth for more details"
      }, { status: 401 });
    }

    // User is already authenticated via requireAuth above

    if (!user.pharmacyId) {
      logger.error("Network API: No pharmacy ID", {
        userId: user.id,
        email: user.email,
        role: user.role
      });
      return NextResponse.json(
        {
          error: "Aucune pharmacie associée à cet utilisateur.",
          debug: {
            userId: user.id,
            email: user.email,
            role: user.role,
            pharmacyId: user.pharmacyId
          }
        },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();

    const currentPharmacyId = user.pharmacyId;

    // Get all pharmacies except the current one
    const { data: pharmacies, error } = await serviceSupabase
      .from("pharmacies")
      .select(`
        id,
        name,
        address,
        phone,
        email,
        city,
        location
      `)
      .neq("id", currentPharmacyId)
      .eq("is_verified", true)
      .limit(20) as { data: Pharmacy[] | null, error: any };

    if (error) {
      throw error;
    }

    // Get current pharmacy location for distance calculation
    const { data: currentPharmacy } = await serviceSupabase
      .from("pharmacies")
      .select("location")
      .eq("id", currentPharmacyId)
      .single() as { data: { location: any } | null, error: any };

    // Format pharmacies data
    const formattedPharmacies = pharmacies?.map((pharmacy: any, index: number) => {
      // Calculate mock distance (in real app, you'd use PostGIS distance functions)
      const mockDistance = Math.round((Math.random() * 10 + 1) * 10) / 10;
      
      // Mock rating and exchanges (in real app, these would come from actual data)
      const mockRating = Math.round((Math.random() * 1 + 4) * 10) / 10;
      const mockExchanges = Math.floor(Math.random() * 30 + 1);
      
      // Mock specialties
      const specialties = [
        ["Urgences", "Pédiatrie"],
        ["Dermatologie", "Cardiologie"],
        ["Homéopathie", "Nutrition"],
        ["Orthopédie", "Gériatrie"],
        ["Oncologie", "Diabétologie"]
      ];

      return {
        id: pharmacy.id,
        name: pharmacy.name,
        address: pharmacy.address || `${pharmacy.city || "Ville inconnue"}`,
        phone: pharmacy.phone || "Non renseigné",
        email: pharmacy.email || "Non renseigné",
        distance: mockDistance,
        rating: mockRating,
        exchanges: mockExchanges,
        lastActive: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        specialties: specialties[index % specialties.length] || ["Généraliste"]
      };
    }) || [];

    // Calculate network statistics
    const stats = {
      activePartners: formattedPharmacies.length,
      averageDistance: formattedPharmacies.length > 0 
        ? Math.round((formattedPharmacies.reduce((sum, p) => sum + p.distance, 0) / formattedPharmacies.length) * 10) / 10
        : 0,
      totalExchanges: formattedPharmacies.reduce((sum, p) => sum + p.exchanges, 0),
      averageRating: formattedPharmacies.length > 0
        ? Math.round((formattedPharmacies.reduce((sum, p) => sum + p.rating, 0) / formattedPharmacies.length) * 10) / 10
        : 0
    };

    return NextResponse.json({ 
      pharmacies: formattedPharmacies,
      stats 
    });
  } catch (error: any) {
    logger.error("Error fetching network pharmacies", { error });
    return NextResponse.json(
      { error: "Failed to fetch network pharmacies" },
      { status: 500 }
    );
  }
}
