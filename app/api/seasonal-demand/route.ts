import { NextRequest, NextResponse } from "next/server";
import { requireAuth, R<PERSON><PERSON> } from "@/lib/auth-helpers";
import { getSupabaseAdmin } from "@/lib/supabase/admin";
import { logger } from "@/lib/logger";
import { demandForecastingEngine } from "@/lib/seasonal-demand/forecasting-engine";
import { getCurrentSeasonalPatterns, getDemandMultiplier } from "@/lib/seasonal-demand/morocco-patterns";

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await requireAuth(request, ROLES.PHARMACY_STAFF);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getSupabaseAdmin();
    const { searchParams } = new URL(request.url);
    
    // Get query parameters
    const type = searchParams.get('type') || 'overview'; // 'overview', 'forecasts', 'insights', 'calendar'
    const category = searchParams.get('category');
    const startDate = searchParams.get('startDate');
    const endDate = searchParams.get('endDate');
    const daysAhead = parseInt(searchParams.get('daysAhead') || '30');

    const currentDate = new Date();
    let responseData: any = {};

    switch (type) {
      case 'overview':
        responseData = await generateOverviewData(serviceSupabase, user.pharmacyId, currentDate);
        break;
      
      case 'forecasts':
        responseData = await generateForecastData(category, currentDate, daysAhead);
        break;
      
      case 'insights':
        responseData = await generateInsightsData(daysAhead);
        break;
      
      case 'calendar':
        responseData = await generateCalendarData(startDate, endDate, category);
        break;
      
      default:
        return NextResponse.json({ error: "Type de données non supporté" }, { status: 400 });
    }

    return NextResponse.json({
      ...responseData,
      metadata: {
        type,
        pharmacyId: user.pharmacyId,
        generatedAt: new Date().toISOString(),
        daysAhead,
        category
      }
    });

  } catch (error: any) {
    logger.error("Error fetching seasonal demand data", { error });
    return NextResponse.json(
      { error: "Failed to fetch seasonal demand data" },
      { status: 500 }
    );
  }
}

// Generate overview data including current patterns and key metrics
async function generateOverviewData(supabase: any, pharmacyId: string, currentDate: Date) {
  try {
    // Get current seasonal patterns
    const activePatterns = getCurrentSeasonalPatterns(currentDate);
    
    // Get category overview from forecasting engine
    const categoryOverview = demandForecastingEngine.generateCategoryOverview(currentDate);
    
    // Get seasonal insights
    const seasonalInsights = demandForecastingEngine.generateSeasonalInsights(30);
    
    // Get historical demand data for the pharmacy
    const { data: historicalData, error } = await supabase
      .from("marketplace_listings")
      .select(`
        created_at,
        category,
        quantity,
        status
      `)
      .eq("pharmacy_id", pharmacyId)
      .gte("created_at", new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString()) // Last 90 days
      .order("created_at", { ascending: false });

    if (error) {
      logger.warn("Could not fetch historical data", { error });
    }

    // Calculate key metrics
    const keyMetrics = {
      activeEvents: seasonalInsights.filter(i => i.daysUntil <= 7).length,
      positiveTrends: categoryOverview.filter(c => c.trend === 'increasing').length,
      criticalAlerts: seasonalInsights.filter(i => i.priority === 'critical').length,
      totalCategories: categoryOverview.length,
      forecastAccuracy: 95, // Mock accuracy percentage
      avgDemandIncrease: Math.round(
        categoryOverview.reduce((sum, c) => sum + Math.max(0, c.trendPercentage), 0) / 
        categoryOverview.filter(c => c.trendPercentage > 0).length || 1
      )
    };

    return {
      activePatterns,
      categoryOverview,
      seasonalInsights: seasonalInsights.slice(0, 10), // Limit to 10 most relevant
      keyMetrics,
      historicalData: historicalData || []
    };

  } catch (error) {
    logger.error("Error generating overview data", { error });
    throw error;
  }
}

// Generate detailed forecast data for specific categories
async function generateForecastData(category: string | null, currentDate: Date, daysAhead: number) {
  try {
    const forecasts = [];
    const categories = category ? [category] : [
      'cold_flu_medicine',
      'digestive_health',
      'diabetes_management',
      'vitamins_supplements',
      'children_medicine',
      'pain_relief'
    ];

    for (const cat of categories) {
      // Generate forecasts for the next 30 days
      for (let i = 0; i <= Math.min(daysAhead, 30); i += 7) { // Weekly forecasts
        const targetDate = new Date(currentDate.getTime() + i * 24 * 60 * 60 * 1000);
        const forecast = demandForecastingEngine.generateForecast(cat, targetDate, 100);
        forecasts.push(forecast);
      }
    }

    // Calculate trend analysis
    const trendAnalysis = categories.map(cat => {
      const catForecasts = forecasts.filter(f => f.category === cat);
      const avgMultiplier = catForecasts.reduce((sum, f) => sum + f.multiplier, 0) / catForecasts.length;
      const maxMultiplier = Math.max(...catForecasts.map(f => f.multiplier));
      const minMultiplier = Math.min(...catForecasts.map(f => f.multiplier));
      
      return {
        category: cat,
        avgMultiplier: Math.round(avgMultiplier * 100) / 100,
        maxMultiplier: Math.round(maxMultiplier * 100) / 100,
        minMultiplier: Math.round(minMultiplier * 100) / 100,
        volatility: Math.round((maxMultiplier - minMultiplier) * 100) / 100,
        peakDate: catForecasts.find(f => f.multiplier === maxMultiplier)?.date
      };
    });

    return {
      forecasts,
      trendAnalysis,
      summary: {
        totalForecasts: forecasts.length,
        avgConfidence: Math.round(forecasts.reduce((sum, f) => sum + f.confidence, 0) / forecasts.length),
        highDemandPeriods: forecasts.filter(f => f.multiplier > 1.5).length,
        categoriesAnalyzed: categories.length
      }
    };

  } catch (error) {
    logger.error("Error generating forecast data", { error });
    throw error;
  }
}

// Generate insights and alerts data
async function generateInsightsData(daysAhead: number) {
  try {
    const insights = demandForecastingEngine.generateSeasonalInsights(daysAhead);
    
    // Group insights by priority and type
    const groupedInsights = {
      critical: insights.filter(i => i.priority === 'critical'),
      high: insights.filter(i => i.priority === 'high'),
      medium: insights.filter(i => i.priority === 'medium'),
      low: insights.filter(i => i.priority === 'low'),
      byType: {
        preparation: insights.filter(i => i.type === 'preparation'),
        opportunity: insights.filter(i => i.type === 'opportunity'),
        warning: insights.filter(i => i.type === 'warning')
      }
    };

    // Calculate insight statistics
    const insightStats = {
      totalInsights: insights.length,
      urgentActions: insights.filter(i => i.daysUntil <= 7).length,
      categoriesAffected: new Set(insights.flatMap(i => i.affectedCategories)).size,
      avgDaysUntil: Math.round(insights.reduce((sum, i) => sum + i.daysUntil, 0) / insights.length),
      preparationTime: Math.max(...insights.map(i => i.daysUntil))
    };

    return {
      insights,
      groupedInsights,
      insightStats,
      recommendations: generateGlobalRecommendations(insights)
    };

  } catch (error) {
    logger.error("Error generating insights data", { error });
    throw error;
  }
}

// Generate calendar-specific data
async function generateCalendarData(startDate: string | null, endDate: string | null, category: string | null) {
  try {
    const start = startDate ? new Date(startDate) : new Date();
    const end = endDate ? new Date(endDate) : new Date(start.getTime() + 90 * 24 * 60 * 60 * 1000); // 90 days
    
    const calendarEvents = [];
    const currentDate = new Date(start);
    
    while (currentDate <= end) {
      const patterns = getCurrentSeasonalPatterns(currentDate);
      const demandMultiplier = category ? getDemandMultiplier(category, currentDate) : 1.0;
      
      if (patterns.length > 0 || demandMultiplier > 1.2) {
        calendarEvents.push({
          date: currentDate.toISOString().split('T')[0],
          patterns: patterns.map(p => ({
            name: p.name,
            category: p.category,
            multiplier: p.demandMultiplier,
            affectedCategories: p.affectedCategories
          })),
          demandLevel: demandMultiplier > 1.8 ? 'critical' :
                      demandMultiplier > 1.5 ? 'high' :
                      demandMultiplier > 1.2 ? 'medium' : 'low',
          demandMultiplier
        });
      }
      
      currentDate.setDate(currentDate.getDate() + 1);
    }

    return {
      calendarEvents,
      dateRange: {
        start: start.toISOString().split('T')[0],
        end: end.toISOString().split('T')[0]
      },
      summary: {
        totalEvents: calendarEvents.length,
        criticalDays: calendarEvents.filter(e => e.demandLevel === 'critical').length,
        highDemandDays: calendarEvents.filter(e => e.demandLevel === 'high').length,
        avgDemandMultiplier: Math.round(
          (calendarEvents.reduce((sum, e) => sum + e.demandMultiplier, 0) / calendarEvents.length) * 100
        ) / 100
      }
    };

  } catch (error) {
    logger.error("Error generating calendar data", { error });
    throw error;
  }
}

// Generate global recommendations based on insights
function generateGlobalRecommendations(insights: any[]) {
  const recommendations = [];
  
  const criticalInsights = insights.filter(i => i.priority === 'critical');
  const urgentActions = insights.filter(i => i.daysUntil <= 7);
  
  if (criticalInsights.length > 0) {
    recommendations.push({
      type: 'critical',
      title: 'Actions Critiques Requises',
      description: `${criticalInsights.length} événements critiques nécessitent une attention immédiate`,
      actions: criticalInsights.flatMap(i => i.actionItems).slice(0, 3)
    });
  }
  
  if (urgentActions.length > 0) {
    recommendations.push({
      type: 'urgent',
      title: 'Actions Urgentes (7 jours)',
      description: `${urgentActions.length} événements dans les 7 prochains jours`,
      actions: urgentActions.flatMap(i => i.actionItems).slice(0, 3)
    });
  }
  
  // Stock optimization recommendations
  const stockCategories = new Set(insights.flatMap(i => i.affectedCategories));
  if (stockCategories.size > 0) {
    recommendations.push({
      type: 'optimization',
      title: 'Optimisation des Stocks',
      description: `${stockCategories.size} catégories nécessitent une attention particulière`,
      actions: [
        'Réviser les niveaux de stock minimum',
        'Planifier les commandes saisonnières',
        'Coordonner avec les fournisseurs'
      ]
    });
  }
  
  return recommendations;
}
