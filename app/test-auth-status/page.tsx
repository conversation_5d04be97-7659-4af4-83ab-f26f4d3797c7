"use client";

import { useAuth } from '@/contexts/auth-context';
import { hasAccessToRoute } from '@/hooks/useAccessControl';
import { Container, Paper, Title, Text, Stack, Group, Badge, Table } from '@mantine/core';

const TEST_ROUTES = [
  '/dashboard',
  '/network',
  '/marketplace',
  '/inventory',
  '/orders',
  '/team',
  '/reports',
  '/urgent-requests',
  '/notifications',
  '/pharmacy-radar',
  '/seasonal-demand',
  '/distributor-intelligence',
  '/supplier',
  '/alerts',
  '/admin',
  '/admin/dashboard'
];

export default function TestAuthStatus() {
  const { user, loading, isPharmacy } = useAuth();

  if (loading) {
    return <div>Loading...</div>;
  }

  if (!user) {
    return <div>Not authenticated</div>;
  }

  const userRole = user.user_metadata?.role || user.role;

  return (
    <Container size="lg" py="xl">
      <Paper p="xl" shadow="sm">
        <Stack gap="lg">
          <Title order={2}>🔐 Authentication Status Test</Title>
          
          <Group>
            <Text size="lg" fw={600}>User Info:</Text>
          </Group>
          
          <Stack gap="xs">
            <Text><strong>Email:</strong> {user.email}</Text>
            <Text><strong>User ID:</strong> {user.id}</Text>
            <Text><strong>Supabase Role:</strong> {user.role}</Text>
            <Text><strong>Actual Role:</strong> {userRole}</Text>
            <Text><strong>Has Pharmacy:</strong> {isPharmacy ? 'Yes' : 'No'}</Text>
          </Stack>

          <Title order={3}>🎯 Route Access Test</Title>
          
          <Table>
            <Table.Thead>
              <Table.Tr>
                <Table.Th>Route</Table.Th>
                <Table.Th>Access</Table.Th>
                <Table.Th>Status</Table.Th>
              </Table.Tr>
            </Table.Thead>
            <Table.Tbody>
              {TEST_ROUTES.map((route) => {
                const hasAccess = hasAccessToRoute(route, user, isPharmacy);
                return (
                  <Table.Tr key={route}>
                    <Table.Td>
                      <Text ff="monospace">{route}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge color={hasAccess ? 'green' : 'red'}>
                        {hasAccess ? 'ALLOWED' : 'DENIED'}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c="dimmed">
                        Role: {userRole} | Pharmacy: {isPharmacy ? 'Yes' : 'No'}
                      </Text>
                    </Table.Td>
                  </Table.Tr>
                );
              })}
            </Table.Tbody>
          </Table>

          <Title order={3}>🧪 API Test</Title>
          <Text size="sm" c="dimmed">
            Visit these API endpoints to test authorization:
          </Text>
          <Stack gap="xs">
            <Text ff="monospace" size="sm">GET /api/network</Text>
            <Text ff="monospace" size="sm">GET /api/urgent-requests</Text>
            <Text ff="monospace" size="sm">GET /api/debug/auth</Text>
            <Text ff="monospace" size="sm">GET /api/stock</Text>
          </Stack>

          <Title order={3}>📋 Debug Info</Title>
          <Text ff="monospace" size="xs" style={{ whiteSpace: 'pre-wrap' }}>
            {JSON.stringify({
              user: {
                id: user.id,
                email: user.email,
                supabaseRole: user.role,
                actualRole: userRole,
                user_metadata: user.user_metadata
              },
              isPharmacy,
              loading
            }, null, 2)}
          </Text>
        </Stack>
      </Paper>
    </Container>
  );
}
