"use client";

import React, { useState, useEffect } from "react";
import { AppShell } from "@/components/layout/app-shell";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  Users,
  MapPin,
  Phone,
  Mail,
  Star,
  Plus,
  Search,
  Building,
  Clock,
  Loader,
  Map,
  List,
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";
import { PharmacyProfileView } from "@/components/profile/pharmacy-profile-view";
import { ContactPharmacyModal } from "@/components/network/contact-pharmacy-modal";
import { PharmacistNetworkMap } from "@/components/maps/pharmacist-network-map";

interface Pharmacy {
  id: string;
  name: string;
  address: string;
  phone: string;
  email: string;
  distance: number;
  rating: number;
  exchanges: number;
  lastActive: string;
  specialties: string[];
}

interface NetworkStats {
  activePartners: number;
  averageDistance: number;
  totalExchanges: number;
  averageRating: number;
}

function NetworkContent() {
  const [activeTab, setActiveTab] = useState("list");
  const [pharmacies, setPharmacies] = useState<Pharmacy[]>([]);
  const [stats, setStats] = useState<NetworkStats>({
    activePartners: 0,
    averageDistance: 0,
    totalExchanges: 0,
    averageRating: 0,
  });
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isInviteOpen, setIsInviteOpen] = useState(false);
  const [inviteForm, setInviteForm] = useState({
    name: "",
    email: "",
    city: "",
  });
  const [submitting, setSubmitting] = useState(false);
  const [inviteSuccess, setInviteSuccess] = useState(false);
  const [profileModalPharmacy, setProfileModalPharmacy] = useState<Pharmacy | null>(null);
  const [contactModalPharmacy, setContactModalPharmacy] = useState<Pharmacy | null>(null);

  // Fetch network data
  useEffect(() => {
    async function fetchNetworkData() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/network");
        const data = await res.json();
        if (!res.ok)
          throw new Error(data.error || "Erreur lors du chargement du réseau");
        setPharmacies(data.pharmacies);
        setStats(data.stats);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchNetworkData();
  }, []);

  // Invite handler
  const handleInviteSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const res = await fetch("/api/network/invite", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(inviteForm),
      });
      if (!res.ok) throw new Error("Erreur lors de l'invitation");
      setIsInviteOpen(false);
      setInviteForm({ name: "", email: "", city: "" });
      setInviteSuccess(true);
    } catch (err) {
      // Optionally handle error
    } finally {
      setSubmitting(false);
    }
  };

  // Contact send handler
  const handleSendContact = async (pharmacy: Pharmacy, data: { subject: string; message: string }) => {
    const res = await fetch("/api/network/contact", {
      method: "POST",
      headers: { "Content-Type": "application/json" },
      body: JSON.stringify({ pharmacyId: pharmacy.id, ...data }),
    });
    if (!res.ok) throw new Error("Erreur lors de l'envoi du message");
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <Loader className="h-8 w-8 animate-spin" />
      </div>
    );
  }

  if (error) {
    return <div className="text-center text-red-500 p-8">Erreur: {error}</div>;
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[#00B5FF]">
            Réseau Professionnel
          </h1>
          <p className="text-muted-foreground mt-1">
            Connectez-vous avec d'autres pharmacies pour optimiser vos échanges
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Développez votre réseau de partenaires de confiance
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={() => setActiveTab(activeTab === "map" ? "list" : "map")}>
            {activeTab === "map" ? <List className="h-4 w-4 mr-2" /> : <Map className="h-4 w-4 mr-2" />}
            {activeTab === "map" ? "Vue Liste" : "Vue Carte"}
          </Button>
          <Button className="bg-[#00B5FF] hover:bg-[#0099CC]" onClick={() => setIsInviteOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Inviter une Pharmacie
          </Button>
        </div>
      </div>

      {/* Inviter une Pharmacie Modal */}
      <Dialog open={isInviteOpen} onOpenChange={setIsInviteOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Inviter une Pharmacie</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleInviteSubmit} className="space-y-4">
            <div className="flex flex-col gap-2">
              <label htmlFor="pharmacy-name">Nom de la pharmacie</label>
              <input
                id="pharmacy-name"
                required
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={inviteForm.name}
                onChange={e => setInviteForm(f => ({ ...f, name: e.target.value }))}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label htmlFor="pharmacy-email">Email</label>
              <input
                id="pharmacy-email"
                type="email"
                required
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={inviteForm.email}
                onChange={e => setInviteForm(f => ({ ...f, email: e.target.value }))}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label htmlFor="pharmacy-city">Ville</label>
              <input
                id="pharmacy-city"
                required
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={inviteForm.city}
                onChange={e => setInviteForm(f => ({ ...f, city: e.target.value }))}
              />
            </div>
            <DialogFooter>
              <Button type="submit" className="w-full" disabled={submitting}>
                {submitting ? "Invitation..." : "Inviter"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Success message */}
      {inviteSuccess && (
        <div className="p-4 bg-green-100 text-green-800 rounded-lg text-center">
          Invitation envoyée avec succès !
        </div>
      )}

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Partenaires Actifs
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Users className="h-5 w-5 text-blue-500" />
              <span className="text-2xl font-bold">{stats.activePartners}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Pharmacies connectées
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Rayon Moyen
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <MapPin className="h-5 w-5 text-green-500" />
              <span className="text-2xl font-bold">
                {stats.averageDistance}km
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Distance partenaires
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Échanges Totaux
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Building className="h-5 w-5 text-purple-500" />
              <span className="text-2xl font-bold">{stats.totalExchanges}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">Ce trimestre</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Note Moyenne
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Star className="h-5 w-5 text-yellow-500" />
              <span className="text-2xl font-bold">{stats.averageRating}</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Satisfaction réseau
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="list" className="flex items-center gap-2">
            <List className="w-4 h-4" />
            Vue Liste
          </TabsTrigger>
          <TabsTrigger value="map" className="flex items-center gap-2">
            <Map className="w-4 h-4" />
            Vue Carte Interactive
          </TabsTrigger>
        </TabsList>

        <TabsContent value="list" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Search className="h-5 w-5 text-[#00B5FF]" />
                Rechercher des Partenaires
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="search">Rechercher</Label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                    <Input
                      id="search"
                      placeholder="Nom, ville, spécialité..."
                      className="pl-10"
                    />
                  </div>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="distance">Rayon de recherche</Label>
                  <Input id="distance" placeholder="Distance en km" type="number" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Users className="h-5 w-5 text-[#00B5FF]" />
                Pharmacies Partenaires
              </CardTitle>
              <CardDescription>
                Votre réseau de pharmacies de confiance
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pharmacies.map((pharmacy) => (
                  <div
                    key={pharmacy.id}
                    className="flex items-center justify-between p-4 border rounded-lg"
                  >
                    <div className="flex items-center gap-4">
                      <div className="p-3 bg-blue-100 rounded-full">
                        <Building className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <h3 className="font-medium">{pharmacy.name}</h3>
                        <p className="text-sm text-muted-foreground flex items-center gap-1">
                          <MapPin className="h-4 w-4" />
                          {pharmacy.address}
                        </p>
                        <div className="flex items-center gap-4 mt-1">
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <Phone className="h-3 w-3" />
                            {pharmacy.phone}
                          </p>
                          <p className="text-xs text-muted-foreground flex items-center gap-1">
                            <Mail className="h-3 w-3" />
                            {pharmacy.email}
                          </p>
                        </div>
                        <div className="flex items-center gap-2 mt-2">
                          {pharmacy.specialties.map((specialty) => (
                            <Badge
                              key={specialty}
                              variant="outline"
                              className="text-xs"
                            >
                              {specialty}
                            </Badge>
                          ))}
                        </div>
                      </div>
                    </div>
                    <div className="flex flex-col items-end gap-2">
                      <div className="flex items-center gap-4 text-sm">
                        <div className="flex items-center gap-1">
                          <MapPin className="h-4 w-4 text-muted-foreground" />
                          <span>{pharmacy.distance}km</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Star className="h-4 w-4 text-yellow-500 fill-current" />
                          <span>{pharmacy.rating}</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Building className="h-4 w-4 text-muted-foreground" />
                          <span>{pharmacy.exchanges} échanges</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <Clock className="h-3 w-3" />
                        Actif le{" "}
                        {new Date(pharmacy.lastActive).toLocaleDateString("fr-FR")}
                      </div>
                      <div className="flex items-center gap-2">
                        <Button size="sm" variant="outline" onClick={() => setContactModalPharmacy(pharmacy)}>
                          Contacter
                        </Button>
                        <Button
                          size="sm"
                          className="bg-[#00B5FF] hover:bg-[#0099CC]"
                          onClick={() => setProfileModalPharmacy(pharmacy)}
                        >
                          Voir Profil
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="map" className="space-y-6">
          <PharmacistNetworkMap />
        </TabsContent>
      </Tabs>
      {/* Profile Modal */}
      <Dialog open={!!profileModalPharmacy} onOpenChange={() => setProfileModalPharmacy(null)}>
        <DialogContent className="max-w-lg">
          <DialogHeader>
            <DialogTitle>Profil de la Pharmacie</DialogTitle>
          </DialogHeader>
          {profileModalPharmacy && <PharmacyProfileView pharmacy={profileModalPharmacy} />}
        </DialogContent>
      </Dialog>
      {/* Contact Modal */}
      {contactModalPharmacy && (
        <ContactPharmacyModal
          open={!!contactModalPharmacy}
          onClose={() => setContactModalPharmacy(null)}
          pharmacy={contactModalPharmacy}
          onSend={async (data) => {
            await handleSendContact(contactModalPharmacy, data);
            setContactModalPharmacy(null);
          }}
        />
      )}
    </div>
  );
}

export default function NetworkPage() {
  return (
    <AppShell>
      <NetworkContent />
    </AppShell>
  );
}
