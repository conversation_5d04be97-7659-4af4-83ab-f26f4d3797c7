"use client";

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';

export default function TestRedirectPage() {
  const router = useRouter();
  const [logs, setLogs] = useState<string[]>([]);
  const [currentUrl, setCurrentUrl] = useState('');

  const addLog = (message: string) => {
    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] ${message}`;
    console.log(logMessage);
    setLogs(prev => [...prev, logMessage]);
  };

  useEffect(() => {
    setCurrentUrl(window.location.href);
    addLog('Test redirect page loaded');
  }, []);

  const testMethods = [
    {
      name: 'window.location.href',
      action: () => {
        addLog('Testing window.location.href');
        window.location.href = '/dashboard';
      }
    },
    {
      name: 'window.location.replace',
      action: () => {
        addLog('Testing window.location.replace');
        window.location.replace('/dashboard');
      }
    },
    {
      name: 'router.push',
      action: () => {
        addLog('Testing router.push');
        router.push('/dashboard');
      }
    },
    {
      name: 'router.replace',
      action: () => {
        addLog('Testing router.replace');
        router.replace('/dashboard');
      }
    },
    {
      name: 'Cache-busted href',
      action: () => {
        addLog('Testing cache-busted window.location.href');
        window.location.href = `/dashboard?cb=${Date.now()}`;
      }
    },
    {
      name: 'Force reload',
      action: () => {
        addLog('Testing force reload');
        window.location.reload();
      }
    }
  ];

  return (
    <div style={{ padding: '20px', fontFamily: 'monospace' }}>
      <h1>🧪 Navigation Test Page</h1>
      <p><strong>Current URL:</strong> {currentUrl}</p>
      
      <h2>Test Navigation Methods:</h2>
      <div style={{ display: 'grid', gap: '10px', marginBottom: '20px' }}>
        {testMethods.map((method, index) => (
          <button
            key={index}
            onClick={method.action}
            style={{
              padding: '10px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '4px',
              cursor: 'pointer'
            }}
          >
            Test {method.name}
          </button>
        ))}
      </div>

      <h2>Logs:</h2>
      <div style={{
        backgroundColor: '#f8f9fa',
        border: '1px solid #dee2e6',
        borderRadius: '4px',
        padding: '10px',
        maxHeight: '300px',
        overflowY: 'auto'
      }}>
        {logs.map((log, index) => (
          <div key={index} style={{ marginBottom: '5px', fontSize: '12px' }}>
            {log}
          </div>
        ))}
      </div>

      <h2>Browser Info:</h2>
      <div style={{ fontSize: '12px', backgroundColor: '#f8f9fa', padding: '10px', borderRadius: '4px' }}>
        <div><strong>User Agent:</strong> {navigator.userAgent}</div>
        <div><strong>Service Worker:</strong> {'serviceWorker' in navigator ? 'Supported' : 'Not supported'}</div>
        <div><strong>Cache API:</strong> {'caches' in window ? 'Supported' : 'Not supported'}</div>
        <div><strong>Location:</strong> {window.location.href}</div>
      </div>
    </div>
  );
}
