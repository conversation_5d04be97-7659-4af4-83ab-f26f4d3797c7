"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  TrendingUp,
  BarChart3,
  AlertTriangle,
  Package,
  Clock,
  Zap,
  Sun,
  Moon,
  Snowflake,
  Leaf,
  Target,
  Brain,
} from "lucide-react";
import { DemandCalendar } from "@/components/seasonal-demand/demand-calendar";
import { demandForecastingEngine, type CategoryDemand, type SeasonalInsight } from "@/lib/seasonal-demand/forecasting-engine";
import { PHARMACEUTICAL_CATEGORIES } from "@/lib/seasonal-demand/forecasting-engine";

export default function SeasonalDemandPage() {
  const [activeTab, setActiveTab] = useState("calendar");
  const [categoryOverview, setCategoryOverview] = useState<CategoryDemand[]>([]);
  const [seasonalInsights, setSeasonalInsights] = useState<SeasonalInsight[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDemandData();
  }, []);

  const loadDemandData = async () => {
    try {
      setLoading(true);
      
      // Generate forecasting data
      const overview = demandForecastingEngine.generateCategoryOverview();
      const insights = demandForecastingEngine.generateSeasonalInsights(90);
      
      setCategoryOverview(overview);
      setSeasonalInsights(insights);
    } catch (error) {
      console.error('Error loading demand data:', error);
    } finally {
      setLoading(false);
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case 'increasing': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'decreasing': return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full"></div>;
    }
  };

  const getSeasonIcon = (period: string) => {
    if (period.toLowerCase().includes('hiver') || period.toLowerCase().includes('winter')) return Snowflake;
    if (period.toLowerCase().includes('été') || period.toLowerCase().includes('summer')) return Sun;
    if (period.toLowerCase().includes('ramadan') || period.toLowerCase().includes('aïd')) return Moon;
    return Leaf;
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Calendar className="w-8 h-8 text-blue-600" />
            Calendrier de Demande Saisonnière
          </h1>
          <p className="text-muted-foreground">
            Prévisions et patterns de demande pharmaceutique au Maroc
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline" onClick={loadDemandData} disabled={loading}>
            <TrendingUp className="w-4 h-4 mr-2" />
            {loading ? 'Actualisation...' : 'Actualiser'}
          </Button>
          <Button variant="outline">
            <Target className="w-4 h-4 mr-2" />
            Paramètres
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Calendar className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Événements Actifs</h3>
              <p className="text-2xl font-bold text-blue-800">
                {seasonalInsights.filter(i => i.daysUntil <= 7).length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Tendances Positives</h3>
              <p className="text-2xl font-bold text-green-800">
                {categoryOverview.filter(c => c.trend === 'increasing').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Alertes Critiques</h3>
              <p className="text-2xl font-bold text-orange-800">
                {seasonalInsights.filter(i => i.priority === 'critical').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">IA Prédictive</h3>
              <p className="text-2xl font-bold text-purple-800">95%</p>
              <p className="text-xs text-purple-600">Précision</p>
            </div>
          </div>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="calendar" className="flex items-center gap-2">
            <Calendar className="w-4 h-4" />
            Calendrier
          </TabsTrigger>
          <TabsTrigger value="forecasts" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Prévisions
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <AlertTriangle className="w-4 h-4" />
            Alertes
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analytiques
          </TabsTrigger>
        </TabsList>

        <TabsContent value="calendar" className="space-y-4">
          <DemandCalendar />
        </TabsContent>

        <TabsContent value="forecasts" className="space-y-4">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <TrendingUp className="w-5 h-5 text-green-600" />
              <h3 className="text-lg font-semibold">Prévisions par Catégorie</h3>
            </div>
            
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
              {categoryOverview.map((category, index) => (
                <Card key={index} className="p-4 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex-1">
                      <h4 className="font-medium text-sm mb-1">{category.categoryName}</h4>
                      <div className="text-xs text-muted-foreground">
                        Catégorie: {category.category}
                      </div>
                    </div>
                    {getTrendIcon(category.trend)}
                  </div>
                  
                  <div className="space-y-2">
                    <div className="flex justify-between text-sm">
                      <span>Demande actuelle:</span>
                      <span className="font-medium">{category.currentDemand}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Prévision:</span>
                      <span className="font-medium">{category.forecastedDemand}</span>
                    </div>
                    <div className="flex justify-between text-sm">
                      <span>Évolution:</span>
                      <span className={`font-medium ${
                        category.trendPercentage > 0 ? 'text-green-600' : 
                        category.trendPercentage < 0 ? 'text-red-600' : 'text-gray-600'
                      }`}>
                        {category.trendPercentage > 0 ? '+' : ''}{category.trendPercentage}%
                      </span>
                    </div>
                  </div>
                  
                  {category.recommendations.length > 0 && (
                    <div className="mt-3 pt-3 border-t">
                      <div className="text-xs font-medium mb-1">Recommandations:</div>
                      <div className="text-xs text-muted-foreground">
                        {category.recommendations[0]}
                      </div>
                    </div>
                  )}
                </Card>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <Card className="p-6">
            <div className="flex items-center gap-2 mb-6">
              <AlertTriangle className="w-5 h-5 text-orange-600" />
              <h3 className="text-lg font-semibold">Alertes et Insights Saisonniers</h3>
            </div>
            
            <div className="space-y-4">
              {seasonalInsights.map((insight, index) => {
                const SeasonIcon = getSeasonIcon(insight.period);
                return (
                  <Card key={index} className="p-4 border-l-4" style={{
                    borderLeftColor: insight.priority === 'critical' ? '#ef4444' :
                                    insight.priority === 'high' ? '#f97316' :
                                    insight.priority === 'medium' ? '#eab308' : '#3b82f6'
                  }}>
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-center gap-3">
                        <SeasonIcon className="w-5 h-5 text-blue-600" />
                        <div>
                          <h4 className="font-semibold">{insight.title}</h4>
                          <p className="text-sm text-muted-foreground">{insight.period}</p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge className={getPriorityColor(insight.priority)}>
                          {insight.priority}
                        </Badge>
                        <div className="text-sm text-muted-foreground">
                          {insight.daysUntil === 0 ? 'Maintenant' : `${insight.daysUntil} jours`}
                        </div>
                      </div>
                    </div>
                    
                    <p className="text-sm mb-3">{insight.description}</p>
                    
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <div className="text-sm font-medium mb-2">Catégories affectées:</div>
                        <div className="flex flex-wrap gap-1">
                          {insight.affectedCategories.slice(0, 4).map((cat, i) => (
                            <Badge key={i} variant="outline" className="text-xs">
                              {PHARMACEUTICAL_CATEGORIES[cat as keyof typeof PHARMACEUTICAL_CATEGORIES] || cat}
                            </Badge>
                          ))}
                          {insight.affectedCategories.length > 4 && (
                            <Badge variant="outline" className="text-xs">
                              +{insight.affectedCategories.length - 4}
                            </Badge>
                          )}
                        </div>
                      </div>
                      
                      <div>
                        <div className="text-sm font-medium mb-2">Actions recommandées:</div>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {insight.actionItems.slice(0, 3).map((action, i) => (
                            <li key={i} className="flex items-start gap-2">
                              <span className="text-blue-600">•</span>
                              <span>{action}</span>
                            </li>
                          ))}
                        </ul>
                      </div>
                    </div>
                  </Card>
                );
              })}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card className="p-6">
            <div className="text-center py-8">
              <BarChart3 className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Analytiques Avancées</h3>
              <p className="text-muted-foreground mb-4">
                Analyses détaillées des patterns de demande et performance des prévisions
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Analyse de précision des prévisions</div>
                <div>• Comparaison historique des patterns</div>
                <div>• ROI des recommandations de stock</div>
                <div>• Optimisation des commandes saisonnières</div>
              </div>
              <Button className="mt-4" disabled>
                <BarChart3 className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
