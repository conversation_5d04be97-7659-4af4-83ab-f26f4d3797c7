'use client';

import { useState, useEffect } from 'react';
import { createClient } from '@supabase/supabase-js';
import { Container, Paper, Title, Text, Button, Stack, Alert, Code, Box } from '@mantine/core';

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://shptfbdcuwaxyosyjsas.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocHRmYmRjdXdheHlvc3lqc2FzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MTgyNDMsImV4cCI6MjA2NjM5NDI0M30.tvoMbGEU3nL0tZvqib_jrITKzRpf1DE3A2CIk9czTd0';

const supabase = createClient(supabaseUrl, supabaseAnonKey);

export default function SupabaseTestPage() {
  const [connectionStatus, setConnectionStatus] = useState<string>('Not tested');
  const [loginStatus, setLoginStatus] = useState<string>('Not tested');
  const [userCount, setUserCount] = useState<number | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);

  const testConnection = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Test basic connection
      const { data, error: connError } = await supabase
        .from('pharmacies')
        .select('count(*)', { count: 'exact' })
        .limit(1);
        
      if (connError) {
        setConnectionStatus(`❌ Failed: ${connError.message}`);
        setError(connError.message);
      } else {
        setConnectionStatus('✅ Connected successfully');
        setUserCount(data?.length || 0);
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setConnectionStatus(`❌ Error: ${errorMsg}`);
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  const testDemoLogin = async () => {
    setLoading(true);
    setError(null);
    
    try {
      const { data, error: loginError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'demo123!'
      });
      
      if (loginError) {
        setLoginStatus(`❌ Failed: ${loginError.message}`);
        setError(loginError.message);
      } else {
        setLoginStatus(`✅ Success: ${data.user?.email}`);
        
        // Sign out immediately
        await supabase.auth.signOut();
      }
    } catch (err) {
      const errorMsg = err instanceof Error ? err.message : 'Unknown error';
      setLoginStatus(`❌ Error: ${errorMsg}`);
      setError(errorMsg);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    testConnection();
  }, []);

  return (
    <Container size="md" py="xl">
      <Paper p="xl" shadow="sm">
        <Stack gap="lg">
          <Title order={2}>🔧 Supabase Connection Test</Title>
          
          <Box>
            <Text size="sm" c="dimmed">Environment Configuration:</Text>
            <Code block mt="xs">
              {`Supabase URL: ${supabaseUrl}
Anon Key: ${supabaseAnonKey.substring(0, 50)}...
Node ENV: ${process.env.NODE_ENV || 'undefined'}`}
            </Code>
          </Box>

          <Stack gap="md">
            <Box>
              <Text fw={500}>Database Connection:</Text>
              <Text size="sm" c={connectionStatus.includes('✅') ? 'green' : 'red'}>
                {connectionStatus}
              </Text>
              {userCount !== null && (
                <Text size="xs" c="dimmed">
                  Pharmacies table accessible
                </Text>
              )}
            </Box>

            <Box>
              <Text fw={500}>Demo Login Test:</Text>
              <Text size="sm" c={loginStatus.includes('✅') ? 'green' : 'red'}>
                {loginStatus}
              </Text>
              <Button 
                size="xs" 
                variant="light" 
                onClick={testDemoLogin}
                loading={loading}
                mt="xs"
              >
                Test Demo Login
              </Button>
            </Box>
          </Stack>

          {error && (
            <Alert color="red" title="Error Details">
              <Code>{error}</Code>
            </Alert>
          )}

          <Button 
            onClick={testConnection}
            loading={loading}
            variant="outline"
          >
            Refresh Connection Test
          </Button>
        </Stack>
      </Paper>
    </Container>
  );
}
