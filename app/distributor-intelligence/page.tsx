"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Brain,
  Heart,
  Compass,
  TrendingUp,
  Users,
  Target,
  Zap,
  DollarSign,
  AlertTriangle,
  CheckCircle,
  Clock,
  Award,
} from "lucide-react";
import { MarketplaceIntelligence } from "@/components/distributor/marketplace-intelligence";
import { DistributorOpportunitiesMap } from "@/components/maps/distributor-opportunities-map";

export default function DistributorIntelligencePage() {
  const [activeTab, setActiveTab] = useState("marketplace");

  // Mock dashboard metrics
  const dashboardMetrics = {
    aiInsights: 12,
    relationshipScore: 87,
    territoryOptimization: 94,
    potentialRevenue: 245000,
    criticalAlerts: 3,
    opportunitiesIdentified: 8,
    pharmaciesManaged: 156,
    avgResponseTime: 2.3
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Brain className="w-8 h-8 text-purple-600" />
            Intelligence Distributeur
          </h1>
          <p className="text-muted-foreground">
            Plateforme d'intelligence artificielle pour optimiser vos relations et territoires
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Rapport Mensuel
          </Button>
          <Button className="bg-purple-600 hover:bg-purple-700">
            <Zap className="w-4 h-4 mr-2" />
            Actions Recommandées
          </Button>
        </div>
      </div>

      {/* Executive Dashboard */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Insights IA</h3>
              <p className="text-2xl font-bold text-purple-800">{dashboardMetrics.aiInsights}</p>
              <p className="text-xs text-purple-600">Nouveaux cette semaine</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-red-50 to-red-100 border-red-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-red-600 rounded-lg">
              <Heart className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-red-900">Score Relations</h3>
              <p className="text-2xl font-bold text-red-800">{dashboardMetrics.relationshipScore}/100</p>
              <p className="text-xs text-red-600">+5 points ce mois</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Compass className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Optimisation Territoire</h3>
              <p className="text-2xl font-bold text-blue-800">{dashboardMetrics.territoryOptimization}%</p>
              <p className="text-xs text-blue-600">Efficacité actuelle</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Revenus Potentiels</h3>
              <p className="text-2xl font-bold text-green-800">
                {(dashboardMetrics.potentialRevenue / 1000).toFixed(0)}K
              </p>
              <p className="text-xs text-green-600">MAD identifiés</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Quick Actions & Alerts */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <Card className="lg:col-span-2 p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Zap className="w-5 h-5 text-orange-600" />
            Actions Prioritaires
          </h3>
          
          <div className="space-y-3">
            <div className="flex items-start gap-3 p-3 bg-red-50 rounded-lg border-l-4 border-red-500">
              <AlertTriangle className="w-5 h-5 text-red-600 mt-0.5" />
              <div className="flex-1">
                <div className="font-medium text-red-900">Risque de Perte Client</div>
                <div className="text-sm text-red-700">Pharmacie du Quartier - Aucune commande depuis 3 semaines</div>
                <div className="text-xs text-red-600 mt-1">Impact: -12K MAD/mois</div>
              </div>
              <Button size="sm" className="bg-red-600 hover:bg-red-700 text-white">
                Agir
              </Button>
            </div>

            <div className="flex items-start gap-3 p-3 bg-orange-50 rounded-lg border-l-4 border-orange-500">
              <TrendingUp className="w-5 h-5 text-orange-600 mt-0.5" />
              <div className="flex-1">
                <div className="font-medium text-orange-900">Opportunité d'Expansion</div>
                <div className="text-sm text-orange-700">Marrakech-Safi - Marché sous-exploité avec ROI 180%</div>
                <div className="text-xs text-orange-600 mt-1">Investissement: 120K MAD</div>
              </div>
              <Button size="sm" className="bg-orange-600 hover:bg-orange-700 text-white">
                Analyser
              </Button>
            </div>

            <div className="flex items-start gap-3 p-3 bg-green-50 rounded-lg border-l-4 border-green-500">
              <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
              <div className="flex-1">
                <div className="font-medium text-green-900">Client Platinum Satisfait</div>
                <div className="text-sm text-green-700">Pharmacie Al Andalous - Potentiel expansion +25%</div>
                <div className="text-xs text-green-600 mt-1">Opportunité: +11K MAD/mois</div>
              </div>
              <Button size="sm" className="bg-green-600 hover:bg-green-700 text-white">
                Proposer
              </Button>
            </div>
          </div>
        </Card>

        <Card className="p-6">
          <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
            <Award className="w-5 h-5 text-blue-600" />
            Performance Clés
          </h3>
          
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Pharmacies Gérées</div>
                <div className="font-semibold">{dashboardMetrics.pharmaciesManaged}</div>
              </div>
              <Users className="w-5 h-5 text-blue-600" />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Alertes Critiques</div>
                <div className="font-semibold text-red-600">{dashboardMetrics.criticalAlerts}</div>
              </div>
              <AlertTriangle className="w-5 h-5 text-red-600" />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Opportunités Identifiées</div>
                <div className="font-semibold text-green-600">{dashboardMetrics.opportunitiesIdentified}</div>
              </div>
              <Target className="w-5 h-5 text-green-600" />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <div className="text-sm text-muted-foreground">Temps Réponse Moyen</div>
                <div className="font-semibold">{dashboardMetrics.avgResponseTime}h</div>
              </div>
              <Clock className="w-5 h-5 text-blue-600" />
            </div>
          </div>

          <Button className="w-full mt-4" variant="outline">
            <TrendingUp className="w-4 h-4 mr-2" />
            Voir Détails
          </Button>
        </Card>
      </div>

      {/* Main Intelligence Modules */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-2">
          <TabsTrigger value="marketplace" className="flex items-center gap-2">
            <Brain className="w-4 h-4" />
            Intelligence Marketplace
          </TabsTrigger>
          <TabsTrigger value="map" className="flex items-center gap-2">
            <Compass className="w-4 h-4" />
            Carte Opportunités
          </TabsTrigger>
        </TabsList>

        <TabsContent value="marketplace" className="space-y-4">
          <MarketplaceIntelligence />
        </TabsContent>

        <TabsContent value="map" className="space-y-4">
          <DistributorOpportunitiesMap />
        </TabsContent>
      </Tabs>

      {/* AI Recommendations Footer */}
      <Card className="p-6 bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <div className="flex items-center gap-4">
          <div className="p-3 bg-purple-600 rounded-full">
            <Brain className="w-6 h-6 text-white" />
          </div>
          <div className="flex-1">
            <h3 className="font-semibold text-purple-900">Recommandation IA du Jour</h3>
            <p className="text-purple-700">
              Basé sur l'analyse de vos données, nous recommandons de prioriser la reconquête de 
              "Pharmacie du Quartier" avec une offre spéciale de 15% sur les antibiotiques. 
              Probabilité de succès: 78%
            </p>
          </div>
          <Button className="bg-purple-600 hover:bg-purple-700">
            <Zap className="w-4 h-4 mr-2" />
            Appliquer
          </Button>
        </div>
      </Card>
    </div>
  );
}
