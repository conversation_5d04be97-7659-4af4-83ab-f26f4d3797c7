"use client";

import { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogHeader,
  DialogTitle,
  <PERSON>alogTrigger,
  DialogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  Plus,
  AlertCircle,
  Clock,
  MapPin,
  PhoneCall,
  CheckCircle,
  XCircle,
  Radar,
  TrendingUp,
  Users,
  Zap,
} from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { UrgentRequestForm } from "@/components/urgent-requests/urgent-request-form";
import { ResponseDialog } from "@/components/urgent-requests/response-dialog";
import { useUrgentRequestsRealtime } from "@/hooks/use-urgent-requests-realtime";
import { useNotification } from "@/contexts/notification-context";
import { RealtimeTestPanel } from "@/components/testing/realtime-test-panel";
import { ResponseSystemTest } from "@/components/testing/response-system-test";

interface UrgentRequest {
  id: string;
  medication: string;
  quantity: number;
  urgency: "critique" | "urgent" | "normal" | "faible";
  status: "en attente" | "accepté" | "refusé" | "expiré";
  timestamp: string;
  notes?: string;
  distance?: number;
  expiresAt?: string;
  responses: RequestResponse[];
  pharmacy?: {
    id: string;
    name: string;
    address: string;
    phone: string;
    latitude: number;
    longitude: number;
  };
  contact?: {
    name?: string;
    phone?: string;
    email?: string;
  };
}

interface RequestResponse {
  id: string;
  pharmacyName: string;
  distance: string;
  quantity: number;
  contactNumber: string;
  timestamp: string;
}

interface NearbyRequestsMetadata {
  radius: number;
  center: { latitude: number; longitude: number };
  total: number;
  urgencyDistribution: Record<string, number>;
  averageDistance: number;
}

const urgencyOptions = {
  critique: "🚨 CRITIQUE",
  urgent: "⚡ URGENT",
  normal: "📋 Normal",
  faible: "📝 Faible",
};

const urgencyStyles = {
  critique: "bg-red-100 text-red-800 dark:bg-red-900/50 animate-pulse border-red-200",
  urgent: "bg-orange-100 text-orange-800 dark:bg-orange-900/50 border-orange-200",
  normal: "bg-blue-100 text-blue-800 dark:bg-blue-900/50 border-blue-200",
  faible: "bg-gray-100 text-gray-800 dark:bg-gray-900/50 border-gray-200",
};

const statusStyles = {
  "en attente": "bg-amber-50 text-amber-800 dark:bg-amber-900/50",
  accepté: "bg-emerald-50 text-emerald-800 dark:bg-emerald-900/50",
  refusé: "bg-red-50 text-red-800 dark:bg-red-900/50",
  expiré: "bg-gray-50 text-gray-800 dark:bg-gray-900/50",
};

export default function UrgentRequestsPage() {
  const [requests, setRequests] = useState<UrgentRequest[]>([]);
  const [nearbyRequests, setNearbyRequests] = useState<UrgentRequest[]>([]);
  const [nearbyMetadata, setNearbyMetadata] = useState<NearbyRequestsMetadata | null>(null);
  const [selectedRequest, setSelectedRequest] = useState<UrgentRequest | null>(null);
  const [selectedNearbyRequest, setSelectedNearbyRequest] = useState<UrgentRequest | null>(null);
  const [isResponsesDialogOpen, setIsResponsesDialogOpen] = useState(false);
  const [isResponseDialogOpen, setIsResponseDialogOpen] = useState(false);
  const [isNewRequestDialogOpen, setIsNewRequestDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [nearbyLoading, setNearbyLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState("my-requests");

  // Real-time functionality
  const {
    nearbyRequests: realtimeNearby,
    myRequests: realtimeMyRequests,
    responses: realtimeResponses,
    setMyRequests: setRealtimeMyRequests,
    setNearbyRequests: setRealtimeNearbyRequests
  } = useUrgentRequestsRealtime();
  const { showNotification } = useNotification();

  // Nearby requests filters
  const [radiusFilter, setRadiusFilter] = useState([20]); // Default 20km
  const [urgencyFilter, setUrgencyFilter] = useState<string>("all");
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Fetch my urgent requests from API
  const fetchMyRequests = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/urgent-requests", {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch requests: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      const myRequests = data.requests || [];
      setRequests(myRequests);
      setRealtimeMyRequests(myRequests);
    } catch (error) {
      console.error("Error fetching my urgent requests:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch requests"
      );
    } finally {
      setLoading(false);
    }
  }, [setRealtimeMyRequests]);

  // Fetch nearby urgent requests
  const fetchNearbyRequests = useCallback(async () => {
    try {
      setNearbyLoading(true);

      const params = new URLSearchParams({
        radius: radiusFilter[0].toString(),
        limit: "50",
      });

      if (urgencyFilter !== "all") {
        params.append("urgency", urgencyFilter);
      }

      const response = await fetch(`/api/urgent-requests/nearby?${params}`, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch nearby requests: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      const nearby = data.requests || [];
      setNearbyRequests(nearby);
      setNearbyMetadata(data.metadata);
      setRealtimeNearbyRequests(nearby);

      console.log(`🔍 Fetched ${nearby.length} nearby requests within ${radiusFilter[0]}km`);
    } catch (error) {
      console.error("Error fetching nearby requests:", error);
      showNotification(
        error instanceof Error ? error.message : "Failed to fetch nearby requests",
        "error"
      );
    } finally {
      setNearbyLoading(false);
    }
  }, [radiusFilter, urgencyFilter, setRealtimeNearbyRequests, showNotification]);

  // Initial data loading
  useEffect(() => {
    fetchMyRequests();
  }, [fetchMyRequests]);

  // Load nearby requests when tab changes or filters change
  useEffect(() => {
    if (activeTab === "nearby") {
      fetchNearbyRequests();
    }
  }, [activeTab, fetchNearbyRequests]);

  // Auto-refresh nearby requests
  useEffect(() => {
    if (!autoRefresh || activeTab !== "nearby") return;

    const interval = setInterval(() => {
      fetchNearbyRequests();
    }, 30000); // Refresh every 30 seconds

    return () => clearInterval(interval);
  }, [autoRefresh, activeTab, fetchNearbyRequests]);

  // Sync with real-time data
  useEffect(() => {
    if (realtimeMyRequests.length > 0) {
      setRequests(realtimeMyRequests);
    }
  }, [realtimeMyRequests]);

  useEffect(() => {
    if (realtimeNearby.length > 0) {
      setNearbyRequests(realtimeNearby);
    }
  }, [realtimeNearby]);

  const handleAddRequest = async (values: any) => {
    try {
      const response = await fetch("/api/urgent-requests", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error(`Failed to create request: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Add the new request to the list
      const newRequest = data.request;
      setRequests((prev) => [newRequest, ...prev]);
      setRealtimeMyRequests((prev) => [newRequest, ...prev]);
      setIsNewRequestDialogOpen(false);

      showNotification(
        `🚨 Demande urgente publiée: ${newRequest.medication}`,
        "success"
      );
    } catch (error) {
      console.error("Error creating urgent request:", error);
      setError(
        error instanceof Error ? error.message : "Failed to create request"
      );
    }
  };

  const handleAcceptResponse = (requestId: string, responseId: string) => {
    setRequests((prev) =>
      prev.map((req) =>
        req.id === requestId ? { ...req, status: "accepté" as const } : req
      )
    );
    setIsResponsesDialogOpen(false);
  };

  // Handle responding to a nearby urgent request
  const handleRespondToRequest = async (requestId: string, responseData: any) => {
    try {
      const response = await fetch("/api/urgent-requests/nearby", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(responseData),
      });

      if (!response.ok) {
        throw new Error(`Failed to send response: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      showNotification(
        `✅ Réponse envoyée avec succès pour ${selectedNearbyRequest?.medication}`,
        "success"
      );

      // Remove the request from nearby list since we've responded
      setNearbyRequests(prev =>
        prev.filter(req => req.id !== requestId)
      );
      setRealtimeNearbyRequests(prev =>
        prev.filter(req => req.id !== requestId)
      );

    } catch (error) {
      console.error("Error sending response:", error);
      showNotification(
        error instanceof Error ? error.message : "Failed to send response",
        "error"
      );
      throw error; // Re-throw to let the dialog handle it
    }
  };

  // Open response dialog for a nearby request
  const openResponseDialog = (request: UrgentRequest) => {
    setSelectedNearbyRequest(request);
    setIsResponseDialogOpen(true);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold flex items-center gap-2">
            <Zap className="w-8 h-8 text-orange-500" />
            SOS Network
          </h1>
          <p className="text-muted-foreground">
            Réseau d'entraide pharmaceutique en temps réel
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => (window.location.href = "/marketplace")}
          >
            Parcourir le Marché
          </Button>
          <Button onClick={() => setIsNewRequestDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Nouvelle demande
          </Button>
        </div>
      </div>

      {error && (
        <Card className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        </Card>
      )}

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="my-requests" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Mes Demandes ({requests.length})
          </TabsTrigger>
          <TabsTrigger value="nearby" className="flex items-center gap-2">
            <Radar className="w-4 h-4" />
            Réseau Local ({nearbyRequests.length})
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Statistiques
          </TabsTrigger>
        </TabsList>

        <TabsContent value="my-requests" className="space-y-4">
          <Card>
            <div className="p-6">
              {loading ? (
                <div className="flex items-center justify-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
                  <span className="ml-2">Chargement de vos demandes...</span>
                </div>
              ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Médicament</TableHead>
                  <TableHead className="text-center">Quantité</TableHead>
                  <TableHead>Urgence</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Publication</TableHead>
                  <TableHead className="text-right">Réponses</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div>{request.medication}</div>
                        {request.notes && (
                          <div className="text-sm text-muted-foreground mt-1">
                            {request.notes}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      {request.quantity} unités
                    </TableCell>
                    <TableCell>
                      <Badge className={urgencyStyles[request.urgency]}>
                        {urgencyOptions[request.urgency]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusStyles[request.status]}>
                        {request.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(request.timestamp), "PPp", {
                            locale: fr,
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant={
                          request.responses.length > 0 ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          setSelectedRequest(request);
                          setIsResponsesDialogOpen(true);
                        }}
                      >
                        {request.responses.length} réponse
                        {request.responses.length !== 1 ? "s" : ""}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {requests.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center gap-2">
                        <AlertCircle className="w-8 h-8 text-muted-foreground/50" />
                        <p className="text-muted-foreground">
                          Aucune demande urgente
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </Card>
    </TabsContent>

    <TabsContent value="nearby" className="space-y-4">
      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <MapPin className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Rayon:</span>
            <div className="w-32">
              <Slider
                value={radiusFilter}
                onValueChange={setRadiusFilter}
                max={100}
                min={5}
                step={5}
                className="w-full"
              />
            </div>
            <span className="text-sm text-muted-foreground">{radiusFilter[0]}km</span>
          </div>

          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Urgence:</span>
            <Select value={urgencyFilter} onValueChange={setUrgencyFilter}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes</SelectItem>
                <SelectItem value="critical">Critique</SelectItem>
                <SelectItem value="high">Urgent</SelectItem>
                <SelectItem value="normal">Normal</SelectItem>
                <SelectItem value="low">Faible</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={fetchNearbyRequests}
            disabled={nearbyLoading}
          >
            {nearbyLoading ? "Actualisation..." : "Actualiser"}
          </Button>

          {nearbyMetadata && (
            <div className="text-sm text-muted-foreground">
              {nearbyMetadata.total} demandes dans un rayon de {nearbyMetadata.radius}km
              {nearbyMetadata.averageDistance > 0 && (
                <span> • Distance moyenne: {nearbyMetadata.averageDistance}km</span>
              )}
            </div>
          )}
        </div>
      </Card>

      <Card>
        <div className="p-6">
          {nearbyLoading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Recherche de demandes à proximité...</span>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Médicament</TableHead>
                  <TableHead className="text-center">Quantité</TableHead>
                  <TableHead>Urgence</TableHead>
                  <TableHead>Pharmacie</TableHead>
                  <TableHead>Distance</TableHead>
                  <TableHead>Publication</TableHead>
                  <TableHead className="text-right">Action</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {nearbyRequests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div>{request.medication}</div>
                        {request.notes && (
                          <div className="text-sm text-muted-foreground mt-1">
                            {request.notes}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      {request.quantity} unités
                    </TableCell>
                    <TableCell>
                      <Badge className={urgencyStyles[request.urgency]}>
                        {urgencyOptions[request.urgency]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div>
                        <div className="font-medium">{request.pharmacy?.name}</div>
                        <div className="text-sm text-muted-foreground">
                          {request.pharmacy?.address}
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-1">
                        <MapPin className="w-3 h-3 text-muted-foreground" />
                        <span className="text-sm">{request.distance?.toFixed(1)}km</span>
                      </div>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(request.timestamp), "PPp", {
                            locale: fr,
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        size="sm"
                        onClick={() => openResponseDialog(request)}
                        className="bg-blue-600 hover:bg-blue-700 text-white"
                      >
                        Répondre
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {nearbyRequests.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={7} className="h-24 text-center">
                      <div className="flex flex-col items-center gap-2">
                        <Radar className="w-8 h-8 text-muted-foreground/50" />
                        <p className="text-muted-foreground">
                          Aucune demande urgente dans votre zone
                        </p>
                        <p className="text-sm text-muted-foreground">
                          Essayez d'augmenter le rayon de recherche
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </Card>
    </TabsContent>

    <TabsContent value="analytics" className="space-y-4">
      <Card className="p-6">
        <div className="text-center py-8">
          <TrendingUp className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
          <h3 className="text-lg font-semibold mb-2">Statistiques du Réseau</h3>
          <p className="text-muted-foreground">
            Tableau de bord analytique en cours de développement
          </p>
          <p className="text-sm text-muted-foreground mt-2">
            Bientôt disponible: temps de réponse, taux de satisfaction, performance du réseau
          </p>
        </div>
      </Card>
    </TabsContent>
  </Tabs>

      <Dialog
        open={isResponsesDialogOpen}
        onOpenChange={setIsResponsesDialogOpen}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Réponses à votre demande</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold">
                    {selectedRequest.medication}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedRequest.quantity} unités demandées
                  </p>
                </div>
                <Badge className={urgencyStyles[selectedRequest.urgency]}>
                  {urgencyOptions[selectedRequest.urgency]}
                </Badge>
              </div>

              <div className="space-y-4">
                {selectedRequest.responses.length > 0 ? (
                  selectedRequest.responses.map((response) => (
                    <Card key={response.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="font-medium">
                            {response.pharmacyName}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <MapPin className="w-4 h-4" />
                              <span>{response.distance}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <PhoneCall className="w-4 h-4" />
                              <span>{response.contactNumber}</span>
                            </div>
                          </div>
                          <div className="text-sm">
                            Peut fournir: {response.quantity} unités
                          </div>
                        </div>
                        {selectedRequest.status === "en attente" && (
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600"
                            >
                              <XCircle className="w-4 h-4 mr-1" />
                              Refuser
                            </Button>
                            <Button
                              size="sm"
                              className="text-emerald-600"
                              onClick={() =>
                                handleAcceptResponse(
                                  selectedRequest.id,
                                  response.id
                                )
                              }
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Accepter
                            </Button>
                          </div>
                        )}
                      </div>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                    <p>Aucune réponse pour le moment</p>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsResponsesDialogOpen(false)}
            >
              Fermer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isNewRequestDialogOpen}
        onOpenChange={setIsNewRequestDialogOpen}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Publier une demande urgente</DialogTitle>
          </DialogHeader>
          <UrgentRequestForm
            onSubmit={handleAddRequest}
            onCancel={() => setIsNewRequestDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>

      {/* Response Dialog */}
      <ResponseDialog
        open={isResponseDialogOpen}
        onClose={() => {
          setIsResponseDialogOpen(false);
          setSelectedNearbyRequest(null);
        }}
        request={selectedNearbyRequest}
        onSubmit={handleRespondToRequest}
      />

      {/* Testing Panels */}
      <RealtimeTestPanel />
      <ResponseSystemTest />
    </div>
  );
}
