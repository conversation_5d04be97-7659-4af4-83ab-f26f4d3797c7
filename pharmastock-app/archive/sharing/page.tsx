"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON>alogContent,
  DialogHeader,
  <PERSON>alogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Badge } from "@/components/ui/badge";
import { Plus, Store, Pencil, Timer } from "lucide-react";
import { format, differenceInDays } from "date-fns";
import { fr } from "date-fns/locale";

interface Medicine {
  id: number;
  name: string;
  quantity: number;
  expiryDate: string;
  batchNumber: string;
  published: boolean;
  publishedQuantity?: number;
}

export default function SharingPage() {
  const [medicines, setMedicines] = useState<Medicine[]>([]);
  const [selectedMedicine, setSelectedMedicine] = useState<Medicine | null>(null);
  const [isPublishDialogOpen, setIsPublishDialogOpen] = useState(false);
  const [publishQuantity, setPublishQuantity] = useState<number>(0);

  const handleAddMedicine = (medicine: Omit<Medicine, 'id' | 'published'>) => {
    setMedicines(prev => [...prev, {
      ...medicine,
      id: Date.now(),
      published: false
    }]);
  };

  const handlePublish = (medicineId: number, quantity: number) => {
    setMedicines(prev => prev.map(med =>
      med.id === medicineId
        ? { ...med, published: true, publishedQuantity: quantity }
        : med
    ));
    setIsPublishDialogOpen(false);
  };

  const getDaysUntilExpiry = (expiryDate: string) => {
    return differenceInDays(new Date(expiryDate), new Date());
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Médicaments à partager</h1>
          <p className="text-muted-foreground">
            Gérez vos médicaments à partager avec d'autres pharmacies
          </p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="w-4 h-4 mr-2" />
              Ajouter un médicament
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Ajouter un médicament</DialogTitle>
            </DialogHeader>
            <form className="space-y-4" onSubmit={(e) => {
              e.preventDefault();
              const formData = new FormData(e.currentTarget);
              handleAddMedicine({
                name: formData.get('name') as string,
                quantity: Number(formData.get('quantity')),
                expiryDate: formData.get('expiryDate') as string,
                batchNumber: formData.get('batchNumber') as string,
              });
              (e.target as HTMLFormElement).reset();
            }}>
              <div className="space-y-2">
                <Label htmlFor="name">Nom du médicament</Label>
                <Input id="name" name="name" required />
              </div>
              <div className="space-y-2">
                <Label htmlFor="quantity">Quantité disponible</Label>
                <Input
                  id="quantity"
                  name="quantity"
                  type="number"
                  min="1"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="expiryDate">Date de péremption</Label>
                <Input
                  id="expiryDate"
                  name="expiryDate"
                  type="date"
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="batchNumber">Numéro de lot</Label>
                <Input id="batchNumber" name="batchNumber" required />
              </div>
              <Button type="submit" className="w-full">
                Ajouter
              </Button>
            </form>
          </DialogContent>
        </Dialog>
      </div>

      <Card>
        <div className="p-6">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Médicament</TableHead>
                <TableHead>N° de lot</TableHead>
                <TableHead className="text-center">Quantité</TableHead>
                <TableHead>Péremption</TableHead>
                <TableHead>Statut</TableHead>
                <TableHead className="text-right">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {medicines.map((medicine) => {
                const daysUntilExpiry = getDaysUntilExpiry(medicine.expiryDate);
                return (
                  <TableRow key={medicine.id}>
                    <TableCell className="font-medium">
                      {medicine.name}
                    </TableCell>
                    <TableCell>{medicine.batchNumber}</TableCell>
                    <TableCell className="text-center">
                      {medicine.published
                        ? `${medicine.publishedQuantity} / ${medicine.quantity}`
                        : medicine.quantity} unités
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Timer className="w-4 h-4 text-muted-foreground" />
                        <span>
                          {format(new Date(medicine.expiryDate), 'dd/MM/yyyy', { locale: fr })}
                        </span>
                        <Badge variant={
                          daysUntilExpiry <= 30 ? "destructive" :
                            daysUntilExpiry <= 90 ? "warning" : "default"
                        }>
                          {daysUntilExpiry <= 30 ? "Urgent" :
                            daysUntilExpiry <= 90 ? "Bientôt" : "OK"}
                        </Badge>
                      </div>
                    </TableCell>
                    <TableCell>
                      {medicine.published ? (
                        <Badge variant="outline" className="bg-emerald-50 text-emerald-700">
                          En vente
                        </Badge>
                      ) : (
                        <Badge variant="outline" className="bg-gray-50 text-gray-700">
                          Non publié
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell className="text-right">
                      {!medicine.published && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setSelectedMedicine(medicine);
                            setPublishQuantity(medicine.quantity);
                            setIsPublishDialogOpen(true);
                          }}
                        >
                          <Store className="w-4 h-4 mr-2" />
                          Mettre en vente
                        </Button>
                      )}
                    </TableCell>
                  </TableRow>
                );
              })}
              {medicines.length === 0 && (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    Aucun médicament ajouté
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </Card>

      <Dialog open={isPublishDialogOpen} onOpenChange={setIsPublishDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Mettre en vente</DialogTitle>
          </DialogHeader>
          <div className="space-y-4">
            {selectedMedicine && (
              <>
                <div>
                  <p className="font-medium">{selectedMedicine.name}</p>
                  <p className="text-sm text-muted-foreground">
                    Quantité disponible: {selectedMedicine.quantity} unités
                  </p>
                </div>
                <div className="space-y-2">
                  <Label htmlFor="publishQuantity">Quantité à mettre en vente</Label>
                  <Input
                    id="publishQuantity"
                    type="number"
                    min="1"
                    max={selectedMedicine.quantity}
                    value={publishQuantity}
                    onChange={(e) => setPublishQuantity(Number(e.target.value))}
                  />
                </div>
                <Button
                  className="w-full"
                  onClick={() => handlePublish(selectedMedicine.id, publishQuantity)}
                >
                  Confirmer
                </Button>
              </>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
} 