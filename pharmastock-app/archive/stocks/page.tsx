import { Card } from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { StockForm } from "@/components/stocks/stock-form";
import { StockTable } from "@/components/stocks/stock-table";

export default function StocksPage() {
  return (
    <div className="p-6 space-y-6">
      <h1 className="text-3xl font-bold">Gestion des stocks</h1>

      <Tabs defaultValue="inventory" className="space-y-6">
        <TabsList>
          <TabsTrigger value="inventory">Inventaire</TabsTrigger>
          <TabsTrigger value="add">Ajouter un produit</TabsTrigger>
        </TabsList>

        <TabsContent value="inventory">
          <Card className="p-6">
            <StockTable />
          </Card>
        </TabsContent>

        <TabsContent value="add">
          <Card className="p-6 max-w-2xl mx-auto">
            <h2 className="text-xl font-semibold mb-4">Ajouter un nouveau produit</h2>
            <StockForm />
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}