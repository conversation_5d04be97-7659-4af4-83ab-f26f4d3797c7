"use client";

import { useEffect, useState } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Loader2, ArrowLeft, Check } from "lucide-react";
import Link from "next/link";

interface MedicationInfo {
  name: string;
  manufacturer: string;
  dosage: string;
  form: string;
  activeIngredients: string;
  posology: string;
  indications: string;
  contraindications: string;
  reimbursementRate: string;
  packaging: string;
  cip13?: string;
}

export default function ScanPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [medication, setMedication] = useState<MedicationInfo | null>(null);

  useEffect(() => {
    const code = searchParams.get("code");
    if (!code) {
      setError("Aucun code-barres fourni");
      setLoading(false);
      return;
    }

    const lookupMedication = async () => {
      try {
        const response = await fetch("/api/lookup-medication", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ barcode: code }),
        });

        if (!response.ok) {
          const data = await response.json();
          throw new Error(data.error || "Erreur lors de la recherche du médicament");
        }

        const medicationData = await response.json();
        setMedication(medicationData);
        setLoading(false);
      } catch (err) {
        console.error("Error looking up medication:", err);
        setError(err instanceof Error ? err.message : "Erreur inconnue");
        setLoading(false);
      }
    };

    lookupMedication();
  }, [searchParams]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto" />
          <p className="text-lg font-medium">Recherche du médicament...</p>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="min-h-screen p-4">
        <div className="max-w-lg mx-auto space-y-4">
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <ArrowLeft className="w-4 h-4 mr-2" />
            Retour
          </Button>

          <Card className="p-6">
            <div className="text-center space-y-4">
              <p className="text-destructive font-medium">{error}</p>
              <Button asChild>
                <Link href="/marketplace/list">
                  Retour à la mise en vente
                </Link>
              </Button>
            </div>
          </Card>
        </div>
      </div>
    );
  }

  if (!medication) {
    return null;
  }

  return (
    <div className="min-h-screen p-4">
      <div className="max-w-lg mx-auto space-y-4">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="w-4 h-4 mr-2" />
          Retour
        </Button>

        <Card className="p-6 space-y-6">
          <div className="flex items-center justify-between">
            <h1 className="text-2xl font-bold">{medication.name}</h1>
            <Check className="w-6 h-6 text-green-500" />
          </div>

          <div className="space-y-4">
            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Fabricant</h2>
              <p>{medication.manufacturer}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Dosage</h2>
              <p>{medication.dosage}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Forme</h2>
              <p>{medication.form}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Principes actifs</h2>
              <p>{medication.activeIngredients}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Posologie</h2>
              <p>{medication.posology}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Indications</h2>
              <p>{medication.indications}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Contre-indications</h2>
              <p>{medication.contraindications}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Taux de remboursement</h2>
              <p>{medication.reimbursementRate}</p>
            </div>

            <div>
              <h2 className="text-sm font-medium text-muted-foreground">Conditionnement</h2>
              <p>{medication.packaging}</p>
            </div>

            {medication.cip13 && (
              <div>
                <h2 className="text-sm font-medium text-muted-foreground">Code CIP13</h2>
                <p>{medication.cip13}</p>
              </div>
            )}
          </div>

          <div className="pt-4 flex justify-end">
            <Button asChild>
              <Link href={`/marketplace/list?code=${medication.cip13}`}>
                Mettre en vente
              </Link>
            </Button>
          </div>
        </Card>
      </div>
    </div>
  );
} 