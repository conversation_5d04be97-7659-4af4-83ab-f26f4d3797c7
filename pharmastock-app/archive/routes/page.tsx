"use client";

import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { cn } from "@/lib/utils";

interface Route {
  path: string;
  description: string;
  roles: ("admin" | "pharmacist" | "staff")[];
}

const routes: Route[] = [
  {
    path: "/dashboard",
    description: "Main dashboard and overview.",
    roles: ["admin", "pharmacist", "staff"],
  },
  {
    path: "/marketplace",
    description: "Browse and manage product listings.",
    roles: ["admin", "pharmacist"],
  },
  {
    path: "/inventory",
    description: "Manage pharmacy inventory.",
    roles: ["admin", "pharmacist", "staff"],
  },
  {
    path: "/admin",
    description: "Admin dashboard and overview.",
    roles: ["admin"],
  },
  {
    path: "/admin/pharmacies",
    description: "Manage all registered pharmacies.",
    roles: ["admin"],
  },
  {
    path: "/admin/users",
    description: "Manage all users and their roles.",
    roles: ["admin"],
  },
  {
    path: "/admin/analytics",
    description: "System-wide analytics and reporting.",
    roles: ["admin"],
  },
  {
    path: "/admin/settings",
    description: "Global system settings.",
    roles: ["admin"],
  },
  {
    path: "/admin/urgent-requests",
    description: "Monitor and manage urgent requests.",
    roles: ["admin"],
  },
  {
    path: "/admin/categories",
    description: "Manage product categories.",
    roles: ["admin"],
  },
  {
    path: "/profile",
    description: "User profile and settings.",
    roles: ["admin", "pharmacist", "staff"],
  },
  {
    path: "/team",
    description: "Manage pharmacy team members.",
    roles: ["admin", "pharmacist"],
  },
];

interface RoleBadgeProps {
  role: string;
  href: string;
}

function RoleBadge({ role, href }: RoleBadgeProps) {
  const variants = {
    admin: "destructive",
    pharmacist: "default",
    staff: "secondary",
  } as const;

  return (
    <Link href={href}>
      <Badge
        variant={variants[role as keyof typeof variants]}
        className="cursor-pointer hover:opacity-80"
      >
        {role}
      </Badge>
    </Link>
  );
}

export default function RoutesPage() {
  return (
    <div className="container mx-auto py-10">
      <Card className="p-6">
        <div className="mb-6">
          <h1 className="text-2xl font-bold mb-2">Available Routes</h1>
          <p className="text-muted-foreground">
            Overview of all available routes and their access levels.
          </p>
        </div>

        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Path</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Access Roles</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {routes.map((route) => (
              <TableRow key={route.path}>
                <TableCell className="font-mono">{route.path}</TableCell>
                <TableCell>{route.description}</TableCell>
                <TableCell>
                  <div className="flex gap-2">
                    {route.roles.map((role) => (
                      <RoleBadge key={role} role={role} href={route.path} />
                    ))}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>

        <div className="mt-6">
          <h2 className="text-lg font-semibold mb-4">Test Users</h2>
          <div className="space-y-4">
            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Admin User</h3>
              <p className="text-sm text-muted-foreground">
                Email: <EMAIL>
                <br />
                Password: Admin123!
              </p>
            </div>
            <div className="p-4 bg-muted rounded-lg">
              <h3 className="font-semibold mb-2">Test User</h3>
              <p className="text-sm text-muted-foreground">
                Email: <EMAIL>
                <br />
                Password: Admin123!
              </p>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
} 