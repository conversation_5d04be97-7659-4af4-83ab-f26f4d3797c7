// OLD DASHBOARD DESIGN - ARCHIVED
// This was the original Tailwind CSS based dashboard
// Replaced with Mantine UI design on 2025-05-28

"use client";

import { useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";

export default function OldDashboardPage() {
  const { user, pharmacy, loading } = useAuth();

  useEffect(() => {
    console.log("Dashboard page mounted", { user, pharmacy, loading });
  }, [user, pharmacy, loading]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-gray-600">Loading...</p>
        </div>
      </div>
    );
  }

  // Super admins (owners) don't need pharmacy data
  if (!pharmacy && user?.role !== 'owner') {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">
            Loading pharmacy data...
          </p>
        </div>
      </div>
    );
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-4" role="heading">Dashboard</h1>
      <div>
        <p className="text-lg mb-4">
          Welcome, {pharmacy?.name || (user?.role === 'owner' ? 'Super Admin' : 'User')}!
        </p>

        {/* Quick Actions */}
        <div className="mb-8" data-testid="quick-actions">
          <h2 className="text-lg font-semibold mb-4" role="heading">Quick Actions</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <a href="/inventory/add" className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
              Add Product
            </a>
            <a href="/inventory" className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
              Manage Inventory
            </a>
            <a href="/orders" className="bg-white p-6 rounded-lg shadow hover:shadow-md transition-shadow">
              View Orders
            </a>
          </div>
        </div>

        {/* Recent Activity */}
        <div className="mb-8" data-testid="recent-activity">
          <h2 className="text-lg font-semibold mb-4" role="heading">Recent Activity</h2>
          <div className="bg-white p-6 rounded-lg shadow">
            <p className="text-gray-500">No recent activity to display</p>
          </div>
        </div>

        {/* System Alerts */}
        <div className="mb-8" data-testid="system-alerts">
          <h2 className="text-lg font-semibold mb-4" role="heading">System Alerts</h2>
          <div className="bg-white p-6 rounded-lg shadow">
            <p className="text-gray-500">No alerts at this time</p>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-2" role="heading">Profile</h2>
            <div className="space-y-2">
              <p>
                <strong>Name:</strong> {pharmacy?.name || (user?.role === 'owner' ? 'Super Admin' : 'N/A')}
              </p>
              <p>
                <strong>Email:</strong> {user?.email}
              </p>
              <p>
                <strong>Phone:</strong> {pharmacy?.phone || 'N/A'}
              </p>
              <p>
                <strong>Address:</strong> {pharmacy?.address || 'N/A'}
              </p>
            </div>
          </div>
          <div className="bg-white p-6 rounded-lg shadow">
            <h2 className="text-lg font-semibold mb-2" role="heading">Role</h2>
            <div className="space-y-2">
              <p>
                <strong>Type:</strong> {user?.role}
              </p>
              <p>
                <strong>Identifiant:</strong> {user?.id}
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
