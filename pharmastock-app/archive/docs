# Edge API Routes vs. Client Context

This document explains the architectural differences between Edge API routes and Client Context in our application.

## Overview

Our application uses both Edge API routes and Client Context patterns for different purposes:

- **Edge API Routes:** For authentication, session management, and secure operations
- **Client Context:** For real-time data and UI state management

## Edge API Routes

Located in `app/api/*`, Edge API routes run on the Edge runtime for optimal performance.

### Characteristics

- **Performance:** Runs closer to the user
- **Security:** No client-side exposure of sensitive operations
- **Stateless:** Each request is independent
- **Headers:** Can set secure cookies and custom headers
- **Environment:** Access to Edge runtime environment variables

### Use Cases

1. **Authentication:**
```typescript
// app/api/auth/login/route.ts
export const runtime = 'edge';

export async function POST(request: NextRequest) {
  // Secure authentication logic
  // Set HTTP-only cookies
  // Return minimal user data
}
```

2. **Session Management:**
```typescript
// app/api/auth/session/route.ts
export const runtime = 'edge';

export async function GET(request: NextRequest) {
  // Validate session tokens
  // Refresh if needed
  // Return current session state
}
```

## Client Context

Located in `contexts/*`, Client Context provides real-time state management.

### Characteristics

- **Real-time:** Immediate updates
- **Caching:** Client-side state caching
- **Reactivity:** Automatic UI updates
- **Persistence:** Optional local storage
- **Environment:** Access to browser environment

### Use Cases

1. **Authentication State:**
```typescript
// contexts/auth-context.tsx
export function AuthProvider({ children }: { children: React.ReactNode }) {
  // Manage user authentication state
  // Provide login/logout methods
  // Handle token refresh
}
```

2. **Database Access:**
```typescript
// contexts/database-context.tsx
export function DatabaseProvider({ children }: { children: React.ReactNode }) {
  // Manage database connections
  // Provide query methods
  // Handle real-time subscriptions
}
```

## Environment Variables

### Edge Environment

```typescript
// lib/config.ts - Edge configuration
export const edgeConfig = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY
  },
  auth: {
    jwtSecret: process.env.JWT_SECRET
  }
};
```

### Client Environment

```typescript
// lib/config.ts - Client configuration
export const clientConfig = {
  supabase: {
    url: process.env.NEXT_PUBLIC_SUPABASE_URL,
    anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  },
  app: {
    url: process.env.NEXT_PUBLIC_APP_URL
  }
};
```

## Best Practices

1. **Edge API Routes**
   - Use for authentication and security-critical operations
   - Keep response payloads minimal
   - Implement proper error handling
   - Use appropriate HTTP status codes
   - Set secure headers and cookies

2. **Client Context**
   - Use for UI state and real-time updates
   - Implement proper cleanup in useEffect
   - Memoize values and callbacks
   - Handle loading and error states
   - Use appropriate TypeScript types

3. **Security Considerations**
   - Never expose sensitive keys in client code
   - Validate all input on the server
   - Use HTTP-only cookies for sensitive data
   - Implement proper CORS policies
   - Rate limit API routes

## Example: Authentication Flow

```typescript
// Edge API Route
// app/api/auth/login/route.ts
export async function POST(request: NextRequest) {
  const { email, password } = await request.json();
  
  // Secure authentication
  const { user, session } = await supabase.auth.signIn({ email, password });
  
  // Set secure cookie
  const response = NextResponse.json({ user: { id: user.id, email: user.email } });
  response.cookies.set('session', session.token, {
    httpOnly: true,
    secure: process.env.NODE_ENV === 'production',
    sameSite: 'lax'
  });
  
  return response;
}

// Client Context
// contexts/auth-context.tsx
export function useAuth() {
  const login = async (email: string, password: string) => {
    const response = await fetch('/api/auth/login', {
      method: 'POST',
      body: JSON.stringify({ email, password })
    });
    
    if (!response.ok) {
      throw new Error('Authentication failed');
    }
    
    const { user } = await response.json();
    setUser(user);
  };
  
  // ... rest of the context
}
``` 