"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Download, Filter } from "lucide-react";
import { useState } from "react";
import {
  <PERSON><PERSON>,
  <PERSON>et<PERSON>ontent,
  <PERSON>etHeader,
  <PERSON>etTitle,
  SheetTrigger,
} from "@/components/ui/sheet";

interface Transaction {
  id: string;
  type: "sale" | "purchase" | "urgent_request" | "message";
  date: Date;
  description: string;
  status: "completed" | "pending" | "cancelled";
  participants: string[];
  details: {
    product?: string;
    quantity?: number;
    price?: number;
    message?: string;
  };
}

// Mock data for transactions
const TRANSACTIONS: Transaction[] = [
  {
    id: "TR001",
    type: "sale",
    date: new Date("2024-01-15T10:30:00"),
    description: "Vente de Doliprane 1000mg",
    status: "completed",
    participants: ["Pharmacie Centrale", "Pharmacie du Parc"],
    details: {
      product: "Doliprane 1000mg",
      quantity: 100,
      price: 350.00,
    },
  },
  {
    id: "TR002",
    type: "urgent_request",
    date: new Date("2024-01-14T15:45:00"),
    description: "Demande urgente d'Efferalgan",
    status: "pending",
    participants: ["Pharmacie des Alpes", "Pharmacie de la Gare"],
    details: {
      product: "Efferalgan 500mg",
      quantity: 50,
      message: "Besoin urgent pour ce soir",
    },
  },
];

function getStatusBadgeVariant(status: Transaction["status"]) {
  switch (status) {
    case "completed":
      return "success";
    case "pending":
      return "warning";
    case "cancelled":
      return "destructive";
    default:
      return "secondary";
  }
}

export function TransactionLog() {
  const [searchTerm, setSearchTerm] = useState("");

  const filteredTransactions = TRANSACTIONS.filter(
    (transaction) =>
      transaction.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.participants.some((p) =>
        p.toLowerCase().includes(searchTerm.toLowerCase())
      )
  );

  const renderMobileCard = (transaction: Transaction) => (
    <div key={transaction.id} className="space-y-3 p-4 border-b last:border-0">
      <div className="flex justify-between items-start">
        <div>
          <div className="font-mono text-sm">{transaction.id}</div>
          <div className="font-medium">{transaction.description}</div>
        </div>
        <Badge variant={getStatusBadgeVariant(transaction.status)}>
          {transaction.status === "completed" && "Terminé"}
          {transaction.status === "pending" && "En attente"}
          {transaction.status === "cancelled" && "Annulé"}
        </Badge>
      </div>

      <div className="text-sm text-muted-foreground">
        <div>Date: {transaction.date.toLocaleDateString()} {transaction.date.toLocaleTimeString()}</div>
        <div>Type: {transaction.type === "sale" && "Vente"}
          {transaction.type === "purchase" && "Achat"}
          {transaction.type === "urgent_request" && "Demande urgente"}
          {transaction.type === "message" && "Message"}
        </div>
        <div>Participants: {transaction.participants.join(", ")}</div>
      </div>

      {Object.keys(transaction.details).length > 0 && (
        <div className="text-sm bg-muted/50 p-2 rounded">
          {transaction.details.product && <div>Produit: {transaction.details.product}</div>}
          {transaction.details.quantity && <div>Quantité: {transaction.details.quantity}</div>}
          {transaction.details.price && <div>Prix: {transaction.details.price}€</div>}
          {transaction.details.message && <div>Message: {transaction.details.message}</div>}
        </div>
      )}
    </div>
  );

  return (
    <div className="space-y-4">
      <div className="flex flex-col sm:flex-row gap-4 justify-between">
        <div className="flex flex-1 gap-2">
          <div className="relative flex-1">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher dans l'historique..."
              className="pl-8"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Sheet>
            <SheetTrigger asChild>
              <Button variant="outline" size="icon">
                <Filter className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="right">
              <SheetHeader>
                <SheetTitle>Filtres</SheetTitle>
              </SheetHeader>
              {/* Add filter options here */}
            </SheetContent>
          </Sheet>
        </div>
        <Button variant="outline" className="sm:w-auto">
          <Download className="mr-2 h-4 w-4" />
          Exporter
        </Button>
      </div>

      {/* Desktop view */}
      <div className="hidden md:block rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>ID</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Description</TableHead>
              <TableHead>Participants</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Détails</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredTransactions.map((transaction) => (
              <TableRow key={transaction.id}>
                <TableCell className="font-mono">{transaction.id}</TableCell>
                <TableCell>
                  {transaction.date.toLocaleDateString()} {transaction.date.toLocaleTimeString()}
                </TableCell>
                <TableCell>
                  <Badge variant="outline">
                    {transaction.type === "sale" && "Vente"}
                    {transaction.type === "purchase" && "Achat"}
                    {transaction.type === "urgent_request" && "Demande urgente"}
                    {transaction.type === "message" && "Message"}
                  </Badge>
                </TableCell>
                <TableCell>{transaction.description}</TableCell>
                <TableCell>
                  <div className="space-y-1">
                    {transaction.participants.map((participant, index) => (
                      <div key={index} className="text-sm">
                        {participant}
                      </div>
                    ))}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(transaction.status)}>
                    {transaction.status === "completed" && "Terminé"}
                    {transaction.status === "pending" && "En attente"}
                    {transaction.status === "cancelled" && "Annulé"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <div className="text-sm">
                    {transaction.details.product && (
                      <div>Produit: {transaction.details.product}</div>
                    )}
                    {transaction.details.quantity && (
                      <div>Quantité: {transaction.details.quantity}</div>
                    )}
                    {transaction.details.price && (
                      <div>Prix: {transaction.details.price}€</div>
                    )}
                    {transaction.details.message && (
                      <div>Message: {transaction.details.message}</div>
                    )}
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Mobile view */}
      <div className="md:hidden rounded-md border divide-y">
        {filteredTransactions.map(renderMobileCard)}
      </div>
    </div>
  );
} 