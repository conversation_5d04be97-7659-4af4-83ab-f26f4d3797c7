"use client";

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { MapPin, Phone, Mail, Star, Building, Clock } from "lucide-react";

interface PharmacyProfileViewProps {
  pharmacy: {
    name: string;
    address: string;
    phone: string;
    email: string;
    specialties?: string[];
    city?: string;
    rating?: number;
    exchanges?: number;
    lastActive?: string;
  };
}

export function PharmacyProfileView({ pharmacy }: PharmacyProfileViewProps) {
  return (
    <Card className="w-full max-w-lg">
      <CardHeader>
        <CardTitle className="text-xl font-bold flex items-center gap-2">
          <Building className="h-5 w-5 text-blue-600" />
          {pharmacy.name}
        </CardTitle>
        <CardDescription>
          {pharmacy.city && (
            <span className="flex items-center gap-1">
              <MapPin className="h-4 w-4" />
              {pharmacy.city}
            </span>
          )}
        </CardDescription>
      </Card<PERSON><PERSON>er>
      <CardContent className="space-y-2">
        <div className="flex items-center gap-2 text-muted-foreground">
          <MapPin className="h-4 w-4" />
          <span>{pharmacy.address}</span>
        </div>
        <div className="flex items-center gap-2 text-muted-foreground">
          <Phone className="h-4 w-4" />
          <span>{pharmacy.phone}</span>
        </div>
        <div className="flex items-center gap-2 text-muted-foreground">
          <Mail className="h-4 w-4" />
          <span>{pharmacy.email}</span>
        </div>
        {pharmacy.specialties && (
          <div className="flex flex-wrap gap-2 mt-2">
            {pharmacy.specialties.map((spec) => (
              <Badge key={spec} variant="outline" className="text-xs">
                {spec}
              </Badge>
            ))}
          </div>
        )}
        <div className="flex items-center gap-4 mt-2">
          {pharmacy.rating !== undefined && (
            <span className="flex items-center gap-1 text-sm">
              <Star className="h-4 w-4 text-yellow-500" />
              {pharmacy.rating}
            </span>
          )}
          {pharmacy.exchanges !== undefined && (
            <span className="flex items-center gap-1 text-sm">
              <Building className="h-4 w-4 text-muted-foreground" />
              {pharmacy.exchanges} échanges
            </span>
          )}
          {pharmacy.lastActive && (
            <span className="flex items-center gap-1 text-xs text-muted-foreground">
              <Clock className="h-3 w-3" />
              Actif le {new Date(pharmacy.lastActive).toLocaleDateString("fr-FR")}
            </span>
          )}
        </div>
      </CardContent>
    </Card>
  );
} 