"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { FileText, Upload, Download, Trash2 } from "lucide-react";

interface Document {
  id: string;
  name: string;
  type: "license" | "insurance" | "certification" | "other";
  status: "valid" | "expired" | "pending";
  uploadDate: Date;
  expiryDate?: Date;
}

const DOCUMENTS: Document[] = [
  {
    id: "doc1",
    name: "Licence pharmaceutique",
    type: "license",
    status: "valid",
    uploadDate: new Date("2023-12-01"),
    expiryDate: new Date("2024-12-01"),
  },
  {
    id: "doc2",
    name: "Assurance professionnelle",
    type: "insurance",
    status: "valid",
    uploadDate: new Date("2023-11-15"),
    expiryDate: new Date("2024-11-15"),
  },
  {
    id: "doc3",
    name: "Certification BPD",
    type: "certification",
    status: "pending",
    uploadDate: new Date("2024-01-10"),
  },
];

function getStatusBadgeVariant(status: Document["status"]) {
  switch (status) {
    case "valid":
      return "success";
    case "expired":
      return "destructive";
    case "pending":
      return "warning";
    default:
      return "secondary";
  }
}

function getDocumentTypeLabel(type: Document["type"]) {
  switch (type) {
    case "license":
      return "Licence";
    case "insurance":
      return "Assurance";
    case "certification":
      return "Certification";
    case "other":
      return "Autre";
  }
}

export function PharmacyDocuments() {
  const [documents] = useState<Document[]>(DOCUMENTS);

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    // TODO: Implement file upload logic
    console.log(event.target.files);
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h2 className="text-lg font-semibold">Documents obligatoires</h2>
        <div className="grid gap-4">
          <div className="space-y-2">
            <Label htmlFor="document">Ajouter un document</Label>
            <div className="flex gap-4">
              <Input
                id="document"
                type="file"
                onChange={handleFileUpload}
                className="w-full"
              />
              <Button>
                <Upload className="mr-2 h-4 w-4" />
                Téléverser
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="rounded-md border">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Document</TableHead>
              <TableHead>Type</TableHead>
              <TableHead>Date d&apos;ajout</TableHead>
              <TableHead>Date d&apos;expiration</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {documents.map((doc) => (
              <TableRow key={doc.id}>
                <TableCell className="font-medium">
                  <div className="flex items-center gap-2">
                    <FileText className="h-4 w-4 text-muted-foreground" />
                    {doc.name}
                  </div>
                </TableCell>
                <TableCell>{getDocumentTypeLabel(doc.type)}</TableCell>
                <TableCell>{doc.uploadDate.toLocaleDateString()}</TableCell>
                <TableCell>
                  {doc.expiryDate ? doc.expiryDate.toLocaleDateString() : "-"}
                </TableCell>
                <TableCell>
                  <Badge variant={getStatusBadgeVariant(doc.status)}>
                    {doc.status === "valid" && "Valide"}
                    {doc.status === "expired" && "Expiré"}
                    {doc.status === "pending" && "En attente"}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <div className="flex justify-end gap-2">
                    <Button variant="ghost" size="icon">
                      <Download className="h-4 w-4" />
                    </Button>
                    <Button variant="ghost" size="icon">
                      <Trash2 className="h-4 w-4 text-destructive" />
                    </Button>
                  </div>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 