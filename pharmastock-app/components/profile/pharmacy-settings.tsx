"use client";

import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Separator } from "@/components/ui/separator";
import { Badge } from "@/components/ui/badge";
import { Bell, Mail, Globe } from "lucide-react";

export function PharmacySettings() {
  return (
    <div className="space-y-6">
      <div>
        <h2 className="text-lg font-semibold">Notifications</h2>
        <p className="text-sm text-muted-foreground">
          Gérez vos préférences de notifications.
        </p>

        <div className="mt-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notifications par email</Label>
              <p className="text-sm text-muted-foreground">
                Recevoir des notifications par email
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Notifications push</Label>
              <p className="text-sm text-muted-foreground">
                Recevoir des notifications dans l&apos;application
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Alertes de stock</Label>
              <p className="text-sm text-muted-foreground">
                Notifications pour les stocks à faible rotation
              </p>
            </div>
            <Switch defaultChecked />
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <h2 className="text-lg font-semibold">Préférences de communication</h2>
        <p className="text-sm text-muted-foreground">
          Définissez vos préférences de communication avec les autres pharmacies.
        </p>

        <div className="mt-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Messages directs</Label>
              <p className="text-sm text-muted-foreground">
                Autoriser les messages directs d&apos;autres pharmacies
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Demandes urgentes</Label>
              <p className="text-sm text-muted-foreground">
                Recevoir les demandes urgentes des pharmacies à proximité
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="space-y-2">
            <Label>Rayon de notification</Label>
            <Select defaultValue="10">
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Sélectionner un rayon" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="5">5 km</SelectItem>
                <SelectItem value="10">10 km</SelectItem>
                <SelectItem value="20">20 km</SelectItem>
                <SelectItem value="50">50 km</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <h2 className="text-lg font-semibold">Confidentialité</h2>
        <p className="text-sm text-muted-foreground">
          Gérez la visibilité de vos informations.
        </p>

        <div className="mt-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Profil public</Label>
              <p className="text-sm text-muted-foreground">
                Rendre votre profil visible aux autres pharmacies
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Afficher les stocks</Label>
              <p className="text-sm text-muted-foreground">
                Rendre vos stocks visibles aux autres pharmacies
              </p>
            </div>
            <Switch defaultChecked />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Statistiques publiques</Label>
              <p className="text-sm text-muted-foreground">
                Partager vos statistiques de vente
              </p>
            </div>
            <Switch />
          </div>
        </div>
      </div>

      <Separator />

      <div>
        <h2 className="text-lg font-semibold">Sécurité</h2>
        <p className="text-sm text-muted-foreground">
          Gérez la sécurité de votre compte.
        </p>

        <div className="mt-4 space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>Authentification à deux facteurs</Label>
              <p className="text-sm text-muted-foreground">
                Ajouter une couche de sécurité supplémentaire
              </p>
            </div>
            <Switch />
          </div>

          <div className="space-y-2">
            <Button variant="outline" className="w-full">
              Changer le mot de passe
            </Button>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button variant="outline">Annuler</Button>
        <Button>Enregistrer les paramètres</Button>
      </div>
    </div>
  );
} 