import { useAuth } from "@/contexts/auth-context";
import { usePermissions } from "@/lib/types/auth";
import Link from "next/link";
import Image from "next/image";
import { usePathname } from "next/navigation";
import {
  LayoutDashboard,
  Store,
  Package,
  ShoppingCart,
  Settings
} from "lucide-react";
import { cn } from "@/lib/utils";

// Sidebar now filters links based on user permissions using usePermissions.
// To add a new link, specify the required action/resource for permission check.
// This ensures the sidebar always matches backend access control.

const sidebarRoutes = [
  {
    label: "Dashboard",
    icon: LayoutDashboard,
    href: "/dashboard",
    color: "text-sky-500",
    permission: { action: "read", resource: "listings" }, // All roles can read dashboard
  },
  {
    label: "Marketplace",
    icon: Store,
    href: "/marketplace",
    color: "text-violet-500",
    permission: { action: "read", resource: "products" },
  },
  {
    label: "Inventory",
    icon: Package,
    color: "text-pink-700",
    href: "/inventory",
    permission: { action: "read", resource: "products" },
  },
  {
    label: "Orders",
    icon: ShoppingCart,
    color: "text-orange-700",
    href: "/orders",
    permission: { action: "read", resource: "transactions" },
  },
  {
    label: "Settings",
    icon: Settings,
    href: "/settings",
    color: "text-gray-500",
    permission: { action: "read", resource: "settings" },
  },
];

export function Sidebar() {
  const { user } = useAuth();
  const pathname = usePathname();
  // Map user to the User type expected by usePermissions
  const mappedUser = user
    ? {
        ...user,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    : null;
  const permissions = usePermissions(mappedUser);

  // Only show links the user can access
  const filteredRoutes = sidebarRoutes.filter(route =>
    permissions.can(route.permission.action as any, route.permission.resource as any)
  );

  return (
    <div className="space-y-4 py-4 flex flex-col h-full bg-[#111827] text-white">
      <div className="px-3 py-2 flex-1">
        <Link href="/dashboard" className="flex items-center pl-3 mb-14">
          <div className="relative h-8 w-8 mr-4">
            <Image fill alt="Logo" src="/logo.png" />
          </div>
          <h1 className="text-2xl font-bold">
            PharmaStock
          </h1>
        </Link>
        <div className="space-y-1">
          {filteredRoutes.map((route) => (
            <Link
              key={route.href}
              href={route.href}
              className={cn(
                "text-sm group flex p-3 w-full justify-start font-medium cursor-pointer hover:text-white hover:bg-white/10 rounded-lg transition",
                pathname === route.href ? "text-white bg-white/10" : "text-zinc-400",
              )}
            >
              <div className="flex items-center flex-1">
                <route.icon className={cn("h-5 w-5 mr-3", route.color)} />
                {route.label}
              </div>
            </Link>
          ))}
        </div>
      </div>
    </div>
  );
}
// ... rest of the file stays the same ... 