"use client";

import { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Search, AlertTriangle, Flag, CheckCircle2, XCircle } from "lucide-react";

type ReportStatus = "pending" | "reviewing" | "resolved" | "dismissed";
type ReportType = "listing" | "user" | "transfer" | "other";
type ContentType = "listing" | "transfer" | "message" | "profile";

interface Report {
  id: number;
  type: ReportType;
  contentType: ContentType;
  contentId: number;
  contentTitle: string;
  reportedBy: string;
  reportedUser: string;
  reason: string;
  status: ReportStatus;
  createdAt: string;
  updatedAt: string;
}

const reports: Report[] = [
  {
    id: 1,
    type: "listing",
    contentType: "listing",
    contentId: 123,
    contentTitle: "Doliprane 1000mg",
    reportedBy: "Pharmacie Atlas",
    reportedUser: "Pharmacie Centrale",
    reason: "Prix incorrect",
    status: "pending",
    createdAt: "2024-02-20T10:30:00",
    updatedAt: "2024-02-20T10:30:00"
  },
  {
    id: 2,
    type: "transfer",
    contentType: "transfer",
    contentId: 456,
    contentTitle: "Transfert #456",
    reportedBy: "Pharmacie du Nord",
    reportedUser: "Pharmacie Moderne",
    reason: "Non-respect des conditions de transfert",
    status: "reviewing",
    createdAt: "2024-02-19T15:45:00",
    updatedAt: "2024-02-20T09:15:00"
  }
];

const statusColors: Record<ReportStatus, string> = {
  pending: "bg-yellow-50 text-yellow-700 border-yellow-200",
  reviewing: "bg-blue-50 text-blue-700 border-blue-200",
  resolved: "bg-green-50 text-green-700 border-green-200",
  dismissed: "bg-gray-50 text-gray-700 border-gray-200"
};

const statusLabels: Record<ReportStatus, string> = {
  pending: "En attente",
  reviewing: "En cours",
  resolved: "Résolu",
  dismissed: "Rejeté"
};

export function ModerationPanel() {
  const [activeTab, setActiveTab] = useState("reports");

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Modération</h2>
          <p className="text-muted-foreground">
            Gérez les signalements et examinez le contenu
          </p>
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Signalements en attente
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">12</div>
            <p className="text-xs text-muted-foreground">
              4 nouveaux aujourd&apos;hui
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              En cours d&apos;examen
            </CardTitle>
            <Flag className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">3</div>
            <p className="text-xs text-muted-foreground">
              Temps moyen: 2h
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Résolus (7j)
            </CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">28</div>
            <p className="text-xs text-muted-foreground">
              +12% cette semaine
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Rejetés (7j)
            </CardTitle>
            <XCircle className="h-4 w-4 text-gray-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">5</div>
            <p className="text-xs text-muted-foreground">
              -15% cette semaine
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="reports" className="space-y-4">
        <TabsList>
          <TabsTrigger value="reports">Signalements</TabsTrigger>
          <TabsTrigger value="content">Contenu à examiner</TabsTrigger>
          <TabsTrigger value="history">Historique</TabsTrigger>
        </TabsList>

        <TabsContent value="reports">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher un signalement..."
                    className="pl-8 w-[300px]"
                  />
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les types</SelectItem>
                    <SelectItem value="listing">Annonces</SelectItem>
                    <SelectItem value="transfer">Transferts</SelectItem>
                    <SelectItem value="user">Utilisateurs</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="pending">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les statuts</SelectItem>
                    <SelectItem value="pending">En attente</SelectItem>
                    <SelectItem value="reviewing">En cours</SelectItem>
                    <SelectItem value="resolved">Résolus</SelectItem>
                    <SelectItem value="dismissed">Rejetés</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Type</TableHead>
                    <TableHead>Contenu</TableHead>
                    <TableHead>Signalé par</TableHead>
                    <TableHead>Raison</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reports.map((report) => (
                    <TableRow key={report.id}>
                      <TableCell>
                        <Badge variant="outline">
                          {report.type === "listing" && "Annonce"}
                          {report.type === "transfer" && "Transfert"}
                          {report.type === "user" && "Utilisateur"}
                          {report.type === "other" && "Autre"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <div className="font-medium">{report.contentTitle}</div>
                        <div className="text-sm text-muted-foreground">
                          {report.reportedUser}
                        </div>
                      </TableCell>
                      <TableCell>{report.reportedBy}</TableCell>
                      <TableCell>{report.reason}</TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={statusColors[report.status]}
                        >
                          {statusLabels[report.status]}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(report.createdAt).toLocaleString('fr-FR', {
                          dateStyle: 'short',
                          timeStyle: 'short'
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button variant="outline" size="sm">
                          Examiner
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="content">
          {/* Content review section will be implemented */}
        </TabsContent>

        <TabsContent value="history">
          {/* History section will be implemented */}
        </TabsContent>
      </Tabs>
    </div>
  );
} 