"use client";

import { useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { 
  AppShell, 
  NavLink, 
  Stack, 
  Group, 
  Text, 
  Avatar, 
  UnstyledButton,
  Divider,
  Badge,
  Tooltip,
  ActionIcon,
  Burger,
  ScrollArea
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  IconDashboard,
  IconBuilding,
  IconUsers,
  IconShoppingCart,
  IconAlertTriangle,
  IconMessages,
  IconChartBar,
  IconFileText,
  IconSettings,
  IconLogout,
  IconCategory,
  IconCreditCard,
  IconEye,
  IconBell,
  IconShield
} from '@tabler/icons-react';
import { useAuth } from '@/contexts/auth-context';
import { usePermissions } from "@/lib/types/auth";

interface NavItem {
  label: string;
  icon: React.ReactNode;
  href: string;
  badge?: string | number;
  color?: string;
  description?: string;
  permission: { action: string; resource: string };
}

// AdminSidebar now filters links based on user permissions using usePermissions.
// To add a new link, specify the required action/resource for permission check.
// This ensures the sidebar always matches backend access control.

const adminNavItems: NavItem[] = [
  {
    label: 'Dashboard',
    icon: <IconDashboard size={20} />,
    href: '/admin/dashboard',
    description: 'System overview and metrics',
    permission: { action: 'manage', resource: 'listings' },
  },
  {
    label: 'Pharmacies',
    icon: <IconBuilding size={20} />,
    href: '/admin/pharmacies',
    description: 'Manage pharmacy accounts',
    permission: { action: 'manage', resource: 'pharmacy' },
  },
  {
    label: 'Users',
    icon: <IconUsers size={20} />,
    href: '/admin/users',
    description: 'User account management',
    permission: { action: 'manage', resource: 'team' },
  },
  {
    label: 'Transactions',
    icon: <IconCreditCard size={20} />,
    href: '/admin/transactions',
    description: 'System-wide transactions',
    permission: { action: 'manage', resource: 'transactions' },
  },
  {
    label: 'Marketplace',
    icon: <IconShoppingCart size={20} />,
    href: '/admin/marketplace',
    description: 'Marketplace oversight',
    permission: { action: 'manage', resource: 'products' },
  },
  {
    label: 'Urgent Requests',
    icon: <IconAlertTriangle size={20} />,
    href: '/admin/urgent-requests',
    badge: '3',
    color: 'red',
    description: 'Critical requests requiring attention',
    permission: { action: 'manage', resource: 'transactions' },
  },
  {
    label: 'Messages',
    icon: <IconMessages size={20} />,
    href: '/admin/messages',
    badge: '12',
    color: 'blue',
    description: 'System-wide messaging',
    permission: { action: 'manage', resource: 'team' },
  },
  {
    label: 'Analytics',
    icon: <IconChartBar size={20} />,
    href: '/admin/analytics',
    description: 'Advanced analytics and insights',
    permission: { action: 'manage', resource: 'reports' },
  },
  {
    label: 'Reports',
    icon: <IconFileText size={20} />,
    href: '/admin/reports',
    description: 'Generate system reports',
    permission: { action: 'manage', resource: 'reports' },
  },
  {
    label: 'Categories',
    icon: <IconCategory size={20} />,
    href: '/admin/categories',
    description: 'Product category management',
    permission: { action: 'manage', resource: 'products' },
  },
  {
    label: 'Audit Logs',
    icon: <IconEye size={20} />,
    href: '/admin/audit-logs',
    description: 'System activity monitoring',
    permission: { action: 'manage', resource: 'reports' },
  },
  {
    label: 'Settings',
    icon: <IconSettings size={20} />,
    href: '/admin/settings',
    description: 'System configuration',
    permission: { action: 'manage', resource: 'settings' },
  }
];

interface AdminSidebarProps {
  opened: boolean;
  toggle: () => void;
}

export function AdminSidebar({ opened, toggle }: AdminSidebarProps) {
  const pathname = usePathname();
  const { user, signOut } = useAuth();

  // Only show sidebar for super_admin
  if (!user || user.role !== 'super_admin') {
    return null;
  }

  // Map user to the User type expected by usePermissions
  const mappedUser = user
    ? {
        ...user,
        status: 'active',
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    : null;
  const permissions = usePermissions(mappedUser);
  // Only show links the user can access
  const filteredNavItems = adminNavItems.filter(item =>
    permissions.can(item.permission.action as any, item.permission.resource as any)
  );

  const handleLogout = async () => {
    try {
      await signOut();
      window.location.href = '/auth/login';
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <AppShell.Navbar p="lg" w={280}>
      <AppShell.Section>
        <Group justify="space-between" mb="lg">
          <Group gap="sm">
            <Avatar
              size="md"
              color="#00B5FF"
              variant="light"
              style={{ border: '1px solid rgba(0, 181, 255, 0.2)' }}
            >
              <IconShield size={18} />
            </Avatar>
            <div>
              <Text size="sm" fw={600} c="dark">Administration</Text>
              <Text size="xs" c="dimmed">Système de gestion</Text>
            </div>
          </Group>
          <Burger
            opened={opened}
            onClick={toggle}
            hiddenFrom="sm"
            size="sm"
            color="gray"
            style={{
              border: '1px solid rgba(0, 0, 0, 0.08)',
              borderRadius: '6px',
              padding: '8px',
              '&:hover': {
                backgroundColor: 'rgba(0, 181, 255, 0.05)',
              },
            }}
          />
        </Group>
        <Divider mb="lg" color="rgba(0, 0, 0, 0.08)" />
      </AppShell.Section>

      <AppShell.Section grow component={ScrollArea}>
        <Stack gap="xs">
          {filteredNavItems.map((item) => {
            const isActive = pathname === item.href || pathname.startsWith(item.href + '/');

            return (
              <Tooltip
                key={item.href}
                label={item.description}
                position="right"
                withArrow
                disabled={opened}
              >
                <NavLink
                  component={Link}
                  href={item.href}
                  label={item.label}
                  leftSection={item.icon}
                  rightSection={
                    item.badge ? (
                      <Badge
                        size="xs"
                        color={item.color || '#00B5FF'}
                        variant="filled"
                        style={{
                          fontSize: '0.7rem',
                          fontWeight: 600,
                        }}
                      >
                        {item.badge}
                      </Badge>
                    ) : null
                  }
                  active={isActive}
                  variant="subtle"
                  style={{
                    borderRadius: '0px',
                    margin: '2px 0',
                    padding: '12px 16px',
                    border: '1px solid transparent',
                    transition: 'all 0.15s ease',
                    fontWeight: isActive ? 600 : 500,
                    color: isActive ? '#00B5FF' : '#374151',
                    backgroundColor: isActive ? 'rgba(0, 181, 255, 0.08)' : 'transparent',
                    borderLeft: isActive ? '3px solid #00B5FF' : '3px solid transparent',
                  }}
                />
              </Tooltip>
            );
          })}
        </Stack>
      </AppShell.Section>

      <AppShell.Section>
        <Divider mb="md" />
        <UnstyledButton
          onClick={handleLogout}
          w="100%"
          p="sm"
          style={{
            borderRadius: '12px',
            transition: 'all 0.2s ease',
          }}
          styles={{
            root: {
              '&:hover': {
                backgroundColor: 'rgba(255, 0, 0, 0.1)',
              },
            },
          }}
        >
          <Group gap="sm">
            <IconLogout size={20} color="red" />
            <Text size="sm" c="red" fw={500}>
              Sign Out
            </Text>
          </Group>
        </UnstyledButton>
      </AppShell.Section>
    </AppShell.Navbar>
  );
}
