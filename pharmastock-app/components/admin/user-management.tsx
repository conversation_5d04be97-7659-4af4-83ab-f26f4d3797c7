"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { Search, MoreHorizontal, Shield } from "lucide-react";

type UserRole = "admin" | "pharmacist" | "staff";

type User = {
  id: number;
  name: string;
  email: string;
  role: UserRole;
  pharmacy: string;
  status: "active" | "inactive";
  lastLogin: string;
};

const users: User[] = [
  {
    id: 1,
    name: "Dr. <PERSON>",
    email: "<EMAIL>",
    role: "pharmacist",
    pharmacy: "Pharmacie Centrale",
    status: "active",
    lastLogin: "2024-02-20T10:30:00"
  },
  {
    id: 2,
    name: "Ad<PERSON> Système",
    email: "<EMAIL>",
    role: "admin",
    pharmacy: "-",
    status: "active",
    lastLogin: "2024-02-20T09:15:00"
  },
  {
    id: 3,
    name: "Sara Tazi",
    email: "<EMAIL>",
    role: "staff",
    pharmacy: "Pharmacie Atlas",
    status: "active",
    lastLogin: "2024-02-19T16:45:00"
  }
];

const roleLabels: Record<UserRole, string> = {
  admin: "Administrateur",
  pharmacist: "Pharmacien",
  staff: "Personnel"
};

const roleColors: Record<UserRole, string> = {
  admin: "bg-purple-50 text-purple-700 border-purple-200",
  pharmacist: "bg-blue-50 text-blue-700 border-blue-200",
  staff: "bg-gray-50 text-gray-700 border-gray-200"
};

export function UserManagement() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher un utilisateur..."
              className="pl-8 w-[300px]"
            />
          </div>
          <Button variant="outline">Filtres</Button>
        </div>
        <Button>Ajouter un utilisateur</Button>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Utilisateur</TableHead>
              <TableHead>Rôle</TableHead>
              <TableHead>Pharmacie</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Dernière connexion</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id}>
                <TableCell>
                  <div>
                    <div className="font-medium">{user.name}</div>
                    <div className="text-sm text-muted-foreground">
                      {user.email}
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={roleColors[user.role]}
                  >
                    <Shield className="h-3 w-3 mr-1" />
                    {roleLabels[user.role]}
                  </Badge>
                </TableCell>
                <TableCell>{user.pharmacy}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={
                      user.status === "active"
                        ? "bg-green-50 text-green-700 border-green-200"
                        : "bg-red-50 text-red-700 border-red-200"
                    }
                  >
                    {user.status === "active" ? "Actif" : "Inactif"}
                  </Badge>
                </TableCell>
                <TableCell>
                  {new Date(user.lastLogin).toLocaleString('fr-FR', {
                    dateStyle: 'short',
                    timeStyle: 'short'
                  })}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Voir le profil</DropdownMenuItem>
                      <DropdownMenuItem>Modifier</DropdownMenuItem>
                      <DropdownMenuItem>Historique</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {user.status === "active" ? (
                        <DropdownMenuItem className="text-red-600">
                          Désactiver
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem className="text-green-600">
                          Activer
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 