"use client";

import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";
import {
  LayoutDashboard,
  Users,
  Store,
  AlertCircle,
  Settings,
  BarChart3,
  Tags,
  Menu,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useState, useEffect } from "react";
import { useAuth } from '@/contexts/auth-context';

interface RouteGroup {
  label: string;
  routes: {
    label: string;
    icon: any;
    href: string;
    color: string;
  }[];
}

const routeGroups: RouteGroup[] = [
  {
    label: "Overview",
    routes: [
      {
        label: "Dashboard",
        icon: LayoutDashboard,
        href: "/admin",
        color: "text-sky-500",
      },
      {
        label: "Analytics",
        icon: BarChart3,
        href: "/admin/analytics",
        color: "text-yellow-500",
      },
    ],
  },
  {
    label: "Management",
    routes: [
      {
        label: "Pharmacies",
        icon: Store,
        href: "/admin/pharmacies",
        color: "text-violet-500",
      },
      {
        label: "Users",
        icon: Users,
        color: "text-pink-700",
        href: "/admin/users",
      },
      {
        label: "Categories",
        icon: Tags,
        color: "text-green-700",
        href: "/admin/categories",
      },
    ],
  },
  {
    label: "Monitoring",
    routes: [
      {
        label: "Urgent Requests",
        icon: AlertCircle,
        color: "text-orange-700",
        href: "/admin/urgent-requests",
      },
      {
        label: "Settings",
        icon: Settings,
        href: "/admin/settings",
        color: "text-gray-500",
      },
    ],
  },
];

export function AdminSidebar() {
  const pathname = usePathname();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMounted, setIsMounted] = useState(false);
  const { user } = useAuth();

  useEffect(() => {
    setIsMounted(true);
  }, []);

  if (!isMounted || !user || user.role !== 'super_admin') {
    return null;
  }

  return (
    <>
      {/* Mobile menu button */}
      <Button
        variant="ghost"
        size="icon"
        className="fixed top-4 left-4 z-50 md:hidden"
        onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
      >
        <Menu className="h-6 w-6" />
      </Button>

      {/* Backdrop for mobile */}
      {isMobileMenuOpen && (
        <div
          className="fixed inset-0 z-40 bg-background/80 backdrop-blur-sm md:hidden"
          onClick={() => setIsMobileMenuOpen(false)}
        />
      )}

      {/* Sidebar */}
      <div
        className={cn(
          "fixed inset-y-0 left-0 z-50 md:relative",
          "bg-card border-r",
          "transform transition-all duration-300 ease-in-out md:transform-none",
          isMobileMenuOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0",
          isCollapsed ? "w-[70px]" : "w-64"
        )}
      >
        <div className="flex flex-col h-full">
          <div className="p-4 flex items-center justify-between border-b">
            <Link
              href="/admin"
              className={cn(
                "flex items-center gap-2 transition-all duration-300",
                isCollapsed && "justify-center"
              )}
            >
              <span className="font-bold text-xl">
                {isCollapsed ? "A" : "Admin"}
              </span>
            </Link>
            <Button
              variant="ghost"
              size="icon"
              className="hidden md:flex"
              onClick={() => setIsCollapsed(!isCollapsed)}
            >
              {isCollapsed ? (
                <ChevronRight className="h-4 w-4" />
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>
          </div>

          <ScrollArea className="flex-1 px-3 py-4">
            <div className="space-y-6">
              {routeGroups.map((group, index) => (
                <div key={index} className="space-y-2">
                  {!isCollapsed && (
                    <h2 className="text-xs font-semibold text-muted-foreground px-2">
                      {group.label}
                    </h2>
                  )}
                  <nav className="space-y-1">
                    {group.routes.map((route) => (
                      <Link
                        key={route.href}
                        href={route.href}
                        onClick={() => setIsMobileMenuOpen(false)}
                        className={cn(
                          "flex items-center gap-3 px-2 py-2 text-sm font-medium rounded-md",
                          "transition-colors duration-200",
                          "hover:bg-accent hover:text-accent-foreground",
                          pathname === route.href
                            ? "bg-accent text-accent-foreground"
                            : "text-muted-foreground",
                          isCollapsed && "justify-center"
                        )}
                      >
                        <route.icon className={cn("h-5 w-5", route.color)} />
                        {!isCollapsed && <span>{route.label}</span>}
                      </Link>
                    ))}
                  </nav>
                </div>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
    </>
  );
} 