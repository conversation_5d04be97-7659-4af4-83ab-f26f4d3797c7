"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  AlertTriangle,
  Bell,
  CheckCircle2,
  Clock,
  Search,
  Zap
} from "lucide-react";
import { useAuth } from "@/contexts/auth-context";

type AlertPriority = "high" | "medium" | "low";
type AlertStatus = "active" | "acknowledged" | "resolved";
type AlertType = "system" | "security" | "transfer" | "urgent" | "stock";

interface Alert {
  id: number;
  title: string;
  description: string;
  type: AlertType;
  priority: AlertPriority;
  status: AlertStatus;
  createdAt: string;
  updatedAt: string;
  affectedUsers?: string[];
  affectedPharmacies?: string[];
}

const priorityColors: Record<AlertPriority, string> = {
  high: "bg-red-50 text-red-700 border-red-200",
  medium: "bg-yellow-50 text-yellow-700 border-yellow-200",
  low: "bg-blue-50 text-blue-700 border-blue-200"
};

const statusColors: Record<AlertStatus, string> = {
  active: "bg-red-50 text-red-700 border-red-200",
  acknowledged: "bg-yellow-50 text-yellow-700 border-yellow-200",
  resolved: "bg-green-50 text-green-700 border-green-200"
};

const typeIcons: Record<AlertType, JSX.Element> = {
  system: <Zap className="h-4 w-4" />,
  security: <AlertTriangle className="h-4 w-4" />,
  transfer: <Clock className="h-4 w-4" />,
  urgent: <Bell className="h-4 w-4" />,
  stock: <CheckCircle2 className="h-4 w-4" />
};

export function AlertSystem() {
  const [activeTab, setActiveTab] = useState("active");
  const { isConnected, sendMessage, subscribeToEvent, unsubscribeFromEvent } = useWebSocket();
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [urgentRequests, setUrgentRequests] = useState<any[]>([]);
  const [stats, setStats] = useState({
    activeAlerts: 0,
    pendingRequests: 0,
    urgentRequests: 0,
    resolvedToday: 0
  });

  // Initialize real-time hooks
  useRealTimeAlerts();
  useRealTimeUrgentRequests();
  useRealTimeStatusUpdates();

  useEffect(() => {
    // Subscribe to real-time updates
    const handleNewAlert = (alert: Alert) => {
      setAlerts(prev => [alert, ...prev]);
      setStats(prev => ({
        ...prev,
        activeAlerts: prev.activeAlerts + 1
      }));
    };

    const handleAlertUpdate = (update: { id: number; status: AlertStatus }) => {
      setAlerts(prev => prev.map(alert =>
        alert.id === update.id ? { ...alert, status: update.status } : alert
      ));

      if (update.status === 'resolved') {
        setStats(prev => ({
          ...prev,
          activeAlerts: prev.activeAlerts - 1,
          resolvedToday: prev.resolvedToday + 1
        }));
      }
    };

    const handleUrgentRequest = (request: any) => {
      setUrgentRequests(prev => [request, ...prev]);
      setStats(prev => ({
        ...prev,
        urgentRequests: prev.urgentRequests + 1
      }));
    };

    const handleStatsUpdate = (newStats: any) => {
      setStats(newStats);
    };

    subscribeToEvent('new_alert', handleNewAlert);
    subscribeToEvent('alert_update', handleAlertUpdate);
    subscribeToEvent('new_urgent_request', handleUrgentRequest);
    subscribeToEvent('stats_update', handleStatsUpdate);

    // Initial data fetch
    sendMessage('get_initial_data', {});

    return () => {
      unsubscribeFromEvent('new_alert', handleNewAlert);
      unsubscribeFromEvent('alert_update', handleAlertUpdate);
      unsubscribeFromEvent('new_urgent_request', handleUrgentRequest);
      unsubscribeFromEvent('stats_update', handleStatsUpdate);
    };
  }, [sendMessage, subscribeToEvent, unsubscribeFromEvent]);

  const handleAlertAction = (alertId: number, action: string) => {
    sendMessage('alert_action', { alertId, action });
  };

  const handleUrgentRequestAction = (requestId: number, action: string) => {
    sendMessage('urgent_request_action', { requestId, action });
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold">Système d&apos;Alertes</h2>
          <p className="text-muted-foreground">
            Gérez et surveillez les alertes système
          </p>
        </div>
        <div className="flex items-center gap-2 text-sm">
          <div className={`w-2 h-2 rounded-full ${isConnected ? 'bg-green-500' : 'bg-red-500'}`} />
          {isConnected ? 'Connecté' : 'Déconnecté'}
        </div>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Alertes Actives
            </CardTitle>
            <AlertTriangle className="h-4 w-4 text-red-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.activeAlerts}</div>
            <p className="text-xs text-muted-foreground">
              {alerts.filter(a => a.priority === 'high').length} haute priorité
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              En attente
            </CardTitle>
            <Clock className="h-4 w-4 text-yellow-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.pendingRequests}</div>
            <p className="text-xs text-muted-foreground">
              Temps moyen: 1h
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Demandes Urgentes
            </CardTitle>
            <Bell className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.urgentRequests}</div>
            <p className="text-xs text-muted-foreground">
              {urgentRequests.filter(r => r.status === 'processing').length} en cours de traitement
            </p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Résolues (24h)
            </CardTitle>
            <CheckCircle2 className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.resolvedToday}</div>
            <p className="text-xs text-muted-foreground">
              Temps moyen: 45min
            </p>
          </CardContent>
        </Card>
      </div>

      <Tabs defaultValue="active" className="space-y-4">
        <TabsList>
          <TabsTrigger value="active">Alertes Actives</TabsTrigger>
          <TabsTrigger value="urgent">Demandes Urgentes</TabsTrigger>
          <TabsTrigger value="history">Historique</TabsTrigger>
          <TabsTrigger value="settings">Paramètres</TabsTrigger>
        </TabsList>

        <TabsContent value="active">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher une alerte..."
                    className="pl-8 w-[300px]"
                  />
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les types</SelectItem>
                    <SelectItem value="system">Système</SelectItem>
                    <SelectItem value="security">Sécurité</SelectItem>
                    <SelectItem value="transfer">Transferts</SelectItem>
                    <SelectItem value="urgent">Urgences</SelectItem>
                    <SelectItem value="stock">Stock</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Priorité" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Toutes les priorités</SelectItem>
                    <SelectItem value="high">Haute</SelectItem>
                    <SelectItem value="medium">Moyenne</SelectItem>
                    <SelectItem value="low">Basse</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Alerte</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Priorité</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {alerts.map((alert) => (
                    <TableRow key={alert.id}>
                      <TableCell>
                        <div>
                          <div className="font-medium">{alert.title}</div>
                          <div className="text-sm text-muted-foreground">
                            {alert.description}
                          </div>
                        </div>
                      </TableCell>
                      <TableCell>
                        <Badge variant="outline" className="flex items-center gap-1">
                          {typeIcons[alert.type]}
                          {alert.type}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={priorityColors[alert.priority]}
                        >
                          {alert.priority === "high" && "Haute"}
                          {alert.priority === "medium" && "Moyenne"}
                          {alert.priority === "low" && "Basse"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        <Badge
                          variant="outline"
                          className={statusColors[alert.status]}
                        >
                          {alert.status === "active" && "Active"}
                          {alert.status === "acknowledged" && "Prise en compte"}
                          {alert.status === "resolved" && "Résolue"}
                        </Badge>
                      </TableCell>
                      <TableCell>
                        {new Date(alert.createdAt).toLocaleString('fr-FR', {
                          dateStyle: 'short',
                          timeStyle: 'short'
                        })}
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleAlertAction(alert.id, 'manage')}
                        >
                          Gérer
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="urgent">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher une demande..."
                    className="pl-8 w-[300px]"
                  />
                </div>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Statut" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les statuts</SelectItem>
                    <SelectItem value="pending">En attente</SelectItem>
                    <SelectItem value="processing">En cours</SelectItem>
                    <SelectItem value="resolved">Résolue</SelectItem>
                  </SelectContent>
                </Select>
                <Select defaultValue="all">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Rayon" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">Tous les rayons</SelectItem>
                    <SelectItem value="5">5 km</SelectItem>
                    <SelectItem value="10">10 km</SelectItem>
                    <SelectItem value="20">20 km</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Demande</TableHead>
                    <TableHead>Pharmacie</TableHead>
                    <TableHead>Rayon</TableHead>
                    <TableHead>Statut</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* Example urgent request data */}
                  <TableRow>
                    <TableCell>
                      <div>
                        <div className="font-medium">Insuline Lantus</div>
                        <div className="text-sm text-muted-foreground">
                          Quantité: 2 boîtes
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>Pharmacie Centrale</TableCell>
                    <TableCell>10 km</TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-yellow-50 text-yellow-700 border-yellow-200">
                        En cours
                      </Badge>
                    </TableCell>
                    <TableCell>
                      {new Date().toLocaleString('fr-FR', {
                        dateStyle: 'short',
                        timeStyle: 'short'
                      })}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleUrgentRequestAction(urgentRequests[0].id, 'view')}
                      >
                        Voir détails
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="history">
          <div className="space-y-4">
            <div className="flex justify-between items-center">
              <div className="flex items-center gap-4">
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Rechercher dans l'historique..."
                    className="pl-8 w-[300px]"
                  />
                </div>
                <Select defaultValue="7">
                  <SelectTrigger className="w-[180px]">
                    <SelectValue placeholder="Période" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="1">24 heures</SelectItem>
                    <SelectItem value="7">7 jours</SelectItem>
                    <SelectItem value="30">30 jours</SelectItem>
                    <SelectItem value="90">90 jours</SelectItem>
                  </SelectContent>
                </Select>
                <Button variant="outline">
                  Exporter
                </Button>
              </div>
            </div>

            <div className="border rounded-lg">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Alerte</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Résolution</TableHead>
                    <TableHead>Durée</TableHead>
                    <TableHead>Date</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {/* Example history data */}
                  <TableRow>
                    <TableCell>
                      <div>
                        <div className="font-medium">Stock critique résolu</div>
                        <div className="text-sm text-muted-foreground">
                          Doliprane 1000mg réapprovisionné
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="flex items-center gap-1">
                        <CheckCircle2 className="h-4 w-4" />
                        stock
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
                        Résolu
                      </Badge>
                    </TableCell>
                    <TableCell>45 min</TableCell>
                    <TableCell>
                      {new Date().toLocaleString('fr-FR', {
                        dateStyle: 'short',
                        timeStyle: 'short'
                      })}
                    </TableCell>
                    <TableCell className="text-right">
                      <Button variant="outline" size="sm">
                        Détails
                      </Button>
                    </TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </div>
          </div>
        </TabsContent>

        <TabsContent value="settings">
          <div className="space-y-6">
            <div className="grid gap-6">
              <div className="space-y-4">
                <h3 className="text-lg font-medium">Notifications</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Alertes urgentes</div>
                      <div className="text-sm text-muted-foreground">
                        Notifications pour les demandes urgentes
                      </div>
                    </div>
                    <Button variant="outline">Configurer</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Alertes de stock</div>
                      <div className="text-sm text-muted-foreground">
                        Seuils et notifications de stock
                      </div>
                    </div>
                    <Button variant="outline">Configurer</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Rapports quotidiens</div>
                      <div className="text-sm text-muted-foreground">
                        Résumé quotidien des alertes
                      </div>
                    </div>
                    <Button variant="outline">Configurer</Button>
                  </div>
                </div>
              </div>

              <div className="space-y-4">
                <h3 className="text-lg font-medium">Automatisation</h3>
                <div className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Règles d&apos;alerte</div>
                      <div className="text-sm text-muted-foreground">
                        Configurer les conditions d&apos;alerte
                      </div>
                    </div>
                    <Button variant="outline">Gérer</Button>
                  </div>
                  <div className="flex items-center justify-between">
                    <div>
                      <div className="font-medium">Actions automatiques</div>
                      <div className="text-sm text-muted-foreground">
                        Réponses automatisées aux alertes
                      </div>
                    </div>
                    <Button variant="outline">Gérer</Button>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}