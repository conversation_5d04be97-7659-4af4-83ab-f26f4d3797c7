"use client";

import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { MoreHorizontal, Search } from "lucide-react";

type Pharmacy = {
  id: number;
  name: string;
  owner: string;
  location: string;
  status: "active" | "pending" | "suspended";
  registrationDate: string;
  lastActive: string;
};

const pharmacies: Pharmacy[] = [
  {
    id: 1,
    name: "Pharmacie Centrale",
    owner: "Dr. <PERSON>",
    location: "Casablanca - Maarif",
    status: "active",
    registrationDate: "2024-01-15",
    lastActive: "2024-02-20"
  },
  {
    id: 2,
    name: "Pharmacie Atlas",
    owner: "<PERSON><PERSON> <PERSON><PERSON>",
    location: "Casablanca - Gauthier",
    status: "active",
    registrationDate: "2024-01-20",
    lastActive: "2024-02-19"
  },
  {
    id: 3,
    name: "Pharmacie du Nord",
    owner: "Dr. Karim Tazi",
    location: "Casablanca - Ain Diab",
    status: "pending",
    registrationDate: "2024-02-18",
    lastActive: "2024-02-18"
  }
];

export function PharmacyManagement() {
  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Rechercher une pharmacie..."
              className="pl-8 w-[300px]"
            />
          </div>
          <Button variant="outline">Filtres</Button>
        </div>
        <Button>Ajouter une pharmacie</Button>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Pharmacie</TableHead>
              <TableHead>Propriétaire</TableHead>
              <TableHead>Localisation</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead>Date d&apos;inscription</TableHead>
              <TableHead>Dernière activité</TableHead>
              <TableHead className="text-right">Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {pharmacies.map((pharmacy) => (
              <TableRow key={pharmacy.id}>
                <TableCell className="font-medium">{pharmacy.name}</TableCell>
                <TableCell>{pharmacy.owner}</TableCell>
                <TableCell>{pharmacy.location}</TableCell>
                <TableCell>
                  <Badge
                    variant="outline"
                    className={
                      pharmacy.status === "active"
                        ? "bg-green-50 text-green-700 border-green-200"
                        : pharmacy.status === "pending"
                          ? "bg-yellow-50 text-yellow-700 border-yellow-200"
                          : "bg-red-50 text-red-700 border-red-200"
                    }
                  >
                    {pharmacy.status === "active" && "Active"}
                    {pharmacy.status === "pending" && "En attente"}
                    {pharmacy.status === "suspended" && "Suspendue"}
                  </Badge>
                </TableCell>
                <TableCell>{new Date(pharmacy.registrationDate).toLocaleDateString('fr-FR')}</TableCell>
                <TableCell>{new Date(pharmacy.lastActive).toLocaleDateString('fr-FR')}</TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem>Voir les détails</DropdownMenuItem>
                      <DropdownMenuItem>Modifier</DropdownMenuItem>
                      <DropdownMenuItem>Historique</DropdownMenuItem>
                      <DropdownMenuSeparator />
                      {pharmacy.status === "active" ? (
                        <DropdownMenuItem className="text-red-600">
                          Suspendre
                        </DropdownMenuItem>
                      ) : pharmacy.status === "pending" ? (
                        <DropdownMenuItem className="text-green-600">
                          Approuver
                        </DropdownMenuItem>
                      ) : (
                        <DropdownMenuItem className="text-green-600">
                          Réactiver
                        </DropdownMenuItem>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 