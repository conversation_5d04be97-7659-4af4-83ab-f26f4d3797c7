'use client';

import { useState, useEffect } from 'react';
import { Card } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { ScrollArea } from '@/components/ui/scroll-area';
import { getLogs } from '@/lib/logger';

interface Log {
  level: string;
  message: string;
  timestamp: string;
}

export function DebugPanel() {
  const [isOpen, setIsOpen] = useState(false);
  const [filter, setFilter] = useState('');
  const [logs, setLogs] = useState<Log[]>([]);
  const [systemInfo, setSystemInfo] = useState<Record<string, any>>({});

  useEffect(() => {
    // Update logs every second
    const interval = setInterval(() => {
      setLogs(getLogs(filter));
    }, 1000);

    // Gather system information
    const gatherSystemInfo = async () => {
      try {
        const info = {
          userAgent: window.navigator.userAgent,
          platform: window.navigator.platform,
          language: window.navigator.language,
          cookiesEnabled: window.navigator.cookieEnabled,
          screenResolution: `${window.screen.width}x${window.screen.height}`,
          viewport: `${window.innerWidth}x${window.innerHeight}`,
          timestamp: new Date().toISOString(),
        };
        setSystemInfo(info);
      } catch (error) {
        console.error('Error gathering system info:', error);
      }
    };

    gatherSystemInfo();

    return () => {
      clearInterval(interval);
    };
  }, [filter]);

  if (!isOpen) {
    return (
      <button
        onClick={() => setIsOpen(true)}
        className="fixed bottom-4 right-4 bg-primary text-primary-foreground p-2 rounded-full shadow-lg hover:bg-primary/90 transition-colors"
      >
        Debug
      </button>
    );
  }

  return (
    <Card className="fixed bottom-4 right-4 w-96 h-96 p-4 shadow-xl bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="flex flex-col h-full">
        <div className="flex justify-between items-center mb-4">
          <h3 className="font-semibold">Debug Panel</h3>
          <button
            onClick={() => setIsOpen(false)}
            className="text-muted-foreground hover:text-foreground"
          >
            Close
          </button>
        </div>

        <Input
          type="text"
          placeholder="Filter logs..."
          value={filter}
          onChange={(e) => setFilter(e.target.value)}
          className="mb-2"
        />

        <ScrollArea className="flex-grow">
          <div className="space-y-2">
            {logs.map((log, index) => (
              <div
                key={index}
                className={`text-xs p-2 rounded ${log.level === 'error'
                  ? 'bg-destructive/10 text-destructive'
                  : log.level === 'warn'
                    ? 'bg-warning/10 text-warning'
                    : 'bg-muted'
                  }`}
              >
                <div className="text-[10px] text-muted-foreground">
                  {new Date(log.timestamp).toLocaleTimeString()}
                </div>
                <div className="font-mono">{log.message}</div>
              </div>
            ))}
          </div>
        </ScrollArea>

        <div className="mt-4">
          <details className="text-xs">
            <summary className="cursor-pointer text-muted-foreground hover:text-foreground">
              System Info
            </summary>
            <div className="mt-2 space-y-1">
              {Object.entries(systemInfo).map(([key, value]) => (
                <div key={key} className="grid grid-cols-2 gap-2">
                  <span className="text-muted-foreground">{key}:</span>
                  <span>{String(value)}</span>
                </div>
              ))}
            </div>
          </details>
        </div>
      </div>
    </Card>
  );
} 