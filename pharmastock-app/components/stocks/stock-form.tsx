"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { useEffect } from "react";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createFormSubmitHandler } from "@/lib/form-utils";
import { logger } from "@/lib/logger";
import { DatePicker } from '@mantine/dates';

const formSchema = z.object({
  name: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
  category: z.string(),
  quantity: z.coerce.number().min(0, "La quantité doit être positive"),
  expiry_date: z.string(),
  unit_price: z.coerce.number().min(0, "Le prix doit être positif"),
  batch_number: z.string().optional(),
});

export type Stock = z.infer<typeof formSchema>;

interface StockFormProps {
  initialValues?: Partial<Stock>;
  onSubmit: (values: Stock) => void;
  loading?: boolean;
}

export function StockForm({
  initialValues,
  onSubmit,
  loading,
}: StockFormProps) {
  const form = useForm<Stock>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialValues?.name || "",
      category: initialValues?.category || "",
      quantity: initialValues?.quantity ?? 0,
      expiry_date: initialValues?.expiry_date || "",
      unit_price: initialValues?.unit_price ?? 0,
      batch_number: initialValues?.batch_number || "",
    },
  });

  // Update form values when initialValues change (for edit)
  useEffect(() => {
    if (initialValues) {
      form.reset({
        name: initialValues.name || "",
        category: initialValues.category || "",
        quantity: initialValues.quantity ?? 0,
        expiry_date: initialValues.expiry_date || "",
        unit_price: initialValues.unit_price ?? 0,
        batch_number: initialValues.batch_number || "",
      });
    }
  }, [initialValues]);

  return (
    <div className="rounded-2xl overflow-hidden shadow-lg">
      {/* Gradient header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4">
        <h2 className="text-xl font-bold text-white">
          {initialValues?.name ? "Modifier le Produit" : "Ajouter un Produit"}
        </h2>
        <p className="text-sm text-blue-100">
          {initialValues?.name
            ? "Mettez à jour les informations du produit."
            : "Ajoutez un nouveau produit à votre inventaire."}
        </p>
      </div>
      <div className="p-6 bg-white">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom du produit</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: Doliprane 1000mg" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catégorie</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionner une catégorie" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="Antalgiques">Antalgiques</SelectItem>
                      <SelectItem value="Antibiotiques">Antibiotiques</SelectItem>
                      <SelectItem value="Anti-inflammatoires">
                        Anti-inflammatoires
                      </SelectItem>
                      <SelectItem value="Respiratoire">Respiratoire</SelectItem>
                      <SelectItem value="Cardiovasculaire">
                        Cardiovasculaire
                      </SelectItem>
                      <SelectItem value="Digestif">Digestif</SelectItem>
                      <SelectItem value="Dermatologie">Dermatologie</SelectItem>
                      <SelectItem value="Neurologie">Neurologie</SelectItem>
                      <SelectItem value="Vitamines">
                        Vitamines et Suppléments
                      </SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantité</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min="0"
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="unit_price"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Prix unitaire (DH)</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        step="0.01"
                        min="0"
                        onChange={(e) => field.onChange(e.target.valueAsNumber)}
                        value={field.value}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="expiry_date"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date de péremption</FormLabel>
                  <FormControl>
                    <DatePicker
                      value={field.value ? new Date(field.value) : null}
                      onChange={(date) => field.onChange(date ? date.toISOString() : null)}
                      placeholder="Sélectionner une date"
                      getYearControlProps={(date) => ({})}
                      getMonthControlProps={(date) => ({})}
                      getDayProps={(date) => ({})}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="batch_number"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Numéro de lot (optionnel)</FormLabel>
                  <FormControl>
                    <Input placeholder="Ex: **********" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <Button
              type="submit"
              className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-3 rounded-xl shadow-md transition"
              disabled={loading}
            >
              {loading
                ? initialValues
                  ? "Enregistrement..."
                  : "Ajout..."
                : initialValues
                ? "Enregistrer"
                : "Ajouter"}
            </Button>
          </form>
        </Form>
      </div>
    </div>
  );
}
