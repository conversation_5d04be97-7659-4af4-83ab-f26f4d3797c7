"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { Product } from "@/lib/types/marketplace";
import { ReserveProductModal } from "./reserve-product-modal";
import { useState } from "react";
import { DatePicker } from '@mantine/dates';
import { Select as MantineSelect } from '@mantine/core';

interface ProductDetailModalProps {
  product: Product;
  isOpen: boolean;
  onClose: () => void;
}

export function ProductDetailModal({ product, isOpen, onClose }: ProductDetailModalProps) {
  const [showReserveModal, setShowReserveModal] = useState(false);

  const handleReserveClick = () => {
    setShowReserveModal(true);
  };

  // Early return if no product data
  if (!product) {
    return null;
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl p-0 overflow-hidden">
        {/* Gradient header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4 rounded-t-2xl">
          <DialogHeader className="p-0">
            <DialogTitle className="text-xl font-bold text-white">
              {product?.name || 'Product Details'}
            </DialogTitle>
          </DialogHeader>
        </div>
        <div className="p-6 bg-white rounded-b-2xl">
          <div className="grid gap-4 py-2">
            <div className="grid grid-cols-2 gap-4">
              <div>
                <h3 className="font-semibold">Détails</h3>
                <p>Fabricant: {product?.manufacturer || 'N/A'}</p>
                <p>Catégorie: {product?.category || 'N/A'}</p>
                <p>Date d'expiration: {product?.expiry_date ? new Date(product.expiry_date).toLocaleDateString() : 'N/A'}</p>
                <p>Quantité disponible: {product?.quantity || 0}</p>
              </div>
              <div>
                <h3 className="font-semibold">Prix</h3>
                <p>Prix original: {product?.original_price || 0} DH</p>
                <p>Prix unitaire: {product?.unit_price || 0} DH</p>
              </div>
            </div>
            {product?.description && (
              <div>
                <h3 className="font-semibold">Description</h3>
                <p>{product.description}</p>
              </div>
            )}
          </div>
          <div className="flex justify-end gap-4 mt-6">
            <Button variant="outline" onClick={onClose} className="font-semibold">
              Fermer
            </Button>
            <Button
              onClick={handleReserveClick}
              className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-2 px-6 rounded-xl shadow-md transition"
            >
              Réserver
            </Button>
          </div>
        </div>
        {showReserveModal && product && (
          <ReserveProductModal
            product={product}
            isOpen={showReserveModal}
            onClose={() => setShowReserveModal(false)}
          />
        )}
      </DialogContent>
    </Dialog>
  );
}