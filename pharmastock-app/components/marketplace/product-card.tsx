"use client";

import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { MapPin, ThermometerSnowflake, Clock, Building2, ArrowLeftRight } from "lucide-react";

interface ProductCardProps {
  product: {
    name: string;
    nameAr?: string;
    pharmacy: string;
    location: string;
    quantity: number;
    price: number;
    originalPrice: number;
    expiryDate: string;
    distance: number;
    transferRestrictions?: {
      requiresAuthorization: boolean;
      temperatureControl: boolean;
      specialHandling: string[];
      validityPeriod: number;
    };
    stockManagement?: {
      minimumTransferQuantity: number;
      recommendedTransferQuantity: number;
      storageConditions: string[];
    };
  };
  onClick: () => void;
}

export function ProductCard({ product, onClick }: ProductCardProps) {
  return (
    <Card className="p-4 hover:shadow-md transition-shadow cursor-pointer" onClick={onClick}>
      <div className="flex justify-between items-start">
        <div>
          <h3 className="font-medium text-[#24292f]">{product.name}</h3>
          {product.nameAr && (
            <p className="text-sm text-gray-600 font-arabic">{product.nameAr}</p>
          )}
          <p className="text-sm text-gray-600">{product.pharmacy}</p>
          <div className="flex items-center gap-1 text-sm text-gray-600 mt-1">
            <MapPin className="h-3.5 w-3.5" />
            <span>{product.location} • {product.distance} km</span>
          </div>
        </div>
        <Badge variant="secondary" className="shrink-0">
          -{Math.round(((product.originalPrice - product.price) / product.originalPrice) * 100)}%
        </Badge>
      </div>

      <div className="mt-4 flex items-end justify-between">
        <div>
          <p className="text-lg font-medium text-[#24292f]">{product.price.toFixed(2)} DH</p>
          <p className="text-sm text-gray-500 line-through">{product.originalPrice.toFixed(2)} DH</p>
        </div>
        <div className="text-right">
          <p className="text-sm font-medium text-[#1a7f37]">
            Quantité: {product.quantity}
          </p>
          <p className="text-xs text-[#cf222e]">
            Expire le {new Date(product.expiryDate).toLocaleDateString('fr-FR')}
          </p>
        </div>
      </div>

      {/* Transfer Information */}
      {product.transferRestrictions && (
        <div className="mt-3 flex flex-wrap gap-2">
          {product.transferRestrictions.temperatureControl && (
            <Badge variant="outline" className="flex items-center gap-1">
              <ThermometerSnowflake className="h-3.5 w-3.5" />
              Température contrôlée
            </Badge>
          )}

          {product.transferRestrictions.requiresAuthorization && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Building2 className="h-3.5 w-3.5" />
              Autorisation DMP
            </Badge>
          )}

          {product.transferRestrictions.validityPeriod && (
            <Badge variant="outline" className="flex items-center gap-1">
              <Clock className="h-3.5 w-3.5" />
              Validité {product.transferRestrictions.validityPeriod}h
            </Badge>
          )}
        </div>
      )}

      {/* Stock Management */}
      {product.stockManagement && (
        <div className="mt-2">
          <Badge variant="outline" className="flex items-center gap-1">
            <ArrowLeftRight className="h-3.5 w-3.5" />
            Min. {product.stockManagement.minimumTransferQuantity} unités
          </Badge>
        </div>
      )}

      <Button className="w-full mt-4" variant="outline">
        Voir les détails
      </Button>
    </Card>
  );
}