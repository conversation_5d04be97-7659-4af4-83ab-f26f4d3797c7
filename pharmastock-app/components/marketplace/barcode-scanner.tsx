"use client";

import { useEffect, useRef, useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Camera, SwitchCamera, AlertCircle } from "lucide-react";
import { Alert, AlertDescription } from "@/components/ui/alert";
import Quagga from '@ericblade/quagga2';

interface BarcodeScannerProps {
  onBarcodeScanned: (data: {
    barcode: string;
    name?: string;
    manufacturer?: string;
    dosage?: string;
    form?: string;
  }) => void;
}

export default function BarcodeScanner({ onBarcodeScanned }: BarcodeScannerProps) {
  const videoRef = useRef<HTMLDivElement>(null);
  const [isScanning, setIsScanning] = useState(false);
  const [cameras, setCameras] = useState<MediaDeviceInfo[]>([]);
  const [currentCamera, setCurrentCamera] = useState(0);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // Get list of available cameras
    const getCameras = async () => {
      try {
        // Check if mediaDevices API is supported
        if (!navigator.mediaDevices || !navigator.mediaDevices.getUserMedia) {
          throw new Error("La fonctionnalité de caméra n'est pas supportée sur votre appareil");
        }

        // Request camera permissions with better error handling
        try {
          const stream = await navigator.mediaDevices.getUserMedia({
            video: {
              facingMode: 'environment'
            }
          });
          // Stop the test stream
          stream.getTracks().forEach(track => track.stop());
        } catch (permissionError: any) {
          if (permissionError.name === 'NotAllowedError') {
            throw new Error("L'accès à la caméra a été refusé. Veuillez autoriser l'accès dans les paramètres de votre navigateur");
          } else if (permissionError.name === 'NotFoundError') {
            throw new Error("Aucune caméra n'a été trouvée sur votre appareil");
          } else {
            throw permissionError;
          }
        }

        // Now that we have permission, enumerate devices
        const devices = await navigator.mediaDevices.enumerateDevices();
        const videoDevices = devices.filter(device => device.kind === 'videoinput');

        if (videoDevices.length === 0) {
          throw new Error("Aucune caméra n'a été trouvée sur votre appareil");
        }

        // On mobile, prefer back camera
        const backCamera = videoDevices.find(device =>
          device.label.toLowerCase().includes('back') ||
          device.label.toLowerCase().includes('rear') ||
          device.label.toLowerCase().includes('arrière')
        );

        setCameras(videoDevices);
        if (backCamera) {
          setCurrentCamera(videoDevices.indexOf(backCamera));
        }
      } catch (err: any) {
        console.error("Error accessing cameras:", err);
        setError(err.message || "Impossible d'accéder aux caméras. Veuillez vérifier les permissions.");
      }
    };

    getCameras();

    return () => {
      if (isScanning) {
        Quagga.stop();
      }
    };
  }, []);

  const lookupMedicationInfo = async (barcode: string) => {
    try {
      const response = await fetch(`/api/medication-lookup?barcode=${barcode}`);
      if (!response.ok) {
        throw new Error("Impossible de trouver les informations du médicament");
      }
      const data = await response.json();
      return data;
    } catch (err) {
      console.error("Failed to lookup medication:", err);
      return { barcode };
    }
  };

  const startScanning = async () => {
    if (!videoRef.current) return;
    setError(null);

    try {
      await Quagga.init({
        inputStream: {
          name: "Live",
          type: "LiveStream",
          target: videoRef.current,
          constraints: {
            deviceId: cameras[currentCamera]?.deviceId,
            facingMode: { ideal: "environment" },
            width: { min: 300, ideal: 1280, max: 1920 },
            height: { min: 200, ideal: 720, max: 1080 },
            aspectRatio: { min: 1, max: 2 },
          },
        },
        decoder: {
          readers: [
            "ean_reader",
            "ean_8_reader",
            "code_128_reader",
            "code_39_reader",
            "upc_reader",
            "upc_e_reader",
          ],
          debug: {
            drawBoundingBox: true,
            showPattern: true,
          },
        },
        locate: true,
      });

      Quagga.start();
      setIsScanning(true);

      Quagga.onDetected(async (result) => {
        if (result.codeResult.code) {
          // Try to play a success sound
          try {
            const audio = new Audio('/sounds/beep.mp3');
            audio.volume = 0.5;
            audio.play();
          } catch (err) {
            console.warn("Could not play success sound:", err);
          }

          // Stop scanning
          Quagga.stop();
          setIsScanning(false);

          // Look up medication info
          const medicationInfo = await lookupMedicationInfo(result.codeResult.code);
          onBarcodeScanned({
            barcode: result.codeResult.code,
            ...medicationInfo
          });
        }
      });
    } catch (err) {
      console.error("Failed to initialize scanner:", err);
      setError("Impossible d'initialiser le scanner. Veuillez vérifier que vous avez autorisé l'accès à la caméra dans les paramètres de votre navigateur.");
      setIsScanning(false);
    }
  };

  const switchCamera = async () => {
    if (isScanning) {
      Quagga.stop();
      setIsScanning(false);
    }
    setCurrentCamera((prev) => (prev + 1) % cameras.length);
  };

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    );
  }

  return (
    <div className="space-y-4">
      <div className="relative">
        <div
          ref={videoRef}
          className="w-full h-64 bg-black rounded-lg overflow-hidden"
        />
        {!isScanning && (
          <div className="absolute inset-0 flex items-center justify-center">
            <Button
              onClick={startScanning}
              className="flex items-center gap-2"
            >
              <Camera className="w-4 h-4" />
              Démarrer la caméra
            </Button>
          </div>
        )}
      </div>

      {cameras.length > 1 && (
        <Button
          variant="outline"
          onClick={switchCamera}
          className="w-full flex items-center justify-center gap-2"
          disabled={isScanning}
        >
          <SwitchCamera className="w-4 h-4" />
          Changer de caméra ({currentCamera + 1}/{cameras.length})
        </Button>
      )}

      <div className="text-sm text-muted-foreground text-center">
        Placez le code-barres du médicament dans le cadre pour le scanner automatiquement
      </div>
    </div>
  );
} 