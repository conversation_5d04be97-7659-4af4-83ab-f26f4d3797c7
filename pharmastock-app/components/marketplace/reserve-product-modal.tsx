"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON>alog<PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "@/components/ui/dialog";
import { useToast } from "@/components/ui/use-toast";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { useState } from "react";
import { DatePicker } from '@mantine/dates';
import { Select as MantineSelect } from '@mantine/core';

interface ReserveProductModalProps {
  product: any; // Using any because ListingWithDetails is in another file.
  isOpen: boolean;
  onClose: () => void;
}

const formSchema = z.object({
  quantity: z.coerce
    .number()
    .min(1, "Quantity must be at least 1")
    .max(1000, "Quantity cannot exceed 1000"),
  date: z.string().optional(),
  category: z.string().min(1, "La catégorie doit être sélectionnée"),
});

type FormValues = z.infer<typeof formSchema>;

export function ReserveProductModal({ product, isOpen, onClose }: ReserveProductModalProps) {
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      quantity: 1,
      date: undefined,
      category: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    try {
      const response = await fetch('/api/marketplace/reservations', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          listing_id: product.listing_id,
          quantity: values.quantity,
          date: values.date,
          category: values.category,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create reservation.');
      }

      toast({
        title: "Réservation envoyée",
        description: "Votre demande de réservation a été envoyée.",
      });
      onClose();
    } catch (error) {
      toast({
        title: "Erreur",
        description: error instanceof Error ? error.message : "Une erreur est survenue lors de la réservation.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-lg p-0 overflow-hidden">
        {/* Gradient header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4 rounded-t-2xl">
          <DialogHeader className="p-0">
            <DialogTitle className="text-xl font-bold text-white">
              Réserver {product.product.name}
            </DialogTitle>
          </DialogHeader>
        </div>
        <div className="p-6 bg-white rounded-b-2xl">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <FormField
                control={form.control}
                name="quantity"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Quantité</FormLabel>
                    <FormControl>
                      <Input
                        type="number"
                        min={1}
                        max={product.quantity}
                        {...field}
                        disabled={isLoading}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              <div className="flex justify-end gap-4">
                <Button variant="outline" type="button" onClick={onClose} disabled={isLoading} className="font-semibold">
                  Annuler
                </Button>
                <Button type="submit" disabled={isLoading} className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-2 px-6 rounded-xl shadow-md transition">
                  {isLoading ? 'Réservation...' : 'Réserver'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}