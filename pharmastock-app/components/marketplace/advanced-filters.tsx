"use client";

import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion";
import { Badge } from "@/components/ui/badge";
import { X } from "lucide-react";
import { Slider } from "@/components/ui/slider";

interface FilterState {
  expiryRange: string;
  minQuantity: string;
  availability: string;
  hasUrgentDelivery: boolean;
  certifications: string[];
  prescriptionType: string;
  city: string;
  proximity: number;
  network: string;
  preferredPartnersOnly: boolean;
  transferType: string;
  temperatureControlled: boolean;
  specialHandling: string[];
  hospitalPriority: boolean;
}

const CERTIFICATIONS = [
  "BPD (Bonnes Pratiques de Distribution)",
  "ISO 9001",
  "GDP (Good Distribution Practice)",
];

const CITIES = [
  "Casablanca - الدار البيضاء",
  "Rabat - الرباط",
  "Fès - فاس",
  "Tanger - طنجة",
  "Marrakech - مراكش",
  "Agadir - أكادير",
  "Meknès - مكناس",
  "Oujda - وجدة",
  "Kénitra - القنيطرة",
  "Tétouan - تطوان",
  "Salé - سلا",
  "Nador - الناظور",
  "Mohammedia - المحمدية",
  "El Jadida - الجديدة",
  "Béni Mellal - بني ملال",
  "Taza - تازة",
  "Khémisset - الخميسات",
  "Settat - سطات"
];

const NETWORKS = [
  "Réseau Nord",
  "Réseau Sud",
  "Réseau Centre",
  "Association des Pharmaciens de Casablanca",
  "Association des Pharmaciens de Rabat",
  "Groupement des Pharmaciens du Maroc"
];

const SPECIAL_HANDLING = [
  "Transport réfrigéré",
  "Emballage sécurisé",
  "Transport isotherme",
  "Chaîne du froid",
  "Protection lumière"
];

export function AdvancedFilters() {
  const [filters, setFilters] = useState<FilterState>({
    expiryRange: "",
    minQuantity: "",
    availability: "",
    hasUrgentDelivery: false,
    certifications: [],
    prescriptionType: "",
    city: "",
    proximity: 50,
    network: "",
    preferredPartnersOnly: false,
    transferType: "",
    temperatureControlled: false,
    specialHandling: [],
    hospitalPriority: false,
  });

  const [activeFilters, setActiveFilters] = useState<string[]>([]);

  const addFilter = (filterName: string, value: string) => {
    if (!activeFilters.includes(filterName)) {
      setActiveFilters([...activeFilters, filterName]);
    }
  };

  const removeFilter = (filterName: string) => {
    setActiveFilters(activeFilters.filter(f => f !== filterName));
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-wrap gap-2 p-4 bg-[#f6f8fa] border rounded-lg mb-4">
        <Button
          variant={filters.transferType === "immediate" ? "default" : "outline"}
          size="sm"
          onClick={() => {
            setFilters(prev => ({ ...prev, transferType: "immediate" }));
            addFilter("Transfert", "Immédiat");
          }}
        >
          Transfert immédiat
        </Button>
        <Button
          variant={filters.hospitalPriority ? "default" : "outline"}
          size="sm"
          onClick={() => {
            setFilters(prev => ({ ...prev, hospitalPriority: !prev.hospitalPriority }));
            if (!filters.hospitalPriority) {
              addFilter("Priorité", "Hôpitaux");
            } else {
              removeFilter("Priorité");
            }
          }}
        >
          Priorité hôpitaux
        </Button>
        <Button
          variant={filters.temperatureControlled ? "default" : "outline"}
          size="sm"
          onClick={() => {
            setFilters(prev => ({ ...prev, temperatureControlled: !prev.temperatureControlled }));
            if (!filters.temperatureControlled) {
              addFilter("Température", "Contrôlée");
            } else {
              removeFilter("Température");
            }
          }}
        >
          Température contrôlée
        </Button>
      </div>

      <div className="flex flex-wrap gap-2">
        {activeFilters.map((filter) => (
          <Badge key={filter} variant="secondary" className="flex items-center gap-1">
            {filter}
            <button onClick={() => removeFilter(filter)} className="ml-1">
              <X className="h-3 w-3" />
            </button>
          </Badge>
        ))}
      </div>

      <Accordion type="single" collapsible>
        <AccordionItem value="location">
          <AccordionTrigger>Localisation</AccordionTrigger>
          <AccordionContent className="space-y-4">
            <div>
              <Label>Ville</Label>
              <Select
                onValueChange={(value) => {
                  setFilters({ ...filters, city: value });
                  addFilter("Ville", value);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner une ville" />
                </SelectTrigger>
                <SelectContent>
                  {CITIES.map((city) => (
                    <SelectItem key={city} value={city.toLowerCase()}>
                      {city}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div>
              <Label>Distance maximale</Label>
              <div className="pt-2">
                <Slider
                  value={[filters.proximity]}
                  onValueChange={(value) => {
                    setFilters({ ...filters, proximity: value[0] });
                    addFilter("Distance", `${value[0]} km`);
                  }}
                  max={100}
                  step={5}
                />
              </div>
              <p className="text-sm text-muted-foreground mt-1">
                {filters.proximity}km maximum
              </p>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="expiry">
          <AccordionTrigger>Date de péremption</AccordionTrigger>
          <AccordionContent>
            <Select
              onValueChange={(value) => {
                setFilters({ ...filters, expiryRange: value });
                addFilter("Péremption", value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner une plage" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1-month">1 mois minimum</SelectItem>
                <SelectItem value="3-months">3 mois minimum</SelectItem>
                <SelectItem value="6-months">6 mois minimum</SelectItem>
                <SelectItem value="1-year">1 an minimum</SelectItem>
              </SelectContent>
            </Select>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="quantity">
          <AccordionTrigger>Quantité minimale</AccordionTrigger>
          <AccordionContent>
            <Select
              onValueChange={(value) => {
                setFilters({ ...filters, minQuantity: value });
                addFilter("Quantité", `Min. ${value} unités`);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Quantité minimale" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="10">10 unités</SelectItem>
                <SelectItem value="50">50 unités</SelectItem>
                <SelectItem value="100">100 unités</SelectItem>
                <SelectItem value="500">500 unités</SelectItem>
              </SelectContent>
            </Select>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="availability">
          <AccordionTrigger>Disponibilité</AccordionTrigger>
          <AccordionContent>
            <Select
              onValueChange={(value) => {
                setFilters({ ...filters, availability: value });
                addFilter("Disponibilité", value);
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner la disponibilité" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="immediate">Immédiate</SelectItem>
                <SelectItem value="24h">Sous 24h</SelectItem>
                <SelectItem value="48h">Sous 48h</SelectItem>
                <SelectItem value="week">Sous 1 semaine</SelectItem>
              </SelectContent>
            </Select>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="delivery">
          <AccordionTrigger>Options de livraison</AccordionTrigger>
          <AccordionContent className="space-y-4">
            <div className="flex items-center space-x-2">
              <Switch
                id="urgent-delivery"
                checked={filters.hasUrgentDelivery}
                onCheckedChange={(checked) => {
                  setFilters({ ...filters, hasUrgentDelivery: checked });
                  if (checked) {
                    addFilter("Livraison", "Urgente disponible");
                  } else {
                    removeFilter("Livraison");
                  }
                }}
              />
              <Label htmlFor="urgent-delivery">Livraison urgente disponible</Label>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="prescription">
          <AccordionTrigger>Type d&apos;ordonnance</AccordionTrigger>
          <AccordionContent>
            <Select
              onValueChange={(value) => {
                setFilters({ ...filters, prescriptionType: value });
                addFilter("Ordonnance", value === "obligatoire" ? "Obligatoire" : "Facultative");
              }}
            >
              <SelectTrigger>
                <SelectValue placeholder="Sélectionner le type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous les médicaments</SelectItem>
                <SelectItem value="obligatoire">Ordonnance obligatoire</SelectItem>
                <SelectItem value="facultative">Ordonnance facultative</SelectItem>
              </SelectContent>
            </Select>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="certifications">
          <AccordionTrigger>Certifications</AccordionTrigger>
          <AccordionContent className="space-y-4">
            {CERTIFICATIONS.map((cert) => (
              <div key={cert} className="flex items-center space-x-2">
                <Switch
                  id={cert}
                  checked={filters.certifications.includes(cert)}
                  onCheckedChange={(checked) => {
                    const newCerts = checked
                      ? [...filters.certifications, cert]
                      : filters.certifications.filter(c => c !== cert);
                    setFilters({ ...filters, certifications: newCerts });
                    if (checked) {
                      addFilter("Certification", cert);
                    } else {
                      removeFilter(cert);
                    }
                  }}
                />
                <Label htmlFor={cert}>{cert}</Label>
              </div>
            ))}
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="networks">
          <AccordionTrigger>Réseaux et Associations</AccordionTrigger>
          <AccordionContent className="space-y-4">
            <div>
              <Label>Réseau de pharmacies</Label>
              <Select
                onValueChange={(value) => {
                  setFilters({ ...filters, network: value });
                  addFilter("Réseau", value);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner un réseau" />
                </SelectTrigger>
                <SelectContent>
                  {NETWORKS.map((network) => (
                    <SelectItem key={network} value={network.toLowerCase()}>
                      {network}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center space-x-2">
              <Switch
                id="preferred-partners"
                checked={filters.preferredPartnersOnly}
                onCheckedChange={(checked) => {
                  setFilters({ ...filters, preferredPartnersOnly: checked });
                  if (checked) {
                    addFilter("Partenaires", "Partenaires préférés uniquement");
                  } else {
                    removeFilter("Partenaires");
                  }
                }}
              />
              <Label htmlFor="preferred-partners">Partenaires préférés uniquement</Label>
            </div>
          </AccordionContent>
        </AccordionItem>

        <AccordionItem value="transfer">
          <AccordionTrigger>Options de transfert</AccordionTrigger>
          <AccordionContent className="space-y-4">
            <div>
              <Label>Type de transfert</Label>
              <Select
                onValueChange={(value) => {
                  setFilters({ ...filters, transferType: value });
                  addFilter("Type de transfert", value);
                }}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner le type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="immediate">Transfert immédiat</SelectItem>
                  <SelectItem value="scheduled">Transfert programmé</SelectItem>
                  <SelectItem value="urgent">Transfert urgent</SelectItem>
                  <SelectItem value="hospital">Priorité hôpitaux</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label>Conditions spéciales</Label>
              {SPECIAL_HANDLING.map((condition) => (
                <div key={condition} className="flex items-center space-x-2">
                  <Switch
                    id={condition}
                    checked={filters.specialHandling.includes(condition)}
                    onCheckedChange={(checked) => {
                      const newConditions = checked
                        ? [...filters.specialHandling, condition]
                        : filters.specialHandling.filter(c => c !== condition);
                      setFilters({ ...filters, specialHandling: newConditions });
                      if (checked) {
                        addFilter("Condition", condition);
                      } else {
                        removeFilter(condition);
                      }
                    }}
                  />
                  <Label htmlFor={condition}>{condition}</Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>

      <Button
        className="w-full"
        variant="outline"
        onClick={() => {
          setFilters({
            expiryRange: "",
            minQuantity: "",
            availability: "",
            hasUrgentDelivery: false,
            certifications: [],
            prescriptionType: "",
            city: "",
            proximity: 50,
            network: "",
            preferredPartnersOnly: false,
            transferType: "",
            temperatureControlled: false,
            specialHandling: [],
            hospitalPriority: false,
          });
          setActiveFilters([]);
        }}
      >
        Réinitialiser les filtres
      </Button>
    </div>
  );
}