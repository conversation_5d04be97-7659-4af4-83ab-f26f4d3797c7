"use client";

import { useState } from "react";
import { Upload, <PERSON>, X, Loader2 } from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import dynamic from 'next/dynamic';

// Dynamically import the barcode scanner component to avoid SSR issues
const BarcodeScanner = dynamic(() => import('@/components/marketplace/barcode-scanner'), {
  ssr: false,
  loading: () => (
    <div className="flex items-center justify-center h-64">
      <Loader2 className="w-8 h-8 animate-spin" />
    </div>
  ),
});

interface DrugRecognitionProps {
  onDataRecognized: (data: any) => void;
}

export function DrugRecognition({ onDataRecognized }: DrugRecognitionProps) {
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [aiStatus, setAiStatus] = useState<"idle" | "analyzing" | "success" | "error">("idle");
  const [error, setError] = useState<string | null>(null);
  const [mode, setMode] = useState<"scan" | "upload">("scan");

  const resetState = () => {
    setImageFile(null);
    setImagePreview(null);
    setAiStatus("idle");
    setError(null);
  };

  const handleImageUpload = async (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    // Reset state
    setAiStatus("idle");
    setError(null);
    setImageFile(file);

    // Create preview
    setImagePreview(URL.createObjectURL(file));

    // Start analysis
    setAiStatus("analyzing");
    try {
      // Compress the image
      const base64Data = await compressImage(file);
      console.log("Sending image data to API...");
      console.log("Base64 length:", base64Data.length);

      const response = await fetch("/api/analyze-image", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          image: base64Data
        }),
      });

      const responseText = await response.text();
      console.log("Raw response:", responseText);

      let responseData;
      try {
        responseData = JSON.parse(responseText);
      } catch (e) {
        console.error("Failed to parse response:", responseText);
        throw new Error("Invalid response from server");
      }

      if (!response.ok) {
        console.error("Server error:", responseData);
        throw new Error(responseData.error || "Failed to analyze image");
      }

      if (!responseData.name && !responseData.manufacturer && !responseData.dosage && !responseData.form) {
        throw new Error("No medication information could be extracted from the image");
      }

      console.log("AI Analysis Result:", responseData);
      onDataRecognized(responseData);
      setAiStatus("success");
    } catch (e) {
      console.error("Image analysis failed:", e);
      setError(e instanceof Error ? e.message : "Failed to analyze image");
      setAiStatus("error");
    }
  };

  const compressImage = async (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = (e) => {
        const img = new Image();
        img.onload = () => {
          const canvas = document.createElement('canvas');
          const MAX_WIDTH = 200;
          const MAX_HEIGHT = 200;
          let width = img.width;
          let height = img.height;

          if (width > height) {
            if (width > MAX_WIDTH) {
              height *= MAX_WIDTH / width;
              width = MAX_WIDTH;
            }
          } else {
            if (height > MAX_HEIGHT) {
              width *= MAX_HEIGHT / height;
              height = MAX_HEIGHT;
            }
          }

          canvas.width = width;
          canvas.height = height;

          const ctx = canvas.getContext('2d');
          if (!ctx) {
            reject(new Error('Failed to get canvas context'));
            return;
          }

          ctx.imageSmoothingEnabled = true;
          ctx.imageSmoothingQuality = 'high';

          ctx.drawImage(img, 0, 0, width, height);
          const base64Data = canvas.toDataURL('image/jpeg', 0.2).split(',')[1];
          resolve(base64Data);
        };
        img.onerror = () => reject(new Error('Failed to load image'));
        img.src = e.target?.result as string;
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  };

  return (
    <div className="space-y-4">
      <div className="flex items-center gap-2 text-lg font-semibold">
        <span className="w-8 h-8 rounded-full bg-primary text-primary-content flex items-center justify-center">
          AI
        </span>
        <h3>Reconnaissance du médicament</h3>
      </div>

      <div className="flex gap-2">
        <Button
          variant={mode === "scan" ? "default" : "outline"}
          className="flex-1"
          onClick={() => setMode("scan")}
        >
          Scanner Code-barres
        </Button>
        <Button
          variant={mode === "upload" ? "default" : "outline"}
          className="flex-1"
          onClick={() => setMode("upload")}
        >
          Télécharger Image
        </Button>
      </div>

      {mode === "scan" ? (
        <BarcodeScanner onBarcodeScanned={onDataRecognized} />
      ) : (
        <div>
          {!imagePreview ? (
            <label className="flex flex-col items-center justify-center w-full h-64 border-2 border-dashed rounded-lg cursor-pointer hover:bg-muted transition-colors">
              <div className="flex flex-col items-center justify-center pt-5 pb-6">
                <Upload className="w-10 h-10 mb-3 text-muted-foreground" />
                <p className="mb-2 text-sm">
                  <span className="font-semibold">
                    Cliquez pour télécharger une image
                  </span>
                </p>
                <p className="text-xs text-muted-foreground">
                  PNG, JPG ou JPEG (max. 5MB)
                </p>
              </div>
              <input
                type="file"
                className="hidden"
                accept="image/*"
                onChange={handleImageUpload}
              />
            </label>
          ) : (
            <div className="relative">
              <img
                src={imagePreview}
                alt="Preview"
                className="w-full h-64 object-cover rounded-lg"
              />
              <Button
                size="icon"
                variant="secondary"
                className="absolute top-2 right-2"
                onClick={resetState}
              >
                <X className="w-4 h-4" />
              </Button>
            </div>
          )}
        </div>
      )}

      {aiStatus === "analyzing" && (
        <Card className="p-6">
          <div className="flex flex-col items-center justify-center py-8 text-muted-foreground">
            <div className="relative mb-6">
              <div className="absolute inset-0 flex items-center justify-center">
                <div className="w-16 h-16 rounded-full bg-primary/10 animate-ping" />
              </div>
              <div className="relative flex items-center justify-center">
                <Brain className="w-12 h-12 text-primary animate-pulse" />
              </div>
            </div>
            <div className="flex flex-col items-center gap-2">
              <span className="text-lg font-medium">
                Analyse en cours...
              </span>
              <div className="flex gap-1">
                <Loader2 className="w-4 h-4 animate-spin" />
              </div>
            </div>
          </div>
        </Card>
      )}

      {error && (
        <div className="text-sm text-destructive">
          {error}
        </div>
      )}
    </div>
  );
} 