'use client';

import React, { useState, useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { toast } from "sonner";
import { DollarSign, Box, Calendar, Tag, PackageSearch, Landmark, Repeat, LucideIcon } from "lucide-react";
import { DatePicker } from '@mantine/dates';
import { Select } from '@mantine/core';

import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input, type InputProps } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { useSupabase } from "@/contexts/supabase-context";
import { useAuth } from "@/contexts/auth-context";
import { ToggleGroup, ToggleGroupItem } from "@/components/ui/toggle-group";
import { Card, CardContent } from "@/components/ui/card";
import { cn } from "@/lib/utils";

// ============================================================================
// InputWithIcon Component
// ============================================================================
export interface InputWithIconProps extends InputProps {
  icon: LucideIcon;
}

const InputWithIcon = React.forwardRef<HTMLInputElement, InputWithIconProps>(
  ({ className, type, icon: Icon, ...props }, ref) => {
    return (
      <div className="relative">
        <Icon className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground" />
        <Input
          type={type}
          className={cn('pl-10', className)}
          ref={ref}
          {...props}
        />
      </div>
    );
  }
);
InputWithIcon.displayName = 'InputWithIcon';

// ============================================================================
// Form Schema
// ============================================================================
const baseSchema = z.object({
  name: z.string().min(1, "Le nom est requis"),
  description: z.string().optional(),
  category: z.string().min(1, "La catégorie est requise"),
  expiry_date: z.string().min(1, "La date d'expiration est requise"),
  quantity: z.coerce.number().min(1, "La quantité doit être supérieure à 0"),
  minimum_order: z.coerce.number().min(1, "La commande minimum doit être supérieure à 0"),
  original_price: z.coerce.number().optional(),
});

const productSchema = z.discriminatedUnion("posting_type", [
  baseSchema.extend({
    posting_type: z.literal("sale"),
    price_per_unit: z.coerce.number().min(0.01, "Le prix doit être supérieur à 0"),
    looking_for: z.string().optional(),
  }),
  baseSchema.extend({
    posting_type: z.literal("exchange"),
    price_per_unit: z.coerce.number().optional(),
    looking_for: z.string().min(1, "Veuillez spécifier ce que vous recherchez"),
  }),
  baseSchema.extend({
    posting_type: z.literal("both"),
    price_per_unit: z.coerce.number().min(0.01, "Le prix doit être supérieur à 0"),
    looking_for: z.string().min(1, "Veuillez spécifier ce que vous recherchez"),
  }),
]);

type ProductForm = z.infer<typeof productSchema>;

// ============================================================================
// Component Props
// ============================================================================
interface SellProductModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

// ============================================================================
// Main Component
// ============================================================================
export function SellProductModal({
  isOpen,
  onClose,
  onSuccess,
}: SellProductModalProps): JSX.Element {
  const { supabase, pharmacy } = useSupabase();
  const { user } = useAuth();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<ProductForm>({
    resolver: zodResolver(productSchema),
    defaultValues: {
      name: "",
      description: "",
      category: "",
      expiry_date: "",
      quantity: 1,
      minimum_order: 1,
      posting_type: 'sale',
      price_per_unit: undefined,
      original_price: undefined,
      looking_for: "",
    },
  });
  
  const postingType = form.watch("posting_type");

  useEffect(() => {
    form.trigger();
  }, [postingType, form]);

  const onSubmit = async (data: ProductForm) => {
    if (!pharmacy?.id) {
      toast.error("Vous devez être connecté pour mettre en vente un produit");
      return;
    }

    if (data.minimum_order > data.quantity) {
      form.setError("minimum_order", {
        type: "manual",
        message: "La commande minimum ne peut pas dépasser la quantité disponible",
      });
      return;
    }

    setIsSubmitting(true);

    try {
      // First create the product
      const { data: product, error: productError } = await supabase
        .from("products")
        .insert({
          name: data.name,
          description: data.description || null,
          category: data.category,
          expiry_date: data.expiry_date,
          original_price: data.original_price,
        })
        .select()
        .single();

      if (productError) throw productError;

      // Then create the listing
      const { error: listingError } = await supabase
        .from("listings")
        .insert({
          product_id: product.id,
          pharmacy_id: pharmacy.id,
          quantity: data.quantity,
          minimum_order: data.minimum_order,
          price_per_unit: data.price_per_unit,
          status: "active",
          looking_for: data.looking_for,
          posting_type: data.posting_type,
        });

      if (listingError) throw listingError;

      toast.success("Annonce créée avec succès");
      form.reset();
      onClose();
      onSuccess();
    } catch (error) {
      console.error("Error creating product:", error);
      toast.error("Erreur lors de la création du produit");
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl p-0 overflow-hidden">
        {/* Gradient header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4 rounded-t-2xl">
          <DialogHeader className="p-0">
            <DialogTitle className="text-xl font-bold text-white">
              Créer une Annonce
            </DialogTitle>
            <DialogDescription className="text-white/80">
              Ajoutez un nouveau produit à la marketplace.
            </DialogDescription>
          </DialogHeader>
        </div>
        <div className="p-6 bg-white rounded-b-2xl">
          <Form {...form}>
            <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
              <Card>
                <CardContent className="pt-6">
                  <FormField
                    control={form.control}
                    name="posting_type"
                    render={({ field }) => (
                      <FormItem className="flex flex-col items-center">
                        <FormLabel className="mb-3">Type d'annonce</FormLabel>
                        <FormControl>
                          <ToggleGroup
                            type="single"
                            defaultValue="sale"
                            value={field.value}
                            onValueChange={(value) => {
                              if (value) field.onChange(value as 'sale' | 'exchange' | 'both');
                            }}
                          >
                            <ToggleGroupItem value="sale" aria-label="Toggle sale">
                              <Landmark className="h-4 w-4 mr-2" />
                              Vente
                            </ToggleGroupItem>
                            <ToggleGroupItem value="exchange" aria-label="Toggle exchange">
                              <Repeat className="h-4 w-4 mr-2" />
                              Échange
                            </ToggleGroupItem>
                            <ToggleGroupItem value="both" aria-label="Toggle both">
                              <Landmark className="h-4 w-4 mr-2" />
                              <Repeat className="h-4 w-4 mr-2" />
                              Les deux
                            </ToggleGroupItem>
                          </ToggleGroup>
                        </FormControl>
                        <FormMessage />
                      </FormItem>
                    )}
                  />
                </CardContent>
              </Card>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  {/* Product Info */}
                  <Card>
                      <CardContent className="pt-6 space-y-4">
                          <FormField control={form.control} name="name" render={({ field }) => (
                              <FormItem>
                                  <FormLabel>Nom du produit</FormLabel>
                                  <FormControl><InputWithIcon placeholder="Doliprane 1000mg" {...field} icon={PackageSearch} /></FormControl>
                                  <FormMessage />
                              </FormItem>
                          )} />
                          <FormField control={form.control} name="description" render={({ field }) => (
                    <FormItem>
                                      <FormLabel>Description</FormLabel>
                                      <FormControl><Textarea placeholder="Conditionnement, état, etc." {...field} /></FormControl>
                    <FormMessage />
                  </FormItem>
                          )} />
                          <div className="grid grid-cols-2 gap-4">
                              <FormField control={form.control} name="category" render={({ field }) => (
                    <FormItem>
                    <FormLabel>Catégorie</FormLabel>
                    <FormControl>
                      <Select
                        data={[
                          { value: "antalgiques", label: "Antalgiques" },
                          { value: "antibiotiques", label: "Antibiotiques" },
                          { value: "antidiabetiques", label: "Antidiabétiques" },
                          { value: "cardiologie", label: "Cardiologie" },
                          { value: "dermatologie", label: "Dermatologie" },
                          { value: "gastro", label: "Gastro-entérologie" },
                          { value: "ophtalmologie", label: "Ophtalmologie" },
                          { value: "pediatrie", label: "Pédiatrie" },
                          { value: "psychiatrie", label: "Psychiatrie" },
                          { value: "oncologie", label: "Oncologie" },
                          { value: "gynecologie", label: "Gynécologie" },
                          { value: "urologie", label: "Urologie" },
                          { value: "autre", label: "Autre" },
                        ]}
                        placeholder="Sélectionner une catégorie"
                        value={field.value}
                        onChange={field.onChange}
                        required
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                              )} />
                              <FormField control={form.control} name="expiry_date" render={({ field }) => (
                    <FormItem>
                    <FormLabel>Expiration</FormLabel>
                    <FormControl>
                      <DatePicker
                        value={field.value && typeof field.value === 'string' ? new Date(field.value) : field.value || null}
                        onChange={(date) => field.onChange(date && typeof date !== 'string' ? (date as Date).toISOString() : null)}
                        placeholder="Sélectionner une date"
                        getYearControlProps={(date) => ({})}
                        getMonthControlProps={(date) => ({})}
                        getDayProps={(date) => ({})}
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                              )} />
                          </div>
                      </CardContent>
                  </Card>

                  {/* Listing Details */}
                  <Card>
                      <CardContent className="pt-6 space-y-4">
                <div className="grid grid-cols-2 gap-4">
                               <FormField control={form.control} name="quantity" render={({ field }) => (
                    <FormItem>
                      <FormLabel>Quantité</FormLabel>
                                      <FormControl><InputWithIcon type="number" placeholder="100" {...field} icon={Box} /></FormControl>
                    <FormMessage />
                  </FormItem>
                               )} />
                               <FormField control={form.control} name="minimum_order" render={({ field }) => (
                    <FormItem>
                                      <FormLabel>Qte Min.</FormLabel>
                                      <FormControl><InputWithIcon type="number" placeholder="10" {...field} icon={Box} /></FormControl>
                    <FormMessage />
                  </FormItem>
                               )} />
                </div>

                        {(postingType === 'sale' || postingType === 'both') && (
                            <div className="grid grid-cols-2 gap-4">
                               <FormField control={form.control} name="price_per_unit" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Prix de vente</FormLabel>
                                        <FormControl><InputWithIcon type="number" step="0.01" placeholder="25.00" {...field} value={field.value ?? ""} icon={DollarSign} /></FormControl>
                                        <FormMessage />
                                    </FormItem>
                               )} />
                               <FormField control={form.control} name="original_price" render={({ field }) => (
                                    <FormItem>
                                        <FormLabel>Prix public</FormLabel>
                                        <FormControl><InputWithIcon type="number" step="0.01" placeholder="35.50" {...field} value={field.value ?? ""} icon={DollarSign} /></FormControl>
                                        <FormMessage />
                                    </FormItem>
                               )} />
                            </div>
                        )}

                        {(postingType === 'exchange' || postingType === 'both') && (
                             <FormField control={form.control} name="looking_for" render={({ field }) => (
                    <FormItem>
                                      <FormLabel>Produit recherché</FormLabel>
                                      <FormControl><Textarea placeholder="Décrivez le produit que vous souhaitez en échange" {...field} value={field.value ?? ""} /></FormControl>
                    <FormMessage />
                  </FormItem>
                            )} />
                  )}
                      </CardContent>
                  </Card>
              </div>

              <div className="flex justify-end gap-4 mt-6">
                <Button variant="outline" type="button" onClick={onClose} className="font-semibold">
                  Annuler
                </Button>
                <Button type="submit" className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-2 px-6 rounded-xl shadow-md transition" disabled={isSubmitting}>
                  {isSubmitting ? 'Création...' : 'Ajouter'}
                </Button>
              </div>
            </form>
          </Form>
        </div>
      </DialogContent>
    </Dialog>
  );
}