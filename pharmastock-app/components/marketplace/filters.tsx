"use client";

import { useState } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";

export function MarketplaceFilters() {
  const [distance, setDistance] = useState([50]);

  return (
    <div className="space-y-4">
      <div>
        <Label>Catégorie</Label>
        <Select>
          <SelectTrigger>
            <SelectValue placeholder="Toutes les catégories" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Toutes les catégories</SelectItem>
            <SelectItem value="analgesique">Analgésique</SelectItem>
            <SelectItem value="antibiotique">Antibiotique</SelectItem>
            <SelectItem value="antiinflammatoire">Anti-inflammatoire</SelectItem>
          </SelectContent>
        </Select>
      </div>

      <div>
        <Label>Distance maximale</Label>
        <div className="pt-2">
          <Slider
            value={distance}
            onValueChange={setDistance}
            max={100}
            step={1}
          />
        </div>
        <p className="text-sm text-muted-foreground mt-1">
          {distance}km maximum
        </p>
      </div>

      <div className="grid grid-cols-2 gap-4">
        <div>
          <Label>Prix minimum</Label>
          <Input type="number" placeholder="0 €" />
        </div>
        <div>
          <Label>Prix maximum</Label>
          <Input type="number" placeholder="1000 €" />
        </div>
      </div>

      <Button className="w-full">Appliquer les filtres</Button>
    </div>
  );
}