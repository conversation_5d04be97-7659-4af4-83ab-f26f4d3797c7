'use client';

// ============================================================================
// Reservations List Component
// ============================================================================
// This component displays and manages product reservations.
// It has two modes:
// 1. Seller mode: Shows incoming reservations for the pharmacy's listings
// 2. Buyer mode: Shows outgoing reservations made by the pharmacy
// ============================================================================

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { toast } from "sonner";
import { useSupabase } from "@/contexts/supabase-context";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// ============================================================================
// Types
// ============================================================================
interface Reservation {
  id: string;
  listing_id: string;
  pharmacy_id: string;
  quantity: number;
  status: 'pending' | 'accepted' | 'rejected';
  message: string | null;
  created_at: string;
  listing: {
    id: string;
    product: {
      name: string;
      expiry_date: string;
    };
    price_per_unit: number;
    pharmacy: {
      name: string;
    };
  };
}

interface ReservationsListProps {
  mode: 'seller' | 'buyer';
}

// ============================================================================
// Main Component
// ============================================================================
export function ReservationsList({ mode }: ReservationsListProps): JSX.Element {
  const { supabase, pharmacy } = useSupabase();
  const router = useRouter();
  const [reservations, setReservations] = useState<Reservation[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch reservations
  useEffect(() => {
    const fetchReservations = async () => {
      if (!pharmacy?.id) return;

      try {
        let query = supabase
          .from('listing_reservations')
          .select(`
            *,
            listing:listings (
              id,
              price_per_unit,
              product:products (
                name,
                expiry_date
              ),
              pharmacy:pharmacies (
                name
              )
            )
          `);

        // Add filters based on mode
        if (mode === 'seller') {
          // First get all listings from this pharmacy
          const { data: pharmacyListings, error: listingsError } = await supabase
            .from('listings')
            .select('id')
            .eq('pharmacy_id', pharmacy.id);

          if (listingsError) throw listingsError;

          // Then filter reservations by these listing IDs
          query = query.in('listing_id', pharmacyListings.map(l => l.id));
        } else {
          query = query.eq('pharmacy_id', pharmacy.id);
        }

        const { data, error } = await query;

        if (error) throw error;

        setReservations(data as Reservation[]);
      } catch (error) {
        console.error('Error fetching reservations:', error);
        toast.error("Erreur lors de la récupération des réservations");
      } finally {
        setIsLoading(false);
      }
    };

    fetchReservations();
  }, [supabase, pharmacy?.id, mode]);

  // Update reservation status
  const updateStatus = async (reservationId: string, newStatus: 'accepted' | 'rejected') => {
    try {
      const { error } = await supabase
        .from('listing_reservations')
        .update({ status: newStatus })
        .eq('id', reservationId);

      if (error) throw error;

      // Update local state
      setReservations(prev =>
        prev.map(r =>
          r.id === reservationId
            ? { ...r, status: newStatus }
            : r
        )
      );

      toast.success(`Réservation ${newStatus === 'accepted' ? 'acceptée' : 'refusée'}`);
    } catch (error) {
      console.error('Error updating reservation:', error);
      toast.error("Erreur lors de la mise à jour de la réservation");
    }
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-semibold">
          {mode === 'seller' ? 'Réservations reçues' : 'Réservations envoyées'}
        </h2>
      </div>

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Produit</TableHead>
              {mode === 'buyer' && <TableHead>Pharmacie</TableHead>}
              <TableHead>Quantité</TableHead>
              <TableHead>Prix total</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Statut</TableHead>
              {mode === 'seller' && <TableHead>Actions</TableHead>}
            </TableRow>
          </TableHeader>
          <TableBody>
            {reservations.map((reservation) => (
              <TableRow key={reservation.id}>
                <TableCell>{reservation.listing.product.name}</TableCell>
                {mode === 'buyer' && (
                  <TableCell>{reservation.listing.pharmacy.name}</TableCell>
                )}
                <TableCell>{reservation.quantity}</TableCell>
                <TableCell>
                  {(reservation.quantity * reservation.listing.price_per_unit).toFixed(2)} DH
                </TableCell>
                <TableCell>
                  {new Date(reservation.created_at).toLocaleDateString('fr-FR')}
                </TableCell>
                <TableCell>
                  <Badge
                    variant={
                      reservation.status === 'pending'
                        ? 'outline'
                        : reservation.status === 'accepted'
                          ? 'default'
                          : 'destructive'
                    }
                  >
                    {reservation.status === 'pending' && 'En attente'}
                    {reservation.status === 'accepted' && 'Acceptée'}
                    {reservation.status === 'rejected' && 'Refusée'}
                  </Badge>
                </TableCell>
                {mode === 'seller' && reservation.status === 'pending' && (
                  <TableCell>
                    <div className="flex gap-2">
                      <Button
                        size="sm"
                        onClick={() => updateStatus(reservation.id, 'accepted')}
                      >
                        Accepter
                      </Button>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => updateStatus(reservation.id, 'rejected')}
                      >
                        Refuser
                      </Button>
                    </div>
                  </TableCell>
                )}
              </TableRow>
            ))}
            {reservations.length === 0 && (
              <TableRow>
                <TableCell
                  colSpan={mode === 'seller' ? 6 : 7}
                  className="text-center h-24 text-muted-foreground"
                >
                  Aucune réservation {mode === 'seller' ? 'reçue' : 'envoyée'}
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
} 