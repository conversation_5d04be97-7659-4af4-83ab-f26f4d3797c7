"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { Button } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Separator } from "@/components/ui/separator";
import { useState } from "react";
import { Card } from "@/components/ui/card";
import {
  Breadcrumb,
  BreadcrumbItem,
  BreadcrumbLink,
  BreadcrumbList,
  BreadcrumbPage,
  BreadcrumbSeparator,
} from "@/components/ui/breadcrumb";
import { useRouter } from "next/navigation";
import { useToast } from "@/components/ui/use-toast";
import { DatePicker } from '@mantine/dates';
import { Select } from '@mantine/core';

interface MedicationInfo {
  name: string;
  manufacturer: string;
  dosage: string;
  form: string;
  activeIngredients: string;
  posology: string;
  indications: string;
  contraindications: string;
  reimbursementRate: string;
  packaging: string;
  cip13?: string;
}

const formSchema = z.object({
  name: z.string().min(2, "Le nom doit contenir au moins 2 caractères"),
  description: z.string().optional(),
  quantity: z.coerce.number().min(1, "La quantité doit être supérieure à 0"),
  expiryDate: z.string(),
  originalPrice: z.coerce.number().min(0, "Le prix d'achat doit être positif"),
  salePrice: z.coerce.number().min(0, "Le prix de vente doit être positif"),
  category: z.string().min(1, "La catégorie doit être sélectionnée"),
});

type FormValues = z.infer<typeof formSchema>;

export function ListForSaleForm({ initialData }: { initialData?: MedicationInfo | null }) {
  const router = useRouter();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: initialData?.name ?? "",
      description: "",
      quantity: 1,
      expiryDate: "",
      originalPrice: 0,
      salePrice: 0,
      category: "",
    },
  });

  const onSubmit = async (values: FormValues) => {
    setIsLoading(true);
    try {
      const productData = {
        name: values.name,
        description: values.description,
        category: values.category,
        original_price: values.originalPrice,
        expiry_date: values.expiryDate,
      };

      const listingData = {
        quantity: values.quantity,
        price_per_unit: values.salePrice,
        status: 'active',
        minimum_order: 1,
        posting_type: 'sale', // TODO: Get this from the parent modal
      };

      const response = await fetch('/api/marketplace/listings', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ productData, listingData }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || 'Failed to create listing.');
      }

        toast({
          title: "Succès",
          description: "Médicament mis en vente avec succès",
        });
      router.push("/marketplace");

    } catch (error) {
        toast({
          variant: "destructive",
          title: "Erreur",
          description: error instanceof Error ? error.message : "Erreur lors de la mise en vente du médicament",
        });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="rounded-2xl overflow-hidden shadow-lg">
      {/* Gradient header */}
      <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4">
        <h2 className="text-xl font-bold text-white">
          Mettre un médicament en vente
        </h2>
        <p className="text-sm text-blue-100">
          L'accès à cette page n'est autorisé qu'aux pharmacies.
        </p>
      </div>
      <div className="p-6 bg-white">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Nom du médicament</FormLabel>
                  <FormControl>
                    <Input {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description</FormLabel>
                  <FormControl>
                    <Textarea {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantité</FormLabel>
                  <FormControl>
                    <Input type="number" min="1" {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="expiryDate"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Date d'expiration</FormLabel>
                  <FormControl>
                    <DatePicker
                      value={field.value && typeof field.value === 'string' ? new Date(field.value) : field.value || null}
                      onChange={(date) => field.onChange(date && typeof date !== 'string' ? (date as Date).toISOString() : null)}
                      placeholder="Sélectionner une date"
                      getYearControlProps={(date) => ({})}
                      getMonthControlProps={(date) => ({})}
                      getDayProps={(date) => ({})}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="originalPrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prix original (DH)</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" {...field} disabled={isLoading} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="salePrice"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Prix réduit (DH)</FormLabel>
                  <FormControl>
                    <Input type="number" min="0" step="0.01" {...field} disabled={isLoading} />
                  </FormControl>
                  <FormDescription>Optionnel</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="category"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Catégorie</FormLabel>
                  <FormControl>
                    <Select
                      data={[
                        { value: "antalgiques", label: "Antalgiques" },
                        { value: "antibiotiques", label: "Antibiotiques" },
                        { value: "antidiabetiques", label: "Antidiabétiques" },
                        { value: "cardiologie", label: "Cardiologie" },
                        { value: "dermatologie", label: "Dermatologie" },
                        { value: "gastro-enterologie", label: "Gastro-entérologie" },
                        { value: "gynecologie", label: "Gynécologie" },
                        { value: "neurologie", label: "Neurologie" },
                        { value: "ophtalmologie", label: "Ophtalmologie" },
                        { value: "orl", label: "ORL" },
                        { value: "pediatrie", label: "Pédiatrie" },
                        { value: "psychiatrie", label: "Psychiatrie" },
                        { value: "rhumatologie", label: "Rhumatologie" },
                        { value: "urologie", label: "Urologie" },
                        { value: "vaccins", label: "Vaccins" },
                        { value: "autres", label: "Autres" },
                      ]}
                      value={field.value}
                      onChange={field.onChange}
                      placeholder="Sélectionner une catégorie"
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4">
              <Button variant="outline" type="button" onClick={() => router.back()} disabled={isLoading}>
                Annuler
              </Button>
              <Button
                type="submit"
                className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-3 rounded-xl shadow-md transition"
                disabled={isLoading}
              >
                {isLoading ? 'Mise en vente...' : 'Mettre en vente'}
              </Button>
            </div>
          </form>
        </Form>
      </div>
    </div>
  );
}