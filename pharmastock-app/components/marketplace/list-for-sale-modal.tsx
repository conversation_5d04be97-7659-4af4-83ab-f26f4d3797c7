import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useState } from "react";

interface ListForSaleModalProps {
  open: boolean;
  onClose: () => void;
}

export function ListForSaleModal({ open, onClose }: ListForSaleModalProps) {
  const [formData, setFormData] = useState({
    name: "",
    manufacturer: "",
    category: "",
    prescriptionType: "",
    quantity: "",
    price: "",
    expiryDate: "",
    lotNumber: "",
    storageConditions: "",
    dosage: "",
    form: "",
    activeIngredients: "",
    description: "",
  });

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    // TODO: Handle form submission
    console.log(formData);
    onClose();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl p-0 overflow-hidden">
        {/* Gradient header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4 rounded-t-2xl">
          <DialogHeader className="p-0">
            <DialogTitle className="text-xl font-bold text-white">
              Mettre en vente
            </DialogTitle>
          </DialogHeader>
        </div>
        <div className="p-6 bg-white rounded-b-2xl">
          <form onSubmit={handleSubmit} className="space-y-6 mt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-2">
                <Label className="text-[#24292f]">Nom du médicament</Label>
                <Input
                  placeholder="Ex: Doliprane 1000mg"
                  value={formData.name}
                  onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                  required
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Fabricant</Label>
                <Input
                  placeholder="Ex: Cooper Pharma"
                  value={formData.manufacturer}
                  onChange={(e) => setFormData(prev => ({ ...prev, manufacturer: e.target.value }))}
                  required
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Catégorie</Label>
                <Select
                  value={formData.category}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, category: value }))}
                >
                  <SelectTrigger className="text-[#24292f]">
                    <SelectValue placeholder="Sélectionnez une catégorie" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="antalgiques">Antalgiques</SelectItem>
                    <SelectItem value="antibiotiques">Antibiotiques</SelectItem>
                    <SelectItem value="antidiabetiques">Antidiabétiques</SelectItem>
                    <SelectItem value="cardiologie">Cardiologie</SelectItem>
                    <SelectItem value="dermatologie">Dermatologie</SelectItem>
                    <SelectItem value="gastro">Gastro-entérologie</SelectItem>
                    <SelectItem value="ophtalmologie">Ophtalmologie</SelectItem>
                    <SelectItem value="pediatrie">Pédiatrie</SelectItem>
                    <SelectItem value="psychiatrie">Psychiatrie</SelectItem>
                    <SelectItem value="oncologie">Oncologie</SelectItem>
                    <SelectItem value="gynecologie">Gynécologie</SelectItem>
                    <SelectItem value="urologie">Urologie</SelectItem>
                    <SelectItem value="autre">Autre</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Type de prescription</Label>
                <Select
                  value={formData.prescriptionType}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, prescriptionType: value }))}
                >
                  <SelectTrigger className="text-[#24292f]">
                    <SelectValue placeholder="Sélectionnez le type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="obligatoire">Obligatoire</SelectItem>
                    <SelectItem value="facultative">Facultative</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Quantité disponible</Label>
                <Input
                  type="number"
                  min="1"
                  value={formData.quantity}
                  onChange={(e) => setFormData(prev => ({ ...prev, quantity: e.target.value }))}
                  required
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Prix unitaire (DH)</Label>
                <Input
                  type="number"
                  min="0"
                  step="0.01"
                  value={formData.price}
                  onChange={(e) => setFormData(prev => ({ ...prev, price: e.target.value }))}
                  required
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Date de péremption</Label>
                <Input
                  type="date"
                  value={formData.expiryDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, expiryDate: e.target.value }))}
                  required
                  className="text-[#24292f] modern-calendar-picker"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Numéro de lot</Label>
                <Input
                  value={formData.lotNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, lotNumber: e.target.value }))}
                  required
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Conditions de stockage</Label>
                <Select
                  value={formData.storageConditions}
                  onValueChange={(value) => setFormData(prev => ({ ...prev, storageConditions: value }))}
                >
                  <SelectTrigger className="text-[#24292f]">
                    <SelectValue placeholder="Sélectionnez les conditions" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="température ambiante">Température ambiante</SelectItem>
                    <SelectItem value="réfrigéré">Réfrigéré (2-8°C)</SelectItem>
                    <SelectItem value="congelé">Congelé (-20°C)</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Dosage</Label>
                <Input
                  placeholder="Ex: 1000mg"
                  value={formData.dosage}
                  onChange={(e) => setFormData(prev => ({ ...prev, dosage: e.target.value }))}
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Forme pharmaceutique</Label>
                <Input
                  placeholder="Ex: Comprimé"
                  value={formData.form}
                  onChange={(e) => setFormData(prev => ({ ...prev, form: e.target.value }))}
                  className="text-[#24292f]"
                />
              </div>

              <div className="space-y-2">
                <Label className="text-[#24292f]">Principes actifs</Label>
                <Input
                  placeholder="Ex: Paracétamol"
                  value={formData.activeIngredients}
                  onChange={(e) => setFormData(prev => ({ ...prev, activeIngredients: e.target.value }))}
                  className="text-[#24292f]"
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label className="text-[#24292f]">Description supplémentaire</Label>
              <Textarea
                placeholder="Ajoutez des informations supplémentaires sur le produit..."
                className="resize-none h-32 text-[#24292f]"
                value={formData.description}
                onChange={(e) => setFormData(prev => ({ ...prev, description: e.target.value }))}
              />
            </div>

            <div className="flex justify-end gap-4 mt-6">
              <Button type="button" variant="outline" onClick={onClose} className="font-semibold">
                Annuler
              </Button>
              <Button type="submit" className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-2 px-6 rounded-xl shadow-md transition">
                Mettre en vente
              </Button>
            </div>
          </form>
        </div>
      </DialogContent>
    </Dialog>
  );
} 