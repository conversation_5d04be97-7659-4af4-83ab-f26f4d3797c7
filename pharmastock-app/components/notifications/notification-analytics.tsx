"use client";

import { useNotifications } from "@/contexts/notification-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { NotificationType, NotificationPriority } from "@/contexts/notification-context";
import {
  <PERSON>,
  <PERSON><PERSON>,
  CartesianGrid,
  Legend,
  Line,
  LineChart,
  Pie,
  Pie<PERSON>hart,
  ResponsiveContainer,
  Tooltip,
  XAxis,
  YAxis,
  Cell,
} from "recharts";

function getNotificationTypeLabel(type: NotificationType): string {
  const labels: Record<NotificationType, string> = {
    price_change: "Prix",
    expiry_alert: "Péremption",
    urgent_request: "Urgent",
    interest_shown: "Intérêt",
    message_received: "Message",
    offer_update: "Offre",
    system_alert: "Système",
    stock_alert: "Stock",
    order_status: "Commande",
    payment_status: "Paiement",
    verification_status: "Vérification",
    market_update: "Marché",
    maintenance_alert: "Maintenance",
    security_alert: "Sécurité",
    product_recall: "Rappel",
    new_feature: "Nouveauté",
  };
  return labels[type] || type;
}

function getPriorityLabel(priority: NotificationPriority): string {
  const labels: Record<NotificationPriority, string> = {
    low: "Basse",
    medium: "Moyenne",
    high: "Haute",
    urgent: "Urgente",
    critical: "Critique",
  };
  return labels[priority] || priority;
}

function getPriorityColor(priority: NotificationPriority): string {
  const colors: Record<NotificationPriority, string> = {
    low: "#94a3b8",
    medium: "#60a5fa",
    high: "#fbbf24",
    urgent: "#f97316",
    critical: "#ef4444",
  };
  return colors[priority] || "#94a3b8";
}

export function NotificationAnalytics() {
  const { state } = useNotifications();
  const { analytics } = state;

  const typeData = Object.entries(analytics.typeBreakdown).map(([type, count]) => ({
    name: getNotificationTypeLabel(type as NotificationType),
    value: count,
  }));

  const priorityData = Object.entries(analytics.priorityBreakdown).map(([priority, count]) => ({
    name: getPriorityLabel(priority as NotificationPriority),
    value: count,
    color: getPriorityColor(priority as NotificationPriority),
  }));

  const engagementData = [
    { name: "Total", value: analytics.totalReceived },
    { name: "Lues", value: analytics.totalRead },
    { name: "Cliquées", value: analytics.totalClicked },
  ];

  return (
    <Card>
      <CardHeader>
        <CardTitle>Analytiques des notifications</CardTitle>
        <CardDescription>
          Statistiques et analyses des notifications
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs defaultValue="overview" className="space-y-4">
          <TabsList>
            <TabsTrigger value="overview">Vue d'ensemble</TabsTrigger>
            <TabsTrigger value="types">Types</TabsTrigger>
            <TabsTrigger value="priorities">Priorités</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Total des notifications
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">{analytics.totalReceived}</div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Taux de lecture
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {((analytics.totalRead / analytics.totalReceived) * 100).toFixed(1)}%
                  </div>
                </CardContent>
              </Card>
              <Card>
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium">
                    Taux d'engagement
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold">
                    {((analytics.totalClicked / analytics.totalReceived) * 100).toFixed(1)}%
                  </div>
                </CardContent>
              </Card>
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Engagement</CardTitle>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-[200px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <BarChart data={engagementData}>
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="name" />
                      <YAxis />
                      <Tooltip />
                      <Bar dataKey="value" fill="#3b82f6" />
                    </BarChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="types">
            <Card>
              <CardHeader>
                <CardTitle>Distribution par type</CardTitle>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={typeData}
                        dataKey="value"
                        nameKey="name"
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        fill="#3b82f6"
                        label
                      />
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="priorities">
            <Card>
              <CardHeader>
                <CardTitle>Distribution par priorité</CardTitle>
              </CardHeader>
              <CardContent className="pt-2">
                <div className="h-[300px]">
                  <ResponsiveContainer width="100%" height="100%">
                    <PieChart>
                      <Pie
                        data={priorityData}
                        dataKey="value"
                        nameKey="name"
                        cx="50%"
                        cy="50%"
                        outerRadius={80}
                        label
                      >
                        {priorityData.map((entry, index) => (
                          <Cell key={`cell-${index}`} fill={entry.color} />
                        ))}
                      </Pie>
                      <Tooltip />
                      <Legend />
                    </PieChart>
                  </ResponsiveContainer>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  );
} 