"use client";

import { <PERSON>, <PERSON>, Setting<PERSON> } from "lucide-react";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
  DropdownMenuSeparator,
} from "@/components/ui/dropdown-menu";
import { useNotifications } from "@/contexts/notification-context";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { cn } from "@/lib/utils";
import { formatDistanceToNow } from "date-fns";
import { fr } from "date-fns/locale";
import { useRouter } from "next/navigation";
import { useRealtimeNotifications } from "@/hooks/use-realtime-notifications";

export function NotificationList() {
  const router = useRouter();
  const { state, markAsRead, markAllAsRead, removeNotification, trackClick } = useNotifications();
  const { notifications, unreadCount } = state;

  console.log('NotificationList: Rendering with state:', {
    notifications,
    unreadCount,
    preferences: state.preferences
  });

  // Enable real-time notifications
  useRealtimeNotifications();

  const handleNotificationClick = (notification: typeof notifications[0]) => {
    console.log('NotificationList: Notification clicked:', notification);
    if (notification.status === "unread") {
      markAsRead(notification.id);
    }
    if (notification.data?.link) {
      trackClick(notification.id);
      router.push(notification.data.link);
    }
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="ghost" size="icon" className="relative">
          <Bell className="h-5 w-5" />
          {unreadCount > 0 && (
            <Badge
              variant="destructive"
              className="absolute -top-1 -right-1 h-5 w-5 flex items-center justify-center p-0 text-xs animate-pulse"
            >
              {unreadCount}
            </Badge>
          )}
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[450px]" forceMount>
        <div className="flex items-center justify-between px-4 py-2 border-b">
          <div className="flex items-center gap-2">
            <h2 className="font-semibold">Notifications</h2>
            {unreadCount > 0 && (
              <Badge variant="secondary" className="h-5">
                {unreadCount} nouvelle{unreadCount > 1 ? "s" : ""}
              </Badge>
            )}
          </div>
          <div className="flex items-center gap-2">
            {unreadCount > 0 && (
              <Button
                variant="ghost"
                size="sm"
                className="text-xs"
                onClick={markAllAsRead}
              >
                Tout marquer comme lu
              </Button>
            )}
            <Button
              variant="ghost"
              size="sm"
              className="h-8 w-8 p-0"
              onClick={() => router.push("/settings/notifications")}
            >
              <Settings className="h-4 w-4" />
            </Button>
          </div>
        </div>
        <ScrollArea className="h-[600px]">
          {notifications.length === 0 ? (
            <div className="px-4 py-8 text-center">
              <div className="text-4xl mb-3">🔔</div>
              <h3 className="font-medium text-base mb-1">Aucune notification</h3>
              <p className="text-sm text-muted-foreground">
                Vous serez notifié ici des mises à jour importantes
              </p>
            </div>
          ) : (
            notifications.map((notification) => (
              <DropdownMenuItem
                key={notification.id}
                className={cn(
                  "px-4 py-3 cursor-default focus:bg-muted/50 transition-colors",
                  notification.status === "unread" && "bg-muted/30",
                  notification.priority === "urgent" && "border-l-2 border-destructive",
                  notification.priority === "high" && "border-l-2 border-warning"
                )}
                onClick={() => handleNotificationClick(notification)}
              >
                <div className="flex items-start gap-3 relative group">
                  <div className="text-xl">
                    {getNotificationIcon(notification.type)}
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-start justify-between gap-2">
                      <div>
                        <span className="font-medium">{notification.title}</span>
                        <div className="flex items-center gap-2 mt-1">
                          <Badge
                            variant="outline"
                            className={cn("h-5 text-[10px]", getPriorityColor(notification.priority))}
                          >
                            {notification.priority}
                          </Badge>
                          {notification.status === "unread" && (
                            <Badge variant="secondary" className="h-5 text-[10px]">
                              Nouveau
                            </Badge>
                          )}
                        </div>
                      </div>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="h-6 w-6 p-0 opacity-0 group-hover:opacity-100 transition-opacity"
                        onClick={(e) => {
                          e.stopPropagation();
                          removeNotification(notification.id);
                        }}
                      >
                        <X className="h-4 w-4" />
                      </Button>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {notification.message}
                    </p>
                    <div className="flex items-center justify-between">
                      <p className="text-xs text-muted-foreground">
                        {formatDistanceToNow(new Date(notification.timestamp), {
                          addSuffix: true,
                          locale: fr,
                        })}
                      </p>
                      {notification.data?.link && (
                        <Button
                          variant="ghost"
                          size="sm"
                          className="h-6 px-2 text-xs"
                          onClick={(e) => {
                            e.stopPropagation();
                            trackClick(notification.id);
                            router.push(notification.data!.link!);
                          }}
                        >
                          Voir détails
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              </DropdownMenuItem>
            ))
          )}
        </ScrollArea>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}

function getNotificationIcon(type: string) {
  switch (type) {
    case "price_change":
      return "💰";
    case "expiry_alert":
      return "⚠️";
    case "urgent_request":
      return "🚨";
    case "interest_shown":
      return "👀";
    case "message_received":
      return "💬";
    case "offer_update":
      return "📦";
    case "system_alert":
      return "🔔";
    case "stock_alert":
      return "📊";
    case "order_status":
      return "🛍️";
    case "payment_status":
      return "💳";
    case "verification_status":
      return "✅";
    case "market_update":
      return "📈";
    case "maintenance_alert":
      return "🔧";
    case "security_alert":
      return "🔒";
    case "product_recall":
      return "⚡";
    case "new_feature":
      return "✨";
    default:
      return "🔔";
  }
}

function getPriorityColor(priority: string) {
  switch (priority) {
    case "critical":
      return "text-destructive border-destructive";
    case "urgent":
      return "text-destructive border-destructive";
    case "high":
      return "text-warning border-warning";
    case "medium":
      return "text-info border-info";
    case "low":
      return "text-muted-foreground";
    default:
      return "";
  }
}