"use client";

import { useNotifications } from "@/contexts/notification-context";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Bell, Mail, Volume2, Clock, BellOff } from "lucide-react";
import { useState } from "react";

export function NotificationPreferences() {
  const { state, updatePreferences } = useNotifications();
  const { preferences } = state;

  const [quietHoursStart, setQuietHoursStart] = useState(preferences.quietHoursStart || "22:00");
  const [quietHoursEnd, setQuietHoursEnd] = useState(preferences.quietHoursEnd || "08:00");
  const [archiveDuration, setArchiveDuration] = useState(preferences.archiveDuration);

  const handlePreferenceChange = (key: keyof typeof preferences, value: boolean | number | string) => {
    updatePreferences({ [key]: value });
  };

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle>Préférences de notification</CardTitle>
          <CardDescription>
            Gérez vos préférences de notification pour rester informé de manière efficace
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          {/* Canaux de notification */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Bell className="h-4 w-4" />
              Canaux de notification
            </h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Notifications push</Label>
                  <p className="text-sm text-muted-foreground">
                    Recevoir des notifications dans l'application
                  </p>
                </div>
                <Switch
                  checked={preferences.pushNotifications}
                  onCheckedChange={(checked) => handlePreferenceChange("pushNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Notifications par email</Label>
                  <p className="text-sm text-muted-foreground">
                    Recevoir des notifications par email
                  </p>
                </div>
                <Switch
                  checked={preferences.emailNotifications}
                  onCheckedChange={(checked) => handlePreferenceChange("emailNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Son des notifications</Label>
                  <p className="text-sm text-muted-foreground">
                    Jouer un son lors des notifications
                  </p>
                </div>
                <Switch
                  checked={preferences.soundEnabled}
                  onCheckedChange={(checked) => handlePreferenceChange("soundEnabled", checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Types de notification */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Mail className="h-4 w-4" />
              Types de notification
            </h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Changements de prix</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les changements de prix
                  </p>
                </div>
                <Switch
                  checked={preferences.priceChanges}
                  onCheckedChange={(checked) => handlePreferenceChange("priceChanges", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Alertes de péremption</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les produits proches de la péremption
                  </p>
                </div>
                <Switch
                  checked={preferences.expiryAlerts}
                  onCheckedChange={(checked) => handlePreferenceChange("expiryAlerts", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Demandes urgentes</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les demandes urgentes
                  </p>
                </div>
                <Switch
                  checked={preferences.urgentRequests}
                  onCheckedChange={(checked) => handlePreferenceChange("urgentRequests", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Marques d'intérêt</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications quand quelqu'un est intéressé par vos produits
                  </p>
                </div>
                <Switch
                  checked={preferences.interestNotifications}
                  onCheckedChange={(checked) => handlePreferenceChange("interestNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Messages</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les nouveaux messages
                  </p>
                </div>
                <Switch
                  checked={preferences.messageNotifications}
                  onCheckedChange={(checked) => handlePreferenceChange("messageNotifications", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Mises à jour d'offres</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les mises à jour d'offres
                  </p>
                </div>
                <Switch
                  checked={preferences.offerUpdates}
                  onCheckedChange={(checked) => handlePreferenceChange("offerUpdates", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Alertes système</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications système importantes
                  </p>
                </div>
                <Switch
                  checked={preferences.systemAlerts}
                  onCheckedChange={(checked) => handlePreferenceChange("systemAlerts", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Alertes de stock</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les niveaux de stock
                  </p>
                </div>
                <Switch
                  checked={preferences.stockAlerts}
                  onCheckedChange={(checked) => handlePreferenceChange("stockAlerts", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Mises à jour de commandes</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les mises à jour de commandes
                  </p>
                </div>
                <Switch
                  checked={preferences.orderUpdates}
                  onCheckedChange={(checked) => handlePreferenceChange("orderUpdates", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Mises à jour de paiement</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les mises à jour de paiement
                  </p>
                </div>
                <Switch
                  checked={preferences.paymentUpdates}
                  onCheckedChange={(checked) => handlePreferenceChange("paymentUpdates", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Mises à jour de vérification</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les mises à jour de vérification
                  </p>
                </div>
                <Switch
                  checked={preferences.verificationUpdates}
                  onCheckedChange={(checked) => handlePreferenceChange("verificationUpdates", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Mises à jour du marché</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les mises à jour du marché
                  </p>
                </div>
                <Switch
                  checked={preferences.marketUpdates}
                  onCheckedChange={(checked) => handlePreferenceChange("marketUpdates", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Alertes de maintenance</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les maintenances prévues
                  </p>
                </div>
                <Switch
                  checked={preferences.maintenanceAlerts}
                  onCheckedChange={(checked) => handlePreferenceChange("maintenanceAlerts", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Alertes de sécurité</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les alertes de sécurité
                  </p>
                </div>
                <Switch
                  checked={preferences.securityAlerts}
                  onCheckedChange={(checked) => handlePreferenceChange("securityAlerts", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Rappels de produits</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les rappels de produits
                  </p>
                </div>
                <Switch
                  checked={preferences.productRecalls}
                  onCheckedChange={(checked) => handlePreferenceChange("productRecalls", checked)}
                />
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Nouvelles fonctionnalités</Label>
                  <p className="text-sm text-muted-foreground">
                    Notifications pour les nouvelles fonctionnalités
                  </p>
                </div>
                <Switch
                  checked={preferences.featureUpdates}
                  onCheckedChange={(checked) => handlePreferenceChange("featureUpdates", checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Paramètres avancés */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <BellOff className="h-4 w-4" />
              Mode silencieux
            </h3>
            <div className="grid gap-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Heures silencieuses</Label>
                    <p className="text-sm text-muted-foreground">
                      Période pendant laquelle les notifications sont silencieuses
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Input
                      type="time"
                      value={quietHoursStart}
                      onChange={(e) => {
                        setQuietHoursStart(e.target.value);
                        handlePreferenceChange("quietHoursStart", e.target.value);
                      }}
                      className="w-32"
                    />
                    <span>à</span>
                    <Input
                      type="time"
                      value={quietHoursEnd}
                      onChange={(e) => {
                        setQuietHoursEnd(e.target.value);
                        handlePreferenceChange("quietHoursEnd", e.target.value);
                      }}
                      className="w-32"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <Separator />

          {/* Paramètres de son */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Volume2 className="h-4 w-4" />
              Sons et groupement
            </h3>
            <div className="grid gap-4">
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Son personnalisé</Label>
                  <p className="text-sm text-muted-foreground">
                    Choisir un son pour les notifications
                  </p>
                </div>
                <select
                  className="w-48 rounded-md border border-input bg-background px-3 py-2 text-sm ring-offset-background"
                  value={preferences.notificationSound || "default"}
                  onChange={(e) => handlePreferenceChange("notificationSound", e.target.value)}
                >
                  <option value="default">Son par défaut</option>
                  <option value="bell">Cloche</option>
                  <option value="chime">Carillon</option>
                  <option value="notification">Notification</option>
                  <option value="alert">Alerte</option>
                </select>
              </div>
              <div className="flex items-center justify-between">
                <div className="space-y-0.5">
                  <Label>Grouper les notifications similaires</Label>
                  <p className="text-sm text-muted-foreground">
                    Regrouper les notifications du même type
                  </p>
                </div>
                <Switch
                  checked={preferences.groupSimilar}
                  onCheckedChange={(checked) => handlePreferenceChange("groupSimilar", checked)}
                />
              </div>
            </div>
          </div>

          <Separator />

          {/* Paramètres d'archivage */}
          <div className="space-y-4">
            <h3 className="font-medium flex items-center gap-2">
              <Clock className="h-4 w-4" />
              Archivage automatique
            </h3>
            <div className="grid gap-4">
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Archiver après</Label>
                    <p className="text-sm text-muted-foreground">
                      Archiver automatiquement les notifications après X jours
                    </p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Slider
                      value={[archiveDuration]}
                      onValueChange={([value]) => {
                        setArchiveDuration(value);
                        handlePreferenceChange("archiveDuration", value);
                      }}
                      max={90}
                      min={1}
                      step={1}
                      className="w-[120px]"
                    />
                    <span className="w-12 text-sm">{archiveDuration} jours</span>
                  </div>
                </div>
                <div className="flex items-center justify-between">
                  <div className="space-y-0.5">
                    <Label>Archivage automatique</Label>
                    <p className="text-sm text-muted-foreground">
                      Archiver automatiquement les anciennes notifications
                    </p>
                  </div>
                  <Switch
                    checked={preferences.autoArchive}
                    onCheckedChange={(checked) => handlePreferenceChange("autoArchive", checked)}
                  />
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
} 