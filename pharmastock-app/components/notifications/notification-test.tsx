"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { useAuth } from "@/contexts/auth-context";
import {
  sendPriceChangeNotification,
  sendExpiryAlertNotification,
  sendUrgentRequestNotification,
  sendInterestNotification,
  sendMessageNotification,
  sendStockAlertNotification,
  sendOrderStatusNotification,
  sendPaymentStatusNotification,
  sendVerificationStatusNotification,
  sendMarketUpdateNotification,
  sendMaintenanceAlertNotification,
  sendSecurityAlertNotification,
  sendProductRecallNotification,
  sendNewFeatureNotification,
} from "@/lib/notifications";

export function NotificationTest() {
  const { user } = useAuth();

  if (!user) return null;

  const sendTestNotifications = async () => {
    try {
      // Price change notification (system)
      await sendPriceChangeNotification({
        recipientId: user.id,
        productName: "Paracétamol 500mg",
        oldPrice: 25,
        newPrice: 20,
        productId: "test-product-1",
      });

      // Expiry alert notification (system)
      await sendExpiryAlertNotification({
        recipientId: user.id,
        productName: "Amoxicilline 1g",
        expiryDate: "2024-06-30",
        productId: "test-product-2",
      });

      // Urgent request notification (from another pharmacy)
      await sendUrgentRequestNotification({
        recipientId: user.id,
        senderId: "test-pharmacy-id", // Different pharmacy
        pharmacyName: "Pharmacie Test",
        productName: "Insuline Rapide",
        requestId: "test-request-1",
      });

      // Interest notification (from another pharmacy)
      await sendInterestNotification({
        recipientId: user.id,
        senderId: "test-pharmacy-id", // Different pharmacy
        pharmacyName: "Pharmacie Test",
        productName: "Vitamine C 1000mg",
        productId: "test-product-3",
      });

      // Message notification (from another pharmacy)
      await sendMessageNotification({
        recipientId: user.id,
        senderId: "test-pharmacy-id", // Different pharmacy
        senderName: "Pharmacie Test",
        messagePreview: "Bonjour, je suis intéressé par votre produit...",
        messageId: "test-message-1",
      });

      // Stock alert notification (system)
      await sendStockAlertNotification({
        recipientId: user.id,
        productName: "Oméprazole 20mg",
        status: "bas",
        currentQuantity: 5,
        threshold: 10,
        productId: "test-product-4",
      });

      // Order status notification (system)
      await sendOrderStatusNotification({
        recipientId: user.id,
        orderId: "test-order-1",
        status: "en cours de livraison",
        details: "Votre commande sera livrée demain",
      });

      // Payment status notification (system)
      await sendPaymentStatusNotification({
        recipientId: user.id,
        orderId: "test-order-1",
        status: "confirmé",
        amount: 1500,
      });

      // Verification status notification (system)
      await sendVerificationStatusNotification({
        recipientId: user.id,
        status: "vérifié",
        details: "Votre compte a été vérifié avec succès",
      });

      // Market update notification (system)
      await sendMarketUpdateNotification({
        recipientId: user.id,
        title: "Nouveau produit disponible",
        summary: "Un nouveau médicament générique est maintenant disponible",
        category: "Nouveautés",
      });

      // Maintenance alert notification (system)
      await sendMaintenanceAlertNotification({
        recipientId: user.id,
        title: "Maintenance planifiée",
        scheduledTime: "2024-03-25 02:00",
        duration: "2 heures",
        impact: "Le système sera indisponible",
      });

      // Security alert notification (system)
      await sendSecurityAlertNotification({
        recipientId: user.id,
        title: "Mise à jour de sécurité importante",
        details: "Une nouvelle mise à jour de sécurité est disponible",
        actionRequired: true,
      });

      // Product recall notification (system)
      await sendProductRecallNotification({
        recipientId: user.id,
        productName: "Médicament Test",
        batchNumber: "LOT-123",
        reason: "Problème de qualité détecté",
        actionRequired: "Retourner le produit immédiatement",
      });

      // New feature notification (system)
      await sendNewFeatureNotification({
        recipientId: user.id,
        title: "Nouvelle fonctionnalité : Chat en direct",
        description: "Communiquez en temps réel avec d'autres pharmacies",
        learnMoreLink: "/features/chat",
      });

    } catch (error) {
      console.error("Error sending test notifications:", error);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Test des notifications</CardTitle>
        <CardDescription>
          Envoyez des notifications de test pour vérifier le système
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        <Button onClick={sendTestNotifications}>
          Envoyer toutes les notifications de test
        </Button>
      </CardContent>
    </Card>
  );
}