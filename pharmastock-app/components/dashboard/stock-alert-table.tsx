"use client";

import { useState, useEffect } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Alert<PERSON>riangle, ArrowUpRight, Share2 } from "lucide-react";
import { useWebSocket } from "@/contexts/websocket-context";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

interface StockAlert {
  id: number;
  name: string;
  quantity: number;
  expiryDate: string;
  daysUntilExpiry: number;
  priority: 'high' | 'medium' | 'low';
  lotNumber?: string;
  manufacturer?: string;
}

const priorityStyles = {
  high: "bg-red-50 text-red-800 dark:bg-red-900/50 dark:text-red-300 border-l-4 border-red-500 shadow-sm",
  medium: "bg-amber-50 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300 border-l-4 border-amber-500 shadow-sm",
  low: "bg-emerald-50 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-300 border-l-4 border-emerald-500 shadow-sm"
};

const priorityLabels = {
  high: "Périme bientôt",
  medium: "À surveiller",
  low: "Moyen terme"
};

const daysUntilExpiryLabel = (days: number) => {
  if (days === 0) return "Expire aujourd'hui";
  if (days === 1) return "Expire demain";
  if (days < 7) return `Expire dans ${days} jours`;
  if (days < 30) return `Expire dans ${Math.floor(days / 7)} semaines`;
  return `Expire dans ${Math.floor(days / 30)} mois`;
};

export function StockAlertTable() {
  const { sendMessage, subscribeToEvent, unsubscribeFromEvent } = useWebSocket();
  const [stockAlerts, setStockAlerts] = useState<StockAlert[]>([]);

  useEffect(() => {
    const handleStockAlert = (alert: StockAlert) => {
      setStockAlerts(prev => {
        const exists = prev.find(a => a.id === alert.id);
        if (exists) {
          return prev.map(a => a.id === alert.id ? alert : a);
        }
        return [alert, ...prev];
      });
    };

    const handleStockUpdate = (update: { id: number; resolved: boolean }) => {
      if (update.resolved) {
        setStockAlerts(prev => prev.filter(alert => alert.id !== update.id));
      }
    };

    subscribeToEvent('stock_alert', handleStockAlert);
    subscribeToEvent('stock_update', handleStockUpdate);

    // Initial data fetch
    sendMessage('get_stock_alerts', {});

    return () => {
      unsubscribeFromEvent('stock_alert', handleStockAlert);
      unsubscribeFromEvent('stock_update', handleStockUpdate);
    };
  }, []);

  const handlePublish = (alertId: number) => {
    sendMessage('publish_stock', { alertId });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold">Médicaments proche-périmés</h2>
          <p className="text-sm text-muted-foreground">
            Gérez vos médicaments proche de la date de péremption
          </p>
        </div>
        <Badge
          variant="outline"
          className={stockAlerts.length > 0
            ? "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:border-red-800"
            : "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:border-emerald-800"
          }
        >
          {stockAlerts.length} {stockAlerts.length > 1 ? 'médicaments' : 'médicament'}
        </Badge>
      </div>

      <div className="border rounded-lg overflow-hidden bg-card dark:bg-card/50">
        <Table>
          <TableHeader>
            <TableRow className="bg-muted/50 dark:bg-muted/20">
              <TableHead className="font-semibold">Médicament</TableHead>
              <TableHead className="text-center font-semibold">N° de lot</TableHead>
              <TableHead className="text-center font-semibold">Quantité</TableHead>
              <TableHead className="w-[150px] font-semibold">Date de péremption</TableHead>
              <TableHead className="text-right w-[100px] font-semibold">Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {stockAlerts.map((alert) => (
              <TableRow key={alert.id} className="hover:bg-muted/50 dark:hover:bg-muted/20">
                <TableCell>
                  <div>
                    <div className="font-semibold flex items-center gap-2">
                      <AlertTriangle className={`w-4 h-4 ${alert.priority === 'high' ? 'text-red-500 dark:text-red-400' :
                          alert.priority === 'medium' ? 'text-amber-500 dark:text-amber-400' :
                            'text-emerald-500 dark:text-emerald-400'
                        }`} />
                      {alert.name}
                    </div>
                    {alert.manufacturer && (
                      <div className="text-sm text-muted-foreground">
                        {alert.manufacturer}
                      </div>
                    )}
                  </div>
                </TableCell>
                <TableCell className="text-center font-medium">
                  {alert.lotNumber || "N/A"}
                </TableCell>
                <TableCell className="text-center">
                  <div className="font-medium">
                    {alert.quantity.toLocaleString('fr-MA')} unités
                  </div>
                </TableCell>
                <TableCell>
                  <Badge
                    variant="secondary"
                    className={`${priorityStyles[alert.priority]} whitespace-nowrap`}
                  >
                    {daysUntilExpiryLabel(alert.daysUntilExpiry)}
                  </Badge>
                </TableCell>
                <TableCell className="text-right">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handlePublish(alert.id)}
                    className="flex items-center gap-2 w-full justify-center"
                  >
                    <Share2 className="w-4 h-4" />
                    <span>Partager</span>
                  </Button>
                </TableCell>
              </TableRow>
            ))}
            {stockAlerts.length === 0 && (
              <TableRow>
                <TableCell colSpan={5} className="h-32 text-center">
                  <div className="flex flex-col items-center justify-center text-muted-foreground">
                    <AlertTriangle className="w-8 h-8 mb-2 text-emerald-500 dark:text-emerald-400" />
                    <p className="font-medium">Aucun médicament proche-périmé</p>
                    <p className="text-sm">Tous vos médicaments sont dans les délais</p>
                  </div>
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>
    </div>
  );
}