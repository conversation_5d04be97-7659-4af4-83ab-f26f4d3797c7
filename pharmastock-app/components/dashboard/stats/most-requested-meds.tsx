"use client";

import { useState, useEffect } from "react";
import { useWebSocket } from "@/contexts/websocket-context";
import { Bar } from "react-chartjs-2";
import {
  Chart as ChartJS,
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
} from 'chart.js';
import { Badge } from "@/components/ui/badge";
import { TrendingUp, TrendingDown, Minus } from "lucide-react";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

ChartJS.register(
  CategoryScale,
  LinearScale,
  BarElement,
  Title,
  Tooltip,
  Legend
);

interface MedicationDemand {
  name: string;
  requests: number;
  trend: number; // percentage change
  category: string;
  isShortage: boolean;
}

const timeRanges = {
  "24h": "24 heures",
  "7d": "7 jours",
  "30d": "30 jours"
};

export function MostRequestedMeds() {
  const { sendMessage, subscribeToEvent, unsubscribeFromEvent } = useWebSocket();
  const [timeRange, setTimeRange] = useState<string>("7d");
  const [data, setData] = useState<MedicationDemand[]>([]);

  useEffect(() => {
    const handleDemandUpdate = (newData: MedicationDemand[]) => {
      setData(newData);
    };

    subscribeToEvent('demand_stats', handleDemandUpdate);
    sendMessage('get_demand_stats', { timeRange });

    return () => {
      unsubscribeFromEvent('demand_stats', handleDemandUpdate);
    };
  }, [timeRange]);

  const chartData = {
    labels: data.map(item => item.name),
    datasets: [
      {
        label: 'Nombre de demandes',
        data: data.map(item => item.requests),
        backgroundColor: data.map(item =>
          item.isShortage ? 'rgba(239, 68, 68, 0.5)' : 'rgba(59, 130, 246, 0.5)'
        ),
        borderColor: data.map(item =>
          item.isShortage ? 'rgb(239, 68, 68)' : 'rgb(59, 130, 246)'
        ),
        borderWidth: 1,
      },
    ],
  };

  const options = {
    responsive: true,
    maintainAspectRatio: false,
    plugins: {
      legend: {
        display: false,
      },
      tooltip: {
        callbacks: {
          label: (context: any) => {
            const index = context.dataIndex;
            const item = data[index];
            const lines = [
              `Demandes: ${item.requests}`,
              `Catégorie: ${item.category}`,
              `Évolution: ${item.trend > 0 ? '+' : ''}${item.trend}%`,
              item.isShortage ? '⚠️ Pénurie potentielle' : ''
            ].filter(Boolean);
            return lines;
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        ticks: {
          precision: 0
        }
      }
    },
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <Select
            value={timeRange}
            onValueChange={setTimeRange}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Sélectionner la période" />
            </SelectTrigger>
            <SelectContent>
              {Object.entries(timeRanges).map(([value, label]) => (
                <SelectItem key={value} value={value}>
                  {label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="h-[300px]">
        {data.length > 0 ? (
          <Bar data={chartData} options={options} />
        ) : (
          <div className="flex items-center justify-center h-full text-muted-foreground">
            Aucune donnée disponible
          </div>
        )}
      </div>

      <div className="space-y-2">
        {data.slice(0, 3).map((item) => (
          <div key={item.name} className="flex items-center justify-between p-2 rounded-lg border bg-card">
            <div>
              <div className="font-medium">{item.name}</div>
              <div className="text-sm text-muted-foreground">{item.category}</div>
            </div>
            <div className="flex items-center gap-2">
              {item.isShortage && (
                <Badge variant="destructive" className="animate-pulse">
                  Pénurie
                </Badge>
              )}
              <Badge variant="outline" className={
                item.trend > 0 ? "text-emerald-600 bg-emerald-50" :
                  item.trend < 0 ? "text-red-600 bg-red-50" :
                    "text-gray-600 bg-gray-50"
              }>
                <span className="flex items-center gap-1">
                  {item.trend > 0 ? <TrendingUp className="w-3 h-3" /> :
                    item.trend < 0 ? <TrendingDown className="w-3 h-3" /> :
                      <Minus className="w-3 h-3" />}
                  {item.trend > 0 ? '+' : ''}{item.trend}%
                </span>
              </Badge>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
} 