"use client";

import { useState, useEffect } from "react";
import { useWebSocket } from "@/contexts/websocket-context";
import { useSupabase } from "@/contexts/supabase-context";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { MapPin, Timer, Percent, ExternalLink } from "lucide-react";

interface LocalOffer {
  id: number;
  medication: string;
  pharmacy: string;
  originalPrice: number;
  discountedPrice: number;
  discount: number;
  distance: number;
  expiryDate: string;
  daysUntilExpiry: number;
  quantity: number;
}

const sortOptions = {
  "discount": "Plus grande réduction",
  "expiry": "Expiration proche",
  "distance": "Plus proche",
  "latest": "Plus récent"
};

const radiusOptions = {
  "2": "2 km",
  "5": "5 km",
  "10": "10 km",
  "20": "20 km",
  "50": "50 km"
};

export function LocalOffers() {
  const { sendMessage, subscribeToEvent, unsubscribeFromEvent } = useWebSocket();
  const { supabase, pharmacy } = useSupabase();
  const [sortBy, setSortBy] = useState<string>("distance");
  const [radius, setRadius] = useState<string>("2");
  const [offers, setOffers] = useState<LocalOffer[]>([]);
  const [isLoading, setIsLoading] = useState(true);

  // Fetch initial data from the database
  useEffect(() => {
    const fetchOffers = async () => {
      if (!pharmacy?.id) return;

      try {
        setIsLoading(true);
        // First get active listings
        const { data: listingsData, error: listingsError } = await supabase
          .from('listings')
          .select(`
            *,
            products:product_id (*),
            pharmacy:pharmacy_id (*)
          `)
          .eq('status', 'active')
          .neq('pharmacy_id', pharmacy.id); // Exclude own pharmacy's listings

        if (listingsError) throw listingsError;

        if (!listingsData) {
          setOffers([]);
          return;
        }

        // Transform the data
        const transformedOffers: LocalOffer[] = listingsData.map(listing => {
          // TODO: Calculate actual distance using pharmacy coordinates
          const distance = Math.random() * 50; // Temporary random distance for demo
          const originalPrice = listing.products.original_price || listing.products.unit_price || 0;
          const discountedPrice = listing.price_per_unit || originalPrice;

          return {
            id: listing.id,
            medication: listing.products.name,
            pharmacy: listing.pharmacy.business_name,
            originalPrice,
            discountedPrice,
            discount: originalPrice > 0 ? Math.round(((originalPrice - discountedPrice) / originalPrice) * 100) : 0,
            distance,
            expiryDate: listing.expires_at || listing.products.expiry_date,
            daysUntilExpiry: Math.ceil((new Date(listing.expires_at || listing.products.expiry_date).getTime() - Date.now()) / (1000 * 60 * 60 * 24)),
            quantity: listing.quantity
          };
        });

        // Filter by radius
        const filteredOffers = transformedOffers.filter(
          offer => offer.distance <= parseInt(radius)
        );

        // Sort the offers
        const sortedOffers = sortOffers(filteredOffers, sortBy);
        setOffers(sortedOffers);
      } catch (error) {
        console.error('Error fetching offers:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchOffers();
  }, [pharmacy?.id, sortBy, radius]);

  // Handle real-time updates
  useEffect(() => {
    const handleOffersUpdate = (newOffers: LocalOffer[]) => {
      setOffers(prevOffers => {
        const updatedOffers = [...prevOffers];
        newOffers
          .filter(offer => offer.distance <= parseInt(radius))
          .forEach(newOffer => {
            const index = updatedOffers.findIndex(o => o.id === newOffer.id);
            if (index >= 0) {
              updatedOffers[index] = newOffer;
            } else {
              updatedOffers.push(newOffer);
            }
          });
        return sortOffers(updatedOffers, sortBy);
      });
    };

    subscribeToEvent('local_offers', handleOffersUpdate);
    sendMessage('get_local_offers', { sortBy, radius: parseInt(radius) });

    return () => {
      unsubscribeFromEvent('local_offers', handleOffersUpdate);
    };
  }, [sortBy, radius]);

  const sortOffers = (offersToSort: LocalOffer[], sortType: string) => {
    return [...offersToSort].sort((a, b) => {
      switch (sortType) {
        case 'discount':
          return b.discount - a.discount;
        case 'expiry':
          return a.daysUntilExpiry - b.daysUntilExpiry;
        case 'distance':
          return a.distance - b.distance;
        case 'latest':
          return b.id - a.id;
        default:
          return 0;
      }
    });
  };

  const handleViewOffer = (offerId: number) => {
    sendMessage('view_offer', { offerId });
  };

  if (isLoading) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <p className="font-medium">Chargement des offres...</p>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center gap-4">
        <Select
          value={radius}
          onValueChange={setRadius}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Rayon" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(radiusOptions).map(([value, label]) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>

        <Select
          value={sortBy}
          onValueChange={setSortBy}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Trier par" />
          </SelectTrigger>
          <SelectContent>
            {Object.entries(sortOptions).map(([value, label]) => (
              <SelectItem key={value} value={value}>
                {label}
              </SelectItem>
            ))}
          </SelectContent>
        </Select>
      </div>

      <div className="space-y-4">
        {offers.length > 0 ? (
          offers.map((offer) => (
            <div
              key={offer.id}
              className="p-4 rounded-lg border bg-card hover:shadow-md transition-shadow"
            >
              <div className="flex justify-between items-start mb-3">
                <div>
                  <h3 className="font-semibold">{offer.medication}</h3>
                  <p className="text-sm text-muted-foreground">{offer.pharmacy}</p>
                </div>
                <Badge
                  variant="secondary"
                  className="bg-emerald-50 text-emerald-700 dark:bg-emerald-900/20 dark:text-emerald-300"
                >
                  <Percent className="w-3 h-3 mr-1" />
                  -{offer.discount}%
                </Badge>
              </div>

              <div className="grid grid-cols-2 gap-4 mb-4">
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Prix original</p>
                  <p className="font-medium line-through">
                    {offer.originalPrice.toLocaleString('fr-FR')} DH
                  </p>
                </div>
                <div className="space-y-1">
                  <p className="text-sm text-muted-foreground">Prix réduit</p>
                  <p className="font-medium text-emerald-600">
                    {offer.discountedPrice.toLocaleString('fr-FR')} DH
                  </p>
                </div>
              </div>

              <div className="flex items-center gap-4 text-sm text-muted-foreground mb-4">
                <div className="flex items-center gap-1">
                  <MapPin className="w-4 h-4" />
                  {offer.distance.toFixed(1)} km
                </div>
                <div className="flex items-center gap-1">
                  <Timer className="w-4 h-4" />
                  {offer.daysUntilExpiry < 30
                    ? `Expire dans ${offer.daysUntilExpiry} jours`
                    : `Expire le ${new Date(offer.expiryDate).toLocaleDateString('fr-FR')}`
                  }
                </div>
              </div>

              <div className="flex justify-between items-center">
                <p className="text-sm font-medium">
                  {offer.quantity} unités disponibles
                </p>
                <Button
                  variant="outline"
                  size="sm"
                  className="gap-2"
                  onClick={() => handleViewOffer(offer.id)}
                >
                  <span>Voir l&apos;offre</span>
                  <ExternalLink className="w-4 h-4" />
                </Button>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-12 text-muted-foreground">
            <p className="font-medium">Aucune offre disponible dans ce rayon</p>
            <p className="text-sm">Essayez d&apos;augmenter la distance de recherche</p>
          </div>
        )}
      </div>
    </div>
  );
} 