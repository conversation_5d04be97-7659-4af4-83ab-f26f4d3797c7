"use client";

import { useState, useEffect, useCallback } from "react";
import { useSupabase } from "@/contexts/supabase-context";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { toast } from "sonner";
import { TrendingUp, TrendingDown, AlertTriangle, Clock, Edit2 } from "lucide-react";
import {
  Tooltip,
  TooltipContent,
  TooltipTrigger,
  TooltipProvider,
} from "@/components/ui/tooltip";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

// ============================================================================
// Types & Interfaces
// ============================================================================
interface Product {
  name: string;
  expiry_date: string;
  original_price: number;
}

interface Pharmacy {
  name: string;
}

interface Reservation {
  id: string;
  pharmacy: Pharmacy;
  quantity: number;
  status: 'pending' | 'accepted' | 'rejected';
  created_at: string;
}

interface DatabaseListingResponse {
  id: string;
  quantity: number;
  price_per_unit: number;
  minimum_order: number;
  status: 'active' | 'inactive' | 'sold';
  created_at: string;
  expires_at: string;
  views: number;
  interested_parties: number;
  last_view_at: string | null;
  product: Array<{
    name: string;
    expiry_date: string;
    original_price: number;
  }>;
}

interface Listing {
  id: string;
  product: {
    name: string;
    expiry_date: string;
    original_price: number;
  };
  quantity: number;
  price_per_unit: number;
  status: 'active' | 'inactive' | 'sold';
  created_at: string;
  expires_at: string;
  minimum_order: number;
  views: number;
  interested_parties: number;
  last_view_at: string | null;
  reservations?: {
    id: string;
    pharmacy_name: string;
    quantity: number;
    status: 'pending' | 'accepted' | 'rejected';
    created_at: string;
  }[];
}

// ============================================================================
// Main Component
// ============================================================================
export function ActiveListings() {
  // Context and state
  const { supabase, pharmacy } = useSupabase();
  const [listings, setListings] = useState<Listing[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [page, setPage] = useState(1);
  const [hasMore, setHasMore] = useState(true);
  const [selectedListings, setSelectedListings] = useState<string[]>([]);
  const [sortBy, setSortBy] = useState<{
    field: keyof Listing | 'product.name' | 'views' | 'interested_parties';
    direction: 'asc' | 'desc';
  }>({ field: 'created_at', direction: 'desc' });
  const [statusFilter, setStatusFilter] = useState<'all' | 'active' | 'inactive' | 'sold'>('all');
  const [expiryFilter, setExpiryFilter] = useState<'all' | '30' | '90' | 'expired'>('all');
  const ITEMS_PER_PAGE = 10;
  const [error, setError] = useState<string | null>(null);

  // Short-circuit for users with no pharmacy
  if (!pharmacy?.id) {
    return (
      <div className="flex flex-col items-center justify-center py-12">
        <img src="/icons/icon-192x192.png" alt="No pharmacy" className="w-20 h-20 mb-4 opacity-60" />
        <h2 className="text-xl font-semibold mb-2 text-muted-foreground">Aucune pharmacie rattachée</h2>
        <p className="text-muted-foreground mb-4">Vous devez être rattaché à une pharmacie pour voir vos annonces actives.</p>
      </div>
    );
  }

  // Memoize fetchListings to prevent infinite loop
  const fetchListings = useCallback(async (currentPage = 1) => {
    if (!pharmacy?.id) {
      console.log('No pharmacy ID available');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      console.log('Fetching listings for pharmacy:', pharmacy.id);

      // Calculate pagination
      const pageSize = 10;
      const from = (currentPage - 1) * pageSize;
      const to = from + pageSize - 1;

      // Fetch listings with product data and count
      const { data: listingsData, count, error: listingsError } = await supabase
        .from('listings')
        .select(`
          id,
          quantity,
          price_per_unit,
          minimum_order,
          status,
          created_at,
          expires_at,
          views,
          interested_parties,
          last_view_at,
          product (
            name,
            expiry_date,
            original_price
          )
        `, { count: 'exact' })
        .eq('pharmacy_id', pharmacy.id)
        .order('created_at', { ascending: false })
        .range(from, to);

      if (listingsError) {
        console.error('Error fetching listings:', listingsError);
        setError('Failed to fetch listings');
        return;
      }

      // Transform and validate the data
      const transformedListings = (listingsData || []).map(item => {
        if (!item?.product?.[0]) return null;

        return {
          id: item.id,
          product: {
            name: item.product[0].name,
            expiry_date: item.product[0].expiry_date,
            original_price: Number(item.product[0].original_price)
          },
          quantity: Number(item.quantity),
          price_per_unit: Number(item.price_per_unit),
          minimum_order: Number(item.minimum_order),
          status: item.status,
          created_at: item.created_at,
          expires_at: item.expires_at,
          views: Number(item.views) || 0,
          interested_parties: Number(item.interested_parties) || 0,
          last_view_at: item.last_view_at,
          reservations: []
        };
      }).filter(Boolean) as Listing[];

      console.log('Transformed listings:', transformedListings);

      setListings(prev => currentPage === 1 ? transformedListings : [...prev, ...transformedListings]);
      setHasMore(count !== null && from + transformedListings.length < count);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Unknown error occurred';
      console.error('Error in fetchListings:', { error, message: errorMessage });
      setError('Failed to fetch listings');
    } finally {
      setIsLoading(false);
    }
  }, [pharmacy?.id, supabase]); // Only depend on pharmacy.id and supabase

  // Initial fetch
  useEffect(() => {
    if (pharmacy?.id) {
      fetchListings(1);
    }
  }, [pharmacy?.id, fetchListings]); // Remove page from dependencies

  // ============================================================================
  // Pagination Handlers
  // ============================================================================
  const loadMore = () => {
    if (!isLoading && hasMore) {
      setPage(prev => prev + 1);
    }
  };

  const refreshListings = () => {
    setPage(1);
    fetchListings(1);
  };

  // ============================================================================
  // Status Management
  // ============================================================================
  const toggleStatus = async (listingId: string, currentStatus: string) => {
    try {
      const newStatus = currentStatus === 'active' ? 'inactive' : 'active';
      const { error } = await supabase
        .from('listings')
        .update({ status: newStatus })
        .eq('id', listingId);

      if (error) throw error;

      toast.success(
        newStatus === 'active'
          ? "Annonce réactivée avec succès"
          : "Annonce désactivée avec succès"
      );

      refreshListings();
    } catch (error) {
      console.error('Error updating listing status:', error);
      toast.error("Erreur lors de la mise à jour du statut");
    }
  };

  // ============================================================================
  // UI Helpers
  // ============================================================================
  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'active':
        return <Badge className="bg-green-100 text-green-800">Actif</Badge>;
      case 'inactive':
        return <Badge variant="secondary">Inactif</Badge>;
      case 'sold':
        return <Badge variant="destructive">Vendu</Badge>;
      default:
        return null;
    }
  };

  const getExpiryStatus = (expiryDate: string) => {
    const daysUntilExpiry = Math.ceil(
      (new Date(expiryDate).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
    );

    if (daysUntilExpiry <= 0) {
      return {
        badge: <Badge variant="destructive">Expiré</Badge>,
        suggestion: "Ce produit est expiré. Veuillez le retirer de la vente."
      };
    } else if (daysUntilExpiry <= 30) {
      return {
        badge: <Badge variant="destructive">Expiration proche</Badge>,
        suggestion: "Considérez une réduction de prix pour écouler le stock rapidement."
      };
    } else if (daysUntilExpiry <= 90) {
      return {
        badge: <Badge variant="secondary">Expiration &lt; 3 mois</Badge>,
        suggestion: "Surveillez l'expiration et ajustez le prix si nécessaire."
      };
    } else {
      return {
        badge: <Badge variant="default">Valide</Badge>,
        suggestion: null
      };
    }
  };

  const getProfitStatus = (listing: Listing) => {
    const originalPrice = listing.product.original_price;
    const salePrice = listing.price_per_unit;
    const profitMargin = ((salePrice - originalPrice) / originalPrice) * 100;

    let color = 'text-gray-500';
    let suggestion = '';

    if (profitMargin <= 0) {
      color = 'text-red-500';
      suggestion = 'Vente à perte. Considérez augmenter le prix.';
    } else if (profitMargin < 10) {
      color = 'text-yellow-500';
      suggestion = 'Faible marge. Optimisation possible.';
    } else if (profitMargin > 30) {
      color = 'text-green-500';
      suggestion = 'Bonne marge. Surveillez la concurrence.';
    }

    return {
      color,
      margin: `${profitMargin.toFixed(1)}%`,
      suggestion
    };
  };

  const calculateProfit = (listing: Listing) => {
    const profit = listing.price_per_unit - listing.product.original_price;
    const profitMargin = (profit / listing.product.original_price) * 100;

    return (
      <Tooltip>
        <TooltipTrigger>
          <div className={`font-medium ${profit >= 0 ? 'text-green-600' : 'text-red-600'}`}>
            {profit.toFixed(2)} DH ({profitMargin.toFixed(1)}%)
          </div>
        </TooltipTrigger>
        <TooltipContent>
          <div className="space-y-2">
            <div className="grid grid-cols-2 gap-2 text-sm">
              <span>Prix de vente:</span>
              <span>{listing.price_per_unit} DH</span>
              <span>Prix d&apos;achat:</span>
              <span>{listing.product.original_price} DH</span>
              <span>Profit:</span>
              <span>{profit.toFixed(2)} DH</span>
              <span>Marge:</span>
              <span>{profitMargin.toFixed(1)}%</span>
            </div>
          </div>
        </TooltipContent>
      </Tooltip>
    );
  };

  const getReservationStatus = (listing: Listing) => {
    if (!listing.reservations?.length) return null;

    const pendingReservations = listing.reservations.filter(r => r.status === 'pending');
    const acceptedReservations = listing.reservations.filter(r => r.status === 'accepted');

    if (pendingReservations.length > 0) {
      return (
        <Tooltip>
          <TooltipTrigger>
            <Badge variant="outline" className="bg-yellow-50 text-yellow-800 hover:bg-yellow-50">
              {pendingReservations.length} réservation{pendingReservations.length > 1 ? 's' : ''} en attente
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-2">
              <p className="font-medium">Réservations en attente:</p>
              {pendingReservations.map(r => (
                <div key={r.id} className="text-sm">
                  <p>{r.pharmacy_name}</p>
                  <p className="text-muted-foreground">{r.quantity} unités</p>
                </div>
              ))}
            </div>
          </TooltipContent>
        </Tooltip>
      );
    }

    if (acceptedReservations.length > 0) {
      return (
        <Tooltip>
          <TooltipTrigger>
            <Badge variant="outline" className="bg-green-50 text-green-800 hover:bg-green-50">
              {acceptedReservations.length} réservation{acceptedReservations.length > 1 ? 's' : ''} acceptée{acceptedReservations.length > 1 ? 's' : ''}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-2">
              <p className="font-medium">Réservations acceptées:</p>
              {acceptedReservations.map(r => (
                <div key={r.id} className="text-sm">
                  <p>{r.pharmacy_name}</p>
                  <p className="text-muted-foreground">{r.quantity} unités</p>
                </div>
              ))}
            </div>
          </TooltipContent>
        </Tooltip>
      );
    }

    return null;
  };

  const handleSort = (field: typeof sortBy.field) => {
    setSortBy(prev => ({
      field,
      direction: prev.field === field && prev.direction === 'asc' ? 'desc' : 'asc'
    }));
  };

  const getSortedListings = () => {
    return [...listings].sort((a, b) => {
      let aValue: any = sortBy.field.includes('.')
        ? a.product.name
        : (a as any)[sortBy.field];
      let bValue: any = sortBy.field.includes('.')
        ? b.product.name
        : (b as any)[sortBy.field];

      if (typeof aValue === 'string') {
        aValue = aValue.toLowerCase();
        bValue = bValue.toLowerCase();
      }

      return sortBy.direction === 'asc'
        ? aValue > bValue ? 1 : -1
        : aValue < bValue ? 1 : -1;
    });
  };

  const getFilteredListings = () => {
    let filtered = getSortedListings();

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(l => l.status === statusFilter);
    }

    // Apply expiry filter
    if (expiryFilter !== 'all') {
      filtered = filtered.filter(l => {
        const daysUntilExpiry = Math.ceil(
          (new Date(l.expires_at || l.product.expiry_date).getTime() - Date.now()) / (1000 * 60 * 60 * 24)
        );
        switch (expiryFilter) {
          case '30':
            return daysUntilExpiry <= 30 && daysUntilExpiry > 0;
          case '90':
            return daysUntilExpiry <= 90 && daysUntilExpiry > 30;
          case 'expired':
            return daysUntilExpiry <= 0;
          default:
            return true;
        }
      });
    }

    return filtered;
  };

  // ============================================================================
  // Bulk Actions
  // ============================================================================
  const bulkToggleStatus = async (newStatus: 'active' | 'inactive') => {
    try {
      const { error } = await supabase
        .from('listings')
        .update({ status: newStatus })
        .in('id', selectedListings);

      if (error) throw error;

      toast.success(
        newStatus === 'active'
          ? "Annonces réactivées avec succès"
          : "Annonces désactivées avec succès"
      );

      setSelectedListings([]);
      refreshListings();
    } catch (error) {
      console.error('Error updating listings status:', error);
      toast.error("Erreur lors de la mise à jour des statuts");
    }
  };

  // ============================================================================
  // Selection Handlers
  // ============================================================================
  const toggleSelectListing = (listingId: string) => {
    setSelectedListings(prev =>
      prev.includes(listingId)
        ? prev.filter(id => id !== listingId)
        : [...prev, listingId]
    );
  };

  const toggleSelectAll = () => {
    setSelectedListings(prev =>
      prev.length === listings.length ? [] : listings.map(l => l.id)
    );
  };

  if (isLoading) {
    return (
      <div className="text-center py-12 text-muted-foreground">
        <p className="font-medium">Chargement des annonces...</p>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-xl font-semibold">Vos annonces</h2>
          <p className="text-sm text-muted-foreground">
            {listings.filter(l => l.status === 'active').length} annonces actives
          </p>
        </div>
        <div className="flex gap-2">
          <Select
            value={statusFilter}
            onValueChange={(value: typeof statusFilter) => setStatusFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrer par statut" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Tous les statuts</SelectItem>
              <SelectItem value="active">Actif</SelectItem>
              <SelectItem value="inactive">Inactif</SelectItem>
              <SelectItem value="sold">Vendu</SelectItem>
            </SelectContent>
          </Select>

          <Select
            value={expiryFilter}
            onValueChange={(value: typeof expiryFilter) => setExpiryFilter(value)}
          >
            <SelectTrigger className="w-[180px]">
              <SelectValue placeholder="Filtrer par expiration" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les dates</SelectItem>
              <SelectItem value="30">Expire dans 30 jours</SelectItem>
              <SelectItem value="90">Expire dans 90 jours</SelectItem>
              <SelectItem value="expired">Expiré</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {selectedListings.length > 0 && (
        <div className="bg-muted p-4 rounded-lg flex items-center justify-between">
          <span>{selectedListings.length} annonce(s) sélectionnée(s)</span>
          <div className="flex gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={() => bulkToggleStatus('active')}
            >
              Activer
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => bulkToggleStatus('inactive')}
            >
              Désactiver
            </Button>
            <Button
              variant="outline"
              size="sm"
              onClick={() => setSelectedListings([])}
            >
              Annuler
            </Button>
          </div>
        </div>
      )}

      <div className="border rounded-lg">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead className="w-[40px]">
                <input
                  type="checkbox"
                  checked={selectedListings.length === listings.length}
                  onChange={toggleSelectAll}
                  className="rounded border-gray-300"
                />
              </TableHead>
              <TableHead
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('product.name')}
              >
                Médicament {sortBy.field === 'product.name' && (sortBy.direction === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead>Prix & Profit</TableHead>
              <TableHead>Quantité</TableHead>
              <TableHead>Expiration</TableHead>
              <TableHead>Statut</TableHead>
              <TableHead
                className="cursor-pointer hover:bg-muted/50"
                onClick={() => handleSort('views')}
              >
                Intérêt {sortBy.field === 'views' && (sortBy.direction === 'asc' ? '↑' : '↓')}
              </TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {getFilteredListings().length === 0 ? (
              <TableRow>
                <TableCell colSpan={8} className="text-center py-6">
                  <p className="text-muted-foreground">Aucune annonce trouvée</p>
                  <p className="text-sm text-muted-foreground">
                    Commencez par mettre en vente un médicament
                  </p>
                </TableCell>
              </TableRow>
            ) : (
              getFilteredListings().map((listing) => (
                <TableRow
                  key={listing.id}
                  className="group hover:bg-muted/50 transition-colors"
                >
                  <TableCell>
                    <input
                      type="checkbox"
                      checked={selectedListings.includes(listing.id)}
                      onChange={() => toggleSelectListing(listing.id)}
                      className="rounded border-gray-300"
                    />
                  </TableCell>
                  <TableCell>
                    <div>
                      <div className="font-medium">{listing.product.name}</div>
                      <div className="text-sm text-muted-foreground">
                        Créé le {new Date(listing.created_at).toLocaleDateString('fr-FR')}
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <div className={getProfitStatus(listing).color}>
                            <div>{listing.price_per_unit} DH</div>
                            <div className="text-sm">
                              Marge: {getProfitStatus(listing).margin}
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{getProfitStatus(listing).suggestion}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>{listing.quantity}</TableCell>
                  <TableCell>
                    <TooltipProvider>
                      <Tooltip>
                        <TooltipTrigger>
                          <div>
                            {getExpiryStatus(listing.expires_at || listing.product.expiry_date).badge}
                            <div className="text-sm text-muted-foreground">
                              {new Date(listing.expires_at || listing.product.expiry_date).toLocaleDateString('fr-FR')}
                            </div>
                          </div>
                        </TooltipTrigger>
                        <TooltipContent>
                          <p>{getExpiryStatus(listing.expires_at || listing.product.expiry_date).suggestion}</p>
                        </TooltipContent>
                      </Tooltip>
                    </TooltipProvider>
                  </TableCell>
                  <TableCell>{getStatusBadge(listing.status)}</TableCell>
                  <TableCell>
                    <div>
                      <div>{listing.views} vues</div>
                      <div className="text-sm text-muted-foreground">
                        {listing.interested_parties} intéressés
                      </div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      onClick={() => toggleStatus(listing.id, listing.status)}
                    >
                      {listing.status === 'active' ? 'Désactiver' : 'Activer'}
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {hasMore && (
        <div className="flex justify-center">
          <Button
            variant="outline"
            onClick={loadMore}
            disabled={isLoading}
          >
            {isLoading ? 'Chargement...' : 'Charger plus'}
          </Button>
        </div>
      )}
    </div>
  );
}