"use client";

import { <PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "recharts";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";

const data = [
  { month: "Jan", value: 1200 },
  { month: "Fév", value: 1100 },
  { month: "Mar", value: 1300 },
  { month: "Avr", value: 1400 },
  { month: "Mai", value: 1200 },
  { month: "Jun", value: 1500 },
];

export function StockEvolution() {
  return (
    <Card>
      <CardHeader>
        <CardTitle>Évolution des stocks</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="h-[200px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart data={data}>
              <XAxis
                dataKey="month"
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={true}
              />
              <YAxis
                stroke="#888888"
                fontSize={12}
                tickLine={false}
                axisLine={true}
                tickFormatter={(value) => `${value}`}
              />
              <Tooltip />
              <Line
                type="monotone"
                dataKey="value"
                stroke="hsl(var(--primary))"
                strokeWidth={2}
                dot={false}
              />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </CardContent>
    </Card>
  );
}