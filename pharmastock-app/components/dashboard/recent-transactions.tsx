"use client";

import { useState, useEffect } from "react";
import { Avatar } from "@/components/ui/avatar";
import { Card } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useWebSocket } from "@/contexts/websocket-context";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { AlertCircle, CheckCircle2, Clock, PhoneCall, MapPin } from "lucide-react";

interface UrgentRequest {
  id: number;
  pharmacy: string;
  medication: string;
  quantity: number;
  status: "en attente" | "accepté" | "refusé";
  timestamp: string;
  urgencyLevel: 'critique' | 'urgent' | 'normal';
  distance?: string;
  contactNumber?: string;
  notes?: string;
}

const statusStyles = {
  "en attente": "bg-amber-50 text-amber-800 dark:bg-amber-900/50 dark:text-amber-300 border-l-4 border-amber-500 shadow-sm",
  "accepté": "bg-emerald-50 text-emerald-800 dark:bg-emerald-900/50 dark:text-emerald-300 border-l-4 border-emerald-500 shadow-sm",
  "refusé": "bg-red-50 text-red-800 dark:bg-red-900/50 dark:text-red-300 border-l-4 border-red-500 shadow-sm",
};

const urgencyStyles = {
  'critique': "bg-red-100 text-red-800 dark:bg-red-900/50 animate-pulse",
  'urgent': "bg-amber-100 text-amber-800 dark:bg-amber-900/50",
  'normal': "bg-blue-100 text-blue-800 dark:bg-blue-900/50"
};

export function UrgentRequestsList() {
  const { sendMessage, subscribeToEvent, unsubscribeFromEvent } = useWebSocket();
  const [requests, setRequests] = useState<UrgentRequest[]>([]);

  useEffect(() => {
    const handleNewRequest = (request: UrgentRequest) => {
      setRequests(prev => [request, ...prev.slice(0, 9)]);
    };

    const handleRequestUpdate = (update: { id: number; status: UrgentRequest["status"] }) => {
      setRequests(prev =>
        prev.map(r => r.id === update.id ? { ...r, status: update.status } : r)
      );
    };

    subscribeToEvent('new_urgent_request', handleNewRequest);
    subscribeToEvent('urgent_request_update', handleRequestUpdate);

    // Initial data fetch
    sendMessage('get_urgent_requests', {});

    return () => {
      unsubscribeFromEvent('new_urgent_request', handleNewRequest);
      unsubscribeFromEvent('urgent_request_update', handleRequestUpdate);
    };
  }, []);

  const handleAccept = (requestId: number) => {
    sendMessage('urgent_request_action', { requestId, action: 'accept' });
  };

  const handleReject = (requestId: number) => {
    sendMessage('urgent_request_action', { requestId, action: 'reject' });
  };

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <div className="space-y-1">
          <h2 className="text-2xl font-bold">Demandes urgentes</h2>
          <p className="text-sm text-muted-foreground">
            Répondez aux demandes urgentes des autres pharmacies
          </p>
        </div>
        <Badge
          variant="outline"
          className={requests.some(r => r.status === 'en attente')
            ? "bg-red-50 text-red-700 border-red-200 dark:bg-red-900/20 dark:border-red-800"
            : "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/20 dark:border-emerald-800"
          }
        >
          {requests.filter(r => r.status === 'en attente').length} en attente
        </Badge>
      </div>

      <div className="space-y-4">
        {requests.map((request) => (
          <Card key={request.id} className="p-4 hover:shadow-md transition-all dark:hover:shadow-lg dark:hover:shadow-primary/5">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Avatar className="h-10 w-10 ring-2 ring-offset-2 ring-amber-500">
                  <span className="font-semibold">
                    {request.pharmacy.split(" ").map(word => word[0]).join("")}
                  </span>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <p className="font-semibold">{request.pharmacy}</p>
                    <Badge className={urgencyStyles[request.urgencyLevel]}>
                      {request.urgencyLevel === 'critique' ? 'Très urgent' :
                        request.urgencyLevel === 'urgent' ? 'Urgent' : 'Normal'}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground mt-1">
                    <AlertCircle className="w-3 h-3" />
                    <span>Recherche {request.quantity} unités de {request.medication}</span>
                  </div>
                  {(request.distance || request.contactNumber) && (
                    <div className="flex items-center gap-4 mt-2 text-sm">
                      {request.distance && (
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <MapPin className="w-3 h-3" />
                          <span>{request.distance}</span>
                        </div>
                      )}
                      {request.contactNumber && (
                        <div className="flex items-center gap-1 text-muted-foreground">
                          <PhoneCall className="w-3 h-3" />
                          <span>{request.contactNumber}</span>
                        </div>
                      )}
                    </div>
                  )}
                  {request.notes && (
                    <p className="text-sm text-muted-foreground mt-2 italic">
                      &quot;{request.notes}&quot;
                    </p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className="flex items-center gap-2 justify-end mb-2">
                  <Badge variant="secondary" className={statusStyles[request.status]}>
                    {request.status}
                  </Badge>
                  <span className="text-xs text-muted-foreground">
                    {format(new Date(request.timestamp), 'HH:mm', { locale: fr })}
                  </span>
                </div>
                {request.status === 'en attente' && (
                  <div className="flex gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => handleReject(request.id)}
                      className="flex items-center gap-2"
                    >
                      <span>Refuser</span>
                    </Button>
                    <Button
                      variant="default"
                      size="sm"
                      onClick={() => handleAccept(request.id)}
                      className="flex items-center gap-2"
                    >
                      <span>Accepter</span>
                      <CheckCircle2 className="w-4 h-4" />
                    </Button>
                  </div>
                )}
              </div>
            </div>
          </Card>
        ))}
        {requests.length === 0 && (
          <Card className="py-12">
            <div className="text-center text-muted-foreground">
              <Clock className="w-12 h-12 mx-auto mb-3 text-muted-foreground/50" />
              <p className="font-medium text-lg">Aucune demande urgente</p>
              <p className="text-sm">Les nouvelles demandes s&apos;afficheront ici automatiquement</p>
            </div>
          </Card>
        )}
      </div>
    </div>
  );
}