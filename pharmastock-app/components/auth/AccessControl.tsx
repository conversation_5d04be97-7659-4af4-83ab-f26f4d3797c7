"use client";

import { usePathname } from 'next/navigation';
import { useAccessControl } from '@/hooks/useAccessControl';
import { Loader2 } from 'lucide-react';

interface AccessControlProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function AccessControl({ children, fallback }: AccessControlProps) {
  const pathname = usePathname();
  const { hasAccess, loading, reason } = useAccessControl(pathname);

  // Show loading state
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <Loader2 className="h-12 w-12 animate-spin mx-auto" />
          <p className="mt-4 text-muted-foreground">Checking access...</p>
        </div>
      </div>
    );
  }

  // Show fallback or redirect will happen via useAccessControl hook
  if (!hasAccess) {
    return fallback || (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Access Denied</h1>
          <p className="text-muted-foreground">{reason}</p>
        </div>
      </div>
    );
  }

  return <>{children}</>;
}

// Higher-order component for page-level access control
export function withAccessControl<P extends object>(
  Component: React.ComponentType<P>,
  fallback?: React.ReactNode
) {
  return function AccessControlledComponent(props: P) {
    return (
      <AccessControl fallback={fallback}>
        <Component {...props} />
      </AccessControl>
    );
  };
}
