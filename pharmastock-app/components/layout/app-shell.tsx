"use client";

import {
  AppShell as MantineAppShell,
  Burger,
  Group,
  Text,
  UnstyledButton,
  rem,
  NavLink,
  Stack,
  Box,
} from "@mantine/core";
import { useDisclosure, useIdle, useMouse } from "@mantine/hooks";
import {
  IconLogout,
  IconSettings,
  IconUser,
  IconDashboard,
  IconShoppingCart,
  IconPackage,
  IconUsers,
  IconBuilding,
  IconClock,
  IconArrowsExchange,
  IconNetwork,
  IconBell,
  IconFileText,
} from "@tabler/icons-react";
import { useRouter, usePathname } from "next/navigation";
import { Toaster } from "@/components/ui/sonner";
import { DebugPanel } from "@/components/debug/debug-panel";
import { PWAInit } from "@/components/pwa/pwa-init";
import { useAuth } from "@/contexts/auth-context";
import { useState, useEffect } from "react";
import { usePermissions, type User } from "@/lib/types/auth";
import { motion, AnimatePresence } from "framer-motion";

// Helper function to determine if a route is active
function isActiveRoute(pathname: string, href: string): boolean {
  // Handle exact matches first
  if (pathname === href) {
    return true;
  }

  // Handle query parameter routes (like /marketplace?tab=reservations)
  if (href.includes("?")) {
    const [basePath, queryString] = href.split("?");
    const searchParams = new URLSearchParams(queryString);

    // Extract current path and search params
    const [currentPath, currentQuery] = pathname.split("?");
    const currentSearchParams = new URLSearchParams(currentQuery || "");

    // Check if base path matches and all query params from href are present
    if (currentPath === basePath) {
      for (const [key, value] of searchParams.entries()) {
        if (currentSearchParams.get(key) !== value) {
          return false;
        }
      }
      return true;
    }
  }

  return false;
}

export function AppShell({ children }: { children: React.ReactNode }) {
  const { user, signOut, isPharmacy } = useAuth();
  const [mobileOpened, { toggle: toggleMobile }] = useDisclosure();
  const [desktopOpened, setDesktopOpened] = useState(true);
  const router = useRouter();
  const pathname = usePathname();
  const idle = useIdle(5000); // 5 seconds of inactivity

  useEffect(() => {
    if (idle) {
      setDesktopOpened(false);
    }
  }, [idle]);

  if (!user) return null; // Don't render shell until auth is resolved

  // Map user to the User type expected by usePermissions
  const mappedUser: User | null = user
    ? {
        ...user,
        role: user.role as import("@/lib/types/auth").UserRole,
        status: "active" as "active",
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    : null;
  const permissions = usePermissions(mappedUser);

  // Define navigation groups for better organization
  const navigationGroups = [
    {
      label: "Principal",
      items: [
        {
          label: "Tableau de bord",
          icon: IconDashboard,
          href: "/dashboard",
          permission: { action: "read", resource: "listings" },
        },
        {
          label: "Inventaire",
          icon: IconPackage,
          href: "/inventory",
          requiresPharmacy: true,
          permission: { action: "read", resource: "products" },
        },
      ],
    },
    {
      label: "Marketplace",
      items: [
        {
          label: "Marketplace",
          icon: IconShoppingCart,
          href: "/marketplace",
          requiresPharmacy: true,
          permission: { action: "read", resource: "products" },
        },
        {
          label: "Commandes",
          icon: IconArrowsExchange,
          href: "/orders",
          requiresPharmacy: true,
          permission: { action: "read", resource: "transactions" },
        },
        {
          label: "Réservations",
          icon: IconClock,
          href: "/marketplace?tab=reservations",
          requiresPharmacy: true,
          permission: { action: "read", resource: "transactions" },
        },
        {
          label: "Demandes urgentes",
          icon: IconBell,
          href: "/urgent-requests",
          requiresPharmacy: true,
          permission: { action: "read", resource: "transactions" },
        },
      ],
    },
    {
      label: "Gestion",
      items: [
        {
          label: "Équipe",
          icon: IconUsers,
          href: "/team",
          requiresPharmacy: true,
          permission: { action: "read", resource: "team" },
        },
        {
          label: "Historique",
          icon: IconPackage,
          href: "/history",
          requiresPharmacy: true,
          permission: { action: "read", resource: "transactions" },
        },
        {
          label: "Réseau",
          icon: IconNetwork,
          href: "/network",
          requiresPharmacy: true,
          permission: { action: "read", resource: "team" },
        },
      ],
    },
    {
      label: "Paramètres",
      items: [
        {
          label: "Profil",
          icon: IconUser,
          href: "/profile",
          permission: { action: "read", resource: "settings" },
        },
        {
          label: "Configuration",
          icon: IconSettings,
          href: "/settings",
          requiresPharmacy: true,
          permission: { action: "read", resource: "settings" },
        },
      ],
    },
  ];

  // Add admin group for super_admin
  if (user.role === "super_admin") {
    navigationGroups.push({
      label: "Administration",
      items: [
        {
          label: "Admin Dashboard",
          icon: IconSettings,
          href: "/admin/dashboard",
          permission: { action: "manage", resource: "listings" },
        },
        {
          label: "Pharmacies",
          icon: IconBuilding,
          href: "/admin/pharmacies",
          permission: { action: "manage", resource: "pharmacy" },
        },
        {
          label: "Users",
          icon: IconUsers,
          href: "/admin/users",
          permission: { action: "manage", resource: "team" },
        },
      ],
    });
  }

  // Filter navigation groups based on permissions
  const filteredNavigationGroups = navigationGroups
    .map((group) => ({
      ...group,
      items: group.items.filter((item) => {
        // For owner, ignore requiresPharmacy if user is owner
        if (
          item.requiresPharmacy &&
          user.role !== "owner" &&
          !user.pharmacyId
        ) {
          return false;
        }
        return (
          item.permission &&
          permissions.can(
            item.permission.action as any,
            item.permission.resource as any
          )
        );
      }),
    }))
    .filter((group) => group.items.length > 0); // Only show groups with accessible items

  return (
    <>
      <PWAInit />
      <MantineAppShell
        header={{ height: 60 }}
        navbar={{
          width: desktopOpened ? 300 : 80,
          breakpoint: "sm",
          collapsed: { mobile: !mobileOpened },
        }}
        padding="md"
        transitionDuration={500}
        transitionTimingFunction="ease"
      >
        <MantineAppShell.Header>
          <Group h="100%" px="md" justify="space-between">
            <Group>
              <Burger
                opened={mobileOpened}
                onClick={toggleMobile}
                hiddenFrom="sm"
                size="sm"
              />
              <Text size="lg" fw={600} c="#00B5FF">
                PharmaStock
              </Text>
            </Group>

            <Group>
              <UnstyledButton
                onClick={() => signOut()}
                style={{
                  padding: rem(8),
                  borderRadius: rem(4),
                  display: "flex",
                  alignItems: "center",
                  gap: rem(8),
                }}
              >
                <IconLogout size={16} />
                <Text size="sm">Déconnexion</Text>
              </UnstyledButton>
            </Group>
          </Group>
        </MantineAppShell.Header>

        <MantineAppShell.Navbar
          p="md"
          onMouseEnter={() => setDesktopOpened(true)}
          onMouseLeave={() => setDesktopOpened(false)}
        >
          <Stack gap="lg">
            {filteredNavigationGroups.map((group, groupIndex) => (
              <div key={group.label}>
                {desktopOpened && (
                  <Text size="xs" c="dimmed" mb="xs" fw={600} tt="uppercase">
                    {group.label}
                  </Text>
                )}
                <Stack gap="xs">
                  {group.items.map((item) => (
                    <NavLink
                      key={item.href}
                      href={item.href}
                      label={desktopOpened ? item.label : ""}
                      leftSection={<item.icon size={desktopOpened ? 16 : 24} />}
                      active={isActiveRoute(pathname, item.href)}
                      onClick={() => router.push(item.href)}
                      style={{
                        borderRadius: "8px",
                        fontWeight: pathname === item.href ? 600 : 400,
                        paddingLeft: desktopOpened ? undefined : rem(12),
                        paddingRight: desktopOpened ? undefined : rem(12),
                        justifyContent: desktopOpened ? "flex-start" : "center",
                      }}
                      styles={{
                        root: {
                          backgroundColor: isActiveRoute(pathname, item.href)
                            ? "#00B5FF"
                            : "transparent",
                          color: isActiveRoute(pathname, item.href)
                            ? "white"
                            : "inherit",
                          "&:hover": {
                            backgroundColor: isActiveRoute(pathname, item.href)
                              ? "#0099CC"
                              : "rgba(0, 181, 255, 0.1)",
                            color: isActiveRoute(pathname, item.href)
                              ? "white"
                              : "#00B5FF",
                          },
                        },
                      }}
                    />
                  ))}
                </Stack>
              </div>
            ))}
          </Stack>

          <Box
            mt="auto"
            style={{
              borderTop: `1px solid var(--mantine-color-gray-2)`,
              paddingTop: rem(8),
            }}
          >
            <UnstyledButton
              onClick={() => router.push("/profile")}
              style={{
                padding: rem(8),
                borderRadius: rem(4),
                display: "flex",
                alignItems: "center",
                gap: rem(8),
                justifyContent: desktopOpened ? "flex-start" : "center",
              }}
            >
              <IconUser size={desktopOpened ? 16 : 24} />
              {desktopOpened && <Text size="sm">{user.email}</Text>}
            </UnstyledButton>
          </Box>
        </MantineAppShell.Navbar>

        <MantineAppShell.Main>{children}</MantineAppShell.Main>
        <Toaster />
        {process.env.NODE_ENV === "development" && <DebugPanel />}
      </MantineAppShell>
    </>
  );
}
