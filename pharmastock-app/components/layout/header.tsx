"use client";

import {
  <PERSON>,
  <PERSON>u,
  Search,
  MessageSquare,
  Heart,
  Store,
  CircleDot,
} from "lucide-react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Badge } from "@/components/ui/badge";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/auth-context";

interface Notification {
  id: string;
  type: "interest" | "price_drop" | "expiry" | "confirmation";
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  metadata?: {
    offerId?: string;
    oldPrice?: number;
    newPrice?: number;
    expiryDate?: string;
  };
}

interface Message {
  id: string;
  sender: string;
  preview: string;
  timestamp: string;
  read: boolean;
}

interface FavoriteOffer {
  id: string;
  medication: string;
  pharmacy: string;
  quantity: number;
  expiryDate: string;
  addedAt: string;
}

// Mock data - will be replaced with real data from Supabase
const notifications: Notification[] = [
  {
    id: "1",
    type: "interest",
    title: "Nouveau intérêt",
    message: "Pharmacie Atlas est intéressée par votre offre de Doliprane",
    timestamp: new Date().toISOString(),
    read: false,
    metadata: {
      offerId: "123",
    },
  },
  {
    id: "2",
    type: "price_drop",
    title: "Baisse de prix",
    message: "Le prix de l'Augmentin a baissé de 20%",
    timestamp: new Date().toISOString(),
    read: false,
    metadata: {
      offerId: "456",
      oldPrice: 100,
      newPrice: 80,
    },
  },
];

const messages: Message[] = [
  {
    id: "1",
    sender: "Pharmacie Centrale",
    preview: "Bonjour, je suis intéressé par...",
    timestamp: new Date().toISOString(),
    read: false,
  },
];

const favoriteOffers: FavoriteOffer[] = [
  {
    id: "1",
    medication: "Doliprane 1000mg",
    pharmacy: "Pharmacie Centrale",
    quantity: 100,
    expiryDate: "2024-06-15",
    addedAt: new Date().toISOString(),
  },
];

export default function Header() {
  const { user } = useAuth();

  return (
    <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
      <div className="container flex h-14 items-center">
        <div className="flex flex-1 items-center justify-end space-x-2">
          <div className="flex items-center space-x-4">
            <Button
              variant="ghost"
              size="icon"
              className="relative"
              aria-label="Messages"
            >
              <MessageSquare className="h-5 w-5" />
              <Badge
                variant="secondary"
                className="absolute -right-1 -top-1 h-4 w-4 rounded-full p-0 text-xs"
              >
                2
              </Badge>
            </Button>

            <Button
              variant="ghost"
              size="icon"
              className="relative"
              aria-label="Notifications"
            >
              <Bell className="h-5 w-5" />
              <Badge
                variant="secondary"
                className="absolute -right-1 -top-1 h-4 w-4 rounded-full p-0 text-xs"
              >
                3
              </Badge>
            </Button>

            {/* Quick-access prescription reader button */}
            <a href="/prescription-reader">
              <Button
                variant="outline"
                size="icon"
                className="relative bg-gradient-to-r from-purple-600 to-blue-600 text-white hover:from-purple-700 hover:to-blue-700 shadow-md"
                aria-label="Lecteur d'Ordonnance"
              >
                <Store className="h-5 w-5" />
              </Button>
            </a>

            <Button
              variant="ghost"
              size="icon"
              className="relative"
              aria-label="Favorite offers"
            >
              <Heart className="h-5 w-5" />
              <Badge
                variant="secondary"
                className="absolute -right-1 -top-1 h-4 w-4 rounded-full p-0 text-xs"
              >
                5
              </Badge>
            </Button>

            <div className="flex items-center space-x-2">
              <CircleDot
                className={cn(
                  "h-4 w-4",
                  user?.id ? "text-green-500" : "text-red-500"
                )}
              />
              <span className="text-sm">
                {user?.id ? (user.email || "Connecté") : "Déconnecté"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </header>
  );
}
