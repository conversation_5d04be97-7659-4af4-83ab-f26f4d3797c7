"use client";

import React from "react";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { UserNav } from "@/components/user-nav";

export function Navbar() {
  const { user } = useAuth();

  return (
    <div className="border-b">
      <div className="flex h-16 items-center px-4">
        <div className="ml-auto flex items-center space-x-4">
          {user ? (
            <UserNav />
          ) : (
            <Button variant="ghost" asChild>
              Connexion
            </Button>
          )}
        </div>
      </div>
    </div>
  );
}