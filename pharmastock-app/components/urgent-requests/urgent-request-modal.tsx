"use client";

import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  DialogTitle,
} from "@/components/ui/dialog";
import { UrgentRequestForm } from "@/components/urgent-requests/urgent-request-form";
import { DatePicker } from '@mantine/dates';
import { Select } from '@mantine/core';

interface UrgentRequestModalProps {
  open: boolean;
  onClose: () => void;
}

export function UrgentRequestModal({ open, onClose }: UrgentRequestModalProps) {
  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-3xl p-0 overflow-hidden">
        {/* Gradient header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4 rounded-t-2xl">
          <DialogHeader className="p-0">
            <DialogTitle className="text-xl font-bold text-white">
              Demande <PERSON>
            </DialogTitle>
          </DialogHeader>
        </div>
        <div className="p-6 bg-white rounded-b-2xl">
          <UrgentRequestForm
            // NOTE: This modal is not used in the main urgent-requests page. See /app/urgent-requests/page.tsx for the main implementation.
            onSubmit={(values) => {
              // This is a placeholder. Main logic is in /app/urgent-requests/page.tsx
              onClose();
            }}
            onCancel={onClose}
            useModernDatePicker
            useMoroccanCategories
          />
        </div>
      </DialogContent>
    </Dialog>
  );
} 