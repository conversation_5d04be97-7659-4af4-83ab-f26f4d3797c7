"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Slider } from "@/components/ui/slider";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { createFormSubmitHandler } from '@/lib/form-utils';
import { DatePicker } from '@mantine/dates';
import { Select as MantineSelect } from '@mantine/core';

const formSchema = z.object({
  medication: z.string().min(1, "Le nom du médicament est requis"),
  quantity: z.string().min(1, "La quantité est requise"),
  urgency: z.string(),
  details: z.string(),
  radius: z.number().min(1).max(20),
  selectedPharmacies: z.array(z.number()),
  expiryDate: z.string().optional(),
  category: z.string().min(1, "La catégorie doit être sélectionnée"),
});

// Mock data for nearby pharmacies - will be replaced with real data from Supabase
const NEARBY_PHARMACIES = [
  { id: 1, name: "Pharmacie Centrale", distance: 1.2, location: "Casablanca - Maarif" },
  { id: 2, name: "Pharmacie du Nord", distance: 2.5, location: "Casablanca - Ain Diab" },
  { id: 3, name: "Pharmacie Atlas", distance: 3.0, location: "Casablanca - Gauthier" },
  { id: 4, name: "Pharmacie Moderne", distance: 3.8, location: "Casablanca - Bourgogne" },
  { id: 5, name: "Pharmacie du Marché", distance: 4.2, location: "Casablanca - CIL" },
];

interface UrgentRequestFormProps {
  onSubmit: (values: z.infer<typeof formSchema>) => void;
  onCancel?: () => void;
}

export function UrgentRequestForm({ onSubmit, onCancel }: UrgentRequestFormProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      medication: "",
      quantity: "1",
      urgency: "asap",
      details: "",
      radius: 5,
      selectedPharmacies: [],
      expiryDate: undefined,
      category: "",
    },
  });

  const handleSubmit = createFormSubmitHandler(
    formSchema,
    async (values) => {
      onSubmit(values);
    },
    {
      successMessage: "Demande urgente envoyée avec succès",
      errorMessage: "Erreur lors de l'envoi de la demande urgente"
    }
  );

  const radius = form.watch("radius");
  const filteredPharmacies = NEARBY_PHARMACIES.filter(
    (pharmacy) => pharmacy.distance <= radius
  );

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-8">
        <div className="space-y-6">
          <FormField
            control={form.control}
            name="medication"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Médicament recherché</FormLabel>
                <FormControl>
                  <Input placeholder="Ex: Doliprane 1000mg" {...field} />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="expiryDate"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Date d'expiration</FormLabel>
                <FormControl>
                  <DatePicker
                    value={field.value && typeof field.value === 'string' ? new Date(field.value) : field.value || null}
                    onChange={(date) => field.onChange(date && typeof date !== 'string' ? (date as Date).toISOString() : null)}
                    placeholder="Sélectionner une date"
                    getYearControlProps={(date) => ({})}
                    getMonthControlProps={(date) => ({})}
                    getDayProps={(date) => ({})}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="category"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Catégorie</FormLabel>
                <FormControl>
                  <MantineSelect
                    data={[
                      { value: "antalgiques", label: "Antalgiques" },
                      { value: "antibiotiques", label: "Antibiotiques" },
                      { value: "antidiabetiques", label: "Antidiabétiques" },
                      { value: "cardiologie", label: "Cardiologie" },
                      { value: "dermatologie", label: "Dermatologie" },
                      { value: "gastro-enterologie", label: "Gastro-entérologie" },
                      { value: "gynecologie", label: "Gynécologie" },
                      { value: "neurologie", label: "Neurologie" },
                      { value: "ophtalmologie", label: "Ophtalmologie" },
                      { value: "orl", label: "ORL" },
                      { value: "pediatrie", label: "Pédiatrie" },
                      { value: "psychiatrie", label: "Psychiatrie" },
                      { value: "rhumatologie", label: "Rhumatologie" },
                      { value: "urologie", label: "Urologie" },
                      { value: "vaccins", label: "Vaccins" },
                      { value: "autres", label: "Autres" },
                    ]}
                    value={field.value}
                    onChange={field.onChange}
                    placeholder="Sélectionner une catégorie"
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <FormField
              control={form.control}
              name="quantity"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Quantité souhaitée</FormLabel>
                  <FormControl>
                    <Input type="number" min="1" {...field} />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="urgency"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Délai maximum</FormLabel>
                  <Select onValueChange={field.onChange} defaultValue={field.value}>
                    <FormControl>
                      <SelectTrigger>
                        <SelectValue placeholder="Sélectionnez le délai" />
                      </SelectTrigger>
                    </FormControl>
                    <SelectContent>
                      <SelectItem value="asap">Dès que possible</SelectItem>
                      <SelectItem value="24h">24 heures</SelectItem>
                      <SelectItem value="48h">48 heures</SelectItem>
                      <SelectItem value="72h">72 heures</SelectItem>
                    </SelectContent>
                  </Select>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>

          <FormField
            control={form.control}
            name="radius"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Rayon de recherche ({field.value} km)</FormLabel>
                <FormControl>
                  <div className="bg-white border rounded-md p-6">
                    <div className="relative py-4">
                      <div
                        className="absolute h-[2px] w-full bg-[#d0d7de]"
                        style={{ top: "calc(50% - 1px)" }}
                      />
                      <div
                        className="absolute h-[2px] bg-[#0969da]"
                        style={{
                          top: "calc(50% - 1px)",
                          width: `${(field.value / 20) * 100}%`,
                        }}
                      />
                      <Slider
                        value={[field.value]}
                        onValueChange={(value) => field.onChange(value[0])}
                        max={20}
                        step={1}
                        className="relative"
                      />
                    </div>
                  </div>
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="selectedPharmacies"
            render={({ field }) => (
              <FormItem>
                <FormLabel id="pharmacy-checkbox-group-label">Pharmacies à proximité</FormLabel>
                <div
                  className="mt-2 space-y-2 max-h-48 overflow-y-auto border rounded-md p-3"
                  role="group"
                  aria-labelledby="pharmacy-checkbox-group-label"
                >
                  {filteredPharmacies.length === 0 ? (
                    <p className="text-sm text-gray-500">Aucune pharmacie trouvée dans ce rayon</p>
                  ) : (
                    filteredPharmacies.map((pharmacy) => (
                      <div key={pharmacy.id} className="flex items-center space-x-2">
                        <input
                          type="checkbox"
                          id={`pharmacy-${pharmacy.id}`}
                          className="rounded border-gray-300"
                          checked={field.value.includes(pharmacy.id)}
                          aria-checked={field.value.includes(pharmacy.id)}
                          aria-labelledby={`pharmacy-label-${pharmacy.id}`}
                          onChange={(e) => {
                            const newValue = e.target.checked
                              ? [...field.value, pharmacy.id]
                              : field.value.filter((id) => id !== pharmacy.id);
                            field.onChange(newValue);
                          }}
                        />
                        <label
                          htmlFor={`pharmacy-${pharmacy.id}`}
                          id={`pharmacy-label-${pharmacy.id}`}
                          className="text-sm"
                        >
                          {pharmacy.name} - {pharmacy.location} ({pharmacy.distance} km)
                        </label>
                      </div>
                    ))
                  )}
                </div>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="details"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Détails supplémentaires</FormLabel>
                <FormControl>
                  <Textarea
                    placeholder="Précisions sur votre demande..."
                    className="resize-none h-32"
                    {...field}
                  />
                </FormControl>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        <div className="flex justify-end gap-4">
          {onCancel && (
            <Button type="button" variant="outline" onClick={onCancel}>
              Annuler
            </Button>
          )}
          <Button type="submit">
            Envoyer la demande
          </Button>
        </div>
      </form>
    </Form>
  );
} 