"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ooter } from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";

interface ContactPharmacyModalProps {
  open: boolean;
  onClose: () => void;
  pharmacy: { name: string; email: string };
  onSend: (data: { subject: string; message: string }) => Promise<void>;
}

export function ContactPharmacyModal({ open, onClose, pharmacy, onSend }: ContactPharmacyModalProps) {
  const [subject, setSubject] = useState("");
  const [message, setMessage] = useState("");
  const [loading, setLoading] = useState(false);
  const [success, setSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError(null);
    try {
      await onSend({ subject, message });
      setSuccess(true);
      setSubject("");
      setMessage("");
    } catch (err: any) {
      setError(err.message || "Erreur lors de l'envoi du message");
    } finally {
      setLoading(false);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-lg p-0 overflow-hidden">
        {/* Gradient header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-500 px-6 py-4 rounded-t-2xl">
          <DialogHeader className="p-0">
            <DialogTitle className="text-xl font-bold text-white">
              Contacter {pharmacy.name}
            </DialogTitle>
          </DialogHeader>
        </div>
        <div className="p-6 bg-white rounded-b-2xl">
          {success ? (
            <div className="text-green-700 text-center py-6">Message envoyé avec succès !</div>
          ) : (
            <form onSubmit={handleSubmit} className="space-y-4">
              <Input
                placeholder="Sujet"
                value={subject}
                onChange={e => setSubject(e.target.value)}
                required
                disabled={loading}
              />
              <Textarea
                placeholder="Votre message..."
                value={message}
                onChange={e => setMessage(e.target.value)}
                required
                rows={5}
                disabled={loading}
              />
              {error && <div className="text-red-600 text-sm">{error}</div>}
              <div className="flex justify-end gap-4 mt-6">
                <Button type="button" variant="outline" onClick={onClose} disabled={loading} className="font-semibold">
                  Annuler
                </Button>
                <Button type="submit" disabled={loading} className="bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700 text-white font-semibold text-base py-2 px-6 rounded-xl shadow-md transition">
                  {loading ? "Envoi..." : "Envoyer"}
                </Button>
              </div>
            </form>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
} 