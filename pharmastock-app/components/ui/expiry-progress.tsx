'use client';

import { cn } from "@/lib/utils";

interface ExpiryProgressProps {
  expiryDate: string;
  className?: string;
}

export function ExpiryProgress({ expiryDate, className }: ExpiryProgressProps) {
  const now = new Date();
  const expiry = new Date(expiryDate);
  const sixMonthsAgo = new Date();
  sixMonthsAgo.setMonth(sixMonthsAgo.getMonth() - 6);

  // Calculate total duration and remaining time
  const totalDuration = expiry.getTime() - sixMonthsAgo.getTime();
  const remainingTime = expiry.getTime() - now.getTime();

  // Calculate progress percentage (0-100)
  const progress = Math.max(0, Math.min(100, (remainingTime / totalDuration) * 100));

  // Determine color based on remaining time
  const getColor = () => {
    if (progress > 66) return 'bg-gradient-to-r from-green-500 to-green-600';
    if (progress > 33) return 'bg-gradient-to-r from-yellow-500 to-orange-500';
    return 'bg-gradient-to-r from-red-500 to-red-600';
  };

  // Calculate days remaining
  const daysRemaining = Math.ceil(remainingTime / (1000 * 60 * 60 * 24));

  return (
    <div className={cn("space-y-1", className)}>
      <div className="h-2 bg-secondary rounded-full overflow-hidden">
        <div
          className={cn("h-full transition-all duration-500", getColor())}
          style={{ width: `${progress}%` }}
        />
      </div>
      <p className="text-xs text-muted-foreground">
        {daysRemaining > 0
          ? `Expire dans ${daysRemaining} jours`
          : "Produit expiré"}
      </p>
    </div>
  );
} 