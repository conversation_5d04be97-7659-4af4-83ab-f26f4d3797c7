'use client';

import * as React from 'react';

const styles = `
  :root {
    --popup-z-index: 1000;
    --background-solid: hsl(var(--background));
    --border-color: hsl(var(--border));
  }

  /* Calendar popup styles */
  .rdp {
    position: relative;
    z-index: var(--popup-z-index) !important;
  }
  
  .rdp-months {
    background-color: var(--background-solid) !important;
    border: 1px solid var(--border-color) !important;
    border-radius: var(--radius);
  }

  /* All dropdown and popover styles */
  [data-radix-popper-content-wrapper] {
    z-index: calc(var(--popup-z-index) + 30) !important;
  }

  [role="listbox"],
  [role="menu"],
  [role="dialog"],
  .popover-content,
  .dropdown-content,
  .select-content,
  .command-menu,
  [cmdk-overlay],
  .select-viewport,
  [data-radix-select-viewport],
  [data-radix-dropdown-menu-content] {
    background-color: var(--background-solid) !important;
    border: 1px solid var(--border-color) !important;
    backdrop-filter: none !important;
  }

  /* Toast notifications */
  .toast-viewport {
    z-index: calc(var(--popup-z-index) + 100) !important;
  }

  /* Fix for portal positioning */
  [data-radix-portal] {
    position: fixed;
    inset: 0;
    z-index: calc(var(--popup-z-index) + 40);
    display: grid;
    place-items: center;
  }

  /* Dialog and modal overlays */
  [data-radix-dialog-overlay],
  [data-radix-modal-overlay],
  .modal-overlay,
  [cmdk-overlay] {
    position: fixed !important;
    inset: 0 !important;
    z-index: calc(var(--popup-z-index) + 40) !important;
    background-color: hsl(var(--background)/.8) !important;
    backdrop-filter: blur(4px);
  }

  /* Dialog and modal content */
  [data-radix-dialog-content],
  [data-radix-modal-content],
  .modal-content,
  [cmdk-dialog],
  .dialog-content {
    position: relative !important;
    z-index: calc(var(--popup-z-index) + 50) !important;
    background-color: var(--background-solid) !important;
    border: 1px solid var(--border-color) !important;
    max-height: 85vh !important;
    width: 90vw !important;
    max-width: 500px !important;
    margin: 0 !important;
    border-radius: var(--radius) !important;
  }

  /* Command menu and search overlays */
  .command-dialog-overlay {
    z-index: calc(var(--popup-z-index) + 98) !important;
  }

  .command-dialog-content {
    z-index: calc(var(--popup-z-index) + 99) !important;
    background-color: var(--background-solid) !important;
  }

  /* Ensure all popups have proper background and border */
  [data-state="open"][role="dialog"],
  [data-state="open"][role="listbox"],
  [data-state="open"][role="menu"],
  [data-radix-popper-content-wrapper] > * {
    background-color: var(--background-solid) !important;
    border: 1px solid var(--border-color) !important;
    backdrop-filter: none !important;
  }

  /* Additional fixes for specific components */
  .select-trigger,
  .combobox-trigger,
  .popover-trigger {
    z-index: var(--popup-z-index) !important;
  }

  .select-content,
  .combobox-content,
  .popover-content {
    z-index: calc(var(--popup-z-index) + 1) !important;
    background-color: var(--background-solid) !important;
    border: 1px solid var(--border-color) !important;
  }

  /* Context menu specific styles */
  [data-radix-context-menu-content],
  [role="menu"] {
    z-index: calc(var(--popup-z-index) + 50) !important;
    background-color: var(--background-solid) !important;
    border: 1px solid var(--border-color) !important;
  }

  /* Fix for dialog animations */
  @keyframes overlayShow {
    from {
      opacity: 0;
    }
    to {
      opacity: 1;
    }
  }

  @keyframes contentShow {
    from {
      opacity: 0;
      transform: translateY(-2%) scale(0.96);
    }
    to {
      opacity: 1;
      transform: translateY(0) scale(1);
    }
  }

  [data-radix-dialog-overlay],
  [data-radix-modal-overlay] {
    animation: overlayShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }

  [data-radix-dialog-content],
  [data-radix-modal-content] {
    animation: contentShow 150ms cubic-bezier(0.16, 1, 0.3, 1);
  }
`;

export function PopupProvider({ children }: { children: React.ReactNode }) {
  return (
    <>
      <style jsx global>{styles}</style>
      {children}
    </>
  );
} 