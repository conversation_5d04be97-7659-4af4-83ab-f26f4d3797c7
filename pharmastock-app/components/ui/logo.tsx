import React from 'react';

interface LogoProps {
  size?: number;
  className?: string;
}

export function Logo({ size = 64, className = "" }: LogoProps) {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 200 200" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      <defs>
        <linearGradient id="primaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{stopColor:"#667eea", stopOpacity:1}} />
          <stop offset="100%" style={{stopColor:"#764ba2", stopOpacity:1}} />
        </linearGradient>
        <linearGradient id="secondaryGradient" x1="0%" y1="0%" x2="100%" y2="100%">
          <stop offset="0%" style={{stopColor:"#10b981", stopOpacity:1}} />
          <stop offset="100%" style={{stopColor:"#059669", stopOpacity:1}} />
        </linearGradient>
      </defs>
      
      {/* Background Circle */}
      <circle cx="100" cy="100" r="90" fill="url(#primaryGradient)" />
      
      {/* Medical Cross */}
      <rect x="85" y="60" width="30" height="80" rx="15" fill="white" />
      <rect x="60" y="85" width="80" height="30" rx="15" fill="white" />
      
      {/* Pill/Capsule Elements */}
      <ellipse cx="70" cy="70" rx="8" ry="15" transform="rotate(-45 70 70)" fill="url(#secondaryGradient)" />
      <ellipse cx="130" cy="70" rx="8" ry="15" transform="rotate(45 130 70)" fill="url(#secondaryGradient)" />
      <ellipse cx="70" cy="130" rx="8" ry="15" transform="rotate(45 70 130)" fill="url(#secondaryGradient)" />
      <ellipse cx="130" cy="130" rx="8" ry="15" transform="rotate(-45 130 130)" fill="url(#secondaryGradient)" />
      
      {/* Central Icon - Inventory/Stock Symbol */}
      <rect x="90" y="90" width="20" height="20" rx="3" fill="url(#primaryGradient)" />
      <rect x="93" y="93" width="14" height="2" fill="white" />
      <rect x="93" y="97" width="14" height="2" fill="white" />
      <rect x="93" y="101" width="14" height="2" fill="white" />
      <rect x="93" y="105" width="14" height="2" fill="white" />
    </svg>
  );
}

export function LogoWhite({ size = 64, className = "" }: LogoProps) {
  return (
    <svg 
      width={size} 
      height={size} 
      viewBox="0 0 200 200" 
      fill="none" 
      xmlns="http://www.w3.org/2000/svg"
      className={className}
    >
      {/* Background Circle */}
      <circle cx="100" cy="100" r="90" fill="white" />
      
      {/* Medical Cross */}
      <rect x="85" y="60" width="30" height="80" rx="15" fill="#667eea" />
      <rect x="60" y="85" width="80" height="30" rx="15" fill="#667eea" />
      
      {/* Pill/Capsule Elements */}
      <ellipse cx="70" cy="70" rx="8" ry="15" transform="rotate(-45 70 70)" fill="#10b981" />
      <ellipse cx="130" cy="70" rx="8" ry="15" transform="rotate(45 130 70)" fill="#10b981" />
      <ellipse cx="70" cy="130" rx="8" ry="15" transform="rotate(45 70 130)" fill="#10b981" />
      <ellipse cx="130" cy="130" rx="8" ry="15" transform="rotate(-45 130 130)" fill="#10b981" />
      
      {/* Central Icon - Inventory/Stock Symbol */}
      <rect x="90" y="90" width="20" height="20" rx="3" fill="#667eea" />
      <rect x="93" y="93" width="14" height="2" fill="white" />
      <rect x="93" y="97" width="14" height="2" fill="white" />
      <rect x="93" y="101" width="14" height="2" fill="white" />
      <rect x="93" y="105" width="14" height="2" fill="white" />
    </svg>
  );
}
