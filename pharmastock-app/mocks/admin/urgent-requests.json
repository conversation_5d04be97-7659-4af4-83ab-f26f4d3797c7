{"requests": [{"id": "1", "pharmacy_id": "dcfc9c59-d3c8-419e-83a8-5ea3cb3db800", "pharmacy_name": "Pharmacy Central", "type": "stock_shortage", "product_name": "Doliprane 1000mg", "quantity_needed": 100, "urgency_level": "high", "status": "active", "created_at": "2024-03-22T13:15:00Z", "expires_at": "2024-03-23T13:15:00Z", "description": "Urgent need for Doliprane 1000mg due to unexpected demand", "contact_info": {"name": "Dr. <PERSON>", "phone": "+212 5-22-22-22-22", "email": "<EMAIL>"}, "responses": [{"pharmacy_id": "a8f37e1e-7d67-41d0-a67b-1435c7e87737", "pharmacy_name": "Pharmacy Atlas", "response_time": "2024-03-22T13:30:00Z", "can_provide": true, "quantity_available": 50, "notes": "Can provide half the requested quantity immediately"}]}, {"id": "2", "pharmacy_id": "a8f37e1e-7d67-41d0-a67b-1435c7e87737", "pharmacy_name": "Pharmacy Atlas", "type": "expiring_stock", "product_name": "Augmentin 1g", "quantity_available": 200, "urgency_level": "medium", "status": "active", "created_at": "2024-03-22T10:00:00Z", "expires_at": "2024-03-24T10:00:00Z", "description": "Stock expiring in 2 months, offering at discounted price", "contact_info": {"name": "Dr. <PERSON>", "phone": "+212 5-35-35-35-35", "email": "<EMAIL>"}, "responses": []}, {"id": "3", "pharmacy_id": "7c5d3e9f-2b1a-4c8d-9e6f-8a7b9c4d3e2f", "pharmacy_name": "Pharmacy Marrakech", "type": "stock_shortage", "product_name": "Voltaren 50mg", "quantity_needed": 50, "urgency_level": "high", "status": "resolved", "created_at": "2024-03-21T15:00:00Z", "expires_at": "2024-03-22T15:00:00Z", "description": "Urgent need for Voltaren 50mg", "contact_info": {"name": "Dr. <PERSON><PERSON>", "phone": "+212 5-24-24-24-24", "email": "<EMAIL>"}, "responses": [{"pharmacy_id": "dcfc9c59-d3c8-419e-83a8-5ea3cb3db800", "pharmacy_name": "Pharmacy Central", "response_time": "2024-03-21T15:30:00Z", "can_provide": true, "quantity_available": 50, "notes": "Can provide full quantity needed"}], "resolution": {"resolved_at": "2024-03-21T16:00:00Z", "resolved_by": "Pharmacy Central", "notes": "Full quantity provided by Pharmacy Central"}}], "stats": {"total": 3, "active": 2, "resolved": 1, "by_urgency": {"high": 2, "medium": 1, "low": 0}, "by_type": {"stock_shortage": 2, "expiring_stock": 1}, "average_resolution_time": "1.5h"}}