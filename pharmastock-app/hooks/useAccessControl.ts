import { useAuth } from '@/contexts/auth-context';
import { useRouter } from 'next/navigation';
import { useEffect } from 'react';

export type UserRole = 'super_admin' | 'owner' | 'pharmacist' | 'staff';

export interface AccessRule {
  roles: UserRole[];
  requiresPharmacy?: boolean;
  redirectTo?: string;
}

// Define access rules for each route pattern
export const ACCESS_RULES: Record<string, AccessRule> = {
  // Public routes (no authentication required)
  '/auth/*': { roles: [] },
  '/unauthorized': { roles: [] },
  '/prescription-reader': { roles: [] },
  '/prescription-reader/*': { roles: [] },

  // Dashboard - all authenticated users
  '/dashboard': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
  '/dashboard/*': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },

  // Super Admin only routes
  '/admin': { roles: ['super_admin'] },
  '/admin/*': { roles: ['super_admin'] },
  '/admin/dashboard': { roles: ['super_admin'] },
  '/admin/transactions': { roles: ['super_admin'] },
  '/admin/marketplace': { roles: ['super_admin'] },
  '/admin/audit-logs': { roles: ['super_admin'] },

  // Pharmacy management routes
  '/pharmacies': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: false },
  '/pharmacies/*': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: false },

  // Team management
  '/team': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: true },
  '/team/*': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: true },

  // Marketplace - configurable for staff
  '/marketplace': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
  '/marketplace/*': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },

  // Inventory management
  '/inventory': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: true },
  '/inventory/*': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: true },

  // Orders
  '/orders': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: true },
  '/orders/*': { roles: ['super_admin', 'owner', 'pharmacist'], requiresPharmacy: true },

  // Messages
  '/messages': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
  '/messages/*': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },

  // Reports
  '/reports': { roles: ['super_admin', 'owner', 'pharmacist'] },
  '/reports/*': { roles: ['super_admin', 'owner', 'pharmacist'] },

  // Urgent requests
  '/urgent-requests': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
  '/urgent-requests/*': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },

  // Profile and settings
  '/profile': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
  '/settings': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },

  // History
  '/history': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
  '/history/*': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },

  // Root path
  '/': { roles: ['super_admin', 'owner', 'pharmacist', 'staff'] },
};

// Utility function for role checks
function hasRole(user: any | null | undefined, roles: UserRole[]): boolean {
  if (!user) return false;

  // Get the actual user role from user_metadata, fallback to user.role
  const userRole = user.user_metadata?.role || user.role;
  if (!userRole) return false;

  // Type guard: only allow valid UserRole values
  const validRoles: UserRole[] = ['super_admin', 'owner', 'pharmacist', 'staff'];
  if (!validRoles.includes(userRole as UserRole)) return false;
  return roles.includes(userRole as UserRole);
}

export function useAccessControl(pathname: string) {
  const { user, loading, isPharmacy } = useAuth();
  const router = useRouter();

  const checkAccess = (path: string): { hasAccess: boolean; reason?: string } => {
    // If still loading, allow access (will be checked again when loaded)
    if (loading) {
      return { hasAccess: true };
    }

    // Find matching rule
    let matchedRule: AccessRule | null = null;
    let matchedPattern = '';

    // Check exact match first
    if (ACCESS_RULES[path]) {
      matchedRule = ACCESS_RULES[path];
      matchedPattern = path;
    } else {
      // Check wildcard patterns
      for (const [pattern, rule] of Object.entries(ACCESS_RULES)) {
        if (pattern.endsWith('/*')) {
          const basePath = pattern.slice(0, -2);
          if (path.startsWith(basePath + '/') || path === basePath) {
            matchedRule = rule;
            matchedPattern = pattern;
            break;
          }
        }
      }
    }

    // If no rule found, default to requiring authentication
    if (!matchedRule) {
      if (!user) {
        return { hasAccess: false, reason: 'Authentication required' };
      }
      return { hasAccess: true };
    }

    // Public routes (empty roles array)
    if (matchedRule.roles.length === 0) {
      return { hasAccess: true };
    }

    // Check authentication
    if (!user) {
      return { hasAccess: false, reason: 'Authentication required' };
    }

    // Check role access
    if (!hasRole(user, matchedRule.roles)) {
      const userRole = user.user_metadata?.role || user.role;
      return {
        hasAccess: false,
        reason: `Role '${userRole}' not allowed. Required: ${matchedRule.roles.join(', ')}`
      };
    }

    // Check pharmacy requirement (only for non-super_admin roles)
    const userRole = user.user_metadata?.role || user.role;
    if (matchedRule.requiresPharmacy && userRole !== 'super_admin' && userRole !== 'owner' && !isPharmacy) {
      return {
        hasAccess: false,
        reason: 'Pharmacy association required'
      };
    }

    return { hasAccess: true };
  };

  const accessCheck = checkAccess(pathname);

  useEffect(() => {
    if (!loading && !accessCheck.hasAccess) {
      console.log('Access denied:', {
        pathname,
        user: user ? {
          id: user.id,
          supabaseRole: user.role,
          actualRole: user.user_metadata?.role || user.role
        } : null,
        reason: accessCheck.reason,
        isPharmacy
      });

      if (!user) {
        router.push('/auth/login');
      } else {
        router.push('/unauthorized');
      }
    }
  }, [pathname, user, loading, accessCheck.hasAccess, accessCheck.reason, isPharmacy, router]);

  return {
    hasAccess: accessCheck.hasAccess,
    reason: accessCheck.reason,
    loading,
    user
  };
}

// Helper function to check if user has access to a specific route
export function hasAccessToRoute(pathname: string, user: any, isPharmacy: boolean): boolean {
  // Find matching rule
  let matchedRule: AccessRule | null = null;

  if (ACCESS_RULES[pathname]) {
    matchedRule = ACCESS_RULES[pathname];
  } else {
    for (const [pattern, rule] of Object.entries(ACCESS_RULES)) {
      if (pattern.endsWith('/*')) {
        const basePath = pattern.slice(0, -2);
        if (pathname.startsWith(basePath + '/') || pathname === basePath) {
          matchedRule = rule;
          break;
        }
      }
    }
  }

  if (!matchedRule) {
    return !!user; // Default to requiring authentication
  }

  // Public routes
  if (matchedRule.roles.length === 0) {
    return true;
  }

  if (!user) {
    return false;
  }

  if (!hasRole(user, matchedRule.roles)) {
    return false;
  }

  // Check pharmacy requirement (only for non-super_admin roles)
  if (matchedRule.requiresPharmacy && user.role !== 'super_admin' && user.role !== 'owner' && !isPharmacy) {
    return false;
  }

  return true;
}
