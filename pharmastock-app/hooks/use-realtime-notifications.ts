import { useEffect } from "react";
import { supabase } from "../src/services/supabase";
import { useAuth } from "../contexts/auth-context";
import { useNotifications } from "../contexts/notification-context";

export function useRealtimeNotifications() {
  const { user } = useAuth();
  const { showNotification, notifications, markAsRead, clearAll } = useNotifications();

  useEffect(() => {
    if (!user) return;

    // Subscribe to notifications for the current user
    const channel = supabase
      .channel(`notifications:user:${user.id}`)
      .on(
        'postgres_changes',
        {
          event: 'INSERT',
          schema: 'public',
          table: 'notifications',
          filter: `recipient_id=eq.${user.id}`,
        },
        (payload) => {
          const notif = payload.new;
          showNotification(notif.title || notif.message, 'info');
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [user]);
} 