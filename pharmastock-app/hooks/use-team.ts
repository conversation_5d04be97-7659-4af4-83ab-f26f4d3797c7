"use client";

import { useState, useEffect } from "react";
import { useSupabase } from "@/contexts/supabase-context";
import { toast } from "sonner";

type TeamMemberRole = "admin" | "pharmacist" | "assistant";
type TeamMemberStatus = "active" | "inactive";

interface PharmacyTeamMember {
  id: string;
  pharmacy_id: string;
  user_id: string;
  role: TeamMemberRole;
  status: TeamMemberStatus;
  last_active: string | null;
  created_at: string;
  user: {
    email: string;
  };
}

interface UseTeamReturn {
  teamMembers: PharmacyTeamMember[];
  isLoading: boolean;
  error: Error | null;
  inviteMember: (email: string, role: TeamMemberRole) => Promise<void>;
  updateMemberRole: (memberId: string, role: TeamMemberRole) => Promise<void>;
  updateMemberStatus: (
    memberId: string,
    status: TeamMemberStatus
  ) => Promise<void>;
  removeMember: (memberId: string) => Promise<void>;
}

export function useTeam(pharmacyId: string | null): UseTeamReturn {
  const { supabase } = useSupabase();
  const [teamMembers, setTeamMembers] = useState<PharmacyTeamMember[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  const fetchTeamMembers = async () => {
    try {
      if (!pharmacyId) {
        setTeamMembers([]);
        return;
      }

      setIsLoading(true);
      const { data, error: fetchError } = await supabase
        .from("pharmacy_team_members")
        .select("*")
        .eq("pharmacy_id", pharmacyId);

      if (fetchError) throw fetchError;

      // Get user emails from auth.users table
      const userIds = data?.map((member) => member.user_id) || [];
      const { data: authUsers } = await supabase.auth.admin.listUsers();

      // Map team members with user emails
      const teamMembersWithUsers =
        data?.map((member) => ({
          ...member,
          user: {
            email:
              authUsers.users.find((user) => user.id === member.user_id)
                ?.email || "Email non trouvé",
          },
        })) || [];

      setTeamMembers(teamMembersWithUsers as PharmacyTeamMember[]);
    } catch (err) {
      setError(err as Error);
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchTeamMembers();
  }, [pharmacyId]);

  const inviteMember = async (email: string, role: TeamMemberRole) => {
    try {
      if (!pharmacyId) {
        throw new Error("No pharmacy ID provided");
      }

      // Generate a random password
      const password = Math.random().toString(36).slice(-8);

      // Use a stored procedure to handle member invitation
      const { data: result, error } = await supabase.rpc("invite_team_member", {
        pharmacy_id_param: pharmacyId,
        email_param: email,
        role_param: role,
        initial_password: password,
      });

      if (error) throw error;

      if (!result) {
        throw new Error("Failed to invite team member");
      }

      // Show the generated password to the admin
      toast.success("Membre ajouté avec succès", {
        description: `
          Email: ${email}
          Mot de passe: ${password}
          
          Veuillez communiquer ces informations au membre de manière sécurisée.
        `,
        duration: 10000,
      });

      // Refresh team members list
      fetchTeamMembers();
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  const updateMemberRole = async (memberId: string, role: TeamMemberRole) => {
    try {
      const { error: updateError } = await supabase
        .from("pharmacy_team_members")
        .update({ role })
        .eq("id", memberId);

      if (updateError) throw updateError;

      toast.success("Rôle mis à jour avec succès");
      fetchTeamMembers();
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  const updateMemberStatus = async (
    memberId: string,
    status: TeamMemberStatus
  ) => {
    try {
      const { error: updateError } = await supabase
        .from("pharmacy_team_members")
        .update({ status })
        .eq("id", memberId);

      if (updateError) throw updateError;

      toast.success("Statut mis à jour avec succès");
      fetchTeamMembers();
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  const removeMember = async (memberId: string) => {
    try {
      const { error: deleteError } = await supabase
        .from("pharmacy_team_members")
        .delete()
        .eq("id", memberId);

      if (deleteError) throw deleteError;

      toast.success("Membre supprimé avec succès");
      fetchTeamMembers();
    } catch (err) {
      setError(err as Error);
      throw err;
    }
  };

  return {
    teamMembers,
    isLoading,
    error,
    inviteMember,
    updateMemberRole,
    updateMemberStatus,
    removeMember,
  };
}
