'use client';

import { useState } from 'react';
import { useSupabase } from '@/contexts/supabase-context';
import { toast } from 'sonner';

export function useProductInterest() {
  const [isSubmitting, setIsSubmitting] = useState(false);
  const { supabase, pharmacy } = useSupabase();

  const showInterest = async (
    productId: string,
    quantity: number,
    message: string
  ) => {
    if (!pharmacy?.id) {
      toast.error('Vous devez être connecté pour montrer votre intérêt');
      return;
    }

    setIsSubmitting(true);
    try {
      // Insert the interest record
      const { error: interestError } = await supabase
        .from('product_interests')
        .insert({
          product_id: productId,
          interested_pharmacy_id: pharmacy.id,
          quantity,
          message,
          status: 'pending'
        });

      if (interestError) throw interestError;

      // Get the product and seller details
      const { data: product } = await supabase
        .from('products')
        .select('*, pharmacy:pharmacies(*)')
        .eq('id', productId)
        .single();

      if (!product) throw new Error('Product not found');

      // Create a notification for the seller
      const { error: notificationError } = await supabase
        .from('notifications')
        .insert({
          user_id: product.pharmacy.id,
          type: 'interest_shown',
          title: 'Nouvel intérêt',
          message: `${pharmacy.name} est intéressé par votre produit ${product.name}`,
          data: {
            product_id: productId,
            interested_pharmacy_id: pharmacy.id,
            quantity,
            message
          }
        });

      if (notificationError) throw notificationError;

      toast.success('Votre intérêt a été enregistré');
    } catch (error) {
      console.error('Error showing interest:', error);
      toast.error('Erreur lors de l\'enregistrement de votre intérêt');
    } finally {
      setIsSubmitting(false);
    }
  };

  const getInterests = async (productId: string) => {
    try {
      const { data, error } = await supabase
        .from('product_interests')
        .select('*, interested_pharmacy:pharmacies(*)')
        .eq('product_id', productId)
        .order('created_at', { ascending: false });

      if (error) throw error;
      return data;
    } catch (error) {
      console.error('Error fetching interests:', error);
      return [];
    }
  };

  const updateInterestStatus = async (
    interestId: string,
    status: 'accepted' | 'rejected' | 'completed'
  ) => {
    if (!pharmacy?.id) return;

    try {
      const { error } = await supabase
        .from('product_interests')
        .update({ status, updated_at: new Date().toISOString() })
        .eq('id', interestId);

      if (error) throw error;

      toast.success('Statut mis à jour avec succès');
    } catch (error) {
      console.error('Error updating interest status:', error);
      toast.error('Erreur lors de la mise à jour du statut');
    }
  };

  return {
    showInterest,
    getInterests,
    updateInterestStatus,
    isSubmitting
  };
}