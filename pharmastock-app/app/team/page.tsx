"use client";

import { useState } from "react";
import {
  <PERSON><PERSON><PERSON>,
  Card,
  Button,
  TextInput,
  Select,
  Table,
  Modal,
  Badge,
  Group,
  Stack,
  Text,
  Title,
  ActionIcon,
  Tooltip,
  Switch,
  Box,
  Paper,
  ThemeIcon,
  Flex,
  Loader,
  Alert,
  rem,
} from "@mantine/core";
import {
  IconUserPlus,
  IconEdit,
  IconTrash,
  IconShield,
  IconUsers,
  IconSettings,
  IconCheck,
  IconX,
  IconAlertCircle,
} from "@tabler/icons-react";
import { useTeam } from "@/hooks/use-team";
import { useAuth } from "@/contexts/auth-context";
import { notifications } from "@mantine/notifications";
import { useSupabase } from "@/contexts/supabase-context";

export default function TeamPage() {
  const [isAddingMember, setIsAddingMember] = useState(false);
  const [newMemberEmail, setNewMemberEmail] = useState("");
  const [newMemberRole, setNewMemberRole] = useState<
    "pharmacist" | "assistant"
  >("assistant");
  const [mounted, setMounted] = useState(false);
  const { pharmacy: userPharmacy, user } = useAuth();
  const {
    teamMembers,
    isLoading,
    error,
    inviteMember,
    updateMemberRole,
    updateMemberStatus,
    removeMember,
  } = useTeam(userPharmacy?.id || null);

  // Handle hydration mismatch by only rendering after mount
  if (typeof window !== "undefined") {
    if (!mounted) {
      setMounted(true);
      return null;
    }
  }

  if (!userPharmacy && user?.role !== "owner") {
    return (
      <Container size="xl" py="md">
        <Title order={1}>Équipe</Title>
        <Text c="dimmed" mt="sm">
          Vous devez être connecté à une pharmacie pour gérer votre équipe.
        </Text>
      </Container>
    );
  }

  const handleInviteMember = async () => {
    try {
      await inviteMember(newMemberEmail, newMemberRole);
      notifications.show({
        title: "Succès",
        message: "Invitation envoyée avec succès",
        color: "green",
        icon: <IconCheck size={16} />,
      });
      setIsAddingMember(false);
      setNewMemberEmail("");
      setNewMemberRole("assistant");
    } catch (error) {
      notifications.show({
        title: "Erreur",
        message: "Erreur lors de l'envoi de l'invitation",
        color: "red",
        icon: <IconX size={16} />,
      });
    }
  };

  const handleUpdateRole = async (
    memberId: string,
    role: "pharmacist" | "assistant"
  ) => {
    try {
      await updateMemberRole(memberId, role);
      notifications.show({
        title: "Succès",
        message: "Rôle mis à jour avec succès",
        color: "green",
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      notifications.show({
        title: "Erreur",
        message: "Erreur lors de la mise à jour du rôle",
        color: "red",
        icon: <IconX size={16} />,
      });
    }
  };

  const handleUpdateStatus = async (
    memberId: string,
    status: "active" | "inactive"
  ) => {
    try {
      await updateMemberStatus(memberId, status);
      notifications.show({
        title: "Succès",
        message: "Statut mis à jour avec succès",
        color: "green",
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      notifications.show({
        title: "Erreur",
        message: "Erreur lors de la mise à jour du statut",
        color: "red",
        icon: <IconX size={16} />,
      });
    }
  };

  const handleRemoveMember = async (memberId: string) => {
    try {
      await removeMember(memberId);
      notifications.show({
        title: "Succès",
        message: "Membre supprimé avec succès",
        color: "green",
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      notifications.show({
        title: "Erreur",
        message: "Erreur lors de la suppression du membre",
        color: "red",
        icon: <IconX size={16} />,
      });
    }
  };

  if (isLoading) {
    return (
      <Container size="xl" py="md">
        <Title order={1}>Équipe</Title>
        <Group mt="md">
          <Loader size="sm" />
          <Text c="dimmed">Chargement...</Text>
        </Group>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="xl" py="md">
        <Title order={1}>Équipe</Title>
        <Alert color="red" icon={<IconAlertCircle size={16} />} mt="md">
          Une erreur est survenue: {error.message}
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        <Group justify="space-between">
          <Box>
            <Title order={1} size="h2" c="#00B5FF" mb="xs">
              <Group gap="sm">
                <ThemeIcon size="lg" radius="md" color="#00B5FF">
                  <IconUsers size={rem(24)} />
                </ThemeIcon>
                Équipe
              </Group>
            </Title>
            <Text c="dimmed" size="sm">
              Gérez les membres de votre équipe et leurs permissions
            </Text>
          </Box>
          <Button
            leftSection={<IconUserPlus size={16} />}
            onClick={() => setIsAddingMember(true)}
          >
            Ajouter un membre
          </Button>
        </Group>

        {/* Add Member Modal */}
        <Modal
          opened={isAddingMember}
          onClose={() => setIsAddingMember(false)}
          title="Ajouter un nouveau membre"
          size="md"
        >
          <Stack gap="md">
            <Text size="sm" c="dimmed">
              Ajoutez un nouveau membre à votre équipe et définissez ses
              permissions
            </Text>

            <TextInput
              label="Email"
              type="email"
              placeholder="Ex: <EMAIL>"
              value={newMemberEmail}
              onChange={(e) => setNewMemberEmail(e.currentTarget.value)}
              required
            />

            <Select
              label="Rôle"
              placeholder="Sélectionnez un rôle"
              value={newMemberRole}
              onChange={(value) =>
                setNewMemberRole(value as "pharmacist" | "assistant")
              }
              data={[
                { value: "pharmacist", label: "Pharmacien" },
                { value: "assistant", label: "Assistant" },
              ]}
              required
            />

            <Group justify="flex-end" mt="md">
              <Button
                variant="outline"
                onClick={() => setIsAddingMember(false)}
              >
                Annuler
              </Button>
              <Button onClick={handleInviteMember}>Ajouter le membre</Button>
            </Group>
          </Stack>
        </Modal>

        <Card withBorder radius="md" p="md">
          <Table.ScrollContainer minWidth={800}>
            <Table striped highlightOnHover withTableBorder>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Membre</Table.Th>
                  <Table.Th>Rôle</Table.Th>
                  <Table.Th>Statut</Table.Th>
                  <Table.Th>Dernière activité</Table.Th>
                  <Table.Th style={{ textAlign: "right" }}>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {teamMembers.map((member) => (
                  <Table.Tr key={member.id}>
                    <Table.Td>
                      <Text fw={500}>{member.user.email}</Text>
                    </Table.Td>
                    <Table.Td>
                      <Select
                        value={member.role}
                        onChange={(value) =>
                          handleUpdateRole(
                            member.id,
                            value as "pharmacist" | "assistant"
                          )
                        }
                        data={[
                          { value: "pharmacist", label: "Pharmacien" },
                          { value: "assistant", label: "Assistant" },
                        ]}
                        w={140}
                      />
                    </Table.Td>
                    <Table.Td>
                      <Badge
                        color={member.status === "active" ? "green" : "red"}
                        variant="light"
                        style={{ cursor: "pointer" }}
                        onClick={() =>
                          handleUpdateStatus(
                            member.id,
                            member.status === "active" ? "inactive" : "active"
                          )
                        }
                      >
                        {member.status === "active" ? "Actif" : "Inactif"}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c="dimmed">
                        {member.last_active || "Jamais"}
                      </Text>
                    </Table.Td>
                    <Table.Td style={{ textAlign: "right" }}>
                      <Group justify="flex-end">
                        <Tooltip label="Supprimer le membre">
                          <ActionIcon
                            color="red"
                            variant="light"
                            onClick={() => handleRemoveMember(member.id)}
                          >
                            <IconTrash size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Table.ScrollContainer>
        </Card>

        <Card withBorder radius="md" p="md">
          <Stack gap="lg">
            <Box>
              <Title order={3}>Permissions des rôles</Title>
              <Text c="dimmed" size="sm">
                Définissez les permissions pour chaque rôle dans votre équipe
              </Text>
            </Box>

            <Stack gap="lg">
              <Box>
                <Title order={4} mb="md">
                  Pharmacien
                </Title>
                <Paper p="md" withBorder radius="sm">
                  <Group justify="space-between">
                    <Box>
                      <Text fw={500}>Gestion complète de la pharmacie</Text>
                      <Text size="sm" c="dimmed">
                        Accès total à toutes les fonctionnalités
                      </Text>
                    </Box>
                    <Switch checked disabled />
                  </Group>
                </Paper>
              </Box>

              <Box>
                <Title order={4} mb="md">
                  Assistant
                </Title>
                <Stack gap="sm">
                  <Paper p="md" withBorder radius="sm">
                    <Group justify="space-between">
                      <Box>
                        <Text fw={500}>Gestion des médicaments</Text>
                        <Text size="sm" c="dimmed">
                          Peut gérer les médicaments et les stocks
                        </Text>
                      </Box>
                      <Switch defaultChecked disabled />
                    </Group>
                  </Paper>
                  <Paper p="md" withBorder radius="sm">
                    <Group justify="space-between">
                      <Box>
                        <Text fw={500}>Gestion des demandes urgentes</Text>
                        <Text size="sm" c="dimmed">
                          Peut créer et répondre aux demandes urgentes
                        </Text>
                      </Box>
                      <Switch defaultChecked disabled />
                    </Group>
                  </Paper>
                </Stack>
              </Box>
            </Stack>
          </Stack>
        </Card>
      </Stack>
    </Container>
  );
}
