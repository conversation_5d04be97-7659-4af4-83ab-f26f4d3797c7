"use client";

export const dynamic = "force-dynamic";

import { useEffect, useState, Suspense } from "react";
import { useSearchParams } from "next/navigation";
import { ListForSaleForm } from "@/components/marketplace/list-for-sale-form";
import { Loader2 } from "lucide-react";

interface MedicationInfo {
  name: string;
  manufacturer: string;
  dosage: string;
  form: string;
  activeIngredients: string;
  posology: string;
  indications: string;
  contraindications: string;
  reimbursementRate: string;
  packaging: string;
  cip13?: string;
}

function ListPageContent() {
  const searchParams = useSearchParams();
  const [loading, setLoading] = useState(false);
  const [medicationData, setMedicationData] = useState<MedicationInfo | null>(
    null
  );

  useEffect(() => {
    const code = searchParams.get("code");
    if (!code) return;

    const lookupMedication = async () => {
      setLoading(true);
      try {
        const response = await fetch("/api/lookup-medication", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({ barcode: code }),
        });

        if (!response.ok) {
          throw new Error("Failed to fetch medication data");
        }

        const data = await response.json();
        setMedicationData(data);
      } catch (err) {
        console.error("Error fetching medication data:", err);
      } finally {
        setLoading(false);
      }
    };

    lookupMedication();
  }, [searchParams]);

  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center space-y-4">
          <Loader2 className="w-8 h-8 animate-spin mx-auto" />
          <p className="text-lg font-medium">Chargement des informations...</p>
        </div>
      </div>
    );
  }

  return <ListForSaleForm initialData={medicationData} />;
}

export default function ListPage() {
  return (
    <Suspense
      fallback={
        <div className="min-h-screen flex items-center justify-center">
          <div className="text-center space-y-4">
            <Loader2 className="w-8 h-8 animate-spin mx-auto" />
            <p className="text-lg font-medium">Chargement...</p>
          </div>
        </div>
      }
    >
      <ListPageContent />
    </Suspense>
  );
}
