"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { toast } from "sonner";
import { useSupabase } from "@/contexts/supabase-context";
import { ProductDetailModal } from "@/components/marketplace/product-detail-modal";
import { SellProductModal } from "@/components/marketplace/sell-product-modal";
import { Button as UIButton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useAuth } from "@/contexts/auth-context";
import {
  Title,
  Text,
  Group,
  TextInput,
  SegmentedControl,
  Paper,
  Loader,
  Select,
  Stack,
  Grid,
  Button,
  Tabs,
  Card as MantineCard,
  Container,
  SimpleGrid,
  Tooltip,
} from "@mantine/core";
import {
  IconPlus,
  IconSearch,
  IconGridDots,
  IconList,
  IconAlertCircle,
  IconRefresh,
  IconMapPin,
  IconCategory,
  IconSortAscending,
  IconFilter,
  IconArrowsExchange,
  IconClock,
  IconCheck,
  IconX,
  IconEye,
} from "@tabler/icons-react";
import {
  Flame,
  ShoppingCart,
  Bookmark,
  ArrowRightLeft,
  Clock,
  Package,
  CheckCircle,
  XCircle,
  AlertCircle,
  Eye,
} from "lucide-react";
import { AppShell } from "@/components/layout/app-shell";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

// --- TYPE DEFINITIONS ---
interface ListingWithDetails {
  id: string;
  listing_id: string;
  pharmacy_id: string;
  product_id: string;
  status: "active" | "inactive" | "sold";
  pharmacy: {
    id: string;
    name: string;
    distance: number;
  };
  product: {
    id: string;
    name: string;
    description: string | null;
    category: string;
    expiry_date: string;
    original_price?: number;
  };
  quantity: number;
  price_per_unit: number;
  minimum_order: number;
  expires_at: string | null;
  created_at: string;
  updated_at: string;
  posting_type: string;
  is_high_demand?: boolean; // For high-demand placeholder
}

interface ActionsOverlayProps {
  listing: ListingWithDetails;
  onReserve: (listing: ListingWithDetails) => void;
  onToggleWatchList: (listingId: string) => void;
  onViewDetails: (listing: ListingWithDetails) => void;
  isInWatchList: boolean;
  inline?: boolean;
}

interface Exchange {
  id: string;
  type: "outgoing" | "incoming";
  product: string;
  quantity: number;
  partner: string;
  status: "pending" | "accepted" | "completed" | "rejected";
  created_at: string;
  expiry_date: string;
}

interface Reservation {
  id: string;
  listing_id: string;
  pharmacy_id: string;
  quantity: number;
  message: string | null;
  status: "pending" | "accepted" | "rejected" | "completed";
  created_at: string;
  type: "incoming" | "outgoing";
  listing: {
    price_per_unit: number;
    product: {
      name: string;
      category: string;
      expiry_date: string;
    };
    pharmacy: {
      name: string;
    };
  };
}

// --- HELPER FUNCTIONS ---
const isUrgent = (expiryDate: string) => {
  if (!expiryDate) return false;
  const now = new Date();
  const expiry = new Date(expiryDate);
  const thirtyDays = 30 * 24 * 60 * 60 * 1000;
  return expiry.getTime() - now.getTime() < thirtyDays;
};

const isNew = (createdAt: string) => {
  if (!createdAt) return false;
  const now = new Date();
  const created = new Date(createdAt);
  const twoDays = 2 * 24 * 60 * 60 * 1000;
  return now.getTime() - created.getTime() < twoDays;
};

// Exchange helper functions
const getStatusColor = (status: string) => {
  switch (status) {
    case "pending":
      return "yellow";
    case "accepted":
      return "blue";
    case "completed":
      return "green";
    case "rejected":
      return "red";
    default:
      return "gray";
  }
};

const getStatusText = (status: string) => {
  switch (status) {
    case "pending":
      return "En attente";
    case "accepted":
      return "Accepté";
    case "completed":
      return "Terminé";
    case "rejected":
      return "Refusé";
    default:
      return status;
  }
};

const getStatusIcon = (status: string) => {
  switch (status) {
    case "pending":
      return <Clock className="h-4 w-4" />;
    case "accepted":
      return <CheckCircle className="h-4 w-4" />;
    case "completed":
      return <CheckCircle className="h-4 w-4" />;
    case "rejected":
      return <XCircle className="h-4 w-4" />;
    default:
      return <AlertCircle className="h-4 w-4" />;
  }
};

// --- CHILD COMPONENTS ---

const ActionsOverlay = ({
  onReserve,
  onToggleWatchList,
  onViewDetails,
  isInWatchList,
  listing,
  inline = false,
}: ActionsOverlayProps) => (
  <div
    className="absolute inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center p-2"
    onClick={(e) => e.stopPropagation()}
  >
    <div
      className={`flex ${
        inline
          ? "flex-row items-center gap-2"
          : "flex-col items-stretch gap-2 w-48"
      }`}
    >
      <UIButton
        variant="default"
        size="sm"
        onClick={() => setTimeout(() => onReserve(listing), 0)}
      >
        <ShoppingCart className="h-4 w-4 mr-2" />
        Réserver
      </UIButton>
      <UIButton
        variant="outline"
        size="sm"
        onClick={() => onToggleWatchList(listing.listing_id)}
      >
        <Bookmark
          className={`h-4 w-4 mr-2 ${isInWatchList ? "fill-current" : ""}`}
        />
        {isInWatchList ? "Suivi" : "Suivre"}
      </UIButton>
      <UIButton
        variant="ghost"
        size="sm"
        onClick={() => setTimeout(() => onViewDetails(listing), 0)}
      >
        Voir détails
      </UIButton>
    </div>
  </div>
);

function MarketplaceContent() {
  const { supabase } = useSupabase();
  const { user, pharmacy } = useAuth();
  const router = useRouter();
  const searchParams = useSearchParams();

  // State Management
  const [listings, setListings] = useState<ListingWithDetails[]>([]);
  const [filteredListings, setFilteredListings] = useState<
    ListingWithDetails[]
  >([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // UI State
  const [viewMode, setViewMode] = useState("grid");
  const [isSellModalOpen, setSellModalOpen] = useState(false);
  const [selectedListing, setSelectedListing] =
    useState<ListingWithDetails | null>(null);
  const [hoveredCard, setHoveredCard] = useState<string | null>(null);

  // Filtering State
  const [searchTerm, setSearchTerm] = useState("");
  const [offerTypeFilter, setOfferTypeFilter] = useState("all");
  const [sortBy, setSortBy] = useState("distance-asc");

  // Handle URL tab parameter
  useEffect(() => {
    const tabParam = searchParams?.get("tab");
    if (
      tabParam &&
      ["all", "sale", "exchange", "exchanges", "reservations"].includes(
        tabParam
      )
    ) {
      setOfferTypeFilter(tabParam);
    }
  }, [searchParams]);
  const [userLocation, setUserLocation] = useState<{
    lat: number;
    lon: number;
  } | null>(null);
  const [maxDistance, setMaxDistance] = useState(50);
  const [favoriteListings, setFavoriteListings] = useState<string[]>([]);

  // Exchange State
  const [exchanges, setExchanges] = useState<Exchange[]>([]);

  // Reservation State
  const [reservations, setReservations] = useState<Reservation[]>([]);

  // New exchange state
  const [isExchangeOpen, setIsExchangeOpen] = useState(false);
  const [exchangeForm, setExchangeForm] = useState({
    product: "",
    quantity: 1,
    partner: "",
    message: "",
  });
  const [submittingExchange, setSubmittingExchange] = useState(false);
  const [exchangeSuccess, setExchangeSuccess] = useState(false);

  // --- DATA FETCHING ---
  const fetchListings = useCallback(
    async (lat: number, lon: number) => {
      setLoading(true);
      setError(null);
      try {
        const { data, error } = await supabase.rpc(
          "search_products_with_distance",
          {
            search_term: "",
            lat: lat,
            lon: lon,
            max_distance: 20000, // Large initial radius
          }
        );

        if (error) throw new Error(error.message);

        const validListings = (data || [])
          .map((item: any) => {
            if (!item) return null;
            return {
              id: item.id || "",
              listing_id: item.listing_id || "",
              pharmacy_id: item.pharmacy_id || "",
              product_id: item.product_id || item.product?.id || "",
              status:
                (item.status as "active" | "inactive" | "sold") || "active",
              pharmacy: {
                id: item.pharmacy_id || item.pharmacy?.id || "",
                name: item.pharmacy_name || "Unknown Pharmacy",
                distance: item.distance || 0,
              },
              product: {
                id: item.product_id || item.id || "",
                name: item.name || "Unknown Product",
                description: item.description || null,
                category: item.category || "N/A",
                expiry_date: item.expiry_date,
                original_price: item.original_price,
              },
              quantity: item.quantity || 0,
              price_per_unit: item.price_per_unit || 0,
              minimum_order: item.minimum_order || 1,
              expires_at: item.expires_at || null,
              created_at: item.created_at || new Date().toISOString(),
              updated_at: item.updated_at || new Date().toISOString(),
              posting_type: item.posting_type || "sale",
              is_high_demand: (item.views || 0) > 50, // Placeholder
            };
          })
          .filter(Boolean) as ListingWithDetails[];

        setListings(validListings);
      } catch (err) {
        setError(
          err instanceof Error ? err.message : "An unknown error occurred."
        );
        toast.error("Erreur lors de la récupération des offres.");
      } finally {
        setLoading(false);
      }
    },
    [supabase]
  );

  useEffect(() => {
    // Simplified location logic
    navigator.geolocation.getCurrentPosition(
      (position) => {
        setUserLocation({
          lat: position.coords.latitude,
          lon: position.coords.longitude,
        });
      },
      () => {
        // Fallback to a default location (e.g., Casablanca)
        setUserLocation({ lat: 33.5731, lon: -7.5898 });
        toast.info(
          "Localisation par défaut utilisée. Veuillez autoriser l'accès à votre position pour des résultats plus précis."
        );
      }
    );
  }, []);

  useEffect(() => {
    if (userLocation) {
      fetchListings(userLocation.lat, userLocation.lon);
      fetchExchanges();
      fetchReservations();
    }
  }, [userLocation, fetchListings]);

  // Fetch real exchanges
  const fetchExchanges = useCallback(async () => {
    try {
      const response = await fetch("/api/marketplace/exchanges", {
        method: "GET",
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setExchanges(data.exchanges || []);
      }
    } catch (error) {
      console.error("Error fetching exchanges:", error);
    }
  }, []);

  // Fetch real reservations
  const fetchReservations = useCallback(async () => {
    try {
      const response = await fetch("/api/marketplace/reservations", {
        method: "GET",
        credentials: "include",
      });

      if (response.ok) {
        const data = await response.json();
        setReservations(data.reservations || []);
      }
    } catch (error) {
      console.error("Error fetching reservations:", error);
    }
  }, []);

  // --- FILTERING AND SORTING ---
  useEffect(() => {
    let result = [...listings];

    // Search term
    if (searchTerm) {
      result = result.filter((l) =>
        l.product.name.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Offer type
    if (offerTypeFilter !== "all") {
      result = result.filter((l) => l.posting_type === offerTypeFilter);
    }

    // Distance
    result = result.filter((l) => l.pharmacy.distance / 1000 <= maxDistance);

    // Sorting
    result.sort((a, b) => {
      switch (sortBy) {
        case "distance-asc":
          return a.pharmacy.distance - b.pharmacy.distance;
        case "price-asc":
          return a.price_per_unit - b.price_per_unit;
        case "expiry-asc":
          return (
            new Date(a.product.expiry_date).getTime() -
            new Date(b.product.expiry_date).getTime()
          );
        default:
          return 0;
      }
    });

    setFilteredListings(result);
  }, [searchTerm, listings, offerTypeFilter, sortBy, maxDistance]);

  // --- EVENT HANDLERS ---
  const handleToggleWatchList = (listingId: string) => {
    setFavoriteListings((prev) =>
      prev.includes(listingId)
        ? prev.filter((id) => id !== listingId)
        : [...prev, listingId]
    );
    toast.success(
      favoriteListings.includes(listingId)
        ? "Offre retirée du suivi."
        : "Offre ajoutée au suivi."
    );
  };

  const handleReservationStatusChange = (
    reservationId: string,
    newStatus: "accepted" | "rejected"
  ) => {
    setReservations((prev) =>
      prev.map((res) =>
        res.id === reservationId ? { ...res, status: newStatus } : res
      )
    );
    toast.success(
      `Réservation ${
        newStatus === "accepted" ? "acceptée" : "refusée"
      } avec succès`
    );
  };

  // Exchange handler
  const handleExchangeSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmittingExchange(true);
    try {
      const res = await fetch("/api/marketplace/exchanges", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(exchangeForm),
      });
      if (!res.ok) throw new Error("Erreur lors de la création de l'échange");
      setIsExchangeOpen(false);
      setExchangeForm({ product: "", quantity: 1, partner: "", message: "" });
      setExchangeSuccess(true);
    } catch (err) {
      // Optionally handle error
    } finally {
      setSubmittingExchange(false);
    }
  };

  // --- RENDER COMPONENTS ---
  const ListingCard = ({ listing }: { listing: ListingWithDetails }) => {
    const { product } = listing;
    const potentialSavings =
      product.original_price && product.original_price > listing.price_per_unit
        ? Math.round(
            ((product.original_price - listing.price_per_unit) /
              product.original_price) *
              100
          )
        : null;
    const urgent = isUrgent(product.expiry_date);
    const newListing = isNew(listing.created_at);
    const highDemand = listing.is_high_demand;

    return (
      <MantineCard
        shadow="sm"
        p="lg"
        radius="md"
        withBorder
        className={`flex flex-col h-full group ${
          urgent ? "border-2 border-red-500" : ""
        }`}
        onMouseEnter={() => setHoveredCard(listing.listing_id)}
        onMouseLeave={() => setHoveredCard(null)}
        onClick={() => setTimeout(() => setSelectedListing(listing), 0)}
      >
        {hoveredCard === listing.listing_id && (
          <ActionsOverlay
            listing={listing}
            onReserve={setSelectedListing}
            onToggleWatchList={handleToggleWatchList}
            onViewDetails={setSelectedListing}
            isInWatchList={favoriteListings.includes(listing.listing_id)}
          />
        )}
        <Stack justify="space-between" className="flex-grow">
          <div>
            <Text size="sm" c="dimmed">
              {product.category}
            </Text>
            <Title order={4} lineClamp={2}>
              {product.name}
            </Title>
            <Group gap="xs" align="center">
              <IconMapPin size={16} />
              <Text size="sm">
                {listing.pharmacy.name} (
                {Math.round(listing.pharmacy.distance / 1000)} km)
              </Text>
            </Group>
          </div>
          <Stack gap="xs">
            <Badge color="blue">Prix Négociable</Badge>
            <Text size="xs" c="dimmed">
              Expire le: {new Date(product.expiry_date).toLocaleDateString()}
            </Text>
            <Text size="xs" c="dimmed">
              Quantité disponible: {listing.quantity}+ unités
            </Text>
            <Text size="xs" c="green" fw={500}>
              Contactez pour prix et conditions
            </Text>
          </Stack>
          <Group gap="xs" wrap="wrap">
            {newListing && <Badge color="green">Nouveau</Badge>}
            {urgent && <Badge color="red">Expiration Urgente</Badge>}
            {highDemand && (
              <Badge color="orange">
                <Flame size={14} className="inline-block mr-1" />
                Forte Demande
              </Badge>
            )}
            <Badge color="green" variant="outline">
              Excédent Disponible
            </Badge>
          </Group>
        </Stack>
      </MantineCard>
    );
  };

  const ListingRow = ({ listing }: { listing: ListingWithDetails }) => {
    const { product } = listing;
    const potentialSavings =
      product.original_price && product.original_price > listing.price_per_unit
        ? Math.round(
            ((product.original_price - listing.price_per_unit) /
              product.original_price) *
              100
          )
        : null;
    const urgent = isUrgent(product.expiry_date);
    const newListing = isNew(listing.created_at);
    const highDemand = listing.is_high_demand;

    return (
      <Paper
        shadow="xs"
        p="md"
        radius="md"
        withBorder
        className={`relative group ${urgent ? "border-2 border-red-500" : ""}`}
        onMouseEnter={() => setHoveredCard(listing.listing_id)}
        onMouseLeave={() => setHoveredCard(null)}
        onClick={() => setTimeout(() => setSelectedListing(listing), 0)}
      >
        {hoveredCard === listing.listing_id && (
          <ActionsOverlay
            listing={listing}
            onReserve={setSelectedListing}
            onToggleWatchList={handleToggleWatchList}
            onViewDetails={setSelectedListing}
            isInWatchList={favoriteListings.includes(listing.listing_id)}
            inline
          />
        )}
        <Grid align="center">
          <Grid.Col span={{ base: 12, sm: 4 }}>
            <Title order={5}>{product.name}</Title>
            <Text size="sm" c="dimmed">
              {listing.pharmacy.name}
            </Text>
          </Grid.Col>
          <Grid.Col span={{ base: 6, sm: 2 }}>
            <Badge color="blue">Prix Négociable</Badge>
            <Text size="xs" c="green" fw={500}>
              Contactez vendeur
            </Text>
          </Grid.Col>
          <Grid.Col span={{ base: 6, sm: 2 }}>
            <Text size="sm">Expire le:</Text>
            <Text size="sm" fw={500}>
              {new Date(product.expiry_date).toLocaleDateString()}
            </Text>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 2 }}>
            <Text size="sm">Quantité:</Text>
            <Text size="sm" fw={500}>
              {listing.quantity} (min. {listing.minimum_order})
            </Text>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 2 }}>
            <Group gap="xs" justify="flex-end">
              {newListing && <Badge color="green">Nouveau</Badge>}
              {urgent && <Badge color="red">Urgent</Badge>}
              {highDemand && (
                <Badge color="orange">
                  <Flame size={14} className="inline-block mr-1" />
                  Populaire
                </Badge>
              )}
              {potentialSavings && (
                <Badge color="blue">Économie {potentialSavings}%</Badge>
              )}
            </Group>
          </Grid.Col>
        </Grid>
      </Paper>
    );
  };

  if (error) {
    return (
      <Container>
        <Paper p="xl" shadow="xs">
          <Title order={3}>Erreur</Title>
          <Text>{error}</Text>
        </Paper>
      </Container>
    );
  }

  return (
    <Stack gap="lg" className="h-full">
      {/* Header & Actions */}
      <Group justify="space-between">
        <div>
          <Title order={2}>Marketplace</Title>
          <Text c="dimmed">
            Trouvez les meilleures offres de produits pharmaceutiques.
          </Text>
        </div>
        <Button
          onClick={() => setSellModalOpen(true)}
          leftSection={<IconPlus size={18} />}
        >
          Créer une Annonce
        </Button>
      </Group>

      {/* Filters */}
      <Paper shadow="xs" p="md" radius="md" withBorder>
        <Grid align="flex-end">
          <Grid.Col span={{ base: 12, md: 5 }}>
            <TextInput
              label="Rechercher un produit"
              placeholder="Doliprane, Spasfon..."
              leftSection={<IconSearch size={16} />}
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.currentTarget.value)}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 3 }}>
            <Select
              label="Trier par"
              data={[
                { value: "distance-asc", label: "Distance" },
                { value: "price-asc", label: "Prix croissant" },
                { value: "expiry-asc", label: "Date d'expiration" },
              ]}
              value={sortBy}
              onChange={(val) => setSortBy(val || "distance-asc")}
              leftSection={<IconSortAscending size={16} />}
            />
          </Grid.Col>
          <Grid.Col span={{ base: 12, md: 4 }}>
            <Stack gap="xs">
              <Text size="sm" fw={500}>
                Distance (km)
              </Text>
              <SegmentedControl
                fullWidth
                value={String(maxDistance)}
                onChange={(val) => setMaxDistance(Number(val))}
                data={[
                  { label: "5 km", value: "5" },
                  { label: "10 km", value: "10" },
                  { label: "25 km", value: "25" },
                  { label: "50 km", value: "50" },
                ]}
              />
            </Stack>
          </Grid.Col>
        </Grid>
      </Paper>

      {/* Tabs & Results */}
      <Tabs
        value={offerTypeFilter}
        onChange={(val) => setOfferTypeFilter(val || "all")}
      >
        <Tabs.List>
          <Tabs.Tab value="all">Toutes les offres</Tabs.Tab>
          <Tabs.Tab value="sale">À vendre</Tabs.Tab>
          <Tabs.Tab value="exchange">À échanger</Tabs.Tab>
          <Tabs.Tab
            value="exchanges"
            leftSection={<IconArrowsExchange size={16} />}
          >
            Mes Échanges
          </Tabs.Tab>
          <Tabs.Tab value="reservations" leftSection={<IconClock size={16} />}>
            Réservations
          </Tabs.Tab>
        </Tabs.List>
        <Group justify="flex-end" my="sm">
          <SegmentedControl
            value={viewMode}
            onChange={setViewMode}
            data={[
              {
                label: (
                  <Tooltip label="Grille">
                    <IconGridDots size={16} />
                  </Tooltip>
                ),
                value: "grid",
              },
              {
                label: (
                  <Tooltip label="Liste">
                    <IconList size={16} />
                  </Tooltip>
                ),
                value: "list",
              },
            ]}
          />
        </Group>

        <Tabs.Panel value={offerTypeFilter} pt="md">
          {loading ? (
            <Group justify="center" p="xl">
              <Loader />
            </Group>
          ) : filteredListings.length > 0 ? (
            viewMode === "grid" ? (
              <SimpleGrid cols={{ base: 1, sm: 2, md: 3, lg: 4 }} spacing="lg">
                {filteredListings.map((listing) => (
                  <ListingCard key={listing.listing_id} listing={listing} />
                ))}
              </SimpleGrid>
            ) : (
              <Stack gap="sm">
                {filteredListings.map((listing) => (
                  <ListingRow key={listing.listing_id} listing={listing} />
                ))}
              </Stack>
            )
          ) : (
            <Paper p="xl" withBorder style={{ textAlign: "center" }}>
              <Title order={4}>Aucun résultat trouvé</Title>
              <Text c="dimmed">
                Essayez d'ajuster vos filtres ou d'élargir votre rayon de
                recherche.
              </Text>
            </Paper>
          )}
        </Tabs.Panel>

        {/* Exchanges Tab Panel */}
        <Tabs.Panel value="exchanges" pt="md">
          <Stack gap="lg">
            {/* Exchange Statistics */}
            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #00B5FF 0%, #0099E6 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      Échanges Actifs
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {
                        exchanges.filter(
                          (e) =>
                            e.status !== "completed" && e.status !== "rejected"
                        ).length
                      }
                    </Text>
                  </div>
                  <ArrowRightLeft className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>

              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #ffd43b 0%, #fab005 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      En Attente
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {exchanges.filter((e) => e.status === "pending").length}
                    </Text>
                  </div>
                  <Clock className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>

              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #51cf66 0%, #40c057 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      Terminés
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {exchanges.filter((e) => e.status === "completed").length}
                    </Text>
                  </div>
                  <CheckCircle className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>

              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #845ef7 0%, #7048e8 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      Économies
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      2.4k DH
                    </Text>
                  </div>
                  <Package className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>
            </SimpleGrid>

            {/* Exchanges List */}
            <MantineCard withBorder radius="md" p="md">
              <Group justify="space-between" mb="md">
                <div>
                  <Title order={4}>Échanges Récents</Title>
                  <Text c="dimmed" size="sm">
                    Historique de vos échanges de produits pharmaceutiques
                  </Text>
                </div>
                <Button leftSection={<IconPlus size={16} />} color="#00B5FF" onClick={() => setIsExchangeOpen(true)}>
                  Nouvel Échange
                </Button>
              </Group>

              {exchanges.length === 0 ? (
                <Paper
                  p="xl"
                  radius="md"
                  style={{ textAlign: "center", background: "#f8f9fa" }}
                >
                  <ArrowRightLeft className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <Text size="lg" fw={500} c="dimmed" mb="xs">
                    Aucun échange
                  </Text>
                  <Text size="sm" c="dimmed">
                    Commencez à échanger des produits avec le réseau
                  </Text>
                </Paper>
              ) : (
                <Stack gap="sm">
                  {exchanges.map((exchange) => (
                    <Paper key={exchange.id} p="md" withBorder radius="md">
                      <Group justify="space-between">
                        <Group gap="md">
                          <div
                            className={`p-2 rounded-full ${
                              exchange.type === "outgoing"
                                ? "bg-blue-100"
                                : "bg-green-100"
                            }`}
                          >
                            <ArrowRightLeft
                              className={`h-4 w-4 ${
                                exchange.type === "outgoing"
                                  ? "text-blue-600"
                                  : "text-green-600"
                              }`}
                            />
                          </div>
                          <div>
                            <Text fw={500}>{exchange.product}</Text>
                            <Text size="sm" c="dimmed">
                              {exchange.type === "outgoing"
                                ? "Envoyé à"
                                : "Reçu de"}{" "}
                              {exchange.partner}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {exchange.quantity} unités • Expire le{" "}
                              {new Date(
                                exchange.expiry_date
                              ).toLocaleDateString("fr-FR")}
                            </Text>
                          </div>
                        </Group>
                        <Group gap="xs">
                          <Badge variant="outline">
                            {getStatusIcon(exchange.status)}
                            <span className="ml-1">
                              {getStatusText(exchange.status)}
                            </span>
                          </Badge>
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                        </Group>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              )}
            </MantineCard>
          </Stack>
        </Tabs.Panel>

        {/* Reservations Tab Panel */}
        <Tabs.Panel value="reservations" pt="md">
          <Stack gap="lg">
            {/* Reservation Statistics */}
            <SimpleGrid cols={{ base: 1, sm: 2, md: 4 }} spacing="md">
              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #00B5FF 0%, #0099E6 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      Total Réservations
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {reservations.length}
                    </Text>
                  </div>
                  <Bookmark className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>

              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #ffd43b 0%, #fab005 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      En Attente
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {
                        reservations.filter((r) => r.status === "pending")
                          .length
                      }
                    </Text>
                  </div>
                  <Clock className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>

              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #51cf66 0%, #40c057 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      Acceptées
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {
                        reservations.filter((r) => r.status === "accepted")
                          .length
                      }
                    </Text>
                  </div>
                  <CheckCircle className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>

              <MantineCard
                withBorder
                radius="md"
                p="md"
                style={{
                  background:
                    "linear-gradient(135deg, #845ef7 0%, #7048e8 100%)",
                }}
              >
                <Group justify="space-between">
                  <div>
                    <Text c="white" size="sm" fw={500}>
                      Valeur Totale
                    </Text>
                    <Text c="white" size="xl" fw={700}>
                      {reservations
                        .reduce(
                          (sum, r) =>
                            sum + r.quantity * r.listing.price_per_unit,
                          0
                        )
                        .toFixed(2)}{" "}
                      DH
                    </Text>
                  </div>
                  <Package className="h-5 w-5 text-white" />
                </Group>
              </MantineCard>
            </SimpleGrid>

            {/* Reservations List */}
            <MantineCard withBorder radius="md" p="md">
              <Group justify="space-between" mb="md">
                <div>
                  <Title order={4}>Réservations</Title>
                  <Text c="dimmed" size="sm">
                    Gérez vos réservations entrantes et sortantes
                  </Text>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  component="a"
                  href="/urgent-requests"
                  leftSection={<IconAlertCircle size={16} />}
                  color="red"
                >
                  Demandes Urgentes
                </Button>
              </Group>

              {reservations.length === 0 ? (
                <Paper
                  p="xl"
                  radius="md"
                  style={{ textAlign: "center", background: "#f8f9fa" }}
                >
                  <Bookmark className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <Text size="lg" fw={500} c="dimmed" mb="xs">
                    Aucune réservation
                  </Text>
                  <Text size="sm" c="dimmed">
                    Les réservations apparaîtront ici
                  </Text>
                </Paper>
              ) : (
                <Stack gap="sm">
                  {reservations.map((reservation) => (
                    <Paper key={reservation.id} p="md" withBorder radius="md">
                      <Group justify="space-between">
                        <Group gap="md">
                          <div
                            className={`p-2 rounded-full ${
                              reservation.type === "incoming"
                                ? "bg-green-100"
                                : "bg-blue-100"
                            }`}
                          >
                            {reservation.type === "incoming" ? (
                              <ArrowRightLeft className="h-4 w-4 text-green-600 rotate-180" />
                            ) : (
                              <ArrowRightLeft className="h-4 w-4 text-blue-600" />
                            )}
                          </div>
                          <div>
                            <Text fw={500}>
                              {reservation.listing.product.name}
                            </Text>
                            <Text size="sm" c="dimmed">
                              {reservation.type === "incoming"
                                ? "Demande de"
                                : "Réservé par"}{" "}
                              {reservation.listing.pharmacy.name}
                            </Text>
                            <Text size="xs" c="dimmed">
                              {reservation.quantity} unités •{" "}
                              {(
                                reservation.quantity *
                                reservation.listing.price_per_unit
                              ).toFixed(2)}{" "}
                              DH
                            </Text>
                            {reservation.message && (
                              <Text
                                size="xs"
                                c="dimmed"
                                style={{ fontStyle: "italic" }}
                              >
                                "{reservation.message}"
                              </Text>
                            )}
                          </div>
                        </Group>
                        <Group gap="xs">
                          <Badge variant="outline">
                            {getStatusIcon(reservation.status)}
                            <span className="ml-1">
                              {getStatusText(reservation.status)}
                            </span>
                          </Badge>
                          {reservation.status === "pending" &&
                            reservation.type === "incoming" && (
                              <Group gap="xs">
                                <Button
                                  size="xs"
                                  color="green"
                                  variant="light"
                                  onClick={() =>
                                    handleReservationStatusChange(
                                      reservation.id,
                                      "accepted"
                                    )
                                  }
                                >
                                  <IconCheck size={14} />
                                </Button>
                                <Button
                                  size="xs"
                                  color="red"
                                  variant="light"
                                  onClick={() =>
                                    handleReservationStatusChange(
                                      reservation.id,
                                      "rejected"
                                    )
                                  }
                                >
                                  <IconX size={14} />
                                </Button>
                              </Group>
                            )}
                          <Button size="xs" variant="outline">
                            <IconEye size={14} />
                          </Button>
                        </Group>
                      </Group>
                    </Paper>
                  ))}
                </Stack>
              )}
            </MantineCard>
          </Stack>
        </Tabs.Panel>
      </Tabs>

      {/* Modals */}
      <SellProductModal
        isOpen={isSellModalOpen}
        onClose={() => setSellModalOpen(false)}
        onSuccess={() => {
          if (userLocation) {
            fetchListings(userLocation.lat, userLocation.lon);
          }
        }}
      />
      {selectedListing && (
        <ProductDetailModal
          isOpen={!!selectedListing}
          onClose={() => setSelectedListing(null)}
          product={{
            id: selectedListing.product.id,
            name: selectedListing.product.name,
            description: selectedListing.product.description ?? undefined,
            manufacturer: "N/A", // This info is not in ListingWithDetails
            category: selectedListing.product.category,
            expiry_date: selectedListing.product.expiry_date,
            original_price: selectedListing.product.original_price || 0,
            unit_price: selectedListing.price_per_unit,
            quantity: selectedListing.quantity,
            created_at: selectedListing.created_at,
            updated_at: selectedListing.updated_at,
            pharmacy: {
              id: selectedListing.pharmacy.id,
              name: selectedListing.pharmacy.name,
              distance: selectedListing.pharmacy.distance,
            },
            listing: {
              id: selectedListing.listing_id,
              product_id: selectedListing.product_id,
              pharmacy_id: selectedListing.pharmacy_id,
              quantity: selectedListing.quantity,
              price_per_unit: selectedListing.price_per_unit,
              minimum_order: selectedListing.minimum_order,
              status: selectedListing.status,
              expires_at: selectedListing.expires_at,
              created_at: selectedListing.created_at,
              updated_at: selectedListing.updated_at,
            },
          }}
        />
      )}

      {/* Nouvel Echange Modal */}
      <Dialog open={isExchangeOpen} onOpenChange={setIsExchangeOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nouvel Echange</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleExchangeSubmit} className="space-y-4">
            <div className="flex flex-col gap-2">
              <label htmlFor="exchange-product">Produit</label>
              <input
                id="exchange-product"
                required
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={exchangeForm.product}
                onChange={e => setExchangeForm(f => ({ ...f, product: e.target.value }))}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label htmlFor="exchange-quantity">Quantité</label>
              <input
                id="exchange-quantity"
                type="number"
                min={1}
                required
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={exchangeForm.quantity}
                onChange={e => setExchangeForm(f => ({ ...f, quantity: Number(e.target.value) }))}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label htmlFor="exchange-partner">Pharmacie partenaire</label>
              <input
                id="exchange-partner"
                required
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={exchangeForm.partner}
                onChange={e => setExchangeForm(f => ({ ...f, partner: e.target.value }))}
              />
            </div>
            <div className="flex flex-col gap-2">
              <label htmlFor="exchange-message">Message</label>
              <textarea
                id="exchange-message"
                className="w-full p-3 border border-gray-300 rounded-lg"
                value={exchangeForm.message}
                onChange={e => setExchangeForm(f => ({ ...f, message: e.target.value }))}
              />
            </div>
            <DialogFooter>
              <Button type="submit" className="w-full" disabled={submittingExchange}>
                {submittingExchange ? "Création..." : "Créer l'Echange"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      {/* Success message */}
      {exchangeSuccess && (
        <div className="p-4 bg-green-100 text-green-800 rounded-lg text-center">
          Echange créé avec succès !
        </div>
      )}
    </Stack>
  );
}

export default function MarketplacePage() {
  return (
    <AppShell>
      <Container fluid p="lg">
        <MarketplaceContent />
      </Container>
    </AppShell>
  );
}
