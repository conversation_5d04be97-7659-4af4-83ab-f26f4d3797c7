"use client";

import { useState, useEffect, useCallback } from "react";
import { useAuth } from "@/contexts/auth-context";
import {
  Grid,
  Card,
  Text,
  Title,
  Group,
  Progress,
  SimpleGrid,
  Paper,
  ThemeIcon,
  RingProgress,
  Center,
  Loader,
  Alert,
  Button,
  Stack,
  Badge,
} from "@mantine/core";
import {
  IconUsers,
  IconBuilding,
  IconShoppingCart,
  IconAlertTriangle,
  IconTrendingUp,
  IconClock,
  IconCheck,
  IconExclamationMark,
  IconPackage,
  IconBell,
  IconPlus,
  IconArrowsExchange,
  IconFileText,
} from "@tabler/icons-react";

interface DashboardStats {
  // Expiry management
  expiringIn30Days: number;
  expiringIn60Days: number;
  expiringIn90Days: number;

  // Stock management
  lowStockProducts: number;
  criticalStock: number;
  totalProducts: number;
  totalInventoryValue: number;

  // Marketplace & exchanges
  activeListings: number;
  exchangeOpportunities: number;
  networkRequests: number;
  exchangeSuccessRate: number;

  // Financial
  potentialSavings: number;

  // Alerts & notifications
  alertsCount: number;

  // Activity metrics
  networkActivity: number;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  description?: string;
  trend?: number;
}

function StatCard({
  title,
  value,
  icon,
  color,
  description,
  trend,
}: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="space-between">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value}
          </Text>
          {description && (
            <Text c="dimmed" fz="sm">
              {description}
            </Text>
          )}
          {trend && (
            <Group gap={4} mt={5}>
              <IconTrendingUp size={16} color={trend > 0 ? "green" : "red"} />
              <Text c={trend > 0 ? "green" : "red"} fz="sm" fw={500}>
                {trend > 0 ? "+" : ""}
                {trend}%
              </Text>
            </Group>
          )}
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

interface ActivityItem {
  id: string;
  type: string;
  title: string;
  description: string;
  timestamp: string;
  status: string;
  icon: string;
}

interface Alert {
  type: string;
  title: string;
  description: string;
  products?: string[];
}

export default function DashboardPage() {
  const { user, pharmacy, loading } = useAuth();
  const [stats, setStats] = useState<DashboardStats>({
    // Expiry management
    expiringIn30Days: 0,
    expiringIn60Days: 0,
    expiringIn90Days: 0,

    // Stock management
    lowStockProducts: 0,
    criticalStock: 0,
    totalProducts: 0,
    totalInventoryValue: 0,

    // Marketplace & exchanges
    activeListings: 0,
    exchangeOpportunities: 0,
    networkRequests: 0,
    exchangeSuccessRate: 0,

    // Financial
    potentialSavings: 0,

    // Alerts & notifications
    alertsCount: 0,

    // Activity metrics
    networkActivity: 0,
  });
  const [activities, setActivities] = useState<ActivityItem[]>([]);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [dashboardLoading, setDashboardLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchDashboardStats = useCallback(async () => {
    try {
      setDashboardLoading(true);
      setError(null);

      // Fetch real data from API
      const response = await fetch("/api/dashboard/stats", {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(
          `Failed to fetch dashboard stats: ${response.statusText}`
        );
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setStats(data.stats);

      // Fetch activity data
      const activityResponse = await fetch("/api/dashboard/activity", {
        method: "GET",
        credentials: "include",
      });

      if (activityResponse.ok) {
        const activityData = await activityResponse.json();
        setActivities(activityData.activities || []);
        setAlerts(activityData.alerts || []);
      }
    } catch (error) {
      console.error("Error fetching dashboard stats:", error);
      setError(
        error instanceof Error
          ? error.message
          : "Failed to fetch dashboard data"
      );
    } finally {
      setDashboardLoading(false);
    }
  }, []);

  useEffect(() => {
    console.log("Dashboard page mounted", { user, pharmacy, loading });
    if (!loading && user) {
      fetchDashboardStats();
    }
  }, [user, pharmacy, loading, fetchDashboardStats]);

  if (loading) {
    return (
      <Center h="100vh">
        <Stack align="center" gap="md">
          <Loader size="lg" />
          <Text c="dimmed">Chargement...</Text>
        </Stack>
      </Center>
    );
  }

  // Allow all authenticated users to access dashboard
  // Pharmacy data is optional and will be displayed if available

  const welcomeMessage =
    pharmacy?.name ||
    (user?.role === "owner" || user?.role === "super_admin"
      ? "Super Admin"
      : "Utilisateur");

  if (dashboardLoading) {
    return (
      <div>
        <Title order={1} mb="xl">
          Tableau de Bord
        </Title>
        <Center h={200}>
          <Loader size="lg" />
        </Center>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Title order={1} mb="xl">
          Tableau de Bord
        </Title>
        <Alert
          icon={<IconExclamationMark size={16} />}
          title="Erreur de chargement"
          color="red"
          mb="xl"
        >
          {error}
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <Group justify="space-between" mb="xl">
        <div>
          <Title order={1} c="#00B5FF">
            Gestion des Péremptions & Échanges
          </Title>
          <Text c="dimmed" size="lg">
            {pharmacy
              ? `${pharmacy.name} - ${pharmacy.city}`
              : "Optimisez vos stocks et réduisez les pertes"}
          </Text>
          <Text c="dimmed" size="sm" mt="xs">
            Valorisez vos médicaments avant expiration • Échangez avec le réseau
            professionnel
          </Text>
        </div>
        <Group>
          <Button
            variant="light"
            leftSection={<IconBell size={16} />}
            color="red"
            size="sm"
          >
            {stats.alertsCount} Alertes urgentes
          </Button>
          <Badge size="lg" variant="light" color="blue">
            {user?.role === "owner" || user?.role === "super_admin"
              ? "Administrateur"
              : user?.role === "pharmacist"
              ? "Pharmacien"
              : "Personnel"}
          </Badge>
        </Group>
      </Group>

      {/* Expiry Management Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <StatCard
          title="Expire dans 30 jours"
          value={stats.expiringIn30Days}
          icon={<IconClock size={18} />}
          color="red"
          description="Action urgente requise"
        />

        <StatCard
          title="Expire dans 60 jours"
          value={stats.expiringIn60Days}
          icon={<IconClock size={18} />}
          color="orange"
          description="Planifier échange"
        />

        <StatCard
          title="Expire dans 90 jours"
          value={stats.expiringIn90Days}
          icon={<IconClock size={18} />}
          color="yellow"
          description="Surveiller"
        />

        <StatCard
          title="Économies Potentielles"
          value={`${(stats.potentialSavings / 1000).toFixed(1)}k DH`}
          icon={<IconTrendingUp size={18} />}
          color="green"
          description="Disponibles sur marketplace"
        />
      </SimpleGrid>

      {/* Exchange Activity Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <StatCard
          title="Mes Annonces"
          value={stats.activeListings}
          icon={<IconArrowsExchange size={18} />}
          color="blue"
          description="Actives sur marketplace"
        />

        <StatCard
          title="Opportunités Marketplace"
          value={stats.exchangeOpportunities}
          icon={<IconPackage size={18} />}
          color="violet"
          description="Produits disponibles"
        />

        <StatCard
          title="Demandes Réseau"
          value={stats.networkRequests}
          icon={<IconUsers size={18} />}
          color="indigo"
          description="Pharmacies cherchent"
        />

        <StatCard
          title="Stock Critique"
          value={stats.criticalStock}
          icon={<IconAlertTriangle size={18} />}
          color="red"
          description="Produits < 5 unités"
        />
      </SimpleGrid>

      {/* Additional Stock & Financial Metrics */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <StatCard
          title="Total Produits"
          value={stats.totalProducts}
          icon={<IconPackage size={18} />}
          color="gray"
          description="Dans l'inventaire"
        />

        <StatCard
          title="Stock Faible"
          value={stats.lowStockProducts}
          icon={<IconAlertTriangle size={18} />}
          color="orange"
          description="Sous seuil minimum"
        />

        <StatCard
          title="Valeur Inventaire"
          value={`${(stats.totalInventoryValue / 1000).toFixed(0)}k DH`}
          icon={<IconTrendingUp size={18} />}
          color="blue"
          description="Valeur totale stock"
        />

        <StatCard
          title="Activité Réseau"
          value={stats.networkActivity}
          icon={<IconUsers size={18} />}
          color="green"
          description="Interactions totales"
        />
      </SimpleGrid>

      {/* Quick Actions */}
      <Card withBorder radius="md" p="xl" mb="xl">
        <Title order={3} mb="md">
          Actions Rapides
        </Title>
        <SimpleGrid
          cols={{ base: 1, sm: 2, md: 4 }}
          spacing="md"
          data-testid="quick-actions"
        >
          <Button
            variant="light"
            leftSection={<IconClock size={16} />}
            component="a"
            href="/inventory?tab=expiry"
            size="md"
            color="red"
          >
            Gérer les Péremptions
          </Button>
          <Button
            variant="light"
            leftSection={<IconShoppingCart size={16} />}
            component="a"
            href="/marketplace?action=sell"
            size="md"
            color="green"
          >
            Vendre Produit
          </Button>
          <Button
            variant="light"
            leftSection={<IconArrowsExchange size={16} />}
            component="a"
            href="/marketplace?tab=exchanges"
            size="md"
            color="blue"
          >
            Créer un Échange
          </Button>
          <Button
            variant="light"
            leftSection={<IconUsers size={16} />}
            component="a"
            href="/network"
            size="md"
            color="violet"
          >
            Explorer le Réseau
          </Button>
          <Button
            variant="light"
            leftSection={<IconBell size={16} />}
            component="a"
            href="/alerts"
            size="md"
            color="orange"
          >
            Configurer Alertes
          </Button>
          <Button
            variant="light"
            leftSection={<IconFileText size={16} />}
            component="a"
            href="/prescription-reader"
            size="md"
            color="purple"
          >
            Lecteur d'Ordonnances IA
          </Button>
        </SimpleGrid>
      </Card>

      <Grid>
        <Grid.Col span={{ base: 12, md: 8 }}>
          {/* Recent Exchange Activity */}
          <Card withBorder radius="md" p="xl" mb="md">
            <Title order={3} mb="md">
              Activité d'Échange Récente
            </Title>
            <div data-testid="recent-activity">
              <Stack gap="md">
                {activities.length > 0 ? (
                  activities.map((activity) => (
                    <Group
                      key={activity.id}
                      justify="space-between"
                      p="sm"
                      style={{
                        backgroundColor:
                          activity.status === "urgent"
                            ? "rgba(255, 0, 0, 0.05)"
                            : activity.status === "warning"
                            ? "rgba(255, 193, 7, 0.05)"
                            : activity.status === "success"
                            ? "rgba(0, 255, 0, 0.05)"
                            : "rgba(0, 181, 255, 0.05)",
                        borderRadius: "8px",
                      }}
                    >
                      <div>
                        <Text fw={500}>{activity.title}</Text>
                        <Text size="sm" c="dimmed">
                          {activity.description}
                        </Text>
                        <Text size="xs" c="dimmed">
                          {new Date(activity.timestamp).toLocaleDateString(
                            "fr-FR",
                            {
                              day: "numeric",
                              month: "short",
                              hour: "2-digit",
                              minute: "2-digit",
                            }
                          )}
                        </Text>
                      </div>
                      <Badge
                        color={
                          activity.status === "urgent"
                            ? "red"
                            : activity.status === "warning"
                            ? "yellow"
                            : activity.status === "success"
                            ? "green"
                            : "blue"
                        }
                        size="sm"
                      >
                        {activity.type === "notification"
                          ? "Notification"
                          : activity.type === "listing"
                          ? "Annonce"
                          : activity.type === "urgent_request"
                          ? "Urgent"
                          : "Info"}
                      </Badge>
                    </Group>
                  ))
                ) : (
                  <Text c="dimmed" ta="center" py="xl">
                    Aucune activité récente
                  </Text>
                )}
              </Stack>
            </div>
          </Card>

          {/* Expiry Alerts */}
          <Card withBorder radius="md" p="xl">
            <Title order={3} mb="md">
              Alertes de Péremption
            </Title>
            <div data-testid="expiry-alerts">
              <Stack gap="md">
                {alerts.length > 0 ? (
                  alerts.map((alert, index) => (
                    <Alert
                      key={index}
                      color={
                        alert.type === "critical"
                          ? "red"
                          : alert.type === "warning"
                          ? "orange"
                          : "blue"
                      }
                      icon={
                        alert.type === "critical" ? (
                          <IconClock size={16} />
                        ) : alert.type === "warning" ? (
                          <IconClock size={16} />
                        ) : (
                          <IconArrowsExchange size={16} />
                        )
                      }
                    >
                      <Text fw={500}>{alert.title}</Text>
                      <Text size="sm">{alert.description}</Text>
                      {alert.products && alert.products.length > 0 && (
                        <Text size="xs" c="dimmed" mt="xs">
                          Exemples: {alert.products.join(", ")}
                        </Text>
                      )}
                    </Alert>
                  ))
                ) : (
                  <Alert color="green" icon={<IconCheck size={16} />}>
                    <Text fw={500}>Aucune alerte critique</Text>
                    <Text size="sm">Votre inventaire est en bon état</Text>
                  </Alert>
                )}
              </Stack>
            </div>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          {/* Profile Information */}
          <Card withBorder radius="md" p="xl" mb="md">
            <Title order={3} mb="md">
              Profil
            </Title>
            <Stack gap="xs">
              <Group justify="space-between">
                <Text fw={500}>Nom:</Text>
                <Text>
                  {pharmacy?.name ||
                    (user?.role === "owner" || user?.role === "super_admin"
                      ? "Super Admin"
                      : "N/A")}
                </Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>Email:</Text>
                <Text>{user?.email}</Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>Téléphone:</Text>
                <Text>{pharmacy?.phone || "N/A"}</Text>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>Adresse:</Text>
                <Text>{pharmacy?.address || "N/A"}</Text>
              </Group>
            </Stack>
          </Card>

          {/* Role Information */}
          <Card withBorder radius="md" p="xl">
            <Title order={3} mb="md">
              Rôle
            </Title>
            <Stack gap="xs">
              <Group justify="space-between">
                <Text fw={500}>Type:</Text>
                <Badge variant="light" color="blue">
                  {user?.role}
                </Badge>
              </Group>
              <Group justify="space-between">
                <Text fw={500}>ID:</Text>
                <Text size="sm" c="dimmed">
                  {user?.id?.slice(0, 8)}...
                </Text>
              </Group>
            </Stack>
          </Card>
        </Grid.Col>
      </Grid>
    </div>
  );
}
