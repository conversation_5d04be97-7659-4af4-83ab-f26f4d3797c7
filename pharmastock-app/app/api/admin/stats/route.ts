import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { UserRole } from '@/lib/types/auth';

export const runtime = "nodejs";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get the current user's session from the request
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser();

    if (userError || !user) {
      logger.error('admin_stats', 'User not authenticated', { error: userError?.message });
      return NextResponse.json({ error: 'User not authenticated.' }, { status: 401 });
    }

    // Explicitly cast user.role to UserRole to ensure type safety
    const userRole = user.user_metadata?.role as UserRole;

    // Check if the user has the 'super_admin' role
    if (userRole !== 'super_admin') {
      logger.warn('admin_stats', 'Unauthorized access attempt to admin stats', { userId: user.id, role: userRole });
      return NextResponse.json({ error: 'Unauthorized: Only super admins can access this resource.' }, { status: 403 });
    }

    // Fetch pharmacy stats
    const { data: pharmacies, error: pharmaciesError } = await serviceSupabase
      .from('pharmacies')
      .select('is_verified');

    if (pharmaciesError) {
      logger.error('admin_stats', 'Error fetching pharmacies', { error: pharmaciesError.message });
      return NextResponse.json({ error: 'Failed to fetch pharmacy data.' }, { status: 500 });
    }

    // Fetch user stats from profiles
    const { data: usersData, error: usersError } = await serviceSupabase
      .from('profiles')
      .select('id, role, created_at');

    if (usersError) {
      logger.error('admin_stats', 'Could not fetch from profiles', { error: usersError.message });
      // Continue without throwing if user data is not critical for overall stats, but log it
    }

    // Fetch transaction stats
    const { data: transactions, error: transactionsError } = await serviceSupabase
      .from('transactions')
      .select('id');

    if (transactionsError) {
      logger.error('admin_stats', 'Could not fetch transactions', { error: transactionsError.message });
    }

    // Fetch urgent requests
    const { data: urgentRequests, error: urgentError } = await serviceSupabase
      .from('urgent_requests')
      .select('id')
      .eq('status', 'pending');

    if (urgentError) {
      logger.error('admin_stats', 'Could not fetch urgent requests', { error: urgentError.message });
    }

    // Fetch active listings
    const { data: listings, error: listingsError } = await serviceSupabase
      .from('listings')
      .select('id')
      .eq('status', 'active');

    if (listingsError) {
      logger.error('admin_stats', 'Could not fetch listings', { error: listingsError.message });
    }

    const verifiedPharmacies = pharmacies?.filter(p => p.is_verified).length || 0;
    const pendingPharmacies = pharmacies?.filter(p => !p.is_verified).length || 0;

    const stats = {
      totalPharmacies: pharmacies?.length || 0,
      totalUsers: usersData?.length || 0,
      totalTransactions: transactions?.length || 0,
      urgentRequests: urgentRequests?.length || 0,
      verifiedPharmacies,
      pendingPharmacies,
      activeListings: listings?.length || 0,
      monthlyGrowth: 12.5, // TODO: Replace with actual calculation
    };

    logger.info('admin_stats', 'Admin stats fetched successfully', { stats });
    return NextResponse.json({ stats }, { status: 200 });

  } catch (error: any) { // Type 'any' used here to catch all potential errors, will refine if specific error types become clear
    logger.error('admin_stats', 'Unhandled error in GET /api/admin/stats', { error: error.message, stack: error.stack });
    return NextResponse.json(
      { error: 'Internal server error.' },
      { status: 500 }
    );
  }
}
