import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/types/auth";

export const runtime = "nodejs";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get the current user's session from the request
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser();

    if (userError || !user) {
      logger.error("admin_users_get", "User not authenticated", {
        error: userError?.message,
      });
      return NextResponse.json(
        { error: "User not authenticated." },
        { status: 401 }
      );
    }

    const userRole = user.user_metadata?.role as UserRole;

    if (userRole !== "super_admin") {
      logger.warn(
        "admin_users_get",
        "Unauthorized access attempt to admin users",
        { userId: user.id, role: userRole }
      );
      return NextResponse.json(
        { error: "Unauthorized: Only super admins can access this resource." },
        { status: 403 }
      );
    }

    // Fetch users from the public.profiles table (service role bypasses RLS)
    const { data: usersData, error: usersError } = await serviceSupabase
      .from("profiles")
      .select("id, email, role, created_at");
    if (usersError) {
      logger.error("admin_users_get", "Error fetching users from profiles", {
        error: usersError.message,
      });
      return NextResponse.json(
        { error: "Failed to fetch users" },
        { status: 500 }
      );
    }

    // Fetch all team member relationships (bypasses RLS)
    const { data: teamMembers, error: teamError } = await serviceSupabase.from(
      "pharmacy_team_members"
    ).select(`
      user_id,
      pharmacy_id,
      status,
      pharmacies (
        id,
        name
      )
    `);
    if (teamError) {
      logger.error("admin_users_get", "Error fetching team members", {
        error: teamError.message,
      });
      return NextResponse.json(
        { error: "Failed to fetch team member data." },
        { status: 500 }
      );
    }

    // Combine user data with pharmacy information
    const enrichedUsers =
      usersData?.map((user) => {
        const teamMember = teamMembers?.find((tm) => tm.user_id === user.id);
        // Use role directly from profiles, which should be consistent with UserRole enum
        const role = user.role || "staff"; // Default to 'staff' if role is null/undefined
        return {
          id: user.id,
          email: user.email,
          role: role as UserRole,
          created_at: user.created_at,
          pharmacy_id: teamMember?.pharmacy_id,
          pharmacy_name: teamMember?.pharmacies?.name,
          status: teamMember?.status || "inactive",
        };
      }) || [];

    logger.info("admin_users_get", "Admin users fetched successfully", {
      count: enrichedUsers.length,
    });
    return NextResponse.json({ users: enrichedUsers }, { status: 200 });
  } catch (error: any) {
    logger.error("admin_users_get", "Unhandled error in GET /api/admin/users", {
      error: error.message,
      stack: error.stack,
    });
    return NextResponse.json(
      { error: "Internal server error." },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  // This endpoint is not intended for direct user creation. Users are created via auth endpoints.
  logger.warn(
    "admin_users_post",
    "Attempted POST request to admin users endpoint (not implemented)"
  );
  return NextResponse.json(
    {
      error:
        "Method Not Allowed: User creation is handled via authentication endpoints.",
    },
    { status: 405 }
  );
}

export async function PUT(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get the current user's session from the request
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser();

    if (userError || !user) {
      logger.error("admin_users_put", "User not authenticated", {
        error: userError?.message,
      });
      return NextResponse.json(
        { error: "User not authenticated." },
        { status: 401 }
      );
    }

    const userRole = user.user_metadata?.role as UserRole;

    if (userRole !== "super_admin") {
      logger.warn(
        "admin_users_put",
        "Unauthorized access attempt to admin users update",
        { userId: user.id, role: userRole }
      );
      return NextResponse.json(
        { error: "Unauthorized: Only super admins can update users." },
        { status: 403 }
      );
    }

    const { id, role, pharmacy_id, status } = await request.json();

    if (!id) {
      logger.warn("admin_users_put", "Missing user ID for update");
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Update user role in profiles
    if (role) {
      const { error: profileError } = await serviceSupabase
        .from("profiles")
        .update({ role })
        .eq("id", id);

      if (profileError) {
        logger.error("admin_users_put", "Error updating user profile", {
          error: profileError.message,
        });
        return NextResponse.json(
          { error: "Failed to update user profile." },
          { status: 500 }
        );
      }
    }

    // Update pharmacy team member relationship
    if (pharmacy_id !== undefined || status !== undefined) {
      const { data: existingTeamMember, error: fetchError } =
        await serviceSupabase
          .from("pharmacy_team_members")
          .select("*")
          .eq("user_id", id)
          .maybeSingle();

      if (fetchError) {
        logger.error("admin_users_put", "Error fetching existing team member", {
          error: fetchError.message,
        });
        return NextResponse.json(
          { error: "Failed to check user's pharmacy affiliation." },
          { status: 500 }
        );
      }

      if (pharmacy_id === null) {
        // User is being explicitly disassociated from a pharmacy
        if (existingTeamMember) {
          const { error: deleteError } = await serviceSupabase
            .from("pharmacy_team_members")
            .delete()
            .eq("user_id", id);

          if (deleteError) {
            logger.error(
              "admin_users_put",
              "Error disassociating user from pharmacy",
              { error: deleteError.message, userId: id }
            );
            return NextResponse.json(
              { error: "Failed to disassociate user from pharmacy." },
              { status: 500 }
            );
          }
          logger.info("admin_users_put", "User disassociated from pharmacy", {
            userId: id,
            oldPharmacyId: existingTeamMember.pharmacy_id,
          });
        } else {
          logger.warn(
            "admin_users_put",
            "Attempted to disassociate non-existent team member",
            { userId: id }
          );
        }
      } else if (typeof pharmacy_id === "string") {
        // User is being associated or re-associated with a pharmacy
        const teamMemberData: {
          user_id: string;
          pharmacy_id: string;
          status?: string;
          updated_at: string;
        } = {
          user_id: id as string,
          pharmacy_id: pharmacy_id,
          updated_at: new Date().toISOString(),
        };

        if (status !== undefined) {
          teamMemberData.status = status;
        } else if (existingTeamMember && existingTeamMember.status) {
          // If status not provided, keep existing status during update
          teamMemberData.status = existingTeamMember.status;
        } else {
          // Default status if creating new and no status provided
          teamMemberData.status = "active";
        }

        const { error: teamError } = await serviceSupabase
          .from("pharmacy_team_members")
          .upsert(teamMemberData as any, {
            onConflict: "user_id",
            ignoreDuplicates: false,
          });

        if (teamError) {
          logger.error("admin_users_put", "Error upserting team member", {
            error: teamError.message,
          });
          return NextResponse.json(
            { error: "Failed to update user" },
            { status: 500 }
          );
        }
        logger.info("admin_users_put", "Team member upserted successfully", {
          userId: id,
          pharmacyId: pharmacy_id,
        });
      } else if (status !== undefined) {
        // Only status is provided, and pharmacy_id is undefined (not null, not string)
        if (existingTeamMember) {
          const { error: teamError } = await serviceSupabase
            .from("pharmacy_team_members")
            .update({ status, updated_at: new Date().toISOString() })
            .eq("user_id", id);

          if (teamError) {
            logger.error(
              "admin_users_put",
              "Error updating team member status",
              { error: teamError.message }
            );
            return NextResponse.json(
              { error: "Failed to update user status." },
              { status: 500 }
            );
          }
          logger.info("admin_users_put", "Team member status updated", {
            userId: id,
            status,
          });
        } else {
          logger.warn(
            "admin_users_put",
            "Attempted to update status for non-existent team member",
            { userId: id, status }
          );
          return NextResponse.json(
            {
              error:
                "Cannot update status for a user not affiliated with a pharmacy.",
            },
            { status: 400 }
          );
        }
      }
    }

    logger.info("admin_users_put", "User updated successfully", { userId: id });
    return NextResponse.json(
      { message: "User updated successfully" },
      { status: 200 }
    );
  } catch (error: any) {
    logger.error("admin_users_put", "Unhandled error in PUT /api/admin/users", {
      error: error.message,
      stack: error.stack,
    });
    return NextResponse.json(
      { error: "Internal server error." },
      { status: 500 }
    );
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get the current user's session from the request
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser();

    if (userError || !user) {
      logger.error("admin_users_delete", "User not authenticated", {
        error: userError?.message,
      });
      return NextResponse.json(
        { error: "User not authenticated." },
        { status: 401 }
      );
    }

    const userRole = user.user_metadata?.role as UserRole;

    if (userRole !== "super_admin") {
      logger.warn(
        "admin_users_delete",
        "Unauthorized access attempt to admin users delete",
        { userId: user.id, role: userRole }
      );
      return NextResponse.json(
        { error: "Unauthorized: Only super admins can delete users." },
        { status: 403 }
      );
    }

    const { searchParams } = new URL(request.url);
    const id = searchParams.get("id");

    if (!id) {
      logger.warn("admin_users_delete", "Missing user ID for delete");
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      );
    }

    // Remove from pharmacy team members
    const { error: teamError } = await serviceSupabase
      .from("pharmacy_team_members")
      .delete()
      .eq("user_id", id);

    if (teamError) {
      logger.error("admin_users_delete", "Error removing team member", {
        error: teamError.message,
      });
      // Do not throw here, as profile deletion can proceed even if team member fails
    }

    // Remove from profiles
    const { error: profileError } = await serviceSupabase
      .from("profiles")
      .delete()
      .eq("id", id);

    if (profileError) {
      logger.error("admin_users_delete", "Error removing profile", {
        error: profileError.message,
      });
      // Do not throw here, as auth.users is the main source
    }

    // Note: We cannot delete from auth.users directly. This operation typically requires
    // calling the Supabase Auth Admin API from a secure backend or using a Supabase Edge Function
    // with admin privileges. For now, we only remove associated profile and team member data.
    logger.info(
      "admin_users_delete",
      "User profile and team member data removed successfully",
      { userId: id }
    );
    return NextResponse.json(
      { message: "User removed successfully (profile and team data only)" },
      { status: 200 }
    );
  } catch (error: any) {
    logger.error(
      "admin_users_delete",
      "Unhandled error in DELETE /api/admin/users",
      { error: error.message, stack: error.stack }
    );
    return NextResponse.json(
      { error: "Internal server error." },
      { status: 500 }
    );
  }
}
