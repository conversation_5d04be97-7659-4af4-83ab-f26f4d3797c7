import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is super admin
    const { data: teamMember } = await supabase
      .from("pharmacy_team_members")
      .select("role")
      .eq("user_id", user.id)
      .single();

    if (teamMember?.role !== "super_admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    // Get URL parameters for filtering
    const url = new URL(request.url);
    const status = url.searchParams.get("status");
    const priority = url.searchParams.get("priority");
    const search = url.searchParams.get("search");
    const tab = url.searchParams.get("tab");

    // Get system messages from various sources
    const messages = [];

    // 1. Get urgent requests as messages
    let urgentRequestsQuery = supabase
      .from("urgent_requests")
      .select(
        `
        *,
        pharmacy:pharmacies(name, email)
      `
      )
      .order("created_at", { ascending: false });

    const { data: urgentRequests } = await urgentRequestsQuery;

    urgentRequests?.forEach((request) => {
      messages.push({
        id: `urgent_${request.id}`,
        type: "urgent_request",
        from: request.pharmacy?.name || "Pharmacie inconnue",
        fromEmail: request.pharmacy?.email || "",
        subject: `Demande urgente - ${request.product_name}`,
        message: `${request.description}\n\nQuantité demandée: ${request.quantity_needed} unités\nContact: ${request.contact_name} (${request.contact_phone})`,
        timestamp: request.created_at,
        status: request.status === "active" ? "unread" : "read",
        priority:
          request.urgency_level === "critical"
            ? "high"
            : request.urgency_level === "urgent"
            ? "medium"
            : "low",
        category: "Demande urgente",
        originalData: request,
      });
    });

    // 2. Get notifications as messages (system-wide notifications)
    let notificationsQuery = supabase
      .from("notifications")
      .select(
        `
        *,
        user:auth.users(email)
      `
      )
      .eq("type", "system")
      .order("created_at", { ascending: false })
      .limit(50);

    const { data: notifications } = await notificationsQuery;

    notifications?.forEach((notification) => {
      messages.push({
        id: `notification_${notification.id}`,
        type: "system_notification",
        from: "Système PharmaStock",
        fromEmail: "<EMAIL>",
        subject: notification.title,
        message: notification.message,
        timestamp: notification.created_at,
        status: notification.read ? "read" : "unread",
        priority:
          notification.priority === "urgent"
            ? "high"
            : notification.priority === "normal"
            ? "medium"
            : "low",
        category: "Notification système",
        originalData: notification,
      });
    });

    // 3. Get recent transactions with issues (mock for now, would need a disputes table)
    // For now, we'll create some sample transaction-related messages
    const { data: recentTransactions } = await supabase
      .from("transactions")
      .select(
        `
        *,
        seller:pharmacies!transactions_seller_id_fkey(name, email),
        buyer:pharmacies!transactions_buyer_id_fkey(name, email)
      `
      )
      .order("created_at", { ascending: false })
      .limit(10);

    // Add some mock dispute messages based on transactions
    recentTransactions?.slice(0, 2).forEach((transaction, index) => {
      messages.push({
        id: `dispute_${transaction.id}`,
        type: "transaction_dispute",
        from: transaction.buyer?.name || "Pharmacie",
        fromEmail: transaction.buyer?.email || "",
        subject: `Litige transaction #${transaction.id}`,
        message: `Il y a un problème avec la transaction. Montant: ${transaction.total_amount} DH. Merci de vérifier.`,
        timestamp: new Date(
          Date.now() - (index + 1) * 24 * 60 * 60 * 1000
        ).toISOString(),
        status: index === 0 ? "unread" : "read",
        priority: "medium",
        category: "Litige",
        originalData: transaction,
      });
    });

    // Sort messages by timestamp (newest first)
    messages.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Apply filters
    let filteredMessages = messages;

    if (search) {
      filteredMessages = filteredMessages.filter(
        (msg) =>
          msg.subject.toLowerCase().includes(search.toLowerCase()) ||
          msg.from.toLowerCase().includes(search.toLowerCase()) ||
          msg.message.toLowerCase().includes(search.toLowerCase())
      );
    }

    if (status && status !== "all") {
      filteredMessages = filteredMessages.filter(
        (msg) => msg.status === status
      );
    }

    if (priority && priority !== "all") {
      filteredMessages = filteredMessages.filter(
        (msg) => msg.priority === priority
      );
    }

    if (tab && tab !== "all") {
      if (tab === "unread") {
        filteredMessages = filteredMessages.filter(
          (msg) => msg.status === "unread"
        );
      } else if (tab === "urgent") {
        filteredMessages = filteredMessages.filter(
          (msg) => msg.priority === "high"
        );
      } else if (tab === "resolved") {
        filteredMessages = filteredMessages.filter(
          (msg) => msg.status === "replied"
        );
      }
    }

    // Calculate stats
    const stats = {
      total: messages.length,
      unread: messages.filter((msg) => msg.status === "unread").length,
      urgent: messages.filter((msg) => msg.priority === "high").length,
      resolved: messages.filter((msg) => msg.status === "replied").length,
    };

    return NextResponse.json({
      messages: filteredMessages,
      stats,
    });
  } catch (error) {
    logger.error("Error fetching admin messages", { error });
    return NextResponse.json(
      { error: "Failed to fetch messages" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { messageId, replyText, action } = await request.json();

    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Check if user is super admin
    const { data: teamMember } = await supabase
      .from("pharmacy_team_members")
      .select("role")
      .eq("user_id", user.id)
      .single();

    if (teamMember?.role !== "super_admin") {
      return NextResponse.json({ error: "Forbidden" }, { status: 403 });
    }

    if (action === "reply") {
      // Handle reply to message
      // For now, we'll just mark the message as replied
      // In a real system, you'd store the reply in a messages/replies table

      // Extract message type and ID
      const [type, id] = messageId.split("_");

      if (type === "urgent") {
        // Update urgent request status or add a note
        await supabase
          .from("urgent_requests")
          .update({
            status: "responded",
            admin_notes: replyText,
          })
          .eq("id", id);
      }

      // Create a notification for the reply
      await supabase.from("notifications").insert([
        {
          title: "Réponse de l'administration",
          message: replyText,
          type: "admin_reply",
          priority: "normal",
          read: false,
        },
      ]);
    }

    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error("Error handling admin message action", { error });
    return NextResponse.json(
      { error: "Failed to process action" },
      { status: 500 }
    );
  }
}
