import { NextRequest, NextResponse } from 'next/server';
import { getServiceSupabaseClient } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { UserRole } from '@/lib/types/auth';

interface MedicationInfo {
  name: string;
  nameAr?: string;  // Arabic name
  manufacturer: string;
  dosage: string;
  form: string;
  activeIngredients: string;
  posology: string;
  indications: string;
  contraindications: string;
  reimbursementRate: string;
  packaging: string;
  cip13: string;
  ppv: number;  // Prix Public de Vente
  amoCoverage: {
    cnops: number;  // CNOPS coverage percentage
    cnss: number;   // CNSS coverage percentage
    ramed: boolean; // RAMED coverage
  };
  ammNumber: string;  // Autorisation de Mise sur le Marché
  dmpCategory: string;  // DMP classification
  transferRestrictions?: {
    requiresAuthorization: boolean;  // If transfer needs DMP authorization
    temperatureControl: boolean;     // If special temperature control needed
    specialHandling: string[];      // Special handling requirements
    validityPeriod: number;         // Validity period for transfer in hours
  };
  stockManagement?: {
    minimumTransferQuantity: number;  // Minimum quantity for transfer
    recommendedTransferQuantity: number;  // Recommended quantity for transfer
    storageConditions: string[];  // Required storage conditions
  };
}

export const runtime = "nodejs";

export async function GET(request: NextRequest) {
  logger.debug('medication_lookup_get', 'Medication lookup initiated');
  const { searchParams } = new URL(request.url);
  const barcode = searchParams.get('barcode');

  const serviceSupabase = getServiceSupabaseClient();

  // Authenticate user
  const { data: { user }, error: userError } = await serviceSupabase.auth.getUser();
  if (userError || !user) {
    logger.warn('medication_lookup_get', 'Unauthorized access', { error: userError?.message });
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  // Authorize roles (e.g., pharmacist, staff, super_admin, owner can look up medications)
  const userRole = user.user_metadata?.role as UserRole | undefined;
  if (!userRole || !['pharmacist', 'staff', 'super_admin', 'owner', 'supplier'].includes(userRole)) {
    logger.warn('medication_lookup_get', 'Forbidden: Insufficient role', { userId: user.id, role: userRole });
    return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
  }

  if (!barcode) {
    logger.warn('medication_lookup_get', 'Barcode not provided');
    return NextResponse.json(
      { error: 'Code-barres requis' },
      { status: 400 }
    );
  }

  try {
    const cleanBarcode = barcode.replace(/[\s-]/g, '');
    let medication: MedicationInfo | null = null;

    // 1. First try DMP API (Direction du Médicament et de la Pharmacie)
    try {
      logger.debug('medication_lookup_get', 'Attempting DMP API lookup', { barcode: cleanBarcode });
      const dmpResponse = await fetch(
        `${process.env.DMP_API_URL}/medicaments/${cleanBarcode}`,
        {
          headers: {
            'Authorization': `Bearer ${process.env.DMP_API_KEY}`,
          }
        }
      );

      if (dmpResponse.ok) {
        const dmpData = await dmpResponse.json();
        logger.info('medication_lookup_get', 'Medication found in DMP', { barcode: cleanBarcode, name: dmpData.nomCommercial });
        medication = {
          name: dmpData.nomCommercial,
          manufacturer: dmpData.laboratoire,
          form: dmpData.formePharmaceutique,
          dosage: dmpData.dosage,
          activeIngredients: dmpData.dci, // Assuming DCI maps to active ingredients
          posology: 'N/A', // DMP might not have this, set default
          indications: 'N/A', // DMP might not have this, set default
          contraindications: 'N/A', // DMP might not have this, set default
          reimbursementRate: dmpData.remboursable ? 'Oui' : 'Non',
          packaging: 'N/A', // DMP might not have this, set default
          cip13: cleanBarcode,
          ppv: dmpData.prixPublic,
          amoCoverage: {
            cnops: 0, // Placeholder, actual data needed
            cnss: 0, // Placeholder, actual data needed
            ramed: false, // Placeholder, actual data needed
          },
          ammNumber: dmpData.numeroAmm,
          dmpCategory: 'N/A', // Placeholder, actual data needed
        };
      }
    } catch (dmpError: any) {
      logger.warn('medication_lookup_get', 'DMP lookup failed', { barcode: cleanBarcode, error: dmpError.message, stack: dmpError.stack });
    }

    if (medication) {
      return NextResponse.json(medication, { status: 200 });
    }

    // 2. Try AMIP database
    try {
      logger.debug('medication_lookup_get', 'Attempting AMIP API lookup', { barcode: cleanBarcode });
      const amipHeaders: HeadersInit = {};
      if (process.env.AMIP_API_KEY) {
        amipHeaders['API-Key'] = process.env.AMIP_API_KEY;
      }
      const amipResponse = await fetch(
        `${process.env.AMIP_API_URL}/products/search?code=${cleanBarcode}`,
        {
          headers: amipHeaders,
        }
      );

      if (amipResponse.ok) {
        const amipData = await amipResponse.json();
        if (amipData.products?.length > 0) {
          const product = amipData.products[0];
          logger.info('medication_lookup_get', 'Medication found in AMIP', { barcode: cleanBarcode, name: product.name });
          medication = {
            name: product.name,
            manufacturer: product.manufacturer,
            form: product.form,
            dosage: product.dosage,
            activeIngredients: product.dci,
            posology: 'N/A',
            indications: 'N/A',
            contraindications: 'N/A',
            reimbursementRate: 'N/A',
            packaging: 'N/A',
            cip13: cleanBarcode,
            ppv: product.price,
            amoCoverage: {
              cnops: 0,
              cnss: 0,
              ramed: false,
            },
            ammNumber: product.registrationNumber,
            dmpCategory: 'N/A',
          };
        }
      }
    } catch (amipError: any) {
      logger.warn('medication_lookup_get', 'AMIP lookup failed', { barcode: cleanBarcode, error: amipError.message, stack: amipError.stack });
    }

    if (medication) {
      return NextResponse.json(medication, { status: 200 });
    }

    // 3. Try OpenFoodFacts Morocco as fallback
    try {
      logger.debug('medication_lookup_get', 'Attempting OpenFoodFacts API lookup', { barcode: cleanBarcode });
      const openFoodFactsResponse = await fetch(
        `https://ma.openfoodfacts.org/api/v2/product/${cleanBarcode}.json`
      );

      if (openFoodFactsResponse.ok) {
        const data = await openFoodFactsResponse.json();

        if (data.status === 1 && data.product) {
          const { product } = data;
          const arabicName = product.product_name_ar || product.generic_name_ar;
          logger.info('medication_lookup_get', 'Medication found in OpenFoodFacts', { barcode: cleanBarcode, name: product.product_name });
          medication = {
            name: product.product_name || arabicName || product.generic_name || 'N/A',
            nameAr: arabicName,
            manufacturer: product.brands || 'N/A',
            form: product.packaging || 'N/A',
            dosage: product.quantity || product.serving_size || 'N/A',
            activeIngredients: product.ingredients_text || 'N/A',
            posology: 'N/A',
            indications: 'N/A',
            contraindications: 'N/A',
            reimbursementRate: 'N/A',
            packaging: 'N/A',
            cip13: cleanBarcode,
            ppv: product.price || 0,
            amoCoverage: {
              cnops: 0,
              cnss: 0,
              ramed: false,
            },
            ammNumber: product.emb_codes || 'N/A',
            dmpCategory: 'N/A',
          };
        }
      }
    } catch (offError: any) {
      logger.warn('medication_lookup_get', 'OpenFoodFacts lookup failed', { barcode: cleanBarcode, error: offError.message, stack: offError.stack });
    }

    if (medication) {
      return NextResponse.json(medication, { status: 200 });
    }

    // 4. Local database lookup (Supabase 'products' table)
    try {
      logger.debug('medication_lookup_get', 'Attempting local database lookup', { barcode: cleanBarcode });
      const { data: localProduct, error: localError } = await serviceSupabase
        .from('products')
        .select('*')
        .eq('barcode', cleanBarcode) // Assuming 'barcode' column exists in products table
        .maybeSingle();

      if (localError) {
        logger.error('medication_lookup_get', 'Error fetching from local database', { barcode: cleanBarcode, error: localError.message, stack: localError.stack });
      } else if (localProduct) {
        logger.info('medication_lookup_get', 'Medication found in local database', { barcode: cleanBarcode, name: localProduct.name });
        // Map local product to MedicationInfo interface
        medication = {
          name: localProduct.name,
          manufacturer: 'N/A', // Assuming manufacturer is not directly in products table, or needs to be fetched from another table
          dosage: 'N/A', // Assuming dosage is not directly in products table
          form: 'N/A', // Assuming form is not directly in products table
          activeIngredients: localProduct.description || 'N/A', // Using description as a fallback for active ingredients
          posology: 'N/A',
          indications: 'N/A',
          contraindications: 'N/A',
          reimbursementRate: 'N/A',
          packaging: 'N/A',
          cip13: cleanBarcode,
          ppv: localProduct.unit_price || 0, // Using unit_price as PPV
          amoCoverage: {
            cnops: 0,
            cnss: 0,
            ramed: false,
          },
          ammNumber: 'N/A',
          dmpCategory: localProduct.category || 'N/A',
        };
      }
    } catch (localDbError: any) {
      logger.warn('medication_lookup_get', 'Local database lookup failed', { barcode: cleanBarcode, error: localDbError.message, stack: localDbError.stack });
    }

    if (medication) {
      return NextResponse.json(medication, { status: 200 });
    }

    // If no data found in any source, return just the barcode with manual input message
    logger.info('medication_lookup_get', 'Medication not found in any source, prompting manual input', { barcode: cleanBarcode });
    return NextResponse.json({
      barcode,
      message: "Médicament non trouvé dans les bases de données. Veuillez saisir les informations manuellement.",
      source: 'manual'
    }, { status: 404 });

  } catch (error: any) {
    logger.error('medication_lookup_get', 'Unhandled error in medication lookup', { error: error.message, stack: error.stack });
    return NextResponse.json(
      { error: 'Erreur interne du serveur' },
      { status: 500 }
    );
  }
} 