import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { Database } from '@/types/supabase';

export const runtime = 'nodejs';

// POST /api/pharmacy/drug-requests - Create a new drug request
export async function POST(request: NextRequest) {
  logger.info('pharmacy_drug_requests', 'POST request received for drug request creation');

  try {
    const { product_id, requested_quantity, notes } = await request.json();

    if (!product_id || !requested_quantity) {
      logger.warn('pharmacy_drug_requests', 'Missing required fields for drug request', { product_id, requested_quantity });
      return NextResponse.json(
        { error: 'Product ID and requested quantity are required.' },
        { status: 400 }
      );
    }

    const supabase = getServiceSupabaseClient();

    // Get the current user's session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      logger.error('pharmacy_drug_requests', 'User not authenticated', { error: userError?.message });
      return NextResponse.json({ error: 'User not authenticated.' }, { status: 401 });
    }

    // Fetch the pharmacy_id for the current user from the pharmacy_team_members table
    const { data: teamMember, error: teamMemberError } = await supabase
      .from('pharmacy_team_members')
      .select('pharmacy_id, role')
      .eq('user_id', user.id)
      .single();

    if (teamMemberError || !teamMember?.pharmacy_id) {
      const errorMessage = teamMemberError?.message || 'Unknown team member error';
      logger.error('pharmacy_drug_requests', 'Could not find pharmacy team member or pharmacy ID for user', {
        userId: user.id,
        error: errorMessage,
      });
      return NextResponse.json({ error: 'Could not determine user\'s pharmacy affiliation.' }, { status: 403 });
    }

    // Ensure the user has the 'pharmacist' or 'owner' role to create drug requests
    if (!['pharmacist', 'owner'].includes(teamMember.role)) {
      logger.warn('pharmacy_drug_requests', 'User does not have permission to create drug requests', { userId: user.id, role: teamMember.role });
      return NextResponse.json({ error: 'Unauthorized: Only pharmacists or pharmacy owners can create drug requests.' }, { status: 403 });
    }

    const { data, error } = await supabase
      .from('pharmacy_drug_requests')
      .insert({
        pharmacy_id: teamMember.pharmacy_id,
        product_id,
        requested_quantity,
        notes,
        status: "pending" as Database['public']['Enums']['request_status'],
      })
      .select()
      .single();

    if (error) {
      logger.error('pharmacy_drug_requests', 'Error inserting drug request', { error: error.message, details: error.details });
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    logger.info('pharmacy_drug_requests', 'Drug request created successfully', { requestId: data.id, pharmacyId: data.pharmacy_id });
    return NextResponse.json(data, { status: 201 });

  } catch (error: any) {
    logger.error('pharmacy_drug_requests', 'Unhandled error in POST /api/pharmacy/drug-requests', { error: error.message });
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
}

// GET /api/pharmacy/drug-requests - Retrieve drug requests for the current pharmacy
export async function GET(request: NextRequest) {
  logger.info('pharmacy_drug_requests', 'GET request received for drug requests');

  try {
    const supabase = getServiceSupabaseClient();

    // Get the current user's session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      logger.error('pharmacy_drug_requests', 'User not authenticated', { error: userError?.message });
      return NextResponse.json({ error: 'User not authenticated.' }, { status: 401 });
    }

    // Fetch the pharmacy_id for the current user from the pharmacy_team_members table
    const { data: teamMember, error: teamMemberError } = await supabase
      .from('pharmacy_team_members')
      .select('pharmacy_id')
      .eq('user_id', user.id)
      .single();

    if (teamMemberError || !teamMember?.pharmacy_id) {
      const errorMessage = teamMemberError?.message || 'Unknown team member error for GET request';
      logger.error('pharmacy_drug_requests', 'Could not find pharmacy team member or pharmacy ID for user for GET request', {
        userId: user.id,
        error: errorMessage,
      });
      return NextResponse.json({ error: 'Could not determine user\'s pharmacy affiliation.' }, { status: 403 });
    }

    const { data, error } = await supabase
      .from('pharmacy_drug_requests')
      .select('*, products(name, description, category)') // Select all request fields and relevant product details
      .eq('pharmacy_id', teamMember.pharmacy_id)
      .order('created_at', { ascending: false });

    if (error) {
      logger.error('pharmacy_drug_requests', 'Error fetching drug requests', { error: error.message, details: error.details });
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    logger.info('pharmacy_drug_requests', 'Drug requests fetched successfully', { count: data.length, pharmacyId: teamMember.pharmacy_id });
    return NextResponse.json(data, { status: 200 });

  } catch (error: any) {
    logger.error('pharmacy_drug_requests', 'Unhandled error in GET /api/pharmacy/drug-requests', { error: error.message });
    return NextResponse.json({ error: 'Internal server error.' }, { status: 500 });
  }
} 