import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

interface GeoJsonPoint {
  type: "Point";
  coordinates: [number, number]; // [longitude, latitude]
}

export const runtime = "nodejs";

export async function GET(
  request: NextRequest,
  context: { params: Promise<{ id: string }> }
) {
  const { id } = await context.params;
  logger.debug("pharmacy_get_by_id", "Fetching pharmacy data", {
    pharmacyId: id,
  });

  try {
    const serviceSupabase = getServiceSupabaseClient();

    const { data: pharmacy, error } = await serviceSupabase
      .from("pharmacies")
      .select(
        `
        id,
        name,
        address,
        phone,
        location
      `
      )
      .eq("id", id)
      .single();

    if (error) {
      logger.error("pharmacy_get_by_id", "Error fetching pharmacy data", {
        error: error.message,
        stack: error.stack,
      });
      return NextResponse.json(
        { error: "Failed to fetch pharmacy data" },
        { status: 500 }
      );
    }

    if (!pharmacy) {
      logger.warn("pharmacy_get_by_id", "Pharmacy not found", {
        pharmacyId: id,
      });
      return NextResponse.json(
        { error: "Pharmacy not found" },
        { status: 404 }
      );
    }

    // Safely extract latitude and longitude from PostGIS point
    let latitude: number | null = null;
    let longitude: number | null = null;

    // Type guard to check if location is a GeoJsonPoint
    const isGeoJsonPoint = (loc: unknown): loc is GeoJsonPoint => {
      return (
        typeof loc === "object" &&
        loc !== null &&
        "type" in loc &&
        (loc as GeoJsonPoint).type === "Point" &&
        "coordinates" in loc &&
        Array.isArray((loc as GeoJsonPoint).coordinates) &&
        (loc as GeoJsonPoint).coordinates.length >= 2
      );
    };

    if (isGeoJsonPoint(pharmacy.location)) {
      longitude = pharmacy.location.coordinates[0];
      latitude = pharmacy.location.coordinates[1];
    }

    const formattedPharmacy = {
      id: pharmacy.id,
      name: pharmacy.name,
      address: pharmacy.address,
      phone: pharmacy.phone,
      latitude: latitude,
      longitude: longitude,
    };

    logger.info("pharmacy_get_by_id", "Successfully fetched pharmacy data", {
      pharmacyId: id,
    });
    return NextResponse.json({ pharmacy: formattedPharmacy });
  } catch (error: any) {
    logger.error(
      "pharmacy_get_by_id",
      "Unexpected error fetching pharmacy data",
      { error: error.message, stack: error.stack }
    );
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}
