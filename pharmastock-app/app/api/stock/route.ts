import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient, parseSession } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/types/auth";
import type { Database } from "@/types/supabase";

type Product = Database["public"]["Tables"]["products"]["Row"];

type Stock = {
  id: string;
  pharmacy_id: string;
  name: string;
  category: string;
  quantity: number;
  expiry_date: string | null;
  unit_price: number;
  batch_number?: string | null;
};

export const runtime = "nodejs";

function isAllowedRole(role: string | undefined): boolean {
  return ["owner", "pharmacist", "super_admin"].includes(role || "");
}

async function getAuthenticatedUser(request: NextRequest) {
  const sessionStr = request.cookies.get("sb-session")?.value;

  if (!sessionStr) {
    return { user: null, error: "No session found" };
  }

  const session = parseSession(sessionStr);
  if (!session) {
    return { user: null, error: "Invalid session" };
  }

  const serviceSupabase = getServiceSupabaseClient();
  const {
    data: { user },
    error: userError,
  } = await serviceSupabase.auth.getUser(session.access_token);

  if (userError || !user) {
    return { user: null, error: "Invalid or expired session" };
  }

  // Get user role and pharmacy info
  const { data: teamData } = await serviceSupabase.rpc("get_user_team_member", {
    user_id: user.id,
  });

  const teamMember = teamData && teamData.length > 0 ? teamData[0] : null;

  const { data: profileData } = await serviceSupabase.rpc("get_user_profile", {
    user_id: user.id,
  });

  const profile = profileData && profileData.length > 0 ? profileData[0] : null;

  // Determine user role
  const metadataRole = user.user_metadata?.role as UserRole | undefined;
  let userRole: UserRole = "staff";

  if (profile?.role) {
    userRole = profile.role as UserRole;
  } else if (teamMember?.role) {
    userRole = teamMember.role as UserRole;
  } else if (metadataRole) {
    userRole = metadataRole;
  }

  const userData = {
    id: user.id,
    email: user.email,
    role: userRole,
    pharmacyId:
      userRole === "super_admin" ? null : teamMember?.pharmacy_id || null,
  };

  return { user: userData, error: null };
}

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!isAllowedRole(user.role)) {
      return NextResponse.json({ error: "Accès refusé" }, { status: 403 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getServiceSupabaseClient();
    const { data: products, error } = await serviceSupabase
      .from("products")
      .select("*")
      .eq("pharmacy_id", user.pharmacyId);
    if (error) {
      logger.error("stock_get", "Erreur lors de la récupération du stock", {
        error: error.message,
      });
      return NextResponse.json(
        { error: "Erreur lors de la récupération du stock" },
        { status: 500 }
      );
    }
    const stocks: Stock[] = (products || []).map((p: Product) => ({
      id: p.id,
      pharmacy_id: p.pharmacy_id,
      name: p.name,
      category: p.category,
      quantity: p.quantity,
      expiry_date: p.expiry_date,
      unit_price: p.unit_price,
      batch_number: p.batch_number ?? null,
    }));
    return NextResponse.json({ stocks });
  } catch (error: any) {
    logger.error("stock_get", "Erreur inattendue", { error: error.message });
    return NextResponse.json({ error: "Erreur inattendue" }, { status: 500 });
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!isAllowedRole(user.role)) {
      return NextResponse.json({ error: "Accès refusé" }, { status: 403 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getServiceSupabaseClient();
    const body = await request.json();
    const { name, category, quantity, expiry_date, unit_price, batch_number } =
      body;
    if (
      !name ||
      !category ||
      quantity == null ||
      !expiry_date ||
      unit_price == null
    ) {
      return NextResponse.json({ error: "Champs manquants" }, { status: 400 });
    }
    const { data, error } = await serviceSupabase
      .from("products")
      .insert([
        {
          pharmacy_id: user.pharmacyId,
          name,
          category,
          quantity,
          expiry_date,
          unit_price,
          original_price: unit_price,
          batch_number,
        },
      ])
      .select()
      .single();
    if (error) {
      logger.error("stock_post", "Erreur lors de l'ajout du stock", {
        error: error.message,
      });
      return NextResponse.json(
        { error: "Erreur lors de l'ajout du stock" },
        { status: 500 }
      );
    }
    const stock: Stock = {
      id: data.id,
      pharmacy_id: data.pharmacy_id,
      name: data.name,
      category: data.category,
      quantity: data.quantity,
      expiry_date: data.expiry_date,
      unit_price: data.unit_price,
      batch_number: data.batch_number ?? null,
    };
    return NextResponse.json({ stock });
  } catch (error: any) {
    logger.error("stock_post", "Erreur inattendue", { error: error.message });
    return NextResponse.json({ error: "Erreur inattendue" }, { status: 500 });
  }
}

export async function PUT(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!isAllowedRole(user.role)) {
      return NextResponse.json({ error: "Accès refusé" }, { status: 403 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getServiceSupabaseClient();
    const body = await request.json();
    const {
      id,
      name,
      category,
      quantity,
      expiry_date,
      unit_price,
      batch_number,
    } = body;
    if (
      !id ||
      !name ||
      !category ||
      quantity == null ||
      !expiry_date ||
      unit_price == null
    ) {
      return NextResponse.json({ error: "Champs manquants" }, { status: 400 });
    }
    const { data, error } = await serviceSupabase
      .from("products")
      .update({
        name,
        category,
        quantity,
        expiry_date,
        unit_price,
        original_price: unit_price,
        batch_number,
      })
      .eq("id", id)
      .eq("pharmacy_id", user.pharmacyId)
      .select()
      .single();
    if (error) {
      logger.error("stock_put", "Erreur lors de la mise à jour du stock", {
        error: error.message,
      });
      return NextResponse.json(
        { error: "Erreur lors de la mise à jour du stock" },
        { status: 500 }
      );
    }
    const stock: Stock = {
      id: data.id,
      pharmacy_id: data.pharmacy_id,
      name: data.name,
      category: data.category,
      quantity: data.quantity,
      expiry_date: data.expiry_date,
      unit_price: data.unit_price,
      batch_number: data.batch_number ?? null,
    };
    return NextResponse.json({ stock });
  } catch (error: any) {
    logger.error("stock_put", "Erreur inattendue", { error: error.message });
    return NextResponse.json({ error: "Erreur inattendue" }, { status: 500 });
  }
}

export async function DELETE(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    if (authError || !user) {
      return NextResponse.json({ error: "Non autorisé" }, { status: 401 });
    }

    if (!isAllowedRole(user.role)) {
      return NextResponse.json({ error: "Accès refusé" }, { status: 403 });
    }

    if (!user.pharmacyId) {
      return NextResponse.json(
        { error: "Aucune pharmacie associée à cet utilisateur." },
        { status: 400 }
      );
    }

    const serviceSupabase = getServiceSupabaseClient();
    const body = await request.json();
    const { id } = body;
    if (!id) {
      return NextResponse.json({ error: "ID manquant" }, { status: 400 });
    }
    const { error } = await serviceSupabase
      .from("products")
      .delete()
      .eq("id", id)
      .eq("pharmacy_id", user.pharmacyId);
    if (error) {
      logger.error("stock_delete", "Erreur lors de la suppression du stock", {
        error: error.message,
      });
      return NextResponse.json(
        { error: "Erreur lors de la suppression du stock" },
        { status: 500 }
      );
    }
    return NextResponse.json({ success: true });
  } catch (error: any) {
    logger.error("stock_delete", "Erreur inattendue", { error: error.message });
    return NextResponse.json({ error: "Erreur inattendue" }, { status: 500 });
  }
}
