import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');
    
    let accessToken = null;
    
    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }
    
    if (!accessToken) {
      return NextResponse.json({ error: 'No access token found' }, { status: 401 });
    }
    
    // Get user from token
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const currentPharmacyId = teamMember.pharmacy_id;

    // Get all pharmacies except the current one
    const { data: pharmacies, error } = await serviceSupabase
      .from("pharmacies")
      .select(`
        id,
        name,
        address,
        phone,
        email,
        city,
        location
      `)
      .neq("id", currentPharmacyId)
      .eq("is_verified", true)
      .limit(20);

    if (error) {
      throw error;
    }

    // Get current pharmacy location for distance calculation
    const { data: currentPharmacy } = await serviceSupabase
      .from("pharmacies")
      .select("location")
      .eq("id", currentPharmacyId)
      .single();

    // Format pharmacies data
    const formattedPharmacies = pharmacies?.map((pharmacy: any, index: number) => {
      // Calculate mock distance (in real app, you'd use PostGIS distance functions)
      const mockDistance = Math.round((Math.random() * 10 + 1) * 10) / 10;
      
      // Mock rating and exchanges (in real app, these would come from actual data)
      const mockRating = Math.round((Math.random() * 1 + 4) * 10) / 10;
      const mockExchanges = Math.floor(Math.random() * 30 + 1);
      
      // Mock specialties
      const specialties = [
        ["Urgences", "Pédiatrie"],
        ["Dermatologie", "Cardiologie"],
        ["Homéopathie", "Nutrition"],
        ["Orthopédie", "Gériatrie"],
        ["Oncologie", "Diabétologie"]
      ];

      return {
        id: pharmacy.id,
        name: pharmacy.name,
        address: pharmacy.address || `${pharmacy.city || "Ville inconnue"}`,
        phone: pharmacy.phone || "Non renseigné",
        email: pharmacy.email || "Non renseigné",
        distance: mockDistance,
        rating: mockRating,
        exchanges: mockExchanges,
        lastActive: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString().split('T')[0],
        specialties: specialties[index % specialties.length] || ["Généraliste"]
      };
    }) || [];

    // Calculate network statistics
    const stats = {
      activePartners: formattedPharmacies.length,
      averageDistance: formattedPharmacies.length > 0 
        ? Math.round((formattedPharmacies.reduce((sum, p) => sum + p.distance, 0) / formattedPharmacies.length) * 10) / 10
        : 0,
      totalExchanges: formattedPharmacies.reduce((sum, p) => sum + p.exchanges, 0),
      averageRating: formattedPharmacies.length > 0
        ? Math.round((formattedPharmacies.reduce((sum, p) => sum + p.rating, 0) / formattedPharmacies.length) * 10) / 10
        : 0
    };

    return NextResponse.json({ 
      pharmacies: formattedPharmacies,
      stats 
    });
  } catch (error) {
    logger.error("Error fetching network pharmacies", { error });
    return NextResponse.json(
      { error: "Failed to fetch network pharmacies" },
      { status: 500 }
    );
  }
}
