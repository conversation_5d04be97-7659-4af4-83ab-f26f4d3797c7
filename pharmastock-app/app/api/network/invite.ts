import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');
    let accessToken = null;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }
    if (!accessToken) {
      return NextResponse.json({ error: 'No access token found' }, { status: 401 });
    }
    // Get user from token
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(accessToken);
    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // Parse body
    const body = await request.json();
    const { name, email, city } = body;
    if (!name || !email || !city) {
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });
    }
    // Insert into pharmacy_invitations
    const { data, error } = await serviceSupabase
      .from("pharmacy_invitations")
      .insert([
        {
          inviter_user_id: user.id,
          name,
          email,
          city,
          status: "pending",
          created_at: new Date().toISOString(),
        },
      ])
      .select()
      .single();
    if (error) {
      logger.error("Error creating pharmacy invitation", { error });
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ invitation: data }, { status: 201 });
  } catch (error) {
    logger.error("Error in POST /api/network/invite", { error });
    return NextResponse.json(
      { error: "Failed to create invitation" },
      { status: 500 }
    );
  }
} 