import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function POST(request: NextRequest) {
  try {
    const { pharmacyId, subject, message } = await request.json();
    if (!pharmacyId || !subject || !message) {
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });
    }
    // In production: send email or notification to pharmacy
    // For now: store in a table or log
    logger.info("Contact message sent", { pharmacyId, subject, message });
    // Optionally, store in a table:
    // const serviceSupabase = getServiceSupabaseClient();
    // await serviceSupabase.from("pharmacy_messages").insert([{ pharmacy_id: pharmacyId, subject, message }]);
    return NextResponse.json({ success: true });
  } catch (error) {
    logger.error("Error sending contact message", { error });
    return NextResponse.json({ error: "Failed to send message" }, { status: 500 });
  }
} 