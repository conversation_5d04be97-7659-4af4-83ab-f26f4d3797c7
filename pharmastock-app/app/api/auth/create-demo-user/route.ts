import { createClient } from "@supabase/supabase-js";
import { NextRequest, NextResponse } from "next/server";

// ============================================================================
// Demo User Configuration
// ============================================================================
const DEMO_USERS = {
  owner: {
    email: "<EMAIL>",
    password: "password123",
    // 9f9b2d39-d586-461c-8f1e-7d6a5a2b3c4d (example UUID)
    id:
      process.env.DEMO_OWNER_USER_ID || "9f9b2d39-d586-461c-8f1e-7d6a5a2b3c4d",
  },
  pharmacist: {
    email: "<EMAIL>",
    password: "password123",
    // 2a8f3e1e-7d67-41d0-a67b-1435c7e87737 (example UUID)
    id:
      process.env.DEMO_PHARMACIST_USER_ID ||
      "2a8f3e1e-7d67-41d0-a67b-1435c7e87737",
  },
};

const DEMO_PHARMACY_ID =
  process.env.DEMO_PHARMACY_ID || "dcfc9c59-d3c8-419e-83a8-5ea3cb3db800";

// ============================================================================
// API Route Handler
// ============================================================================
export async function POST(req: NextRequest) {
  if (process.env.NODE_ENV !== "development") {
    return NextResponse.json(
      { error: "This endpoint is only available in development mode." },
      { status: 403 }
    );
  }

  const supabase = createClient(
    process.env.NEXT_PUBLIC_SUPABASE_URL || "http://127.0.0.1:54351",
    process.env.SUPABASE_SERVICE_ROLE_KEY ||
      "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU",
    {
      auth: {
        autoRefreshToken: false,
        persistSession: false,
      },
    }
  );

  try {
    const { role } = await req.json();
    if (role !== "owner" && role !== "pharmacist") {
      return NextResponse.json(
        { error: "Invalid role specified." },
        { status: 400 }
      );
    }

    const demoUser = DEMO_USERS[role];

    // 1. Check if user already exists by email
    const { data: existingUsers, error: getUserError } =
      await supabase.auth.admin.listUsers();
    if (getUserError) {
      console.error(
        `Error checking for demo user ${demoUser.email}:`,
        getUserError
      );
      throw new Error(`Failed to check for user: ${getUserError.message}`);
    }

    const existingUser = existingUsers?.users?.find(
      (u) => u.email === demoUser.email
    );
    if (existingUser) {
      console.log(
        `Demo user ${demoUser.email} already exists. Skipping creation.`
      );
      return NextResponse.json(
        { message: "Demo user already exists." },
        { status: 200 }
      );
    }

    // 2. Create the user in Auth
    console.log(`Creating demo user: ${demoUser.email}`);
    const { data: authUser, error: authError } =
      await supabase.auth.admin.createUser({
        email: demoUser.email,
        password: demoUser.password,
        email_confirm: true,
        user_metadata: {
          full_name: `Demo ${role.charAt(0).toUpperCase() + role.slice(1)}`,
          role: role,
        },
      });

    if (authError) {
      console.error(
        `Failed to create demo auth user ${demoUser.email}:`,
        authError
      );
      throw new Error(`Auth user creation failed: ${authError.message}`);
    }
    if (!authUser || !authUser.user) {
      throw new Error("User object not returned from createUser");
    }

    console.log(`Demo user ${demoUser.email} created successfully.`);

    // 3. Create Pharmacy (if owner) and Team Members
    if (role === "owner") {
      await createDemoPharmacy(supabase);
      await linkUserToPharmacy(
        supabase,
        authUser.user.id,
        DEMO_PHARMACY_ID,
        "owner"
      );
    } else {
      await linkUserToPharmacy(
        supabase,
        authUser.user.id,
        DEMO_PHARMACY_ID,
        "pharmacist"
      );
    }

    // 4. Seed associated data
    await seedDemoData(supabase, DEMO_PHARMACY_ID);

    return NextResponse.json(
      { message: `Demo ${role} user created successfully.` },
      { status: 201 }
    );
  } catch (error) {
    console.error("An unexpected error occurred:", error);
    const errorMessage =
      error instanceof Error ? error.message : "An unknown error occurred.";
    return NextResponse.json(
      { error: `An unexpected error occurred: ${errorMessage}` },
      { status: 500 }
    );
  }
}

// ============================================================================
// Seeding Helper Functions
// ============================================================================

async function createDemoPharmacy(supabase: any) {
  console.log("Creating demo pharmacy...");
  const { error } = await supabase.from("pharmacies").upsert({
    id: DEMO_PHARMACY_ID,
    name: "Pharmacie de Démo",
    address: "123 Rue de la République, Casablanca",
    phone: "0522000000",
    latitude: 33.5731,
    longitude: -7.5898,
    location: "SRID=4326;POINT(-7.5898 33.5731)",
  });
  if (error) {
    console.error("Failed to create demo pharmacy:", error);
    throw new Error(`Pharmacy creation failed: ${error.message}`);
  }
  console.log("Demo pharmacy created.");
}

async function linkUserToPharmacy(
  supabase: any,
  userId: string,
  pharmacyId: string,
  role: string
) {
  console.log(`Linking user ${userId} to pharmacy ${pharmacyId} as ${role}...`);

  // Update profiles table (not users table)
  const { error: updateProfileError } = await supabase
    .from("profiles")
    .update({ role: role })
    .eq("id", userId);
  if (updateProfileError)
    throw new Error(
      `Failed to update user's profile role: ${updateProfileError.message}`
    );

  // Insert into pharmacy_team_members
  const { error: teamMemberError } = await supabase
    .from("pharmacy_team_members")
    .upsert({
      pharmacy_id: pharmacyId,
      user_id: userId,
      role: role,
      status: "active",
    });
  if (teamMemberError)
    throw new Error(
      `Failed to create team member link: ${teamMemberError.message}`
    );

  console.log("User linked successfully.");
}

async function seedDemoData(supabase: any, pharmacyId: string) {
  console.log(`Seeding data for pharmacy ${pharmacyId}...`);
  const products = [
    {
      name: "Doliprane 1000mg",
      category: "Antalgique",
      price: 18.5,
      qty: 150,
      expiry_months: 6,
    },
    {
      name: "Spasfon Lyoc 80mg",
      category: "Antispasmodique",
      price: 29.0,
      qty: 80,
      expiry_months: 12,
    },
    {
      name: "Amoxicilline 500mg",
      category: "Antibiotique",
      price: 65.0,
      qty: 45,
      expiry_months: 3,
    },
    {
      name: "Cetirizine 10mg",
      category: "Antihistaminique",
      price: 35.5,
      qty: 120,
      expiry_months: 24,
    },
    {
      name: "Ibuprofène 400mg",
      category: "AINS",
      price: 22.0,
      qty: 200,
      expiry_months: 8,
      is_urgent: true,
    },
    {
      name: "Ventoline 100µg",
      category: "Respiratoire",
      price: 45.0,
      qty: 60,
      expiry_months: 18,
      is_high_demand: true,
    },
  ];

  for (const p of products) {
    const expiry_date = new Date();
    expiry_date.setMonth(expiry_date.getMonth() + p.expiry_months);

    const { data: product, error: productError } = await supabase
      .from("products")
      .insert({
        name: p.name,
        description: `Boîte standard de ${p.name}`,
        category: p.category,
        expiry_date: expiry_date.toISOString(),
        original_price: p.price * 1.25, // Simulate a 25% markup for original price
      })
      .select()
      .single();

    if (productError)
      throw new Error(
        `Failed to insert product ${p.name}: ${productError.message}`
      );

    const { error: listingError } = await supabase.from("listings").insert({
      product_id: product.id,
      pharmacy_id: pharmacyId,
      quantity: p.qty,
      minimum_order: Math.floor(p.qty / 10),
      price_per_unit: p.price,
      status: "active",
      posting_type: Math.random() > 0.5 ? "sale" : "both",
      is_urgent: p.is_urgent || false,
      is_high_demand: p.is_high_demand || false,
    });

    if (listingError)
      throw new Error(
        `Failed to create listing for ${p.name}: ${listingError.message}`
      );
  }
  console.log("Demo data seeded successfully.");
}
