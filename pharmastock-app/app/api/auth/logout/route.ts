import { NextRequest, NextResponse } from "next/server";
import { logger } from "@/lib/logger";
import { authConfig } from '@/lib/config';
import { getServiceSupabaseClient } from '@/lib/supabaseClient';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  logger.debug('Logout initiated');

  try {
    const serviceSupabase = getServiceSupabaseClient();
    const { error: signOutError } = await serviceSupabase.auth.signOut();

    if (signOutError) {
      logger.error('logout', 'Error signing out of Supabase', { error: signOutError.message });
      // Proceed to clear cookies even if server-side sign out fails
    }

    const response = NextResponse.json(
      { success: true },
      { status: 200 }
    );

    // Clear all auth-related cookies with proper settings
    const cookieOptionsHttpOnly = {
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
      maxAge: 0
    };

    const cookieOptionsNonHttpOnly = {
      httpOnly: false,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax' as const,
      path: '/',
      maxAge: 0
    };

    // Clear Supabase session cookies
    response.cookies.set('sb-session', '', cookieOptionsHttpOnly);
    response.cookies.set('sb-access-token', '', cookieOptionsNonHttpOnly);
    response.cookies.set('sb-refresh-token', '', cookieOptionsHttpOnly);

    // Clear any additional session-related cookies
    response.cookies.set('pharmacy-id', '', cookieOptionsNonHttpOnly);
    response.cookies.set('user-role', '', cookieOptionsNonHttpOnly);

    logger.info('All session cookies cleared successfully');
    return response;
  } catch (error: any) {
    logger.error('logout', 'Unhandled error during logout', { error: error.message, stack: error.stack });
    return NextResponse.json(
      { error: 'Internal server error during logout' },
      { status: 500 }
    );
  }
} 