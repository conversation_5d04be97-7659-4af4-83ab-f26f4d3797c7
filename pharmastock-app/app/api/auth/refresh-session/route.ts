import { NextRequest, NextResponse } from 'next/server';
import { getServiceSupabaseClient } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { UserRole } from '@/lib/types/auth';

export const runtime = 'nodejs';

export async function POST(request: NextRequest) {
  try {
    // Get session from cookies
    const sessionCookie = request.cookies.get('sb-session');
    if (!sessionCookie) {
      return NextResponse.json({ error: 'No session found' }, { status: 401 });
    }

    const session = JSON.parse(sessionCookie.value);
    if (!session.user?.id) {
      return NextResponse.json({ error: 'Invalid session' }, { status: 401 });
    }

    const userId = session.user.id;
    logger.info('auth_refresh_session', 'Refreshing session for user', { userId });

    // Use service role to get updated user data
    const serviceSupabase = getServiceSupabaseClient();

    // Get pharmacy team member info
    const { data: teamMember, error: teamError } = await serviceSupabase
      .from('pharmacy_team_members')
      .select('pharmacy_id, status, role')
      .eq('user_id', userId)
      .maybeSingle();

    if (teamError) {
      logger.error('auth_refresh_session', 'Error fetching team member info', { error: teamError.message, stack: teamError.stack });
    }

    // Get user profile
    const { data: profile, error: profileError } = await serviceSupabase
      .from('profiles')
      .select('role')
      .eq('id', userId)
      .maybeSingle();

    if (profileError) {
      logger.error('auth_refresh_session', 'Error fetching user profile', { error: profileError.message, stack: profileError.stack });
    }

    // Get pharmacy info if user has one
    let pharmacy = null;
    if (teamMember?.pharmacy_id) {
      const { data: pharmacyData, error: pharmacyError } = await serviceSupabase
        .from('pharmacies')
        .select('*')
        .eq('id', teamMember.pharmacy_id)
        .single();

      if (pharmacyError) {
        logger.error('auth_refresh_session', 'Error fetching pharmacy info', { error: pharmacyError.message, stack: pharmacyError.stack });
      } else {
        pharmacy = pharmacyData;
      }
    }

    // Determine user role with priority: profile.role > teamMember.role > metadata.role > 'staff'
    const metadataRole = session.user.role as UserRole | undefined; // From existing session
    let userRole: UserRole = 'staff'; // Default role

    if (profile?.role) {
      userRole = profile.role as UserRole;
    } else if (teamMember?.role) {
      userRole = teamMember.role as UserRole;
    } else if (metadataRole) {
      userRole = metadataRole;
    }

    // Create updated user data
    const updatedUserData = {
      id: session.user.id,
      email: session.user.email,
      role: userRole,
      pharmacyId: teamMember?.pharmacy_id || null
    };

    // Create response
    const response = NextResponse.json({
      user: updatedUserData,
      pharmacy,
      teamMember: teamMember || null
    });

    // Update session cookie with new data
    response.cookies.set({
      name: 'sb-session',
      value: JSON.stringify({
        user: updatedUserData,
        access_token: session.access_token,
        refresh_token: session.refresh_token,
        expires_at: session.expires_at,
      }),
      httpOnly: true,
      secure: process.env.NODE_ENV === 'production',
      sameSite: 'lax',
      path: '/',
      maxAge: session.expires_at ? session.expires_at - Math.floor(Date.now() / 1000) : 60 * 60, // Use remaining expiry, fallback to 1 hour
    });

    logger.info('auth_refresh_session', 'Session refreshed successfully', {
      userId,
      pharmacyId: teamMember?.pharmacy_id,
      role: userRole
    });

    return response;

  } catch (error: any) {
    logger.error('auth_refresh_session', 'Unhandled error refreshing session', { error: error.message, stack: error.stack });
    return NextResponse.json(
      { error: 'Failed to refresh session' },
      { status: 500 }
    );
  }
}
