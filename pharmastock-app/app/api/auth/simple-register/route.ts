import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!;

export async function POST(request: NextRequest) {
  try {
    console.log("🔍 Simple registration API called");
    
    const body = await request.json();
    const { email, password } = body;

    console.log("📧 Registration attempt for:", email);

    // Create Supabase client with anon key (like the frontend would)
    const supabase = createClient(supabaseUrl, supabaseAnonKey);

    // Try to sign up the user
    const { data, error } = await supabase.auth.signUp({
      email,
      password,
      options: {
        emailRedirectTo: `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`,
      },
    });

    if (error) {
      console.error("❌ Supabase signup error:", error);
      return NextResponse.json(
        { error: error.message },
        { status: 400 }
      );
    }

    console.log("✅ User created successfully:", data.user?.id);

    return NextResponse.json({
      user: data.user,
      session: data.session,
      message: "Registration successful",
    });

  } catch (error) {
    console.error("❌ Registration API error:", error);
    return NextResponse.json(
      { error: "Internal server error" },
      { status: 500 }
    );
  }
}
