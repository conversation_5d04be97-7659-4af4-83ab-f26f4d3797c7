import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { UserRole } from '@/lib/types/auth';

export const runtime = "nodejs";

export async function GET(request: NextRequest) {
  logger.debug('GET request received for /api/auth/pharmacy');

  const { searchParams } = new URL(request.url);
  const pharmacyId = searchParams.get("id");

  const serviceSupabase = getServiceSupabaseClient();

  // Get the current user's session
  const { data: { user }, error: userError } = await serviceSupabase.auth.getUser();

  if (userError || !user) {
    logger.warn('auth_pharmacy_get', 'Unauthorized: User not authenticated', { error: userError?.message });
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const userRole = user.user_metadata?.role as UserRole;

  if (!pharmacyId) {
    // If no specific pharmacyId is requested, return the user's own pharmacy if they are a pharmacist or owner
    if (userRole === 'pharmacist' || userRole === 'owner') {
      const { data: teamMember, error: teamMemberError } = await serviceSupabase
        .from('pharmacy_team_members')
        .select('pharmacy_id')
        .eq('user_id', user.id)
        .maybeSingle();

      if (teamMemberError || !teamMember?.pharmacy_id) {
        logger.error('auth_pharmacy_get', 'Could not find pharmacy ID for user', { userId: user.id, error: teamMemberError?.message });
        return NextResponse.json({ error: 'Pharmacy not found for user.' }, { status: 404 });
      }

      const { data: pharmacyData, error: pharmacyError } = await serviceSupabase
        .from('pharmacies')
        .select('*')
        .eq('id', teamMember.pharmacy_id)
        .single();

      if (pharmacyError) {
        logger.error('auth_pharmacy_get', 'Error fetching user\'s pharmacy', { error: pharmacyError.message });
        return NextResponse.json({ error: 'Failed to fetch pharmacy data.' }, { status: 500 });
      }
      logger.info('auth_pharmacy_get', 'User\'s pharmacy fetched successfully', { userId: user.id, pharmacyId: pharmacyData.id });
      return NextResponse.json(pharmacyData, { status: 200 });

    } else if (userRole === 'super_admin') {
      // Super admins can get a list of all pharmacies or query by ID
      logger.warn('auth_pharmacy_get', 'Super admin attempting to fetch all pharmacies without ID, which is not supported by this endpoint logic. Please provide a pharmacy ID for specific lookup or use admin/stats for aggregate data.', { userId: user.id });
      return NextResponse.json({ error: 'Please provide a pharmacy ID.' }, { status: 400 });
    } else {
      logger.warn('auth_pharmacy_get', 'Unauthorized role attempting to access pharmacy data without ID', { userId: user.id, role: userRole });
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
  } else {
    // Specific pharmacyId is requested
    let canAccess = false;

    if (userRole === 'super_admin') {
      canAccess = true; // Super admin can access any pharmacy
    } else if (userRole === 'pharmacist' || userRole === 'owner') {
      // Pharmacist/owner can only access their own pharmacy
      const { data: teamMember, error: teamMemberError } = await serviceSupabase
        .from('pharmacy_team_members')
        .select('pharmacy_id')
        .eq('user_id', user.id)
        .eq('pharmacy_id', pharmacyId)
        .maybeSingle();

      if (teamMemberError) {
        logger.error('auth_pharmacy_get', 'Error checking pharmacy access', { userId: user.id, pharmacyId, error: teamMemberError.message });
        return NextResponse.json({ error: 'Failed to verify pharmacy access.' }, { status: 500 });
      }
      canAccess = !!teamMember; // True if a team member record exists for this pharmacy and user
    }

    if (!canAccess) {
      logger.warn('auth_pharmacy_get', 'Forbidden: User attempting to access unauthorized pharmacy', { userId: user.id, role: userRole, requestedPharmacyId: pharmacyId });
      return NextResponse.json({ error: 'Forbidden' }, { status: 403 });
    }

    // Fetch the specific pharmacy data
    const { data: pharmacyData, error: pharmacyError } = await serviceSupabase
      .from('pharmacies')
      .select('*')
      .eq('id', pharmacyId)
      .single();

    if (pharmacyError) {
      logger.error('auth_pharmacy_get', 'Error fetching specific pharmacy', { pharmacyId, error: pharmacyError.message });
      return NextResponse.json({ error: 'Failed to fetch pharmacy data.' }, { status: 500 });
    }
    logger.info('auth_pharmacy_get', 'Pharmacy fetched successfully', { userId: user.id, pharmacyId: pharmacyData.id });
    return NextResponse.json(pharmacyData, { status: 200 });
  }
}
