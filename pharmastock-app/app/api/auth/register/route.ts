import { NextResponse } from "next/server";
import {
  getAnonSupabaseClient,
  getServiceSupabaseClient,
} from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";
import { supabaseConfig } from "@/lib/config";
import { UserRole } from "@/lib/types/auth"; // Assuming UserRole is defined here

export const runtime = "nodejs";

export async function POST(request: Request) {
  logger.info("auth_register", "Registration attempt initiated");

  try {
    // Parse request body
    const {
      email,
      password,
      role: roleString, // Rename to avoid conflict with enum
      name,
      license_number,
      phone,
      address,
      city,
    } = await request.json();

    const role = roleString as UserRole;

    logger.debug("auth_register", "Registration data received", {
      email,
      role,
      pharmacyName: name,
      hasLicense: !!license_number,
    });

    // Validate input
    if (!email || !password || !role) {
      logger.error(
        "auth_register",
        "Registration failed - Missing required fields",
        {
          missingEmail: !email,
          missingPassword: !password,
          missingRole: !role,
        }
      );
      return NextResponse.json(
        { error: "Email, mot de passe et rôle sont requis" },
        { status: 400 }
      );
    }

    // Validate pharmacy data for pharmacist role
    if (role === "pharmacist" && (!name || !license_number || !address)) {
      logger.error(
        "auth_register",
        "Registration failed - Missing pharmacy data",
        {
          missingName: !name,
          missingLicense: !license_number,
          missingAddress: !address,
        }
      );
      return NextResponse.json(
        { error: "Informations de la pharmacie requises" },
        { status: 400 }
      );
    }

    // Create Supabase client
    const supabase = getAnonSupabaseClient();

    // Sign up the user with Supabase Auth
    const { data: authData, error: signUpError } = await supabase.auth.signUp({
      email,
      password,
      options: {
        data: {
          role,
          full_name: "", // You might want to get this from the request
        },
      },
    });

    if (signUpError) {
      // Safely extract error information without reading response body multiple times
      const errorMessage = signUpError.message || "Registration failed";
      const errorStatus = signUpError.status || 400;
      const errorCode = signUpError.code || "unknown_error";

      logger.error("auth_register", "Supabase signup error", {
        error: errorMessage,
        status: errorStatus,
        code: errorCode,
      });

      return NextResponse.json(
        { error: `Registration failed: ${errorMessage}` },
        { status: errorStatus }
      );
    }

    if (!authData.user || !authData.session) {
      throw new Error(
        "No user or session data returned from Supabase after signup"
      );
    }

    let pharmacy = null;
    let pharmacyId = null;

    // Handle pharmacy creation or association (skip for suppliers)
    if (name && license_number && role !== "supplier") {
      logger.debug("auth_register", "Handling pharmacy for user", {
        pharmacyName: name,
        role,
      });

      const serviceSupabase = getServiceSupabaseClient();

      // First check if pharmacy already exists with this email
      const { data: existingPharmacies, error: checkError } =
        await serviceSupabase.from("pharmacies").select("*").eq("email", email);

      if (checkError) {
        logger.error("auth_register", "Error checking existing pharmacy", {
          error: checkError.message,
          code: checkError.code,
        });
        throw new Error(`Database error: ${checkError.message}`);
      }

      const existingPharmacy =
        existingPharmacies && existingPharmacies.length > 0
          ? existingPharmacies[0]
          : null;

      if (existingPharmacy) {
        // Use existing pharmacy
        pharmacy = existingPharmacy;
        pharmacyId = existingPharmacy.id;
        logger.info("auth_register", "Using existing pharmacy", {
          pharmacyId: existingPharmacy.id,
          pharmacyName: existingPharmacy.name,
        });
      } else {
        // Create new pharmacy
        const { data: pharmacyData, error: pharmacyError } =
          await serviceSupabase
            .from("pharmacies")
            .insert([
              {
                name,
                email,
                phone: phone || "",
                address: address || "",
                city: city || "",
                license_number,
                is_verified: false,
              },
            ])
            .select()
            .single();

        if (pharmacyError) {
          logger.error("auth_register", "Error creating pharmacy", {
            error: pharmacyError.message,
            stack: pharmacyError.stack,
          });
          throw pharmacyError;
        }

        pharmacy = pharmacyData;
        pharmacyId = pharmacyData.id;
        logger.info("auth_register", "Pharmacy created successfully", {
          pharmacyId: pharmacyData.id,
          pharmacyName: pharmacyData.name,
        });
      }

      // Create team member entry (check if already exists first)
      const { data: existingTeamMembers, error: teamCheckError } =
        await serviceSupabase
          .from("pharmacy_team_members")
          .select("*")
          .eq("pharmacy_id", pharmacyId)
          .eq("user_id", authData.user.id);

      if (teamCheckError) {
        logger.error("auth_register", "Error checking existing team member", {
          error: teamCheckError.message,
          code: teamCheckError.code,
        });
        throw new Error(`Database error: ${teamCheckError.message}`);
      }

      const existingTeamMember =
        existingTeamMembers && existingTeamMembers.length > 0
          ? existingTeamMembers[0]
          : null;

      if (!existingTeamMember) {
        const { error: teamMemberError } = await serviceSupabase
          .from("pharmacy_team_members")
          .insert([
            {
              pharmacy_id: pharmacyId,
              user_id: authData.user.id,
              role: role,
              status: "active",
            },
          ]);

        if (teamMemberError) {
          logger.error("auth_register", "Error creating team member record", {
            error: teamMemberError.message,
            stack: teamMemberError.stack,
          });
          // Log the error but don't block registration if team member creation is not strictly critical
        } else {
          logger.info("auth_register", "Team member created successfully", {
            pharmacyId,
            userId: authData.user.id,
            role,
          });
        }
      } else {
        logger.info("auth_register", "Team member already exists", {
          pharmacyId,
          userId: authData.user.id,
          role: existingTeamMember.role,
        });
      }
    }

    logger.info("auth_register", "Registration successful", {
      userId: authData.user.id,
      email: authData.user.email,
      role: role,
      pharmacyId: pharmacyId,
    });

    // Create user data for session
    const userData = {
      id: authData.user.id,
      email: authData.user.email,
      role: role,
      pharmacyId: pharmacyId || null,
    };

    const response = NextResponse.json(
      {
        user: userData,
        pharmacy,
      },
      { status: 201 }
    );

    // Set the session cookie that middleware expects directly from signup data
    response.cookies.set({
      name: "sb-session",
      value: JSON.stringify({
        user: userData,
        access_token: authData.session.access_token,
        refresh_token: authData.session.refresh_token,
        expires_at: authData.session.expires_at,
      }),
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: authData.session.expires_in,
    });

    logger.info("auth_register", "Session cookie set after registration", {
      userId: userData.id,
    });

    // Set additional auth cookies for compatibility (if needed by frontend)
    response.cookies.set({
      name: "sb-access-token",
      value: authData.session.access_token,
      httpOnly: false, // Match login route
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: authData.session.expires_in,
    });

    response.cookies.set({
      name: "sb-refresh-token",
      value: authData.session.refresh_token,
      httpOnly: true,
      secure: process.env.NODE_ENV === "production",
      sameSite: "lax",
      path: "/",
      maxAge: 60 * 60 * 24 * 7, // 7 days
    });

    return response;
  } catch (error: any) {
    // Safely extract error information
    const errorMessage =
      error?.message || "Une erreur est survenue lors de l'inscription";
    const errorStatus = error?.status || 500;

    logger.error("auth_register", "Unhandled error during registration", {
      error: errorMessage,
      stack: error?.stack,
      context: "Top-level catch in POST /api/auth/register",
    });

    return NextResponse.json(
      { error: `Database error: ${errorMessage}` },
      { status: errorStatus }
    );
  }
}
