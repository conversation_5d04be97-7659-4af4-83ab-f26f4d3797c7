import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient, parseSession } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { UserRole } from '@/lib/types/auth'; // Assuming UserRole is defined here

export const runtime = 'nodejs';

export async function GET(request: NextRequest) {
  logger.debug('session_get', 'Session check initiated');

  try {
    const sessionStr = request.cookies.get('sb-session')?.value;

    logger.debug('session_get', 'Session API Cookie Check', {
      hasSessionCookie: !!sessionStr,
      sessionLength: sessionStr?.length,
      allCookies: Object.fromEntries(request.cookies.getAll().map(c => [c.name, c.value?.substring(0, 50) + '...']))
    });

    if (!sessionStr) {
      logger.debug('session_get', 'No session cookie found');
      return NextResponse.json({ user: null });
    }

    const session = parseSession(sessionStr);
    logger.debug('session_get', 'Session Parse Result', {
      hasSession: !!session,
      hasAccessToken: !!session?.access_token,
      hasRefreshToken: !!session?.refresh_token,
      expiresAt: session?.expires_at
    });

    if (!session) {
      logger.debug('session_get', 'Session parsing failed');
      return NextResponse.json({ user: null });
    }

    const serviceSupabase = getServiceSupabaseClient();

    // Verify the session is still valid
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(session.access_token);

    logger.debug('session_get', 'Session API User Check', {
      hasUser: !!user,
      userId: user?.id,
      userEmail: user?.email,
      userError: userError?.message,
      accessTokenLength: session.access_token?.length
    });

    if (userError || !user) {
      logger.error('session_get', 'Invalid or expired session', { error: userError?.message, stack: userError?.stack });
      return NextResponse.json({ user: null });
    }

    // Get pharmacy team member info using service functions (bypasses RLS)
    let teamMember = null;
    const { data: teamData, error: teamError } = await serviceSupabase
      .rpc('get_user_team_member', { user_id: user.id });

    if (teamError) {
      logger.error('session_get', 'Error fetching team member info', {
        error: teamError.message,
        stack: teamError.stack,
        userId: user.id
      });
    } else if (teamData && teamData.length > 0) {
      teamMember = {
        pharmacy_id: teamData[0].pharmacy_id,
        status: teamData[0].status,
        role: teamData[0].role
      };
    }

    // Get user profile from database for accurate role information using service function
    let profile = null;
    const { data: profileData, error: profileError } = await serviceSupabase
      .rpc('get_user_profile', { user_id: user.id });

    if (profileError) {
      logger.error('session_get', 'Error fetching user profile', {
        error: profileError.message,
        stack: profileError.stack,
        userId: user.id
      });
    } else if (profileData && profileData.length > 0) {
      profile = profileData[0];
    }

    logger.debug('session_get', 'Session API Debug', {
      userId: user.id,
      email: user.email,
      profileData: profile,
      profileRole: profile?.role,
      metadataRole: user.user_metadata?.role,
      hasTeamMember: !!teamMember,
      // The finalRole calculation below is illustrative; actual `userRole` will be determined properly
      finalRoleDebug: profile?.role || (user.user_metadata?.role === 'owner' ? user.user_metadata?.role : 'staff')
    });

    // Determine user role with priority: profile.role > teamMember.role (if exists) > metadata.role > 'staff'
    const metadataRole = user.user_metadata?.role as UserRole | undefined;
    let userRole: UserRole = 'staff'; // Default role

    if (profile?.role) {
      userRole = profile.role as UserRole;
    } else if (teamMember?.role) {
      userRole = teamMember.role as UserRole;
    } else if (metadataRole) {
      userRole = metadataRole;
    }

    logger.info('session_get', 'User role determination complete', {
      userId: user.id,
      role: userRole,
      metadataRole,
      teamMember: {
        hasTeamMember: !!teamMember,
        status: teamMember?.status,
        pharmacyId: teamMember?.pharmacy_id
      },
      rawUser: {
        id: user.id,
        email: user.email,
        createdAt: user.created_at
      }
    });

    const userData = {
      id: user.id,
      email: user.email,
      role: userRole,
      // Super admins don't need a specific pharmacy association
      pharmacyId: userRole === 'super_admin' ? null : teamMember?.pharmacy_id || null
    };

    logger.debug('session_get', 'Session validation successful', {
      userData,
      sessionInfo: {
        hasAccessToken: !!session.access_token,
        hasRefreshToken: !!session.refresh_token,
        expiresAt: session.expires_at
      }
    });

    return NextResponse.json({ user: userData });
  } catch (error: any) {
    logger.error('session_get', 'Unexpected error checking session', { error: error.message, stack: error.stack });
    return NextResponse.json(
      { error: 'An unexpected error occurred' },
      { status: 500 }
    );
  }
}
