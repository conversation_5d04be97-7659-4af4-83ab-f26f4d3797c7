import { NextRequest, NextResponse } from 'next/server';
import { getServiceSupabaseClient, parseSession } from '@/lib/supabaseClient';
import { logger } from '@/lib/logger';
import { UserRole } from '@/lib/types/auth';

async function getAuthenticatedUser(request: NextRequest) {
  const sessionStr = request.cookies.get('sb-session')?.value;
  
  if (!sessionStr) {
    return { user: null, error: 'No session found' };
  }

  const session = parseSession(sessionStr);
  if (!session) {
    return { user: null, error: 'Invalid session' };
  }

  const serviceSupabase = getServiceSupabaseClient();
  const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(session.access_token);

  if (userError || !user) {
    return { user: null, error: 'Invalid or expired session' };
  }

  // Get user role and pharmacy info
  const { data: teamData } = await serviceSupabase
    .rpc('get_user_team_member', { user_id: user.id });

  const teamMember = teamData && teamData.length > 0 ? teamData[0] : null;
  
  const { data: profileData } = await serviceSupabase
    .rpc('get_user_profile', { user_id: user.id });

  const profile = profileData && profileData.length > 0 ? profileData[0] : null;

  // Determine user role
  const metadataRole = user.user_metadata?.role as UserRole | undefined;
  let userRole: UserRole = 'staff';

  if (profile?.role) {
    userRole = profile.role as UserRole;
  } else if (teamMember?.role) {
    userRole = teamMember.role as UserRole;
  } else if (metadataRole) {
    userRole = metadataRole;
  }

  const userData = {
    id: user.id,
    email: user.email,
    role: userRole,
    pharmacyId: userRole === 'super_admin' ? null : teamMember?.pharmacy_id || null
  };

  return { user: userData, error: null };
}

function isAllowedRole(role: string | undefined): boolean {
  return ["owner", "pharmacist", "super_admin"].includes(role || "");
}

export async function GET(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }
    
    if (!isAllowedRole(user.role)) {
      return NextResponse.json({ error: 'Accès refusé' }, { status: 403 });
    }

    const serviceSupabase = getServiceSupabaseClient();

    // For now, return mock suppliers until we create the suppliers table
    const mockSuppliers = [
      { id: '1', name: 'Fournisseur Principal', contact: '<EMAIL>' },
      { id: '2', name: 'Pharma Distribution', contact: '<EMAIL>' },
      { id: '3', name: 'MediSupply', contact: '<EMAIL>' },
    ];

    return NextResponse.json({ suppliers: mockSuppliers });
  } catch (error: any) {
    logger.error('Error fetching suppliers:', error);
    return NextResponse.json(
      { error: 'Erreur lors du chargement des fournisseurs' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const { user, error: authError } = await getAuthenticatedUser(request);
    if (authError || !user) {
      return NextResponse.json({ error: 'Non autorisé' }, { status: 401 });
    }
    
    if (!isAllowedRole(user.role)) {
      return NextResponse.json({ error: 'Accès refusé' }, { status: 403 });
    }

    const { name, contact, address, phone } = await request.json();

    if (!name || !contact) {
      return NextResponse.json(
        { error: 'Le nom et le contact sont requis' },
        { status: 400 }
      );
    }

    const serviceSupabase = getServiceSupabaseClient();

    // TODO: Create suppliers table and implement real creation
    // For now, return mock response
    const newSupplier = {
      id: Date.now().toString(),
      name,
      contact,
      address: address || '',
      phone: phone || '',
      created_at: new Date().toISOString(),
    };

    return NextResponse.json({ supplier: newSupplier }, { status: 201 });
  } catch (error: any) {
    logger.error('Error creating supplier:', error);
    return NextResponse.json(
      { error: 'Erreur lors de la création du fournisseur' },
      { status: 500 }
    );
  }
}
