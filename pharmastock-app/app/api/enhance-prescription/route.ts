import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const { ocrText, imageData } = body;

    if (!ocrText && !imageData) {
      return NextResponse.json(
        { error: 'OCR text or image data is required' },
        { status: 400 }
      );
    }

    logger.info('enhance_prescription', 'Prescription enhancement requested', {
      ocrTextLength: ocrText?.length || 0,
      hasImageData: !!imageData
    });

    // TODO: Implement AI enhancement with OpenAI GPT-4 Vision or similar
    // For now, return enhanced OCR text with basic formatting
    
    const enhancedText = enhanceTextWithBasicFormatting(ocrText);
    
    // In production, you would use something like:
    /*
    const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4-vision-preview',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Please analyze this prescription OCR text and improve its readability. Format it properly and correct any obvious OCR errors. Focus on medication names, dosages, and instructions. OCR Text: ${ocrText}`
              },
              ...(imageData ? [{
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageData}`
                }
              }] : [])
            ]
          }
        ],
        max_tokens: 1000
      })
    });
    */

    return NextResponse.json({
      enhancedText,
      confidence: 0.85,
      improvements: [
        'Formatage amélioré',
        'Correction des erreurs OCR courantes',
        'Structure des médicaments clarifiée'
      ]
    });

  } catch (error: any) {
    logger.error('enhance_prescription', 'Error enhancing prescription', {
      error: error.message,
      stack: error.stack
    });

    return NextResponse.json(
      { error: 'Erreur lors de l\'amélioration de l\'ordonnance' },
      { status: 500 }
    );
  }
}

function enhanceTextWithBasicFormatting(text: string): string {
  if (!text) return '';
  
  // Basic text enhancement and formatting
  let enhanced = text
    // Fix common OCR errors
    .replace(/\b0\b/g, 'O') // Replace standalone 0 with O
    .replace(/\bl\b/g, 'I') // Replace standalone l with I
    .replace(/rng/g, 'mg') // Common OCR error
    .replace(/\brnl\b/g, 'ml') // Common OCR error
    
    // Format medication instructions
    .replace(/(\d+)\s*(fois|x)\s*(par|\/)\s*(jour|j)/gi, '$1 fois par jour')
    .replace(/(\d+)\s*cp\b/gi, '$1 comprimé(s)')
    .replace(/(\d+)\s*ml\b/gi, '$1 ml')
    .replace(/(\d+)\s*mg\b/gi, '$1 mg')
    
    // Add proper line breaks
    .replace(/([.!?])\s*([A-Z])/g, '$1\n\n$2')
    .replace(/(\d+\s*(mg|ml|cp|comprimé))/gi, '\n• $1')
    
    // Clean up extra whitespace
    .replace(/\s+/g, ' ')
    .replace(/\n\s+/g, '\n')
    .trim();

  // Add structure if it looks like a prescription
  if (enhanced.match(/\b(mg|ml|comprimé|fois|jour)\b/i)) {
    enhanced = `📋 ORDONNANCE ANALYSÉE\n\n${enhanced}\n\n⚠️ Vérifiez toujours avec l'ordonnance originale`;
  }

  return enhanced;
}
