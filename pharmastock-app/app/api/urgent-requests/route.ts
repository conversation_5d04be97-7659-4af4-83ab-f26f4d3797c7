import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    const cookieHeader = request.headers.get("cookie");

    let accessToken = null;

    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith("Bearer ")) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: "No access token found" },
        { status: 401 }
      );
    }

    // Get user from token
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Get urgent requests from this pharmacy
    const { data: urgentRequests, error } = await serviceSupabase
      .from("urgent_requests")
      .select("*")
      .eq("pharmacy_id", pharmacyId)
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    // Format the data to match the frontend interface
    const formattedRequests =
      urgentRequests?.map((request) => ({
        id: request.id,
        medication: request.product_name,
        quantity: request.quantity_needed,
        urgency:
          request.urgency_level === "critical"
            ? "critique"
            : request.urgency_level === "urgent"
            ? "urgent"
            : "normal",
        status:
          request.status === "active"
            ? "en attente"
            : request.status === "fulfilled"
            ? "accepté"
            : "refusé",
        timestamp: request.created_at,
        notes: request.description,
        responses: [], // TODO: Implement responses system
      })) || [];

    return NextResponse.json({ requests: formattedRequests });
  } catch (error) {
    logger.error("Error fetching urgent requests", { error });
    return NextResponse.json(
      { error: "Failed to fetch urgent requests" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    const body = await request.json();

    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    const cookieHeader = request.headers.get("cookie");

    let accessToken = null;

    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith("Bearer ")) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: "No access token found" },
        { status: 401 }
      );
    }

    // Get user from token
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Get pharmacy details for contact info
    const { data: pharmacy } = await serviceSupabase
      .from("pharmacies")
      .select("name, phone")
      .eq("id", pharmacyId)
      .single();

    // Map urgency levels
    const urgencyMapping = {
      asap: "critical",
      "24h": "urgent",
      normal: "normal",
    };

    // Create urgent request
    const { data: newRequest, error } = await serviceSupabase
      .from("urgent_requests")
      .insert([
        {
          pharmacy_id: pharmacyId,
          product_name: body.medication,
          description: body.details || "",
          quantity_needed: parseInt(body.quantity),
          urgency_level: urgencyMapping[body.urgency] || "normal",
          contact_name: pharmacy?.name || "Pharmacie",
          contact_phone: pharmacy?.phone || "",
          status: "active",
        },
      ])
      .select()
      .single();

    if (error) {
      throw error;
    }

    // Format response to match frontend interface
    const formattedRequest = {
      id: newRequest.id,
      medication: newRequest.product_name,
      quantity: newRequest.quantity_needed,
      urgency:
        newRequest.urgency_level === "critical"
          ? "critique"
          : newRequest.urgency_level === "urgent"
          ? "urgent"
          : "normal",
      status: "en attente",
      timestamp: newRequest.created_at,
      notes: newRequest.description,
      responses: [],
    };

    return NextResponse.json({ request: formattedRequest });
  } catch (error) {
    logger.error("Error creating urgent request", { error });
    return NextResponse.json(
      { error: "Failed to create urgent request" },
      { status: 500 }
    );
  }
}
