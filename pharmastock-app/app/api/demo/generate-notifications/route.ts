import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@supabase/supabase-js";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!;
const serviceKey = process.env.SUPABASE_SERVICE_ROLE_KEY!;

const supabase = createClient(supabaseUrl, serviceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

export async function POST(request: NextRequest) {
  try {
    const { userId, pharmacyId } = await request.json();

    if (!userId || !pharmacyId) {
      return NextResponse.json(
        { error: "Missing userId or pharmacyId" },
        { status: 400 }
      );
    }

    console.log('🔔 Generating fresh demo notifications for user:', userId);

    // Get current products and listings for context
    const { data: products } = await supabase
      .from('products')
      .select('*')
      .eq('pharmacy_id', pharmacyId);

    const { data: listings } = await supabase
      .from('listings')
      .select('*, pharmacy:pharmacies(name), product:products(name)')
      .neq('pharmacy_id', pharmacyId);

    // Generate time-based notifications
    const now = new Date();
    const notifications = [];

    // 1. New marketplace opportunity (just appeared)
    if (listings && listings.length > 0) {
      const randomListing = listings[Math.floor(Math.random() * listings.length)];
      notifications.push({
        user_id: userId,
        pharmacy_id: pharmacyId,
        type: 'marketplace_opportunity',
        title: '🆕 Nouvelle Offre Disponible',
        message: `${randomListing.product?.name || 'Produit'} disponible - ${randomListing.pharmacy?.name || 'Pharmacie'}`,
        priority: 'normal',
        read: false,
        created_at: now.toISOString(),
        data: {
          listing_id: randomListing.id,
          seller_name: randomListing.pharmacy?.name,
          price: randomListing.price_per_unit,
          just_appeared: true
        }
      });
    }

    // 2. Stock level changed notification
    if (products && products.length > 0) {
      const lowStockProduct = products.find(p => p.quantity < 10);
      if (lowStockProduct) {
        notifications.push({
          user_id: userId,
          pharmacy_id: pharmacyId,
          type: 'stock_alert',
          title: '📉 Mise à Jour Stock',
          message: `${lowStockProduct.name} - Stock mis à jour: ${lowStockProduct.quantity} unités`,
          priority: lowStockProduct.quantity < 5 ? 'urgent' : 'normal',
          read: false,
          created_at: new Date(now.getTime() - 30000).toISOString(), // 30 seconds ago
          data: {
            product_id: lowStockProduct.id,
            current_stock: lowStockProduct.quantity,
            threshold_crossed: true
          }
        });
      }
    }

    // 3. System activity notification
    notifications.push({
      user_id: userId,
      pharmacy_id: pharmacyId,
      type: 'system',
      title: '👋 Activité Récente',
      message: `Connexion détectée à ${now.toLocaleTimeString('fr-FR')} - Système opérationnel`,
      priority: 'low',
      read: false,
      created_at: new Date(now.getTime() - 60000).toISOString(), // 1 minute ago
      data: {
        login_time: now.toISOString(),
        system_status: 'operational'
      }
    });

    // 4. Random urgent request appeared
    if (Math.random() > 0.7) { // 30% chance
      notifications.push({
        user_id: userId,
        pharmacy_id: pharmacyId,
        type: 'urgent_request',
        title: '🚨 Nouvelle Demande Urgente',
        message: 'Un collègue recherche un médicament dans votre région',
        priority: 'urgent',
        read: false,
        created_at: new Date(now.getTime() - 120000).toISOString(), // 2 minutes ago
        data: {
          urgency_level: 'high',
          region: 'Casablanca',
          expires_soon: true
        }
      });
    }

    // Insert notifications
    if (notifications.length > 0) {
      const { data: createdNotifications, error } = await supabase
        .from('notifications')
        .insert(notifications)
        .select();

      if (error) {
        console.error('Error creating notifications:', error);
        return NextResponse.json(
          { error: 'Failed to create notifications' },
          { status: 500 }
        );
      }

      console.log(`✅ Generated ${createdNotifications.length} fresh notifications`);

      return NextResponse.json({
        success: true,
        notifications: createdNotifications,
        count: createdNotifications.length
      });
    }

    return NextResponse.json({
      success: true,
      notifications: [],
      count: 0
    });

  } catch (error) {
    console.error('Error generating notifications:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
