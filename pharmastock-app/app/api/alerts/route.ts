import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');
    
    let accessToken = null;
    
    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }
    
    if (!accessToken) {
      return NextResponse.json({ error: 'No access token found' }, { status: 401 });
    }
    
    // Get user from token
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Generate alerts based on real data
    const alerts = [];

    // 1. Check for expiring products
    const { data: expiringProducts } = await serviceSupabase
      .from("stock")
      .select("id, product:products(name), expiry_date")
      .eq("pharmacy_id", pharmacyId)
      .gte("expiry_date", new Date().toISOString())
      .lte("expiry_date", new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString())
      .limit(10);

    if (expiringProducts && expiringProducts.length > 0) {
      alerts.push({
        id: "expiry-alert",
        type: "expiry",
        title: "Produits expirant bientôt",
        message: `${expiringProducts.length} produits expirent dans moins de 15 jours`,
        timestamp: new Date().toISOString(),
        read: false,
        priority: "high"
      });
    }

    // 2. Check for low stock
    const { data: lowStockProducts } = await serviceSupabase
      .from("stock")
      .select("id, product:products(name), quantity")
      .eq("pharmacy_id", pharmacyId)
      .lt("quantity", 10)
      .limit(10);

    if (lowStockProducts && lowStockProducts.length > 0) {
      alerts.push({
        id: "stock-alert",
        type: "stock",
        title: "Stock faible",
        message: `${lowStockProducts.length} produits ont un stock faible`,
        timestamp: new Date(Date.now() - 2 * 60 * 60 * 1000).toISOString(),
        read: false,
        priority: "medium"
      });
    }

    // 3. Check for new urgent requests
    const { data: urgentRequests } = await serviceSupabase
      .from("urgent_requests")
      .select("id")
      .eq("pharmacy_id", pharmacyId)
      .eq("status", "pending")
      .gte("created_at", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString());

    if (urgentRequests && urgentRequests.length > 0) {
      alerts.push({
        id: "urgent-alert",
        type: "urgent",
        title: "Nouvelles demandes urgentes",
        message: `${urgentRequests.length} nouvelles demandes urgentes reçues`,
        timestamp: new Date(Date.now() - 4 * 60 * 60 * 1000).toISOString(),
        read: false,
        priority: "high"
      });
    }

    // 4. Check for marketplace opportunities
    const { data: marketplaceListings } = await serviceSupabase
      .from("listings")
      .select("id")
      .neq("pharmacy_id", pharmacyId)
      .eq("status", "active")
      .gte("created_at", new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString())
      .limit(5);

    if (marketplaceListings && marketplaceListings.length > 0) {
      alerts.push({
        id: "marketplace-alert",
        type: "marketplace",
        title: "Nouvelles opportunités",
        message: `${marketplaceListings.length} nouvelles offres disponibles sur le marketplace`,
        timestamp: new Date(Date.now() - 6 * 60 * 60 * 1000).toISOString(),
        read: true,
        priority: "low"
      });
    }

    // 5. Add a system alert
    alerts.push({
      id: "system-alert",
      type: "system",
      title: "Mise à jour système",
      message: "Nouvelles fonctionnalités disponibles dans PharmaStock",
      timestamp: new Date(Date.now() - 12 * 60 * 60 * 1000).toISOString(),
      read: true,
      priority: "low"
    });

    return NextResponse.json({ alerts });
  } catch (error) {
    logger.error("Error fetching alerts", { error });
    return NextResponse.json(
      { error: "Failed to fetch alerts" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');
    let accessToken = null;
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }
    if (!accessToken) {
      return NextResponse.json({ error: 'No access token found' }, { status: 401 });
    }
    // Get user from token
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(accessToken);
    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();
    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }
    const pharmacyId = teamMember.pharmacy_id;
    // Parse body
    const body = await request.json();
    const { type, title, message, priority } = body;
    if (!type || !title || !message || !priority) {
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });
    }
    // Insert into custom_alerts
    const { data, error } = await serviceSupabase
      .from("custom_alerts")
      .insert([
        {
          user_id: user.id,
          pharmacy_id: pharmacyId,
          type,
          title,
          message,
          priority,
          timestamp: new Date().toISOString(),
          read: false,
        },
      ])
      .select()
      .single();
    if (error) {
      logger.error("Error creating custom alert", { error });
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ alert: data }, { status: 201 });
  } catch (error) {
    logger.error("Error in POST /api/alerts", { error });
    return NextResponse.json(
      { error: "Failed to create alert" },
      { status: 500 }
    );
  }
}
