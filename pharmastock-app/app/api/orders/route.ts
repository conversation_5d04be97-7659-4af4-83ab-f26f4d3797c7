import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');
    
    let accessToken = null;
    
    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }
    
    if (!accessToken) {
      return NextResponse.json({ error: 'No access token found' }, { status: 401 });
    }
    
    // Get user from token
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Get transactions for this pharmacy (both as buyer and seller)
    const { data: transactions, error } = await serviceSupabase
      .from("transactions")
      .select(`
        id,
        quantity,
        total_amount,
        status,
        created_at,
        updated_at,
        listing:listings(
          id,
          price_per_unit,
          product:products(
            id,
            name,
            category,
            description
          )
        ),
        seller:seller_id(
          id,
          name
        ),
        buyer:buyer_id(
          id,
          name
        )
      `)
      .or(`seller_id.eq.${pharmacyId},buyer_id.eq.${pharmacyId}`)
      .order("created_at", { ascending: false });

    if (error) {
      throw error;
    }

    // Format transactions to match frontend interface
    const formattedOrders = transactions?.map((transaction: any) => ({
      id: `ORD-${transaction.id.slice(-6).toUpperCase()}`,
      product: transaction.listing?.product?.name || "Produit inconnu",
      quantity: transaction.quantity,
      status: transaction.status,
      date: transaction.created_at,
      total: parseFloat(transaction.total_amount),
      supplier: transaction.seller?.name || "Fournisseur inconnu",
      category: transaction.listing?.product?.category || "Non catégorisé",
      type: transaction.seller_id === pharmacyId ? "sale" : "purchase",
      pricePerUnit: parseFloat(transaction.listing?.price_per_unit || 0),
      description: transaction.listing?.product?.description || "",
    })) || [];

    return NextResponse.json({ orders: formattedOrders });
  } catch (error) {
    logger.error("Error fetching orders", { error });
    return NextResponse.json(
      { error: "Failed to fetch orders" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();
    const body = await request.json();
    
    // Get the authorization header
    const authHeader = request.headers.get('authorization');
    const cookieHeader = request.headers.get('cookie');
    
    let accessToken = null;
    
    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith('Bearer ')) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }
    
    if (!accessToken) {
      return NextResponse.json({ error: 'No access token found' }, { status: 401 });
    }
    
    // Get user from token
    const { data: { user }, error: userError } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Create new transaction
    const { data: newTransaction, error } = await serviceSupabase
      .from("transactions")
      .insert([
        {
          listing_id: body.listing_id,
          seller_id: body.seller_id,
          buyer_id: pharmacyId,
          quantity: body.quantity,
          total_amount: body.total_amount,
          status: "pending",
        },
      ])
      .select()
      .single();

    if (error) {
      throw error;
    }

    return NextResponse.json({ transaction: newTransaction });
  } catch (error) {
    logger.error("Error creating order", { error });
    return NextResponse.json(
      { error: "Failed to create order" },
      { status: 500 }
    );
  }
}
