import { NextRequest, NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get the authorization header
    const authHeader = request.headers.get("authorization");
    const cookieHeader = request.headers.get("cookie");

    let accessToken = null;

    // Try to get token from Authorization header first
    if (authHeader && authHeader.startsWith("Bearer ")) {
      accessToken = authHeader.substring(7);
    } else if (cookieHeader) {
      // Try to extract from sb-access-token cookie
      const tokenMatch = cookieHeader.match(/sb-access-token=([^;]+)/);
      if (tokenMatch) {
        accessToken = decodeURIComponent(tokenMatch[1]);
      }
    }

    if (!accessToken) {
      return NextResponse.json(
        { error: "No access token found" },
        { status: 401 }
      );
    }

    // Get user from token
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser(accessToken);

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Get current date for calculations
    const now = new Date().toISOString();
    const in30Days = new Date(
      Date.now() + 30 * 24 * 60 * 60 * 1000
    ).toISOString();
    const in60Days = new Date(
      Date.now() + 60 * 24 * 60 * 60 * 1000
    ).toISOString();
    const in90Days = new Date(
      Date.now() + 90 * 24 * 60 * 60 * 1000
    ).toISOString();

    // Execute all queries in parallel for better performance
    const [
      expiryStats,
      stockStats,
      listingStats,
      opportunityStats,
      urgentRequestStats,
      notificationStats,
    ] = await Promise.all([
      // 1. Get expiry statistics with efficient SQL
      serviceSupabase
        .from("products")
        .select("expiry_date")
        .eq("pharmacy_id", pharmacyId)
        .gte("expiry_date", now)
        .lte("expiry_date", in90Days),

      // 2. Get stock and inventory statistics
      serviceSupabase
        .from("products")
        .select("quantity, unit_price, reorder_level")
        .eq("pharmacy_id", pharmacyId),

      // 3. Get active listings count
      serviceSupabase
        .from("listings")
        .select("id", { count: "exact" })
        .eq("pharmacy_id", pharmacyId)
        .eq("status", "active"),

      // 4. Get marketplace opportunities count
      serviceSupabase
        .from("listings")
        .select("id", { count: "exact" })
        .neq("pharmacy_id", pharmacyId)
        .eq("status", "active"),

      // 5. Get urgent requests count
      serviceSupabase
        .from("urgent_requests")
        .select("id", { count: "exact" })
        .eq("status", "active"),

      // 6. Get unread notifications count
      serviceSupabase
        .from("notifications")
        .select("id", { count: "exact" })
        .eq("user_id", user.id)
        .eq("read", false),
    ]);

    // Process expiry statistics efficiently
    const expiryProducts = expiryStats.data || [];
    const expiringIn30Days = expiryProducts.filter(
      (p) => p.expiry_date <= in30Days
    ).length;
    const expiringIn60Days = expiryProducts.filter(
      (p) => p.expiry_date > in30Days && p.expiry_date <= in60Days
    ).length;
    const expiringIn90Days = expiryProducts.filter(
      (p) => p.expiry_date > in60Days && p.expiry_date <= in90Days
    ).length;

    // Process stock statistics efficiently
    const stockProducts = stockStats.data || [];
    const lowStockProducts = stockProducts.filter(
      (p) => p.quantity <= (p.reorder_level || 10)
    ).length;
    const criticalStock = stockProducts.filter((p) => p.quantity <= 5).length;
    const totalInventoryValue = stockProducts.reduce(
      (sum, p) => sum + p.quantity * p.unit_price,
      0
    );

    // Get counts from the count queries
    const activeListings = listingStats.count || 0;
    const exchangeOpportunities = opportunityStats.count || 0;
    const networkRequests = urgentRequestStats.count || 0;
    const alertsCount = notificationStats.count || 0;

    // Calculate potential savings (simplified for performance)
    const potentialSavings = Math.round(totalInventoryValue * 0.15); // Estimate 15% potential savings

    // Calculate success rate (mock for now)
    const exchangeSuccessRate = 87.5;

    const stats = {
      // Expiry management
      expiringIn30Days,
      expiringIn60Days,
      expiringIn90Days,

      // Stock management
      lowStockProducts,
      criticalStock,
      totalProducts: stockProducts.length,
      totalInventoryValue: Math.round(totalInventoryValue),

      // Marketplace & exchanges
      activeListings,
      exchangeOpportunities,
      networkRequests,
      exchangeSuccessRate,

      // Financial
      potentialSavings,

      // Alerts & notifications
      alertsCount,

      // Activity metrics
      networkActivity: activeListings + exchangeOpportunities + networkRequests,
    };

    logger.info("Dashboard stats calculated", {
      pharmacyId,
      userId: user.id,
      stats,
    });

    return NextResponse.json({ stats });
  } catch (error) {
    logger.error("Error fetching dashboard stats", { error });
    return NextResponse.json(
      { error: "Failed to fetch dashboard statistics" },
      { status: 500 }
    );
  }
}
