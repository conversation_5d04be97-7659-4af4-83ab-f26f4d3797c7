import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const supabase = createRouteHandlerClient({ cookies });

    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await supabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Execute all queries in parallel for better performance
    const [
      { data: notifications },
      { data: recentListings },
      { data: recentRequests },
      { data: products },
      { data: opportunities },
    ] = await Promise.all([
      // Get recent notifications
      supabase
        .from("notifications")
        .select("id, title, message, priority, created_at")
        .eq("user_id", user.id)
        .order("created_at", { ascending: false })
        .limit(5),

      // Get recent listings
      supabase
        .from("listings")
        .select(
          "id, product_name, quantity, price_per_unit, status, created_at"
        )
        .eq("pharmacy_id", pharmacyId)
        .order("created_at", { ascending: false })
        .limit(3),

      // Get recent urgent requests
      supabase
        .from("urgent_requests")
        .select("id, product_name, quantity_needed, urgency_level, created_at")
        .order("created_at", { ascending: false })
        .limit(3),

      // Get products for expiry alerts
      supabase
        .from("products")
        .select("name, expiry_date, quantity")
        .eq("pharmacy_id", pharmacyId)
        .gte("expiry_date", new Date().toISOString())
        .lte(
          "expiry_date",
          new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString()
        ),

      // Get marketplace opportunities
      supabase
        .from("listings")
        .select("product_name")
        .neq("pharmacy_id", pharmacyId)
        .eq("status", "active")
        .limit(3),
    ]);

    // Format activity items
    const activities = [];

    // Add notifications as activities
    notifications?.forEach((notif) => {
      activities.push({
        id: `notif-${notif.id}`,
        type: "notification",
        title: notif.title,
        description: notif.message,
        timestamp: notif.created_at,
        status: notif.priority === "urgent" ? "urgent" : "info",
        icon: "bell",
      });
    });

    // Add recent listings as activities
    recentListings?.forEach((listing) => {
      activities.push({
        id: `listing-${listing.id}`,
        type: "listing",
        title: `Annonce créée: ${listing.product_name}`,
        description: `${listing.quantity} unités à ${listing.price_per_unit} DH`,
        timestamp: listing.created_at,
        status: listing.status === "active" ? "success" : "pending",
        icon: "package",
      });
    });

    // Add recent urgent requests as activities
    recentRequests?.forEach((request) => {
      activities.push({
        id: `request-${request.id}`,
        type: "urgent_request",
        title: `Demande urgente: ${request.product_name}`,
        description: `${request.quantity_needed} unités - ${request.urgency_level}`,
        timestamp: request.created_at,
        status: request.urgency_level === "critical" ? "urgent" : "warning",
        icon: "alert",
      });
    });

    // Sort all activities by timestamp
    activities.sort(
      (a, b) =>
        new Date(b.timestamp).getTime() - new Date(a.timestamp).getTime()
    );

    // Take only the most recent 6 activities
    const recentActivities = activities.slice(0, 6);

    // Process expiry alerts efficiently
    const now = new Date();
    const in15Days = new Date(now.getTime() + 15 * 24 * 60 * 60 * 1000);

    const expiringCritical =
      products?.filter((p) => new Date(p.expiry_date) <= in15Days) || [];

    const expiringWarning =
      products?.filter((p) => new Date(p.expiry_date) > in15Days) || [];

    const alerts = [];

    if (expiringCritical.length > 0) {
      alerts.push({
        type: "critical",
        title: `${expiringCritical.length} produits expirent dans moins de 15 jours`,
        description: "Action immédiate requise pour éviter les pertes",
        products: expiringCritical.slice(0, 3).map((p) => p.name),
      });
    }

    if (expiringWarning.length > 0) {
      alerts.push({
        type: "warning",
        title: `${expiringWarning.length} produits expirent dans 15-30 jours`,
        description: "Opportunités d'échange disponibles",
        products: expiringWarning.slice(0, 3).map((p) => p.name),
      });
    }

    // Add marketplace opportunities alert
    if (opportunities && opportunities.length > 0) {
      alerts.push({
        type: "info",
        title: `${opportunities.length} nouvelles opportunités marketplace`,
        description: "Des pharmacies proposent des produits intéressants",
        products: opportunities.map((o) => o.product_name),
      });
    }

    return NextResponse.json({
      activities: recentActivities,
      alerts: alerts.slice(0, 3), // Limit to 3 alerts
    });
  } catch (error) {
    logger.error("Error fetching dashboard activity", { error });
    return NextResponse.json(
      { error: "Failed to fetch dashboard activity" },
      { status: 500 }
    );
  }
}
