import { NextRequest, NextResponse } from 'next/server';
import { logger } from '@/lib/logger';

export async function POST(request: NextRequest) {
  try {
    console.log('🔍 Starting prescription analysis...');
    const body = await request.json();
    const { imageData } = body;
    console.log('✅ Image data received, length:', imageData?.length || 'undefined');

    if (!imageData) {
      return NextResponse.json(
        { error: 'Image data is required' },
        { status: 400 }
      );
    }

    // Check available AI providers
    console.log('🔑 Checking available AI providers...');
    const hasGemini = !!process.env.GOOGLE_AI_API_KEY;
    const hasOpenAI = !!process.env.OPENAI_API_KEY;

    console.log('🤖 Available providers:', { hasGemini, hasOpenAI });

    if (!hasGemini && !hasOpenAI) {
      logger.error('analyze_prescription', 'No AI provider configured');
      return NextResponse.json(
        { error: 'Service de configuration manquant. Veuillez contacter l\'administrateur.' },
        { status: 500 }
      );
    }

    logger.info('analyze_prescription', 'Prescription analysis requested', {
      hasImageData: !!imageData,
      providers: { hasGemini, hasOpenAI }
    });

    // Try Google Gemini Vision first (free tier)
    if (hasGemini) {
      console.log('🚀 Using Google Gemini Vision API...');
      try {
        const geminiResponse = await fetch(`https://generativelanguage.googleapis.com/v1beta/models/gemini-1.5-flash:generateContent?key=${process.env.GOOGLE_AI_API_KEY}`, {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            contents: [{
              parts: [
                {
                  text: `Analyze this prescription image and extract the following information in JSON format:
                  {
                    "medications": [
                      {
                        "name": "medication name",
                        "dosage": "dosage amount",
                        "frequency": "how often to take",
                        "duration": "how long to take",
                        "instructions": "special instructions"
                      }
                    ],
                    "patient_info": {
                      "name": "patient name if visible",
                      "age": "age if visible"
                    },
                    "doctor_info": {
                      "name": "doctor name if visible",
                      "clinic": "clinic name if visible"
                    },
                    "date": "prescription date if visible",
                    "confidence": "high/medium/low based on image clarity"
                  }

                  Please provide ONLY the JSON response, no additional text.`
                },
                {
                  inline_data: {
                    mime_type: "image/jpeg",
                    data: imageData.includes(',') ? imageData.split(',')[1] : imageData // Handle both formats
                  }
                }
              ]
            }]
          })
        });

        console.log('📡 Gemini API response status:', geminiResponse.status);

        if (geminiResponse.ok) {
          const geminiData = await geminiResponse.json();
          console.log('✅ Gemini Vision successful');

          const content = geminiData.candidates?.[0]?.content?.parts?.[0]?.text;
          if (content) {
            console.log('📄 Gemini response preview:', content.substring(0, 200) + '...');

            // Try to parse JSON from the response
            try {
              const jsonMatch = content.match(/\{[\s\S]*\}/);
              if (jsonMatch) {
                const analysisResult = JSON.parse(jsonMatch[0]);
                return NextResponse.json({
                  success: true,
                  analysis: analysisResult,
                  provider: 'gemini',
                  model: 'gemini-1.5-flash',
                  timestamp: new Date().toISOString(),
                  disclaimer: 'Cette analyse est à des fins informatives uniquement. Toujours se référer à l\'ordonnance originale.'
                });
              }
            } catch (parseError) {
              console.log('⚠️ Gemini response not valid JSON, using as text');
              return NextResponse.json({
                success: true,
                analysis: { raw_text: content },
                provider: 'gemini',
                model: 'gemini-1.5-flash',
                timestamp: new Date().toISOString(),
                disclaimer: 'Cette analyse est à des fins informatives uniquement. Toujours se référer à l\'ordonnance originale.'
              });
            }
          }
        } else {
          const errorData = await geminiResponse.json();
          console.log('❌ Gemini API error:', errorData);
        }
      } catch (geminiError) {
        console.log('❌ Gemini failed:', geminiError.message);
      }
    }

    // Fallback to OpenAI GPT-4 Vision if available
    if (hasOpenAI) {
      console.log('🚀 Falling back to OpenAI API...');
      const openaiResponse = await fetch('https://api.openai.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${process.env.OPENAI_API_KEY}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        model: 'gpt-4o',
        messages: [
          {
            role: 'user',
            content: [
              {
                type: 'text',
                text: `Analysez cette ordonnance médicale en français. Extrayez et structurez les informations suivantes:

1. **Informations du patient** (si visibles)
2. **Médicaments prescrits** avec:
   - Nom du médicament
   - Dosage
   - Forme pharmaceutique
   - Posologie (fréquence, durée)
   - Instructions spéciales

3. **Informations du prescripteur** (si visibles)
4. **Date de prescription** (si visible)

Formatez la réponse de manière claire et structurée. Si certaines informations ne sont pas lisibles, indiquez-le clairement.

⚠️ **Important**: Cette analyse est à des fins informatives uniquement. Toujours se référer à l'ordonnance originale pour la prise de médicaments.`
              },
              {
                type: 'image_url',
                image_url: {
                  url: `data:image/jpeg;base64,${imageData}`
                }
              }
            ]
          }
        ],
        max_tokens: 1500,
        temperature: 0.1 // Low temperature for more consistent medical analysis
      })
    });

    console.log('📡 OpenAI API response status:', openaiResponse.status);

    if (!openaiResponse.ok) {
      const errorData = await openaiResponse.json();
      console.log('❌ OpenAI API error:', errorData);
      logger.error('analyze_prescription', 'OpenAI API error', {
        status: openaiResponse.status,
        error: errorData
      });
      
      return NextResponse.json(
        { error: 'Erreur lors de l\'analyse de l\'ordonnance. Veuillez réessayer.' },
        { status: 500 }
      );
    }

    const result = await openaiResponse.json();
    const analysis = result.choices?.[0]?.message?.content;

    if (!analysis) {
      logger.error('analyze_prescription', 'No analysis content returned from OpenAI');
      return NextResponse.json(
        { error: 'Aucune analyse n\'a pu être générée pour cette image.' },
        { status: 500 }
      );
    }

    logger.info('analyze_prescription', 'Prescription analysis completed successfully', {
      analysisLength: analysis.length
    });

      return NextResponse.json({
        analysis,
        model: 'gpt-4o',
        provider: 'openai',
        timestamp: new Date().toISOString(),
        disclaimer: 'Cette analyse est à des fins informatives uniquement. Toujours se référer à l\'ordonnance originale.'
      });
    }

    // If we reach here, no AI provider worked
    logger.error('analyze_prescription', 'All AI providers failed');
    return NextResponse.json(
      { error: 'Service d\'analyse temporairement indisponible. Veuillez réessayer plus tard.' },
      { status: 503 }
    );

  } catch (error: any) {
    logger.error('analyze_prescription', 'Error analyzing prescription', {
      error: error.message,
      stack: error.stack
    });

    return NextResponse.json(
      { error: 'Erreur lors de l\'analyse de l\'ordonnance. Veuillez réessayer.' },
      { status: 500 }
    );
  }
}
