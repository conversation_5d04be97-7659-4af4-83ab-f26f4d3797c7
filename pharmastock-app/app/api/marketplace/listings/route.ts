import { createRouteHandlerClient } from '@supabase/auth-helpers-nextjs';
import { cookies } from 'next/headers';
import { NextResponse } from 'next/server';

export async function POST(request: Request) {
  const body = await request.json();
  const supabase = createRouteHandlerClient({ cookies });

  const { data: { session } } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }

  const { data: userProfile, error: profileError } = await supabase
    .from('profiles')
    .select('pharmacy_id')
    .eq('id', session.user.id)
    .single();

  if (profileError || !userProfile || !userProfile.pharmacy_id) {
    return NextResponse.json({ error: 'User pharmacy not found' }, { status: 400 });
  }

  const pharmacy_id = userProfile.pharmacy_id;

  const { productData, listingData } = body;

  try {
    // Step 1: Insert the product, ensuring original_price is included
    const { data: newProduct, error: productError } = await supabase
      .from('products')
      .insert({ 
        name: productData.name,
        description: productData.description,
        category: productData.category,
        original_price: productData.original_price,
        expiry_date: productData.expiry_date,
        pharmacy_id,
       })
      .select('id')
      .single();

    if (productError) {
      console.error('Error creating product:', productError);
      return NextResponse.json({ error: productError.message }, { status: 500 });
    }

    // Step 2: Create the listing using the new product's ID
    const { data: newListing, error: listingError } = await supabase
      .from('listings')
      .insert({
        product_id: newProduct.id,
        pharmacy_id: pharmacy_id,
        quantity: listingData.quantity,
        price_per_unit: listingData.price_per_unit,
        minimum_order: listingData.minimum_order,
        status: listingData.status,
        expires_at: listingData.expires_at,
        posting_type: listingData.posting_type,
      })
      .select();

    if (listingError) {
      console.error('Error creating listing:', listingError);
      // Attempt to clean up the created product if listing fails
      await supabase.from('products').delete().eq('id', newProduct.id);
      return NextResponse.json({ error: listingError.message }, { status: 500 });
    }

    return NextResponse.json(newListing);
  } catch (error) {
    console.error('An unexpected error occurred:', error);
    return NextResponse.json({ error: 'An unexpected error occurred' }, { status: 500 });
  }
} 