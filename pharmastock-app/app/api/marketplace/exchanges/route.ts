import { NextRequest, NextResponse } from "next/server";
import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { logger } from "@/lib/logger";

export async function GET(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await supabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Get exchanges (reservations that involve this pharmacy)
    const { data: outgoingReservations } = await supabase
      .from("listing_reservations")
      .select(
        `
        *,
        listing:listings(
          *,
          product:products(name, category, expiry_date),
          pharmacy:pharmacies(name)
        )
      `
      )
      .eq("pharmacy_id", pharmacyId);

    const { data: incomingReservations } = await supabase
      .from("listing_reservations")
      .select(
        `
        *,
        listing:listings!inner(
          *,
          product:products(name, category, expiry_date),
          pharmacy:pharmacies(name)
        )
      `
      )
      .eq("listing.pharmacy_id", pharmacyId);

    // Format exchanges
    const exchanges = [];

    // Outgoing exchanges (reservations made by this pharmacy)
    outgoingReservations?.forEach((reservation) => {
      if (reservation.listing) {
        exchanges.push({
          id: `out-${reservation.id}`,
          type: "outgoing",
          product: reservation.listing.product?.name || "Produit inconnu",
          quantity: reservation.quantity,
          partner: reservation.listing.pharmacy?.name || "Pharmacie inconnue",
          status: reservation.status,
          created_at: reservation.created_at,
          expiry_date: reservation.listing.product?.expiry_date || "",
          reservation_id: reservation.id,
          listing_id: reservation.listing_id,
        });
      }
    });

    // Incoming exchanges (reservations made on this pharmacy's listings)
    incomingReservations?.forEach((reservation) => {
      if (reservation.listing) {
        exchanges.push({
          id: `in-${reservation.id}`,
          type: "incoming",
          product: reservation.listing.product?.name || "Produit inconnu",
          quantity: reservation.quantity,
          partner: "Pharmacie demandeur", // Would need to get the requesting pharmacy name
          status: reservation.status,
          created_at: reservation.created_at,
          expiry_date: reservation.listing.product?.expiry_date || "",
          reservation_id: reservation.id,
          listing_id: reservation.listing_id,
        });
      }
    });

    // Sort by creation date (newest first)
    exchanges.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    return NextResponse.json({ exchanges });
  } catch (error) {
    logger.error("Error fetching exchanges", { error });
    return NextResponse.json(
      { error: "Failed to fetch exchanges" },
      { status: 500 }
    );
  }
}

export async function PATCH(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    const { reservationId, status } = await request.json();

    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Update reservation status
    const { data, error } = await supabase
      .from("listing_reservations")
      .update({ status })
      .eq("id", reservationId)
      .select();

    if (error) {
      throw error;
    }

    return NextResponse.json({ success: true, data });
  } catch (error) {
    logger.error("Error updating exchange status", { error });
    return NextResponse.json(
      { error: "Failed to update exchange status" },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });
    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await supabase.auth.getUser();
    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }
    // Parse body
    const body = await request.json();
    const { product, quantity, partner, message } = body;
    if (!product || !quantity || !partner) {
      return NextResponse.json({ error: "Missing fields" }, { status: 400 });
    }
    // Insert into exchanges
    const { data, error } = await supabase
      .from("exchanges")
      .insert([
        {
          product,
          quantity,
          partner,
          message,
          status: "pending",
          created_at: new Date().toISOString(),
          created_by: user.id,
        },
      ])
      .select()
      .single();
    if (error) {
      logger.error("Error creating exchange", { error });
      return NextResponse.json({ error: error.message }, { status: 500 });
    }
    return NextResponse.json({ exchange: data }, { status: 201 });
  } catch (error) {
    logger.error("Error in POST /api/marketplace/exchanges", { error });
    return NextResponse.json(
      { error: "Failed to create exchange" },
      { status: 500 }
    );
  }
}
