import { createRouteHandlerClient } from "@supabase/auth-helpers-nextjs";
import { cookies } from "next/headers";
import { NextResponse } from "next/server";
import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { logger } from "@/lib/logger";

export async function POST(request: Request) {
  const { listing_id, quantity } = await request.json();
  const supabase = createRouteHandlerClient({ cookies });

  const {
    data: { session },
  } = await supabase.auth.getSession();

  if (!session) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  const { data: userProfile, error: profileError } = await supabase
    .from("profiles")
    .select("pharmacy_id")
    .eq("id", session.user.id)
    .single();

  if (profileError || !userProfile || !userProfile.pharmacy_id) {
    return NextResponse.json(
      { error: "User pharmacy not found" },
      { status: 400 }
    );
  }

  const reserving_pharmacy_id = userProfile.pharmacy_id;

  try {
    const { data, error } = await supabase
      .from("listing_reservations")
      .insert([
        {
          listing_id,
          quantity,
          pharmacy_id: reserving_pharmacy_id,
          status: "pending",
        },
      ])
      .select();

    if (error) {
      console.error("Error creating reservation:", error);
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json(data);
  } catch (error) {
    console.error("An unexpected error occurred:", error);
    return NextResponse.json(
      { error: "An unexpected error occurred" },
      { status: 500 }
    );
  }
}

export async function GET(request: Request) {
  try {
    const serviceSupabase = getServiceSupabaseClient();

    // Get user from session
    const {
      data: { user },
      error: userError,
    } = await serviceSupabase.auth.getUser();

    if (userError || !user) {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
    }

    // Get user's pharmacy
    const { data: teamMember } = await serviceSupabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();

    if (!teamMember?.pharmacy_id) {
      return NextResponse.json(
        { error: "No pharmacy associated" },
        { status: 400 }
      );
    }

    const pharmacyId = teamMember.pharmacy_id;

    // Get reservations made by this pharmacy (outgoing)
    const { data: outgoingReservations } = await serviceSupabase
      .from("listing_reservations")
      .select(
        `
        *,
        listing:listings(
          price_per_unit,
          product:products(name, category, expiry_date),
          pharmacy:pharmacies(name)
        )
      `
      )
      .eq("pharmacy_id", pharmacyId);

    // Get reservations made on this pharmacy's listings (incoming)
    const { data: incomingReservations } = await serviceSupabase
      .from("listing_reservations")
      .select(
        `
        *,
        listing:listings!inner(
          price_per_unit,
          product:products(name, category, expiry_date),
          pharmacy:pharmacies(name)
        )
      `
      )
      .eq("listing.pharmacy_id", pharmacyId);

    // Format reservations
    const reservations = [];

    // Outgoing reservations
    outgoingReservations?.forEach((reservation) => {
      if (reservation.listing) {
        reservations.push({
          id: reservation.id,
          listing_id: reservation.listing_id,
          pharmacy_id: reservation.pharmacy_id,
          quantity: reservation.quantity,
          message: reservation.message,
          status: reservation.status,
          created_at: reservation.created_at,
          type: "outgoing",
          listing: {
            price_per_unit: reservation.listing.price_per_unit,
            product: {
              name: reservation.listing.product?.name || "Produit inconnu",
              category: reservation.listing.product?.category || "N/A",
              expiry_date: reservation.listing.product?.expiry_date || "",
            },
            pharmacy: {
              name: reservation.listing.pharmacy?.name || "Pharmacie inconnue",
            },
          },
        });
      }
    });

    // Incoming reservations
    incomingReservations?.forEach((reservation) => {
      if (reservation.listing) {
        reservations.push({
          id: reservation.id,
          listing_id: reservation.listing_id,
          pharmacy_id: reservation.pharmacy_id,
          quantity: reservation.quantity,
          message: reservation.message,
          status: reservation.status,
          created_at: reservation.created_at,
          type: "incoming",
          listing: {
            price_per_unit: reservation.listing.price_per_unit,
            product: {
              name: reservation.listing.product?.name || "Produit inconnu",
              category: reservation.listing.product?.category || "N/A",
              expiry_date: reservation.listing.product?.expiry_date || "",
            },
            pharmacy: {
              name: "Pharmacie demandeur",
            },
          },
        });
      }
    });

    // Sort by creation date (newest first)
    reservations.sort(
      (a, b) =>
        new Date(b.created_at).getTime() - new Date(a.created_at).getTime()
    );

    return NextResponse.json({ reservations });
  } catch (error) {
    logger.error("Error fetching reservations", { error });
    return NextResponse.json(
      { error: "Failed to fetch reservations" },
      { status: 500 }
    );
  }
}
