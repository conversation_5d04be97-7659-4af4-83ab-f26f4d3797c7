"use client";

import { useAuth } from '@/contexts/auth-context';
import { useAccessControl, hasAccessToRoute } from '@/hooks/useAccessControl';
import { usePathname } from 'next/navigation';

export default function TestAccessPage() {
  const { user, loading, isPharmacy } = useAuth();
  const pathname = usePathname();
  const { hasAccess, reason } = useAccessControl(pathname);

  const testRoutes = [
    '/dashboard',
    '/admin',
    '/admin/pharmacies',
    '/admin/users',
    '/marketplace',
    '/team',
    '/inventory',
    '/orders',
    '/reports',
    '/settings'
  ];

  if (loading) {
    return <div>Loading...</div>;
  }

  return (
    <div className="p-8">
      <h1 className="text-2xl font-bold mb-6">Access Control Test</h1>
      
      <div className="mb-6 p-4 bg-gray-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Current User Info</h2>
        <p><strong>Email:</strong> {user?.email || 'Not logged in'}</p>
        <p><strong>Role:</strong> {user?.role || 'None'}</p>
        <p><strong>User ID:</strong> {user?.id || 'None'}</p>
        <p><strong>Pharmacy ID:</strong> {user?.pharmacyId || 'None'}</p>
        <p><strong>Is Pharmacy:</strong> {isPharmacy ? 'Yes' : 'No'}</p>
      </div>

      <div className="mb-6 p-4 bg-blue-100 rounded">
        <h2 className="text-lg font-semibold mb-2">Current Page Access</h2>
        <p><strong>Path:</strong> {pathname}</p>
        <p><strong>Has Access:</strong> {hasAccess ? 'Yes' : 'No'}</p>
        {reason && <p><strong>Reason:</strong> {reason}</p>}
      </div>

      <div className="p-4 bg-green-100 rounded">
        <h2 className="text-lg font-semibold mb-4">Route Access Test</h2>
        <div className="grid grid-cols-2 gap-4">
          {testRoutes.map(route => {
            const access = hasAccessToRoute(route, user, isPharmacy);
            return (
              <div key={route} className={`p-2 rounded ${access ? 'bg-green-200' : 'bg-red-200'}`}>
                <strong>{route}</strong>: {access ? '✅ Allowed' : '❌ Denied'}
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
