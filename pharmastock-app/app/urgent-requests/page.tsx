"use client";

import { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>T<PERSON><PERSON>,
  <PERSON><PERSON>Trig<PERSON>,
  <PERSON>alogFooter,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import {
  Plus,
  AlertCircle,
  Clock,
  MapPin,
  PhoneCall,
  CheckCircle,
  XCircle,
} from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { UrgentRequestForm } from "@/components/urgent-requests/urgent-request-form";

interface UrgentRequest {
  id: number;
  medication: string;
  quantity: number;
  urgency: "critique" | "urgent" | "normal";
  status: "en attente" | "accepté" | "refusé";
  timestamp: string;
  notes?: string;
  responses: RequestResponse[];
}

interface RequestResponse {
  id: number;
  pharmacyName: string;
  distance: string;
  quantity: number;
  contactNumber: string;
  timestamp: string;
}

const urgencyOptions = {
  critique: "Critique (< 24h)",
  urgent: "Urgent (< 48h)",
  normal: "Normal",
};

const urgencyStyles = {
  critique: "bg-red-100 text-red-800 dark:bg-red-900/50 animate-pulse",
  urgent: "bg-amber-100 text-amber-800 dark:bg-amber-900/50",
  normal: "bg-blue-100 text-blue-800 dark:bg-blue-900/50",
};

const statusStyles = {
  "en attente": "bg-amber-50 text-amber-800 dark:bg-amber-900/50",
  accepté: "bg-emerald-50 text-emerald-800 dark:bg-emerald-900/50",
  refusé: "bg-red-50 text-red-800 dark:bg-red-900/50",
};

export default function UrgentRequestsPage() {
  const [requests, setRequests] = useState<UrgentRequest[]>([]);
  const [selectedRequest, setSelectedRequest] = useState<UrgentRequest | null>(
    null
  );
  const [isResponsesDialogOpen, setIsResponsesDialogOpen] = useState(false);
  const [isNewRequestDialogOpen, setIsNewRequestDialogOpen] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch urgent requests from API
  const fetchRequests = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      const response = await fetch("/api/urgent-requests", {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch requests: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setRequests(data.requests || []);
    } catch (error) {
      console.error("Error fetching urgent requests:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch requests"
      );
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    fetchRequests();
  }, [fetchRequests]);

  const handleAddRequest = async (values: any) => {
    try {
      const response = await fetch("/api/urgent-requests", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(values),
      });

      if (!response.ok) {
        throw new Error(`Failed to create request: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Add the new request to the list
      setRequests((prev) => [data.request, ...prev]);
      setIsNewRequestDialogOpen(false);
    } catch (error) {
      console.error("Error creating urgent request:", error);
      setError(
        error instanceof Error ? error.message : "Failed to create request"
      );
    }
  };

  const handleAcceptResponse = (requestId: number, responseId: number) => {
    setRequests((prev) =>
      prev.map((req) =>
        req.id === requestId ? { ...req, status: "accepté" as const } : req
      )
    );
    setIsResponsesDialogOpen(false);
  };

  return (
    <div className="p-6 space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">Demandes urgentes</h1>
          <p className="text-muted-foreground">
            Publiez vos besoins urgents en médicaments
          </p>
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            onClick={() => (window.location.href = "/marketplace")}
          >
            Parcourir le Marché
          </Button>
          <Button onClick={() => setIsNewRequestDialogOpen(true)}>
            <Plus className="w-4 h-4 mr-2" />
            Nouvelle demande
          </Button>
        </div>
      </div>

      {error && (
        <Card className="p-6">
          <div className="flex items-center gap-2 text-red-600">
            <AlertCircle className="w-5 h-5" />
            <span>{error}</span>
          </div>
        </Card>
      )}

      <Card>
        <div className="p-6">
          {loading ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
              <span className="ml-2">Chargement des demandes...</span>
            </div>
          ) : (
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Médicament</TableHead>
                  <TableHead className="text-center">Quantité</TableHead>
                  <TableHead>Urgence</TableHead>
                  <TableHead>Statut</TableHead>
                  <TableHead>Publication</TableHead>
                  <TableHead className="text-right">Réponses</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {requests.map((request) => (
                  <TableRow key={request.id}>
                    <TableCell className="font-medium">
                      <div>
                        <div>{request.medication}</div>
                        {request.notes && (
                          <div className="text-sm text-muted-foreground mt-1">
                            {request.notes}
                          </div>
                        )}
                      </div>
                    </TableCell>
                    <TableCell className="text-center">
                      {request.quantity} unités
                    </TableCell>
                    <TableCell>
                      <Badge className={urgencyStyles[request.urgency]}>
                        {urgencyOptions[request.urgency]}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <Badge className={statusStyles[request.status]}>
                        {request.status}
                      </Badge>
                    </TableCell>
                    <TableCell>
                      <div className="flex items-center gap-2">
                        <Clock className="w-4 h-4 text-muted-foreground" />
                        <span className="text-sm text-muted-foreground">
                          {format(new Date(request.timestamp), "PPp", {
                            locale: fr,
                          })}
                        </span>
                      </div>
                    </TableCell>
                    <TableCell className="text-right">
                      <Button
                        variant={
                          request.responses.length > 0 ? "default" : "outline"
                        }
                        size="sm"
                        onClick={() => {
                          setSelectedRequest(request);
                          setIsResponsesDialogOpen(true);
                        }}
                      >
                        {request.responses.length} réponse
                        {request.responses.length !== 1 ? "s" : ""}
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
                {requests.length === 0 && (
                  <TableRow>
                    <TableCell colSpan={6} className="h-24 text-center">
                      <div className="flex flex-col items-center gap-2">
                        <AlertCircle className="w-8 h-8 text-muted-foreground/50" />
                        <p className="text-muted-foreground">
                          Aucune demande urgente
                        </p>
                      </div>
                    </TableCell>
                  </TableRow>
                )}
              </TableBody>
            </Table>
          )}
        </div>
      </Card>

      <Dialog
        open={isResponsesDialogOpen}
        onOpenChange={setIsResponsesDialogOpen}
      >
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Réponses à votre demande</DialogTitle>
          </DialogHeader>
          {selectedRequest && (
            <div className="space-y-6">
              <div className="flex items-start justify-between">
                <div>
                  <h3 className="font-semibold">
                    {selectedRequest.medication}
                  </h3>
                  <p className="text-sm text-muted-foreground">
                    {selectedRequest.quantity} unités demandées
                  </p>
                </div>
                <Badge className={urgencyStyles[selectedRequest.urgency]}>
                  {urgencyOptions[selectedRequest.urgency]}
                </Badge>
              </div>

              <div className="space-y-4">
                {selectedRequest.responses.length > 0 ? (
                  selectedRequest.responses.map((response) => (
                    <Card key={response.id} className="p-4">
                      <div className="flex items-start justify-between">
                        <div className="space-y-2">
                          <div className="font-medium">
                            {response.pharmacyName}
                          </div>
                          <div className="flex items-center gap-4 text-sm text-muted-foreground">
                            <div className="flex items-center gap-1">
                              <MapPin className="w-4 h-4" />
                              <span>{response.distance}</span>
                            </div>
                            <div className="flex items-center gap-1">
                              <PhoneCall className="w-4 h-4" />
                              <span>{response.contactNumber}</span>
                            </div>
                          </div>
                          <div className="text-sm">
                            Peut fournir: {response.quantity} unités
                          </div>
                        </div>
                        {selectedRequest.status === "en attente" && (
                          <div className="flex gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-red-600"
                            >
                              <XCircle className="w-4 h-4 mr-1" />
                              Refuser
                            </Button>
                            <Button
                              size="sm"
                              className="text-emerald-600"
                              onClick={() =>
                                handleAcceptResponse(
                                  selectedRequest.id,
                                  response.id
                                )
                              }
                            >
                              <CheckCircle className="w-4 h-4 mr-1" />
                              Accepter
                            </Button>
                          </div>
                        )}
                      </div>
                    </Card>
                  ))
                ) : (
                  <div className="text-center py-8 text-muted-foreground">
                    <AlertCircle className="w-8 h-8 mx-auto mb-2" />
                    <p>Aucune réponse pour le moment</p>
                  </div>
                )}
              </div>
            </div>
          )}
          <DialogFooter>
            <Button
              variant="outline"
              onClick={() => setIsResponsesDialogOpen(false)}
            >
              Fermer
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>

      <Dialog
        open={isNewRequestDialogOpen}
        onOpenChange={setIsNewRequestDialogOpen}
      >
        <DialogContent className="max-w-3xl">
          <DialogHeader>
            <DialogTitle>Publier une demande urgente</DialogTitle>
          </DialogHeader>
          <UrgentRequestForm
            onSubmit={handleAddRequest}
            onCancel={() => setIsNewRequestDialogOpen(false)}
          />
        </DialogContent>
      </Dialog>
    </div>
  );
}
