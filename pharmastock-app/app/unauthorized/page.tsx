"use client";

import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/contexts/auth-context";

export default function UnauthorizedPage() {
  const { user } = useAuth();
  const router = useRouter();

  const getErrorMessage = () => {
    if (!user) {
      return "Vous devez être connecté pour accéder à cette page";
    }

    switch (user.role) {
      case "pharmacy":
        return "Votre compte ne dispose pas des autorisations nécessaires";
      case "user":
        return "Vous devez être connecté en tant que pharmacie enregistrée pour accéder à cette section";
      default:
        return "Accès non autorisé";
    }
  };

  return (
    <div className="flex h-screen flex-col items-center justify-center gap-4">
      <h1 className="text-2xl font-bold">Accès restreint</h1>
      <p className="text-muted-foreground">{getErrorMessage()}</p>
      <Button onClick={() => router.back()}>Retour à la page précédente</Button>
    </div>
  );
}
