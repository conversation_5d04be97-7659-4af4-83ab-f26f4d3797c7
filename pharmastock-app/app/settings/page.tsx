"use client";

import React from "react";
import { AppShell } from "@/components/layout/app-shell";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Settings,
  User,
  Building,
  Bell,
  Shield,
  Palette,
  Save,
} from "lucide-react";

function SettingsContent() {
  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[#00B5FF]">Paramètres</h1>
          <p className="text-muted-foreground mt-1">
            Configurez votre compte et vos préférences
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Personnalisez votre expérience PharmaStock
          </p>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5 text-[#00B5FF]" />
              Profil Utilisateur
            </CardTitle>
            <CardDescription>
              Informations personnelles et de contact
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="firstName">Prénom</Label>
                <Input id="firstName" placeholder="Votre prénom" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="lastName">Nom</Label>
                <Input id="lastName" placeholder="Votre nom" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="email">Email</Label>
              <Input id="email" type="email" placeholder="<EMAIL>" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="phone">Téléphone</Label>
              <Input id="phone" type="tel" placeholder="+33 1 23 45 67 89" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="role">Fonction</Label>
              <Select defaultValue="pharmacist">
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="pharmacist">Pharmacien</SelectItem>
                  <SelectItem value="owner">Propriétaire</SelectItem>
                  <SelectItem value="staff">Personnel</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <Button className="w-full bg-[#00B5FF] hover:bg-[#0099CC]">
              <Save className="h-4 w-4 mr-2" />
              Sauvegarder le Profil
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5 text-[#00B5FF]" />
              Informations Pharmacie
            </CardTitle>
            <CardDescription>
              Détails de votre établissement
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="pharmacyName">Nom de la pharmacie</Label>
              <Input id="pharmacyName" placeholder="Pharmacie..." />
            </div>
            <div className="space-y-2">
              <Label htmlFor="address">Adresse</Label>
              <Input id="address" placeholder="123 Rue de la Paix" />
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="city">Ville</Label>
                <Input id="city" placeholder="Paris" />
              </div>
              <div className="space-y-2">
                <Label htmlFor="postalCode">Code postal</Label>
                <Input id="postalCode" placeholder="75001" />
              </div>
            </div>
            <div className="space-y-2">
              <Label htmlFor="siret">SIRET</Label>
              <Input id="siret" placeholder="12345678901234" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="license">Numéro de licence</Label>
              <Input id="license" placeholder="Licence ARS..." />
            </div>
            <Button className="w-full bg-[#00B5FF] hover:bg-[#0099CC]">
              <Save className="h-4 w-4 mr-2" />
              Sauvegarder la Pharmacie
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-[#00B5FF]" />
              Préférences de Notification
            </CardTitle>
            <CardDescription>
              Configurez vos alertes et notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="emailNotifs">Notifications Email</Label>
                <p className="text-sm text-muted-foreground">Recevoir les alertes par email</p>
              </div>
              <Switch id="emailNotifs" defaultChecked />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="smsNotifs">Notifications SMS</Label>
                <p className="text-sm text-muted-foreground">Alertes urgentes par SMS</p>
              </div>
              <Switch id="smsNotifs" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="pushNotifs">Notifications Push</Label>
                <p className="text-sm text-muted-foreground">Notifications dans l'application</p>
              </div>
              <Switch id="pushNotifs" defaultChecked />
            </div>
            <div className="space-y-2">
              <Label>Fréquence des rapports</Label>
              <Select defaultValue="weekly">
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="daily">Quotidien</SelectItem>
                  <SelectItem value="weekly">Hebdomadaire</SelectItem>
                  <SelectItem value="monthly">Mensuel</SelectItem>
                  <SelectItem value="never">Jamais</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5 text-[#00B5FF]" />
              Sécurité et Confidentialité
            </CardTitle>
            <CardDescription>
              Gérez la sécurité de votre compte
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="currentPassword">Mot de passe actuel</Label>
              <Input id="currentPassword" type="password" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="newPassword">Nouveau mot de passe</Label>
              <Input id="newPassword" type="password" />
            </div>
            <div className="space-y-2">
              <Label htmlFor="confirmPassword">Confirmer le mot de passe</Label>
              <Input id="confirmPassword" type="password" />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="twoFactor">Authentification à deux facteurs</Label>
                <p className="text-sm text-muted-foreground">Sécurité renforcée</p>
              </div>
              <Switch id="twoFactor" />
            </div>
            <Button className="w-full bg-[#00B5FF] hover:bg-[#0099CC]">
              <Shield className="h-4 w-4 mr-2" />
              Mettre à jour la Sécurité
            </Button>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Palette className="h-5 w-5 text-[#00B5FF]" />
              Préférences d'Affichage
            </CardTitle>
            <CardDescription>
              Personnalisez l'interface utilisateur
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Thème</Label>
              <Select defaultValue="light">
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="light">Clair</SelectItem>
                  <SelectItem value="dark">Sombre</SelectItem>
                  <SelectItem value="auto">Automatique</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Langue</Label>
              <Select defaultValue="fr">
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="fr">Français</SelectItem>
                  <SelectItem value="en">English</SelectItem>
                  <SelectItem value="es">Español</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <Label>Format de date</Label>
              <Select defaultValue="dd/mm/yyyy">
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="dd/mm/yyyy">DD/MM/YYYY</SelectItem>
                  <SelectItem value="mm/dd/yyyy">MM/DD/YYYY</SelectItem>
                  <SelectItem value="yyyy-mm-dd">YYYY-MM-DD</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="compactMode">Mode compact</Label>
                <p className="text-sm text-muted-foreground">Interface plus dense</p>
              </div>
              <Switch id="compactMode" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-[#00B5FF]" />
              Paramètres Avancés
            </CardTitle>
            <CardDescription>
              Configuration système et données
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label>Sauvegarde automatique</Label>
              <Select defaultValue="daily">
                <SelectTrigger>
                  <SelectValue placeholder="Sélectionner..." />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="hourly">Toutes les heures</SelectItem>
                  <SelectItem value="daily">Quotidienne</SelectItem>
                  <SelectItem value="weekly">Hebdomadaire</SelectItem>
                  <SelectItem value="manual">Manuelle</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <Label htmlFor="analytics">Données d'utilisation</Label>
                <p className="text-sm text-muted-foreground">Aider à améliorer l'application</p>
              </div>
              <Switch id="analytics" defaultChecked />
            </div>
            <div className="space-y-3 pt-4 border-t">
              <Button variant="outline" className="w-full">
                Exporter mes données
              </Button>
              <Button variant="outline" className="w-full">
                Importer des données
              </Button>
              <Button variant="destructive" className="w-full">
                Supprimer mon compte
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function SettingsPage() {
  return (
    <AppShell>
      <SettingsContent />
    </AppShell>
  );
}
