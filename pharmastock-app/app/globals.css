@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --card: 0 0% 100%;
  --card-foreground: 222.2 84% 4.9%;
  --border: 214.3 31.8% 91.4%;
  --input: 214.3 31.8% 91.4%;
  --ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;
}

@layer components {

  /* Layout */
  .container-main {
    @apply max-w-[1280px] mx-auto px-4 py-6;
  }

  /* Headers */
  .page-header {
    @apply flex items-center justify-between mb-6;
  }

  .page-title {
    @apply text-xl font-semibold text-[#24292f];
  }

  /* Search */
  .search-input {
    @apply w-full px-3 py-1.5 text-sm border border-[#d0d7de] rounded-md placeholder-[#6e7781] focus:border-[#0969da] focus:outline-none focus:ring-1 focus:ring-[#0969da];
  }

  /* Filters */
  .filter-section {
    @apply space-y-4 md:space-y-0 md:flex md:flex-wrap md:gap-4 mb-6;
  }

  .filter-group {
    @apply w-full md:w-auto md:flex-1 min-w-[200px];
  }

  .filter-label {
    @apply text-sm font-medium text-[#24292f] mb-2;
  }

  .filter-options {
    @apply flex flex-wrap gap-1.5;
  }

  .filter-button {
    @apply px-2.5 py-1 text-sm bg-[#f6f8fa] text-[#24292f] border border-[#d0d7de] rounded-md hover:bg-[#f3f4f6] transition-colors flex items-center gap-1 whitespace-nowrap;
  }

  .filter-button-selected {
    @apply px-2.5 py-1 text-sm bg-[#0969da] text-white border border-[#0969da] rounded-md hover:bg-[#0969da]/90 transition-colors flex items-center gap-1 font-medium whitespace-nowrap;
  }

  /* Product Cards */
  .product-card {
    @apply bg-white p-4 border border-[#d0d7de] rounded-md hover:shadow-sm transition-shadow cursor-pointer;
  }

  .product-title {
    @apply text-base font-medium text-[#24292f] hover:text-[#0969da] transition-colors;
  }

  .product-subtitle {
    @apply text-sm text-[#57606a];
  }

  .product-meta {
    @apply text-sm text-[#57606a] flex items-center gap-1;
  }

  .product-price {
    @apply text-xl font-semibold text-[#24292f];
  }

  .product-price-original {
    @apply text-sm text-[#57606a] line-through;
  }

  /* Badges */
  .badge-discount {
    @apply inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-full bg-[#dafbe1] text-[#1a7f37];
  }

  .badge-delivery {
    @apply inline-flex items-center px-2 py-0.5 text-xs font-medium rounded-md bg-[#ddf4ff] text-[#0969da] border border-[#54aeff]/30;
  }

  /* Buttons */
  .btn-primary {
    @apply bg-[#2da44e] text-white px-3 py-1.5 text-sm font-medium rounded-md hover:bg-[#2c974b] transition-colors border border-[rgba(31, 35, 40, 0.15)] shadow-sm;
  }

  .btn-secondary {
    @apply bg-[#f6f8fa] text-[#24292f] px-3 py-1.5 text-sm font-medium rounded-md hover:bg-[#f3f4f6] transition-colors border border-[rgba(31, 35, 40, 0.15)] shadow-sm;
  }

  /* Contact Button */
  .btn-contact {
    @apply w-full mt-4 bg-[#f6f8fa] text-[#24292f] px-3 py-1.5 text-sm font-medium rounded-md hover:bg-[#f3f4f6] transition-colors border border-[#d0d7de];
  }

  /* Mobile Optimizations */
  @media (max-width: 768px) {
    .filter-group {
      @apply border-b border-[#d0d7de] pb-4 last:border-b-0 last:pb-0;
    }

    .filter-options {
      @apply flex-wrap;
    }

    .filter-button,
    .filter-button-selected {
      @apply flex-1 justify-center;
    }
  }
}

body {
  @apply bg-white text-[#24292f];
}