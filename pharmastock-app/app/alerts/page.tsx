"use client";

import React, { useState, useEffect } from "react";
import { AppShell } from "@/components/layout/app-shell";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bell,
  Clock,
  AlertTriangle,
  Package,
  Plus,
  Settings,
  Mail,
  Smartphone,
  Eye,
  Trash2,
  Loader,
} from "lucide-react";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

interface Alert {
  id: string;
  type: "expiry" | "stock" | "urgent" | "marketplace" | "system";
  title: string;
  message: string;
  timestamp: string;
  read: boolean;
  priority: "low" | "medium" | "high";
}

function AlertsContent() {
  const [emailNotifications, setEmailNotifications] = useState(true);
  const [smsNotifications, setSmsNotifications] = useState(false);
  const [pushNotifications, setPushNotifications] = useState(true);
  const [alerts, setAlerts] = useState<Alert[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [isNewAlertOpen, setIsNewAlertOpen] = useState(false);
  const [newAlert, setNewAlert] = useState({
    type: "system",
    title: "",
    message: "",
    priority: "medium",
  });
  const [submitting, setSubmitting] = useState(false);

  // Fetch alerts from API
  useEffect(() => {
    async function fetchAlerts() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/alerts");
        const data = await res.json();
        if (!res.ok)
          throw new Error(
            data.error || "Erreur lors du chargement des alertes"
          );
        setAlerts(data.alerts);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchAlerts();
  }, []);

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "destructive";
      case "medium":
        return "default";
      case "low":
        return "secondary";
      default:
        return "secondary";
    }
  };

  const getPriorityText = (priority: string) => {
    switch (priority) {
      case "high":
        return "Urgent";
      case "medium":
        return "Moyen";
      case "low":
        return "Faible";
      default:
        return priority;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "expiry":
        return <Clock className="h-4 w-4" />;
      case "stock":
        return <AlertTriangle className="h-4 w-4" />;
      case "urgent":
        return <Package className="h-4 w-4" />;
      case "marketplace":
        return <Package className="h-4 w-4" />;
      case "system":
        return <Settings className="h-4 w-4" />;
      default:
        return <Bell className="h-4 w-4" />;
    }
  };

  const unreadCount = alerts.filter((alert) => !alert.read).length;

  // Add new alert handler
  const handleNewAlertSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setSubmitting(true);
    try {
      const res = await fetch("/api/alerts", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(newAlert),
      });
      if (!res.ok) throw new Error("Erreur lors de la création de l'alerte");
      setIsNewAlertOpen(false);
      setNewAlert({ type: "system", title: "", message: "", priority: "medium" });
      // Refresh alerts
      const data = await res.json();
      setAlerts((prev) => [data.alert, ...prev]);
    } catch (err: any) {
      setError(err.message || "Erreur lors de la création de l'alerte");
    } finally {
      setSubmitting(false);
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-[#00B5FF]">
            Centre d'Alertes
          </h1>
          <p className="text-muted-foreground mt-1">
            Gérez vos notifications et alertes pharmaceutiques
          </p>
          <p className="text-sm text-muted-foreground mt-1">
            Restez informé des événements importants en temps réel
          </p>
        </div>
        <div className="flex items-center gap-3">
          <Button className="bg-[#00B5FF] hover:bg-[#0099CC]" onClick={() => setIsNewAlertOpen(true)}>
            <Plus className="h-4 w-4 mr-2" />
            Nouvelle Alerte
          </Button>
        </div>
      </div>

      {/* Nouvelle Alerte Modal */}
      <Dialog open={isNewAlertOpen} onOpenChange={setIsNewAlertOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Nouvelle Alerte</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleNewAlertSubmit} className="space-y-4">
            <div className="flex flex-col gap-2">
              <Label htmlFor="alert-type">Type</Label>
              <Select
                value={newAlert.type}
                onValueChange={v => setNewAlert(a => ({ ...a, type: v }))}
              >
                <SelectTrigger id="alert-type">
                  <SelectValue placeholder="Type d'alerte" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="expiry">Expiration</SelectItem>
                  <SelectItem value="stock">Stock</SelectItem>
                  <SelectItem value="urgent">Urgent</SelectItem>
                  <SelectItem value="marketplace">Marketplace</SelectItem>
                  <SelectItem value="system">Système</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="alert-title">Titre</Label>
              <Input
                id="alert-title"
                required
                value={newAlert.title}
                onChange={e => setNewAlert(a => ({ ...a, title: e.target.value }))}
                placeholder="Titre de l'alerte"
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="alert-message">Message</Label>
              <Input
                id="alert-message"
                required
                value={newAlert.message}
                onChange={e => setNewAlert(a => ({ ...a, message: e.target.value }))}
                placeholder="Message de l'alerte"
              />
            </div>
            <div className="flex flex-col gap-2">
              <Label htmlFor="alert-priority">Priorité</Label>
              <Select
                value={newAlert.priority}
                onValueChange={v => setNewAlert(a => ({ ...a, priority: v }))}
              >
                <SelectTrigger id="alert-priority">
                  <SelectValue placeholder="Priorité" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="high">Haute</SelectItem>
                  <SelectItem value="medium">Moyenne</SelectItem>
                  <SelectItem value="low">Faible</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <DialogFooter>
              <Button type="submit" className="w-full" disabled={submitting}>
                {submitting ? "Création..." : "Créer l'Alerte"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Alertes Non Lues
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-red-500" />
              <span className="text-2xl font-bold text-red-600">
                {unreadCount}
              </span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Nécessitent attention
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Alertes Urgentes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <AlertTriangle className="h-5 w-5 text-orange-500" />
              <span className="text-2xl font-bold text-orange-600">1</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">Priorité haute</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Alertes Aujourd'hui
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-blue-500" />
              <span className="text-2xl font-bold">2</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">
              Reçues aujourd'hui
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <CardTitle className="text-sm font-medium text-muted-foreground">
              Alertes Totales
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-2">
              <Package className="h-5 w-5 text-purple-500" />
              <span className="text-2xl font-bold">4</span>
            </div>
            <p className="text-xs text-muted-foreground mt-1">Cette semaine</p>
          </CardContent>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Settings className="h-5 w-5 text-[#00B5FF]" />
              Paramètres de Notification
            </CardTitle>
            <CardDescription>
              Configurez vos préférences de notification
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Mail className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <Label htmlFor="email-notifications">
                      Notifications Email
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Recevoir les alertes par email
                    </p>
                  </div>
                </div>
                <Switch
                  id="email-notifications"
                  checked={emailNotifications}
                  onCheckedChange={setEmailNotifications}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Smartphone className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <Label htmlFor="sms-notifications">Notifications SMS</Label>
                    <p className="text-sm text-muted-foreground">
                      Recevoir les alertes urgentes par SMS
                    </p>
                  </div>
                </div>
                <Switch
                  id="sms-notifications"
                  checked={smsNotifications}
                  onCheckedChange={setSmsNotifications}
                />
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Bell className="h-5 w-5 text-muted-foreground" />
                  <div>
                    <Label htmlFor="push-notifications">
                      Notifications Push
                    </Label>
                    <p className="text-sm text-muted-foreground">
                      Recevoir les notifications dans l'application
                    </p>
                  </div>
                </div>
                <Switch
                  id="push-notifications"
                  checked={pushNotifications}
                  onCheckedChange={setPushNotifications}
                />
              </div>
            </div>

            <div className="space-y-4 pt-4 border-t">
              <div className="space-y-2">
                <Label>Seuil d'alerte expiration</Label>
                <Select defaultValue="15">
                  <SelectTrigger>
                    <SelectValue placeholder="Sélectionner..." />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="7">7 jours</SelectItem>
                    <SelectItem value="15">15 jours</SelectItem>
                    <SelectItem value="30">30 jours</SelectItem>
                    <SelectItem value="60">60 jours</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              <div className="space-y-2">
                <Label>Seuil stock faible</Label>
                <Input
                  type="number"
                  placeholder="Quantité minimale"
                  defaultValue="5"
                />
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Bell className="h-5 w-5 text-[#00B5FF]" />
              Alertes Récentes
            </CardTitle>
            <CardDescription>Vos dernières notifications</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {loading ? (
                <div className="flex items-center justify-center h-32">
                  <Loader className="h-6 w-6 animate-spin" />
                </div>
              ) : error ? (
                <div className="text-center text-red-500 p-4">
                  Erreur: {error}
                </div>
              ) : alerts.length === 0 ? (
                <div className="text-center text-muted-foreground p-4">
                  Aucune alerte disponible
                </div>
              ) : (
                alerts.map((alert) => (
                  <div
                    key={alert.id}
                    className={`flex items-start gap-3 p-3 border rounded-lg ${
                      !alert.read ? "bg-blue-50 border-blue-200" : ""
                    }`}
                  >
                    <div className="p-2 bg-gray-100 rounded-full">
                      {getTypeIcon(alert.type)}
                    </div>
                    <div className="flex-1">
                      <div className="flex items-start justify-between">
                        <div>
                          <h4 className="font-medium">{alert.title}</h4>
                          <p className="text-sm text-muted-foreground">
                            {alert.message}
                          </p>
                          <p className="text-xs text-muted-foreground mt-1">
                            {new Date(alert.timestamp).toLocaleDateString(
                              "fr-FR"
                            )}{" "}
                            à{" "}
                            {new Date(alert.timestamp).toLocaleTimeString(
                              "fr-FR",
                              {
                                hour: "2-digit",
                                minute: "2-digit",
                              }
                            )}
                          </p>
                        </div>
                        <div className="flex items-center gap-2">
                          <Badge
                            variant={getPriorityColor(alert.priority)}
                            className="text-xs"
                          >
                            {getPriorityText(alert.priority)}
                          </Badge>
                          {!alert.read && (
                            <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2 mt-2">
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3 mr-1" />
                          Voir
                        </Button>
                        <Button size="sm" variant="ghost">
                          <Trash2 className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}

export default function AlertsPage() {
  return (
    <AppShell>
      <AlertsContent />
    </AppShell>
  );
}
