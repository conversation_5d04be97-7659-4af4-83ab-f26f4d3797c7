"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/auth-context";
import { useFreemium } from "@/src/hooks/useFreemium";
import { Logo } from "@/components/ui/logo";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { Loader2, Upload, Copy, Camera, Sparkles, Crown, Lock } from "lucide-react";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogFooter } from "@/components/ui/dialog";

export default function PrescriptionReaderPage() {
  const { user } = useAuth();
  const router = useRouter();
  const { isProfessionalTier, isEnterpriseTier } = useFreemium();
  const [image, setImage] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);
  const [text, setText] = useState("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState("");
  const [showProModal, setShowProModal] = useState(false);
  // Registration modal state
  const [showEmailModal, setShowEmailModal] = useState(false);
  const [showPharmacyModal, setShowPharmacyModal] = useState(false);
  const [email, setEmail] = useState("");
  const [pharmacyName, setPharmacyName] = useState("");
  const [pharmacyCity, setPharmacyCity] = useState("");
  const [registering, setRegistering] = useState(false);
  const [usageCount, setUsageCount] = useState(0);
  const [usageLimitReached, setUsageLimitReached] = useState(false);

  // Track usage for anonymous users (localStorage)
  useEffect(() => {
    if (user) return; // Only for anonymous
    const now = new Date();
    const monthKey = `${now.getFullYear()}-${now.getMonth() + 1}`;
    const usageData = JSON.parse(localStorage.getItem("prescription_reader_usage") || '{}');
    if (usageData[monthKey]) {
      setUsageCount(usageData[monthKey]);
      if (usageData[monthKey] >= 10) setUsageLimitReached(true);
    }
  }, [user]);

  // Helper to update usage in localStorage
  const incrementUsage = () => {
    const now = new Date();
    const monthKey = `${now.getFullYear()}-${now.getMonth() + 1}`;
    const usageData = JSON.parse(localStorage.getItem("prescription_reader_usage") || '{}');
    usageData[monthKey] = (usageData[monthKey] || 0) + 1;
    localStorage.setItem("prescription_reader_usage", JSON.stringify(usageData));
    setUsageCount(usageData[monthKey]);
    if (usageData[monthKey] >= 10) setUsageLimitReached(true);
  };

  // Check if user has Pro subscription
  const hasProSubscription = isProfessionalTier || isEnterpriseTier;

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setImageFile(file);
      setImage(URL.createObjectURL(file));
      setText("");
      setError("");
    }
  };

  const convertFileToBase64 = (file: File): Promise<string> => {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        // Remove the data:image/jpeg;base64, prefix
        const base64Data = result.split(',')[1];
        resolve(base64Data);
      };
      reader.onerror = () => reject(new Error('Failed to read file'));
      reader.readAsDataURL(file);
    });
  };

  const handleAnalyze = async () => {
    if (!image || !imageFile) return;
    setLoading(true);
    setError("");
    setText("");

    // Anonymous user: enforce registration and usage limit
    if (!user) {
      if (usageCount === 0) {
        setShowEmailModal(true);
        setLoading(false);
        return;
      }
      if (usageCount >= 10) {
        setUsageLimitReached(true);
        setLoading(false);
        return;
      }
      incrementUsage();
    }

    try {
      if (hasProSubscription || user) {
        // Use AI Vision API for prescription analysis
        console.log("Analyzing prescription with AI Vision...");

        // Convert file to base64
        const base64Data = await convertFileToBase64(imageFile);

        const response = await fetch("/api/analyze-prescription", {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            imageData: base64Data
          }),
        });

        if (response.ok) {
          const result = await response.json();

          // Format the analysis result properly
          if (result.analysis) {
            let formattedText = "";

            // Add analysis timestamp
            if (result.timestamp) {
              formattedText += `⏰ Analysé par PharmaStock le: ${new Date(result.timestamp).toLocaleString('fr-FR')}\n\n`;
            }

            // Add patient info
            if (result.analysis.patient_info) {
              formattedText += "👤 INFORMATIONS PATIENT:\n";
              if (result.analysis.patient_info.name) {
                formattedText += `   Nom: ${result.analysis.patient_info.name}\n`;
              }
              if (result.analysis.patient_info.age) {
                formattedText += `   Âge: ${result.analysis.patient_info.age} ans\n`;
              }
              formattedText += "\n";
            }

            // Add doctor/clinic info
            if (result.analysis.doctor_info) {
              formattedText += "🏥 INFORMATIONS MÉDECIN/CLINIQUE:\n";
              if (result.analysis.doctor_info.name) {
                formattedText += `   Médecin: ${result.analysis.doctor_info.name}\n`;
              }
              if (result.analysis.doctor_info.clinic) {
                formattedText += `   Clinique: ${result.analysis.doctor_info.clinic}\n`;
              }
              formattedText += "\n";
            }

            // Add date
            if (result.analysis.date) {
              formattedText += `📅 Date de prescription: ${result.analysis.date}\n\n`;
            }

            // Add medications
            if (result.analysis.medications && result.analysis.medications.length > 0) {
              formattedText += "💊 MÉDICAMENTS PRESCRITS:\n\n";
              result.analysis.medications.forEach((med: any, index: number) => {
                formattedText += `${index + 1}. ${med.name || 'Médicament non spécifié'}\n`;
                if (med.dosage) {
                  formattedText += `   💊 Dosage: ${med.dosage}\n`;
                }
                if (med.frequency) {
                  formattedText += `   ⏰ Fréquence: ${med.frequency}\n`;
                }
                if (med.duration) {
                  formattedText += `   📅 Durée: ${med.duration}\n`;
                }
                if (med.instructions) {
                  formattedText += `   📝 Instructions: ${med.instructions}\n`;
                }
                formattedText += "\n";
              });
            }

            // Add confidence level
            if (result.analysis.confidence) {
              const confidenceEmoji = result.analysis.confidence === 'high' ? '🟢' :
                                    result.analysis.confidence === 'medium' ? '🟡' : '🔴';
              formattedText += `${confidenceEmoji} Niveau de confiance: ${result.analysis.confidence}\n\n`;
            }

            // Add disclaimer
            if (result.disclaimer) {
              formattedText += `⚠️ ${result.disclaimer}`;
            }

            setText(formattedText);
          } else {
            setText("Aucune analyse disponible.");
          }
        } else {
          const errorData = await response.json();
          setError(errorData.error || "Erreur lors de l'analyse de l'ordonnance.");
        }
      } else {
        // Free users get a message to upgrade
        setError("L'analyse d'ordonnance nécessite un abonnement PRO. Veuillez vous abonner pour accéder à cette fonctionnalité.");
      }

    } catch (err) {
      console.error("Analysis Error:", err);
      setError("Impossible d'analyser l'ordonnance. Veuillez réessayer.");
    }
    setLoading(false);
  };

  // Registration logic
  const handleEmailSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setShowEmailModal(false);
    setShowPharmacyModal(true);
  };

  const handlePharmacySubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setRegistering(true);
    // Generate a random password
    const password = Math.random().toString(36).slice(-10) + "!A1";
    try {
      const res = await fetch("/api/auth/simple-register", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const data = await res.json();
      if (!res.ok) throw new Error(data.error || "Registration failed");
      // Auto-login
      const loginRes = await fetch("/api/auth/login", {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ email, password }),
      });
      const loginData = await loginRes.json();
      if (!loginRes.ok) throw new Error(loginData.error || "Login failed");
      // Optionally: update profile with pharmacy info (if supported)
      // await fetch('/api/profile/update', { ... })
      setShowPharmacyModal(false);
      window.location.reload(); // Refresh to update auth context
    } catch (err: any) {
      setError(err.message || "Registration error");
      setShowPharmacyModal(false);
    } finally {
      setRegistering(false);
    }
  };

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 to-blue-50">
      {/* Header */}
      <div className="bg-white shadow-sm border-b">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-4">
            <div className="flex items-center space-x-3">
              <Logo size={40} />
              <div>
                <h1 className="text-xl font-bold text-gray-900">PharmaStock</h1>
                <p className="text-sm text-gray-500">Lecteur d'Ordonnances IA</p>
              </div>
            </div>
            <div className="flex items-center space-x-4">
              {user ? (
                <Link href="/dashboard">
                  <Button variant="outline" size="sm">
                    Retour au Dashboard
                  </Button>
                </Link>
              ) : (
                <Link href="/auth/login">
                  <Button variant="outline" size="sm">
                    Se connecter
                  </Button>
                </Link>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="text-center mb-8">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">
            🔍 Lecteur d'Ordonnances Intelligent
          </h2>
          <p className="text-lg text-gray-600 max-w-2xl mx-auto">
            Téléchargez ou prenez une photo d'une ordonnance manuscrite.
            Notre IA avancée extrait automatiquement le texte pour vous.
          </p>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Upload Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Camera className="w-5 h-5" />
                <span>Télécharger l'Ordonnance</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {/* Subscription Status */}
              <div className={`flex items-center justify-between p-3 rounded-lg border ${
                hasProSubscription
                  ? 'bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200'
                  : 'bg-gradient-to-r from-amber-50 to-orange-50 border-amber-200'
              }`}>
                <div className="flex items-center space-x-2">
                  {hasProSubscription ? (
                    <Sparkles className="w-4 h-4 text-purple-600" />
                  ) : (
                    <Crown className="w-4 h-4 text-amber-600" />
                  )}
                  <span className="text-sm font-medium">
                    {hasProSubscription ? 'Analyse IA Avancée' : 'Analyse IA'}
                    {!hasProSubscription && (
                      <span className="ml-1 text-xs bg-amber-100 text-amber-800 px-2 py-0.5 rounded-full">
                        PRO
                      </span>
                    )}
                  </span>
                </div>
                <div className="flex items-center space-x-2">
                  {hasProSubscription ? (
                    <span className="text-xs text-purple-600 font-medium">Activé</span>
                  ) : (
                    <span className="text-xs text-amber-600 font-medium">Requis</span>
                  )}
                </div>
              </div>

              {!hasProSubscription && (
                <div className="text-xs text-amber-700 bg-amber-50 p-2 rounded border border-amber-200">
                  <Lock className="w-3 h-3 inline mr-1" />
                  L'analyse d'ordonnance avancée nécessite un abonnement Pro
                </div>
              )}

              {/* File Upload */}
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-purple-400 transition-colors">
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleImageChange}
                  className="hidden"
                  id="file-upload"
                />
                <label htmlFor="file-upload" className="cursor-pointer">
                  <Upload className="w-12 h-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-sm text-gray-600 mb-2">
                    <span className="font-semibold text-purple-600">Cliquez pour télécharger</span> ou glissez-déposez
                  </p>
                  <p className="text-xs text-gray-500">PNG, JPG, JPEG (max. 10MB)</p>
                </label>
              </div>

              {/* Image Preview */}
              {image && (
                <div className="space-y-4">
                  <img
                    src={image}
                    alt="Prescription"
                    className="w-full rounded-lg shadow-md max-h-64 object-contain bg-white"
                  />
                  <Button
                    onClick={handleAnalyze}
                    disabled={loading}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    size="lg"
                  >
                    {loading ? (
                      <>
                        <Loader2 className="w-4 h-4 mr-2 animate-spin" />
                        Analyse en cours...
                      </>
                    ) : (
                      <>
                        <Sparkles className="w-4 h-4 mr-2" />
                        {hasProSubscription ? "Analyser avec IA Avancée" : "Analyser l'Ordonnance"}
                      </>
                    )}
                  </Button>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Results Section */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center space-x-2">
                <Copy className="w-5 h-5" />
                <span>Texte Extrait</span>
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4 bg-white">
              {text ? (
                <div className="space-y-4">
                  <textarea
                    value={text}
                    onChange={(e) => setText(e.target.value)}
                    rows={12}
                    className="w-full p-3 border border-gray-300 rounded-lg focus:ring-2 focus:ring-purple-500 focus:border-transparent resize-none bg-white"
                    placeholder="Le texte extrait apparaîtra ici..."
                  />
                  <Button
                    onClick={() => navigator.clipboard.writeText(text)}
                    variant="outline"
                    className="w-full"
                  >
                    <Copy className="w-4 h-4 mr-2" />
                    Copier dans le Presse-papiers
                  </Button>
                </div>
              ) : (
                <div className="text-center py-12 text-gray-500">
                  <Camera className="w-16 h-16 mx-auto mb-4 text-gray-300" />
                  <p>Téléchargez une ordonnance pour commencer l'analyse</p>
                </div>
              )}

              {error && (
                <Alert variant="destructive">
                  <AlertDescription>{error}</AlertDescription>
                </Alert>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Footer */}
        <div className="mt-12 text-center">
          <Alert className="max-w-2xl mx-auto">
            <AlertDescription className="text-sm">
              <strong>Avertissement:</strong> Cet outil est destiné à l'assistance uniquement.
              Vérifiez toujours avec l'ordonnance originale. Le traitement est effectué localement
              dans votre navigateur pour garantir la confidentialité.
            </AlertDescription>
          </Alert>
        </div>
      </div>

      {/* Pro Upgrade Modal */}
      {showProModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-4">
          <div className="bg-white rounded-2xl max-w-md w-full p-6 relative">
            <button
              onClick={() => setShowProModal(false)}
              className="absolute top-4 right-4 text-gray-400 hover:text-gray-600"
            >
              ✕
            </button>

            <div className="text-center">
              <div className="w-16 h-16 bg-gradient-to-r from-purple-500 to-blue-500 rounded-full flex items-center justify-center mx-auto mb-4">
                <Crown className="w-8 h-8 text-white" />
              </div>

              <h3 className="text-xl font-bold text-gray-900 mb-2">
                🤖 Débloquez l'IA Avancée
              </h3>

              <p className="text-gray-600 mb-6">
                Analysez vos ordonnances avec notre IA avancée pour
                une compréhension précise et détaillée du contenu médical.
              </p>

              <div className="bg-gradient-to-r from-purple-50 to-blue-50 rounded-lg p-4 mb-6">
                <h4 className="font-semibold text-gray-900 mb-2">Fonctionnalités Pro :</h4>
                <ul className="text-sm text-gray-700 space-y-1">
                  <li>🤖 IA Vision avancée pour analyse complète</li>
                  <li>🎯 Compréhension contextuelle avancée</li>
                  <li>📋 Extraction structurée des informations</li>
                  <li>🔍 Identification des médicaments et posologies</li>
                  <li>🏥 Support multilingue (français/arabe)</li>
                </ul>
              </div>

              <div className="space-y-3">
                {user ? (
                  <Button
                    onClick={() => router.push('/pricing')}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    size="lg"
                  >
                    <Crown className="w-4 h-4 mr-2" />
                    Passer à Pro - 299 DH/mois
                  </Button>
                ) : (
                  <Button
                    onClick={() => router.push('/auth/register')}
                    className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700"
                    size="lg"
                  >
                    Créer un Compte Pro
                  </Button>
                )}

                <Button
                  variant="outline"
                  onClick={() => setShowProModal(false)}
                  className="w-full"
                >
                  Fermer
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Registration Step 1: Email */}
      <Dialog open={showEmailModal} onOpenChange={setShowEmailModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Créer un compte pour continuer</DialogTitle>
          </DialogHeader>
          <form onSubmit={handleEmailSubmit} className="space-y-4">
            <input
              type="email"
              required
              placeholder="Votre email professionnel"
              className="w-full p-3 border border-gray-300 rounded-lg"
              value={email}
              onChange={e => setEmail(e.target.value)}
              autoFocus
            />
            <DialogFooter>
              <Button type="submit" className="w-full">Continuer</Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      {/* Registration Step 2: Pharmacy Info */}
      <Dialog open={showPharmacyModal} onOpenChange={setShowPharmacyModal}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Informations sur la pharmacie</DialogTitle>
          </DialogHeader>
          <form onSubmit={handlePharmacySubmit} className="space-y-4">
            <input
              type="text"
              required
              placeholder="Nom de la pharmacie"
              className="w-full p-3 border border-gray-300 rounded-lg"
              value={pharmacyName}
              onChange={e => setPharmacyName(e.target.value)}
            />
            <input
              type="text"
              required
              placeholder="Ville"
              className="w-full p-3 border border-gray-300 rounded-lg"
              value={pharmacyCity}
              onChange={e => setPharmacyCity(e.target.value)}
            />
            <DialogFooter>
              <Button type="submit" className="w-full" disabled={registering}>
                {registering ? "Création du compte..." : "Créer mon compte"}
              </Button>
            </DialogFooter>
          </form>
        </DialogContent>
      </Dialog>
      {/* Usage Limit Modal */}
      <Dialog open={usageLimitReached} onOpenChange={setUsageLimitReached}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Limite atteinte</DialogTitle>
          </DialogHeader>
          <p className="mb-4">Vous avez atteint la limite de 10 analyses gratuites ce mois-ci. Passez à un compte PRO pour continuer à utiliser l'analyse d'ordonnance sans limite.</p>
          <DialogFooter>
            <Button onClick={() => router.push('/pricing')} className="w-full bg-gradient-to-r from-purple-600 to-blue-600 hover:from-purple-700 hover:to-blue-700">
              Passer à PRO
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </div>
  );
} 