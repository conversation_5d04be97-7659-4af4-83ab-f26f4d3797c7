"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { useAuth } from "@/contexts/auth-context";
import { Loader, Box, Text, Stack } from "@mantine/core";

export default function Home() {
  const router = useRouter();
  const { user, loading } = useAuth();

  useEffect(() => {
    if (!loading) {
      if (user) {
        // Only super_admin goes to admin dashboard; all others go to regular dashboard
        switch (user.role) {
          case "super_admin":
            router.replace("/admin/dashboard");
            break;
          case "owner":
          case "pharmacist":
          case "admin":
          case "staff":
          default:
            router.replace("/dashboard");
            break;
        }
      } else {
        router.replace("/auth/login");
      }
    }
  }, [user, loading, router]);

  return (
    <Box
      style={{
        minHeight: "100vh",
        background:
          "linear-gradient(135deg, #00B5FF 0%, #0099E6 50%, #007ACC 100%)",
        display: "flex",
        alignItems: "center",
        justifyContent: "center",
      }}
    >
      <Stack align="center" gap="md">
        <Loader size="lg" color="white" />
        <Text c="white" size="sm">
          Redirection en cours...
        </Text>
      </Stack>
    </Box>
  );
}
