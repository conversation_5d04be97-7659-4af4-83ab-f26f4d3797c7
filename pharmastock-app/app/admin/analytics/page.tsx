"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { adminMockServices } from "@/services/mock/admin";
import { Card } from "@/components/ui/card";
import { redirect } from "next/navigation";
import {
  Bar<PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  LineChart,
  Line,
} from "recharts";

export default function AnalyticsPage() {
  const { user, loading } = useAuth();
  const [analyticsData, setAnalyticsData] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [overview, trends, geographic, product, user] = await Promise.all([
          adminMockServices.analytics.getOverview(),
          adminMockServices.analytics.getTrends(),
          adminMockServices.analytics.getGeographicData(),
          adminMockServices.analytics.getProductAnalytics(),
          adminMockServices.analytics.getUserAnalytics(),
        ]);

        setAnalyticsData({
          overview,
          trends,
          geographic,
          product,
          user,
        });
      } catch (error) {
        console.error("Error fetching analytics:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Show loading state while checking session
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  // Only redirect if we're sure the user is not a super_admin
  if (!loading && user?.role !== "super_admin") {
    redirect("/unauthorized");
  }

  if (isLoading || !analyticsData) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading analytics...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">Analytics Dashboard</h1>

      {/* Overview Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Total Transactions</h3>
          <p className="text-3xl font-bold">{analyticsData.overview.total_transactions}</p>
        </Card>
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Total Volume</h3>
          <p className="text-3xl font-bold">{analyticsData.overview.total_volume} DH</p>
        </Card>
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Average Order Value</h3>
          <p className="text-3xl font-bold">{analyticsData.overview.average_order_value} DH</p>
        </Card>
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Active Pharmacies</h3>
          <p className="text-3xl font-bold">{analyticsData.overview.active_pharmacies}</p>
        </Card>
      </div>

      {/* User Activity Chart */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">User Activity</h2>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <LineChart
              data={analyticsData.trends.daily_active_users.map((value: number, index: number) => ({
                day: index + 1,
                users: value,
              }))}
              margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
            >
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="day" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Line type="monotone" dataKey="users" stroke="#8884d8" name="Daily Active Users" />
            </LineChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Product Categories Chart */}
      <Card className="p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Product Categories</h2>
        <div className="h-[400px]">
          <ResponsiveContainer width="100%" height="100%">
            <BarChart data={analyticsData.product.top_categories}>
              <CartesianGrid strokeDasharray="3 3" />
              <XAxis dataKey="name" />
              <YAxis />
              <Tooltip />
              <Legend />
              <Bar dataKey="transaction_count" fill="#8884d8" name="Transactions" />
              <Bar dataKey="total_volume" fill="#82ca9d" name="Volume (DH)" />
            </BarChart>
          </ResponsiveContainer>
        </div>
      </Card>

      {/* Geographic Distribution */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Pharmacies by City</h2>
          <div className="space-y-4">
            {Object.entries(analyticsData.geographic.pharmacies_by_city).map(([city, count]: [string, any]) => (
              <div key={city} className="flex justify-between items-center">
                <span>{city}</span>
                <span className="font-semibold">{count}</span>
              </div>
            ))}
          </div>
        </Card>

        <Card className="p-6">
          <h2 className="text-xl font-semibold mb-4">Transactions by Region</h2>
          <div className="space-y-4">
            {Object.entries(analyticsData.geographic.transactions_by_region).map(
              ([region, count]: [string, any]) => (
                <div key={region} className="flex justify-between items-center">
                  <span>{region}</span>
                  <span className="font-semibold">{count}</span>
                </div>
              )
            )}
          </div>
        </Card>
      </div>
    </div>
  );
}