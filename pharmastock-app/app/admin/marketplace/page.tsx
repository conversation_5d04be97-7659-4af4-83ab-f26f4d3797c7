"use client";

import { useState, useEffect } from 'react';
import { useSupabase } from '@/contexts/supabase-context';
import { 
  Title, 
  Card, 
  Table, 
  Group, 
  Text, 
  Badge, 
  TextInput, 
  Select, 
  Button,
  Stack,
  ActionIcon,
  Tooltip,
  Paper,
  SimpleGrid,
  ThemeIcon,
  Modal,
  Textarea
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import { 
  IconSearch, 
  IconEye,
  IconFlag,
  IconCheck,
  IconX,
  IconShoppingCart,
  IconAlertTriangle,
  IconClock,
  IconTrendingUp
} from '@tabler/icons-react';

interface MarketplaceListing {
  id: string;
  product_name: string;
  pharmacy_name: string;
  price: number;
  quantity: number;
  status: 'active' | 'inactive' | 'flagged' | 'expired';
  created_at: string;
  expiry_date: string;
  reports_count: number;
  views_count: number;
}

interface MarketplaceStats {
  totalListings: number;
  activeListings: number;
  flaggedListings: number;
  totalViews: number;
}

export default function AdminMarketplacePage() {
  const { supabase } = useSupabase();
  const [listings, setListings] = useState<MarketplaceListing[]>([]);
  const [stats, setStats] = useState<MarketplaceStats>({
    totalListings: 0,
    activeListings: 0,
    flaggedListings: 0,
    totalViews: 0
  });
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [selectedListing, setSelectedListing] = useState<MarketplaceListing | null>(null);
  const [moderationReason, setModerationReason] = useState('');
  const [moderationOpened, { open: openModeration, close: closeModeration }] = useDisclosure(false);

  useEffect(() => {
    fetchListings();
    fetchStats();
  }, [searchTerm, statusFilter]);

  const fetchListings = async () => {
    try {
      setLoading(true);
      
      // Mock data - replace with real marketplace listings query
      const mockListings: MarketplaceListing[] = [
        {
          id: '1',
          product_name: 'Paracetamol 500mg',
          pharmacy_name: 'Pharmacie Central',
          price: 12.50,
          quantity: 100,
          status: 'active',
          created_at: new Date().toISOString(),
          expiry_date: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
          reports_count: 0,
          views_count: 45
        },
        {
          id: '2',
          product_name: 'Ibuprofen 400mg',
          pharmacy_name: 'Pharmacie du Marché',
          price: 8.99,
          quantity: 50,
          status: 'flagged',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          expiry_date: new Date(Date.now() + 15 * 24 * 60 * 60 * 1000).toISOString(),
          reports_count: 3,
          views_count: 23
        }
      ];

      setListings(mockListings);
    } catch (error) {
      console.error('Error fetching listings:', error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      setStats({
        totalListings: 1247,
        activeListings: 1180,
        flaggedListings: 12,
        totalViews: 45678
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'gray';
      case 'flagged': return 'red';
      case 'expired': return 'orange';
      default: return 'blue';
    }
  };

  const handleModeration = (listing: MarketplaceListing, action: 'approve' | 'reject') => {
    setSelectedListing(listing);
    openModeration();
  };

  const submitModeration = async () => {
    if (!selectedListing) return;

    try {
      // Here you would update the listing status in the database
      console.log('Moderating listing:', selectedListing.id, 'Reason:', moderationReason);
      
      // Refresh listings
      await fetchListings();
      
      closeModeration();
      setModerationReason('');
      setSelectedListing(null);
    } catch (error) {
      console.error('Error moderating listing:', error);
    }
  };

  const filteredListings = listings.filter(listing => {
    const matchesSearch = 
      listing.product_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      listing.pharmacy_name.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesStatus = statusFilter === 'all' || listing.status === statusFilter;
    
    return matchesSearch && matchesStatus;
  });

  return (
    <Stack gap="xl">
      <Group justify="space-between">
        <div>
          <Title order={1}>Marketplace Oversight</Title>
          <Text c="dimmed" mt="xs">
            Monitor and moderate marketplace listings
          </Text>
        </div>
      </Group>

      {/* Stats Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Total Listings
              </Text>
              <Text fw={700} fz="xl">
                {stats.totalListings.toLocaleString()}
              </Text>
            </div>
            <ThemeIcon color="blue" variant="light" size={38} radius="md">
              <IconShoppingCart size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Active Listings
              </Text>
              <Text fw={700} fz="xl">
                {stats.activeListings.toLocaleString()}
              </Text>
            </div>
            <ThemeIcon color="green" variant="light" size={38} radius="md">
              <IconCheck size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Flagged Listings
              </Text>
              <Text fw={700} fz="xl">
                {stats.flaggedListings}
              </Text>
            </div>
            <ThemeIcon color="red" variant="light" size={38} radius="md">
              <IconAlertTriangle size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Total Views
              </Text>
              <Text fw={700} fz="xl">
                {stats.totalViews.toLocaleString()}
              </Text>
            </div>
            <ThemeIcon color="violet" variant="light" size={38} radius="md">
              <IconTrendingUp size={18} />
            </ThemeIcon>
          </Group>
        </Paper>
      </SimpleGrid>

      {/* Filters */}
      <Card withBorder>
        <Group>
          <TextInput
            placeholder="Search listings..."
            leftSection={<IconSearch size={16} />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Status"
            data={[
              { value: 'all', label: 'All Status' },
              { value: 'active', label: 'Active' },
              { value: 'inactive', label: 'Inactive' },
              { value: 'flagged', label: 'Flagged' },
              { value: 'expired', label: 'Expired' }
            ]}
            value={statusFilter}
            onChange={(value) => setStatusFilter(value || 'all')}
          />
        </Group>
      </Card>

      {/* Listings Table */}
      <Card withBorder>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Product</Table.Th>
              <Table.Th>Pharmacy</Table.Th>
              <Table.Th>Price</Table.Th>
              <Table.Th>Quantity</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Reports</Table.Th>
              <Table.Th>Views</Table.Th>
              <Table.Th>Expiry</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredListings.map((listing) => (
              <Table.Tr key={listing.id}>
                <Table.Td>
                  <Text fw={500}>{listing.product_name}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{listing.pharmacy_name}</Text>
                </Table.Td>
                <Table.Td>
                  <Text fw={600}>€{listing.price.toFixed(2)}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{listing.quantity}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge color={getStatusColor(listing.status)} variant="light">
                    {listing.status}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Text size="sm">{listing.reports_count}</Text>
                    {listing.reports_count > 0 && (
                      <IconFlag size={14} color="red" />
                    )}
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{listing.views_count}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">
                    {new Date(listing.expiry_date).toLocaleDateString()}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Tooltip label="View Details">
                      <ActionIcon variant="subtle" color="blue">
                        <IconEye size={16} />
                      </ActionIcon>
                    </Tooltip>
                    {listing.status === 'flagged' && (
                      <>
                        <Tooltip label="Approve">
                          <ActionIcon 
                            variant="subtle" 
                            color="green"
                            onClick={() => handleModeration(listing, 'approve')}
                          >
                            <IconCheck size={16} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Reject">
                          <ActionIcon 
                            variant="subtle" 
                            color="red"
                            onClick={() => handleModeration(listing, 'reject')}
                          >
                            <IconX size={16} />
                          </ActionIcon>
                        </Tooltip>
                      </>
                    )}
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>
      </Card>

      {/* Moderation Modal */}
      <Modal
        opened={moderationOpened}
        onClose={closeModeration}
        title="Moderate Listing"
        centered
      >
        <Stack>
          <Text size="sm">
            <strong>Product:</strong> {selectedListing?.product_name}
          </Text>
          <Text size="sm">
            <strong>Pharmacy:</strong> {selectedListing?.pharmacy_name}
          </Text>
          <Text size="sm">
            <strong>Reports:</strong> {selectedListing?.reports_count}
          </Text>
          
          <Textarea
            label="Moderation Reason"
            placeholder="Enter reason for moderation action..."
            value={moderationReason}
            onChange={(e) => setModerationReason(e.currentTarget.value)}
            minRows={3}
          />
          
          <Group justify="flex-end">
            <Button variant="light" onClick={closeModeration}>
              Cancel
            </Button>
            <Button onClick={submitModeration}>
              Submit
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
