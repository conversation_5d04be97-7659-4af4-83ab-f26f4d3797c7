"use client";

import { useState, useEffect } from "react";
import { useSupabase } from "@/contexts/supabase-context";
import { useAuth } from "@/contexts/auth-context";
import { redirect } from "next/navigation";
import {
  Title,
  Card,
  Table,
  Group,
  Text,
  Badge,
  TextInput,
  Select,
  Button,
  Stack,
  Pagination,
  Paper,
  SimpleGrid,
  ThemeIcon,
  Code,
  Collapse,
  ActionIcon,
} from "@mantine/core";
import { useDisclosure } from "@mantine/hooks";
import {
  IconSearch,
  IconDownload,
  IconEye,
  IconChevronDown,
  IconChevronRight,
  IconShield,
  IconUser,
  IconDatabase,
  IconActivity,
} from "@tabler/icons-react";
// import { DatePickerInput } from '@mantine/dates';

interface AuditLog {
  id: string;
  action: string;
  resource_type: string;
  resource_id: string;
  user_id: string;
  user_email: string;
  user_role: string;
  ip_address: string;
  user_agent: string;
  details: Record<string, any>;
  timestamp: string;
  severity: "low" | "medium" | "high" | "critical";
}

interface AuditStats {
  totalLogs: number;
  todayLogs: number;
  criticalLogs: number;
  uniqueUsers: number;
}

export default function AdminAuditLogsPage() {
  const { supabase } = useSupabase();
  const { user, loading } = useAuth();
  if (!loading && user?.role !== "super_admin") {
    redirect("/unauthorized");
    return null;
  }
  const [logs, setLogs] = useState<AuditLog[]>([]);
  const [isLoading, setLoading] = useState(false);
  const [stats, setStats] = useState<AuditStats>({
    totalLogs: 0,
    todayLogs: 0,
    criticalLogs: 0,
    uniqueUsers: 0,
  });
  const [searchTerm, setSearchTerm] = useState("");
  const [actionFilter, setActionFilter] = useState<string>("all");
  const [severityFilter, setSeverityFilter] = useState<string>("all");
  // const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [currentPage, setCurrentPage] = useState(1);
  const [expandedRows, setExpandedRows] = useState<Set<string>>(new Set());
  const itemsPerPage = 20;

  useEffect(() => {
    fetchAuditLogs();
    fetchStats();
  }, [currentPage, searchTerm, actionFilter, severityFilter]);

  const fetchAuditLogs = async () => {
    try {
      setLoading(true);

      // Mock data - replace with real audit logs query
      const mockLogs: AuditLog[] = [
        {
          id: "1",
          action: "user.login",
          resource_type: "user",
          resource_id: "user_123",
          user_id: "user_123",
          user_email: "<EMAIL>",
          user_role: "owner",
          ip_address: "*************",
          user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
          details: {
            login_method: "email",
            success: true,
            session_duration: "2h 15m",
          },
          timestamp: new Date().toISOString(),
          severity: "low",
        },
        {
          id: "2",
          action: "pharmacy.verify",
          resource_type: "pharmacy",
          resource_id: "pharmacy_456",
          user_id: "admin_789",
          user_email: "<EMAIL>",
          user_role: "owner",
          ip_address: "*************",
          user_agent: "Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7)",
          details: {
            pharmacy_name: "Pharmacie Central",
            verification_status: "approved",
            documents_checked: ["license", "insurance", "registration"],
          },
          timestamp: new Date(Date.now() - 3600000).toISOString(),
          severity: "medium",
        },
        {
          id: "3",
          action: "system.security_breach_attempt",
          resource_type: "system",
          resource_id: "security_001",
          user_id: "unknown",
          user_email: "<EMAIL>",
          user_role: "unknown",
          ip_address: "************",
          user_agent: "curl/7.68.0",
          details: {
            attack_type: "sql_injection",
            blocked: true,
            endpoint: "/api/admin/users",
            payload: "&apos;; DROP TABLE users; --",
          },
          timestamp: new Date(Date.now() - 7200000).toISOString(),
          severity: "critical",
        },
      ];

      setLogs(mockLogs);
    } catch (error) {
      console.error("Error fetching audit logs:", error);
    } finally {
      setLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      setStats({
        totalLogs: 15247,
        todayLogs: 342,
        criticalLogs: 5,
        uniqueUsers: 89,
      });
    } catch (error) {
      console.error("Error fetching stats:", error);
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "critical":
        return "red";
      case "high":
        return "orange";
      case "medium":
        return "yellow";
      case "low":
        return "blue";
      default:
        return "gray";
    }
  };

  const getActionColor = (action: string) => {
    if (action.includes("login") || action.includes("logout")) return "blue";
    if (action.includes("create") || action.includes("add")) return "green";
    if (action.includes("delete") || action.includes("remove")) return "red";
    if (action.includes("update") || action.includes("modify")) return "orange";
    if (action.includes("security") || action.includes("breach")) return "red";
    return "gray";
  };

  const toggleRowExpansion = (logId: string) => {
    const newExpanded = new Set(expandedRows);
    if (newExpanded.has(logId)) {
      newExpanded.delete(logId);
    } else {
      newExpanded.add(logId);
    }
    setExpandedRows(newExpanded);
  };

  const filteredLogs = logs.filter((log) => {
    const matchesSearch =
      log.action.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      log.resource_type.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesAction =
      actionFilter === "all" || log.action.includes(actionFilter);
    const matchesSeverity =
      severityFilter === "all" || log.severity === severityFilter;

    return matchesSearch && matchesAction && matchesSeverity;
  });

  return (
    <Stack gap="xl">
      <Group justify="space-between">
        <div>
          <Title order={1}>Audit Logs</Title>
          <Text c="dimmed" mt="xs">
            Monitor system activity and security events
          </Text>
        </div>
        <Group>
          <Button leftSection={<IconDownload size={16} />} variant="light">
            Export Logs
          </Button>
        </Group>
      </Group>

      {/* Stats Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Total Logs
              </Text>
              <Text fw={700} fz="xl">
                {stats.totalLogs.toLocaleString()}
              </Text>
            </div>
            <ThemeIcon color="blue" variant="light" size={38} radius="md">
              <IconDatabase size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Today&apos;s Activity
              </Text>
              <Text fw={700} fz="xl">
                {stats.todayLogs}
              </Text>
            </div>
            <ThemeIcon color="green" variant="light" size={38} radius="md">
              <IconActivity size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Critical Events
              </Text>
              <Text fw={700} fz="xl">
                {stats.criticalLogs}
              </Text>
            </div>
            <ThemeIcon color="red" variant="light" size={38} radius="md">
              <IconShield size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Active Users
              </Text>
              <Text fw={700} fz="xl">
                {stats.uniqueUsers}
              </Text>
            </div>
            <ThemeIcon color="violet" variant="light" size={38} radius="md">
              <IconUser size={18} />
            </ThemeIcon>
          </Group>
        </Paper>
      </SimpleGrid>

      {/* Filters */}
      <Card withBorder>
        <Group>
          <TextInput
            placeholder="Search logs..."
            leftSection={<IconSearch size={16} />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Action"
            data={[
              { value: "all", label: "All Actions" },
              { value: "login", label: "Login/Logout" },
              { value: "create", label: "Create" },
              { value: "update", label: "Update" },
              { value: "delete", label: "Delete" },
              { value: "security", label: "Security" },
            ]}
            value={actionFilter}
            onChange={(value) => setActionFilter(value || "all")}
          />
          <Select
            placeholder="Severity"
            data={[
              { value: "all", label: "All Severities" },
              { value: "critical", label: "Critical" },
              { value: "high", label: "High" },
              { value: "medium", label: "Medium" },
              { value: "low", label: "Low" },
            ]}
            value={severityFilter}
            onChange={(value) => setSeverityFilter(value || "all")}
          />
        </Group>
      </Card>

      {/* Audit Logs Table */}
      <Card withBorder>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Timestamp</Table.Th>
              <Table.Th>Action</Table.Th>
              <Table.Th>User</Table.Th>
              <Table.Th>Resource</Table.Th>
              <Table.Th>Severity</Table.Th>
              <Table.Th>IP Address</Table.Th>
              <Table.Th>Details</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredLogs.map((log) => (
              <>
                <Table.Tr key={log.id}>
                  <Table.Td>
                    <Text size="sm">
                      {new Date(log.timestamp).toLocaleString()}
                    </Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge color={getActionColor(log.action)} variant="light">
                      {log.action}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <div>
                      <Text size="sm" fw={500}>
                        {log.user_email}
                      </Text>
                      <Text size="xs" c="dimmed">
                        {log.user_role}
                      </Text>
                    </div>
                  </Table.Td>
                  <Table.Td>
                    <Text size="sm">{log.resource_type}</Text>
                  </Table.Td>
                  <Table.Td>
                    <Badge
                      color={getSeverityColor(log.severity)}
                      variant="light"
                    >
                      {log.severity}
                    </Badge>
                  </Table.Td>
                  <Table.Td>
                    <Code>{log.ip_address}</Code>
                  </Table.Td>
                  <Table.Td>
                    <ActionIcon
                      variant="subtle"
                      onClick={() => toggleRowExpansion(log.id)}
                    >
                      {expandedRows.has(log.id) ? (
                        <IconChevronDown size={16} />
                      ) : (
                        <IconChevronRight size={16} />
                      )}
                    </ActionIcon>
                  </Table.Td>
                </Table.Tr>
                <Table.Tr>
                  <Table.Td colSpan={7} p={0}>
                    <Collapse in={expandedRows.has(log.id)}>
                      <Paper p="md" bg="gray.0">
                        <Stack gap="xs">
                          <Text size="sm" fw={500}>
                            Details:
                          </Text>
                          <Code block>
                            {JSON.stringify(log.details, null, 2)}
                          </Code>
                          <Group>
                            <Text size="xs" c="dimmed">
                              <strong>User Agent:</strong> {log.user_agent}
                            </Text>
                          </Group>
                        </Stack>
                      </Paper>
                    </Collapse>
                  </Table.Td>
                </Table.Tr>
              </>
            ))}
          </Table.Tbody>
        </Table>

        <Group justify="center" mt="md">
          <Pagination
            value={currentPage}
            onChange={setCurrentPage}
            total={Math.ceil(filteredLogs.length / itemsPerPage)}
          />
        </Group>
      </Card>
    </Stack>
  );
}
