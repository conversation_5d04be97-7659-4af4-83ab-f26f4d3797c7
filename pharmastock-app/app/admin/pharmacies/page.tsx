"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  CheckCircle,
  XCircle,
  Search,
  Building2,
  MapPin,
  Phone,
  Mail,
} from "lucide-react";
import { useSupabase } from "@/contexts/supabase-context";
import { useAuth } from "@/contexts/auth-context";

interface Pharmacy {
  id: string;
  name: string;
  business_name: string;
  email: string;
  phone: string;
  address: string;
  city: string;
  license_number: string;
  is_verified: boolean;
  created_at: string;
}

export default function PharmaciesPage() {
  const { supabase } = useSupabase();
  const { user, loading: authLoading } = useAuth();
  const [pharmacies, setPharmacies] = useState<Pharmacy[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");

  const fetchPharmacies = async () => {
    if (!user) return;
    try {
      const { data, error } = await supabase
        .from("pharmacies")
        .select("*")
        .order("created_at", { ascending: false });

      if (error) throw error;
      setPharmacies(data || []);
    } catch (error) {
      console.error("Error fetching pharmacies:", error);
    } finally {
      setIsLoading(false);
    }
  };

  const toggleVerification = async (pharmacyId: string, currentStatus: boolean) => {
    if (!user) return;
    try {
      const { error } = await supabase
        .from("pharmacies")
        .update({ is_verified: !currentStatus })
        .eq("id", pharmacyId);

      if (error) throw error;

      // Refresh the list
      fetchPharmacies();
    } catch (error) {
      console.error("Error updating pharmacy:", error);
    }
  };

  // Only allow fetch if user is authenticated
  useEffect(() => {
    if (authLoading) return;
    if (user) fetchPharmacies();
  }, [user, authLoading]);

  if (authLoading) {
    return <div className="container mx-auto py-10 text-center">Chargement...</div>;
  }

  if (!user) {
    return <div className="container mx-auto py-10 text-center text-destructive">Non autorisé. Veuillez vous connecter.</div>;
  }

  const filteredPharmacies = pharmacies.filter(pharmacy =>
    pharmacy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    pharmacy.city.toLowerCase().includes(searchTerm.toLowerCase()) ||
    pharmacy.license_number.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <div>
          <h1 className="text-3xl font-bold mb-2">Pharmacy Management</h1>
          <p className="text-muted-foreground">
            View and manage all registered pharmacies in the system.
          </p>
        </div>
        <div className="flex items-center gap-4">
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
            <Input
              placeholder="Search pharmacies..."
              className="pl-10 w-[300px]"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
        </div>
      </div>

      <Card className="p-6">
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Pharmacy</TableHead>
              <TableHead>Contact</TableHead>
              <TableHead>Location</TableHead>
              <TableHead>License</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredPharmacies.map((pharmacy) => (
              <TableRow key={pharmacy.id}>
                <TableCell>
                  <div className="flex items-start gap-3">
                    <Building2 className="h-5 w-5 text-muted-foreground mt-1" />
                    <div>
                      <div className="font-medium">{pharmacy.name}</div>
                      <div className="text-sm text-muted-foreground">
                        {pharmacy.business_name}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Mail className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{pharmacy.email}</span>
                    </div>
                    <div className="flex items-center gap-2">
                      <Phone className="h-4 w-4 text-muted-foreground" />
                      <span className="text-sm">{pharmacy.phone}</span>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <div className="flex items-center gap-2">
                    <MapPin className="h-4 w-4 text-muted-foreground" />
                    <div>
                      <div className="text-sm">{pharmacy.address}</div>
                      <div className="text-sm text-muted-foreground">
                        {pharmacy.city}
                      </div>
                    </div>
                  </div>
                </TableCell>
                <TableCell>
                  <code className="bg-muted px-2 py-1 rounded text-sm">
                    {pharmacy.license_number}
                  </code>
                </TableCell>
                <TableCell>
                  <Badge
                    variant={pharmacy.is_verified ? "default" : "secondary"}
                  >
                    {pharmacy.is_verified ? "Verified" : "Pending"}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => toggleVerification(pharmacy.id, pharmacy.is_verified)}
                  >
                    {pharmacy.is_verified ? (
                      <XCircle className="h-4 w-4 text-destructive" />
                    ) : (
                      <CheckCircle className="h-4 w-4 text-primary" />
                    )}
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </Card>
    </div>
  );
}