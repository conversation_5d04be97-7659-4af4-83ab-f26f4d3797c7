"use client";

import { useState, useEffect, useCallback } from "react";
import {
  Paper,
  Title,
  Text,
  Group,
  Stack,
  Grid,
  Card,
  Badge,
  Button,
  Select,
  TextInput,
  Table,
  ActionIcon,
  Tooltip,
  Box,
  Avatar,
  Divider,
  Modal,
  Textarea,
  Tabs,
  Alert,
  <PERSON><PERSON>ation,
} from "@mantine/core";
import {
  IconMessage,
  IconSearch,
  IconFilter,
  IconRefresh,
  IconEye,
  IconTrash,
  IconSend,
  IconAlertTriangle,
  IconCheck,
  IconX,
  IconMessageCircle,
  IconMail,
  IconBell,
} from "@tabler/icons-react";
import { useAuth } from "@/contexts/auth-context";
import { redirect } from "next/navigation";

// Interface for messages
interface SystemMessage {
  id: string;
  type: string;
  from: string;
  fromEmail: string;
  subject: string;
  message: string;
  timestamp: string;
  status: string;
  priority: string;
  category: string;
  originalData?: any;
}

interface MessageStats {
  total: number;
  unread: number;
  urgent: number;
  resolved: number;
}

// Mock data for messages (will be replaced with real data)
const systemMessages: SystemMessage[] = [
  {
    id: "1",
    type: "urgent_request",
    from: "Pharmacie Centrale",
    fromEmail: "<EMAIL>",
    subject: "Demande urgente - Insuline",
    message:
      "Nous avons un patient diabétique en urgence qui a besoin d'insuline. Pouvez-vous nous aider ?",
    timestamp: "2024-01-15 14:30",
    status: "unread",
    priority: "high",
    category: "Demande urgente",
  },
  {
    id: "2",
    type: "transaction_dispute",
    from: "Pharmacie Al Amal",
    fromEmail: "<EMAIL>",
    subject: "Litige transaction #TR-2024-001",
    message:
      "Il y a un problème avec la livraison de la commande. Les médicaments reçus ne correspondent pas à la commande.",
    timestamp: "2024-01-15 12:15",
    status: "read",
    priority: "medium",
    category: "Litige",
  },
  {
    id: 3,
    type: "support",
    from: "Pharmacie Moderne",
    fromEmail: "<EMAIL>",
    subject: "Problème technique - Application",
    message:
      "Nous rencontrons des difficultés pour accéder à notre tableau de bord depuis ce matin.",
    timestamp: "2024-01-15 10:45",
    status: "replied",
    priority: "low",
    category: "Support technique",
  },
  {
    id: 4,
    type: "verification",
    from: "Pharmacie Atlas",
    fromEmail: "<EMAIL>",
    subject: "Demande de vérification de compte",
    message:
      "Nous souhaitons mettre à jour nos informations de vérification et obtenir le badge vérifié.",
    timestamp: "2024-01-14 16:20",
    status: "unread",
    priority: "medium",
    category: "Vérification",
  },
  {
    id: 5,
    type: "feedback",
    from: "Pharmacie Salam",
    fromEmail: "<EMAIL>",
    subject: "Suggestion d'amélioration",
    message:
      "Nous aimerions suggérer l'ajout d'une fonctionnalité de notification push pour les demandes urgentes.",
    timestamp: "2024-01-14 14:10",
    status: "read",
    priority: "low",
    category: "Feedback",
  },
];

const messageStats = {
  total: 127,
  unread: 23,
  urgent: 8,
  resolved: 96,
};

export default function AdminMessagesPage() {
  const { user, loading } = useAuth();
  if (!loading && user?.role !== "super_admin") {
    redirect("/unauthorized");
    return null;
  }

  const [selectedTab, setSelectedTab] = useState("all");
  const [searchQuery, setSearchQuery] = useState("");
  const [filterStatus, setFilterStatus] = useState<string>("all");
  const [filterPriority, setFilterPriority] = useState<string>("all");
  const [selectedMessage, setSelectedMessage] = useState<SystemMessage | null>(
    null
  );
  const [replyModalOpen, setReplyModalOpen] = useState(false);
  const [replyText, setReplyText] = useState("");
  const [currentPage, setCurrentPage] = useState(1);
  const [loadingMessages, setLoadingMessages] = useState(false);

  // Real data state
  const [messages, setMessages] = useState<SystemMessage[]>([]);
  const [messageStats, setMessageStats] = useState<MessageStats>({
    total: 0,
    unread: 0,
    urgent: 0,
    resolved: 0,
  });
  const [error, setError] = useState<string | null>(null);

  // Fetch messages from API
  const fetchMessages = useCallback(async () => {
    try {
      setLoadingMessages(true);
      setError(null);

      const params = new URLSearchParams();
      if (filterStatus !== "all") params.append("status", filterStatus);
      if (filterPriority !== "all") params.append("priority", filterPriority);
      if (searchQuery) params.append("search", searchQuery);
      if (selectedTab !== "all") params.append("tab", selectedTab);

      const response = await fetch(`/api/admin/messages?${params.toString()}`, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch messages: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setMessages(data.messages || []);
      setMessageStats(
        data.stats || { total: 0, unread: 0, urgent: 0, resolved: 0 }
      );
    } catch (error) {
      console.error("Error fetching messages:", error);
      setError(
        error instanceof Error ? error.message : "Failed to fetch messages"
      );
    } finally {
      setLoadingMessages(false);
    }
  }, [filterStatus, filterPriority, searchQuery, selectedTab]);

  useEffect(() => {
    fetchMessages();
  }, [fetchMessages]);

  const handleViewMessage = (message: SystemMessage) => {
    setSelectedMessage(message);
  };

  const handleReplyMessage = (message: SystemMessage) => {
    setSelectedMessage(message);
    setReplyModalOpen(true);
  };

  const handleSendReply = async () => {
    if (!selectedMessage || !replyText.trim()) return;

    try {
      setLoadingMessages(true);

      const response = await fetch("/api/admin/messages", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify({
          messageId: selectedMessage.id,
          replyText: replyText.trim(),
          action: "reply",
        }),
      });

      if (!response.ok) {
        throw new Error(`Failed to send reply: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      // Refresh messages and close modal
      await fetchMessages();
      setReplyModalOpen(false);
      setReplyText("");
      setSelectedMessage(null);
    } catch (error) {
      console.error("Error sending reply:", error);
      setError(error instanceof Error ? error.message : "Failed to send reply");
    } finally {
      setLoadingMessages(false);
    }
  };

  const handleDeleteMessage = (messageId: number) => {
    // Handle delete message
    console.log("Delete message:", messageId);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high":
        return "red";
      case "medium":
        return "orange";
      case "low":
        return "blue";
      default:
        return "gray";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "unread":
        return "red";
      case "read":
        return "blue";
      case "replied":
        return "green";
      default:
        return "gray";
    }
  };

  // Messages are already filtered by the API, so we can use them directly
  const filteredMessages = messages;

  return (
    <Stack gap="lg">
      {/* Header */}
      <Group justify="space-between" align="center">
        <div>
          <Title order={1} size="h2" c="#00B5FF">
            <Group gap="sm">
              <IconMessage size={32} />
              Messages Système
            </Group>
          </Title>
          <Text c="dimmed" size="sm" mt="xs">
            Gestion des messages et communications système
          </Text>
        </div>
        <Group gap="sm">
          <Tooltip label="Actualiser">
            <ActionIcon
              variant="light"
              color="#00B5FF"
              size="lg"
              onClick={fetchMessages}
            >
              <IconRefresh size={18} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* Stats Cards */}
      <Grid>
        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card
            withBorder
            p="md"
            style={{
              background: "linear-gradient(135deg, #00B5FF15, #00B5FF05)",
            }}
          >
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>
                  Total Messages
                </Text>
                <Text size="xl" fw={700} c="#00B5FF">
                  {messageStats.total}
                </Text>
              </div>
              <IconMail size={24} color="#00B5FF" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card
            withBorder
            p="md"
            style={{
              background: "linear-gradient(135deg, #e64c3c15, #e64c3c05)",
            }}
          >
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>
                  Non lus
                </Text>
                <Text size="xl" fw={700} c="red">
                  {messageStats.unread}
                </Text>
              </div>
              <IconBell size={24} color="red" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card
            withBorder
            p="md"
            style={{
              background: "linear-gradient(135deg, #fd7e1415, #fd7e1405)",
            }}
          >
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>
                  Urgents
                </Text>
                <Text size="xl" fw={700} c="orange">
                  {messageStats.urgent}
                </Text>
              </div>
              <IconAlertTriangle size={24} color="orange" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card
            withBorder
            p="md"
            style={{
              background: "linear-gradient(135deg, #28a74515, #28a74505)",
            }}
          >
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>
                  Résolus
                </Text>
                <Text size="xl" fw={700} c="green">
                  {messageStats.resolved}
                </Text>
              </div>
              <IconCheck size={24} color="green" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Filters and Search */}
      <Paper p="md" withBorder>
        <Group gap="md" align="end">
          <TextInput
            placeholder="Rechercher des messages..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Statut"
            value={filterStatus}
            onChange={(value) => setFilterStatus(value || "all")}
            data={[
              { value: "all", label: "Tous les statuts" },
              { value: "unread", label: "Non lu" },
              { value: "read", label: "Lu" },
              { value: "replied", label: "Répondu" },
            ]}
            leftSection={<IconFilter size={16} />}
            style={{ minWidth: 150 }}
          />
          <Select
            placeholder="Priorité"
            value={filterPriority}
            onChange={(value) => setFilterPriority(value || "all")}
            data={[
              { value: "all", label: "Toutes priorités" },
              { value: "high", label: "Haute" },
              { value: "medium", label: "Moyenne" },
              { value: "low", label: "Basse" },
            ]}
            style={{ minWidth: 150 }}
          />
        </Group>
      </Paper>

      {/* Tabs */}
      <Tabs
        value={selectedTab}
        onChange={(value) => setSelectedTab(value || "all")}
      >
        <Tabs.List>
          <Tabs.Tab value="all" leftSection={<IconMessage size={16} />}>
            Tous ({messageStats.total})
          </Tabs.Tab>
          <Tabs.Tab value="unread" leftSection={<IconBell size={16} />}>
            Non lus ({messageStats.unread})
          </Tabs.Tab>
          <Tabs.Tab
            value="urgent"
            leftSection={<IconAlertTriangle size={16} />}
          >
            Urgents ({messageStats.urgent})
          </Tabs.Tab>
          <Tabs.Tab value="resolved" leftSection={<IconCheck size={16} />}>
            Résolus ({messageStats.resolved})
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value={selectedTab} pt="md">
          <Paper withBorder>
            <Table striped highlightOnHover>
              <Table.Thead>
                <Table.Tr>
                  <Table.Th>Expéditeur</Table.Th>
                  <Table.Th>Sujet</Table.Th>
                  <Table.Th>Catégorie</Table.Th>
                  <Table.Th>Priorité</Table.Th>
                  <Table.Th>Statut</Table.Th>
                  <Table.Th>Date</Table.Th>
                  <Table.Th>Actions</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
                {filteredMessages.map((message) => (
                  <Table.Tr key={message.id}>
                    <Table.Td>
                      <Group gap="sm">
                        <Avatar size="sm" color="#00B5FF">
                          {message.from.charAt(0)}
                        </Avatar>
                        <div>
                          <Text size="sm" fw={500}>
                            {message.from}
                          </Text>
                          <Text size="xs" c="dimmed">
                            {message.fromEmail}
                          </Text>
                        </div>
                      </Group>
                    </Table.Td>
                    <Table.Td>
                      <Text
                        size="sm"
                        fw={message.status === "unread" ? 600 : 400}
                      >
                        {message.subject}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Badge variant="light" size="sm">
                        {message.category}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge
                        color={getPriorityColor(message.priority)}
                        variant="light"
                        size="sm"
                      >
                        {message.priority === "high"
                          ? "Haute"
                          : message.priority === "medium"
                          ? "Moyenne"
                          : "Basse"}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Badge
                        color={getStatusColor(message.status)}
                        variant="light"
                        size="sm"
                      >
                        {message.status === "unread"
                          ? "Non lu"
                          : message.status === "read"
                          ? "Lu"
                          : "Répondu"}
                      </Badge>
                    </Table.Td>
                    <Table.Td>
                      <Text size="sm" c="dimmed">
                        {message.timestamp}
                      </Text>
                    </Table.Td>
                    <Table.Td>
                      <Group gap="xs">
                        <Tooltip label="Voir le message">
                          <ActionIcon
                            variant="light"
                            color="blue"
                            size="sm"
                            onClick={() => handleViewMessage(message)}
                          >
                            <IconEye size={14} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Répondre">
                          <ActionIcon
                            variant="light"
                            color="green"
                            size="sm"
                            onClick={() => handleReplyMessage(message)}
                          >
                            <IconSend size={14} />
                          </ActionIcon>
                        </Tooltip>
                        <Tooltip label="Supprimer">
                          <ActionIcon
                            variant="light"
                            color="red"
                            size="sm"
                            onClick={() => handleDeleteMessage(message.id)}
                          >
                            <IconTrash size={14} />
                          </ActionIcon>
                        </Tooltip>
                      </Group>
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>

            {filteredMessages.length === 0 && (
              <Box p="xl" ta="center">
                <Text c="dimmed">Aucun message trouvé</Text>
              </Box>
            )}

            <Group justify="center" p="md">
              <Pagination
                total={Math.ceil(filteredMessages.length / 10)}
                value={currentPage}
                onChange={setCurrentPage}
                color="#00B5FF"
              />
            </Group>
          </Paper>
        </Tabs.Panel>
      </Tabs>

      {/* Reply Modal */}
      <Modal
        opened={replyModalOpen}
        onClose={() => setReplyModalOpen(false)}
        title={`Répondre à: ${selectedMessage?.subject}`}
        size="lg"
      >
        <Stack gap="md">
          <Alert
            icon={<IconMessageCircle size={16} />}
            color="blue"
            variant="light"
          >
            <Text size="sm">
              <strong>De:</strong> {selectedMessage?.from} (
              {selectedMessage?.fromEmail})
            </Text>
            <Text size="sm" mt="xs">
              <strong>Message original:</strong> {selectedMessage?.message}
            </Text>
          </Alert>

          <Textarea
            label="Votre réponse"
            placeholder="Tapez votre réponse ici..."
            value={replyText}
            onChange={(e) => setReplyText(e.target.value)}
            minRows={4}
            required
          />

          <Group justify="end">
            <Button variant="light" onClick={() => setReplyModalOpen(false)}>
              Annuler
            </Button>
            <Button
              leftSection={<IconSend size={16} />}
              loading={loadingMessages}
              onClick={handleSendReply}
              disabled={!replyText.trim()}
            >
              Envoyer la réponse
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}
