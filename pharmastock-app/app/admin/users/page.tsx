"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSupabase } from '@/contexts/supabase-context';
import { useAuth } from '@/contexts/auth-context';
import { redirect } from 'next/navigation';
import {
  Paper,
  Title,
  Text,
  Group,
  Stack,
  Table,
  Badge,
  Button,
  TextInput,
  Select,
  Modal,
  ActionIcon,
  Tooltip,
  Alert,
  Loader,
  Center,
  Menu,
  Divider
} from '@mantine/core';
import {
  IconUsers,
  IconSearch,
  IconPlus,
  IconEdit,
  IconTrash,
  IconDots,
  IconShield,
  IconUser,
  IconBuilding,
  IconExclamationMark,
  IconCheck,
  IconX,
  IconClock
} from '@tabler/icons-react';
import { useDisclosure } from '@mantine/hooks';

interface User {
  id: string;
  email: string;
  role: string;
  created_at: string;
  pharmacy_id?: string;
  pharmacy_name?: string;
  status: string;
}

export default function AdminUsersPage() {
  const { supabase } = useSupabase();
  const { user, loading } = useAuth();
  const [users, setUsers] = useState<User[]>([]);
  const [loadingUsers, setLoadingUsers] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [searchQuery, setSearchQuery] = useState('');
  const [roleFilter, setRoleFilter] = useState<string>('all');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);

  const [opened, setOpened] = useState(false);
  const [deleteOpened, setDeleteOpened] = useState(false);

  if (!loading && user?.role !== 'super_admin') {
    redirect('/unauthorized');
    return null;
  }

  const fetchUsers = useCallback(async () => {
    try {
      setLoadingUsers(true);
      setError(null);

      // Fetch users from admin API
      const response = await fetch('/api/admin/users');
      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      setUsers(data.users || []);

    } catch (error) {
      console.error('Error fetching users:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch users');
    } finally {
      setLoadingUsers(false);
    }
  }, []);

  useEffect(() => {
    fetchUsers();
  }, [fetchUsers]);

  const filteredUsers = users.filter(user => {
    const matchesSearch = user.email.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         user.pharmacy_name?.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesRole = roleFilter === 'all' || user.role === roleFilter;
    return matchesSearch && matchesRole;
  });

  const getRoleBadgeColor = (role: string) => {
    switch (role) {
      case 'owner': return 'red';
      case 'admin': return 'blue';
      case 'pharmacist': return 'green';
      case 'staff': return 'gray';
      default: return 'gray';
    }
  };

  const getStatusBadgeColor = (status: string) => {
    switch (status) {
      case 'active': return 'green';
      case 'inactive': return 'gray';
      case 'suspended': return 'red';
      default: return 'gray';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'active':
        return <IconCheck size={16} />;
      case 'inactive':
        return <IconClock size={16} />;
      case 'suspended':
        return <IconX size={16} />;
      default:
        return null;
    }
  };

  const handleEditUser = (user: User) => {
    setSelectedUser(user);
    setOpened(true);
  };

  const handleDeleteUser = (user: User) => {
    setSelectedUser(user);
    setDeleteOpened(true);
  };

  if (loadingUsers) {
    return (
      <div>
        <Title order={1} mb="xl">Gestion des Utilisateurs</Title>
        <Center h={200}>
          <Loader size="lg" />
        </Center>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Title order={1} mb="xl">Gestion des Utilisateurs</Title>
        <Alert
          icon={<IconExclamationMark size={16} />}
          title="Erreur de chargement"
          color="red"
          mb="xl"
        >
          {error}
        </Alert>
      </div>
    );
  }

  return (
    <Stack gap="lg">
      {/* Header */}
      <Group justify="space-between" align="center">
        <div>
          <Title order={1} size="h2" c="#00B5FF">
            <Group gap="sm">
              <IconUsers size={32} />
              Gestion des Utilisateurs
            </Group>
          </Title>
          <Text c="dimmed" size="sm" mt="xs">
            Gérer les comptes utilisateurs et leurs permissions
          </Text>
        </div>
        <Button
          leftSection={<IconPlus size={16} />}
          variant="filled"
          color="#00B5FF"
          onClick={() => setOpened(true)}
        >
          Nouvel Utilisateur
        </Button>
      </Group>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group gap="md">
          <TextInput
            placeholder="Rechercher par email ou pharmacie..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Filtrer par rôle"
            value={roleFilter}
            onChange={(value) => setRoleFilter(value || 'all')}
            data={[
              { value: 'all', label: 'Tous les rôles' },
              { value: 'owner', label: 'Propriétaire' },
              { value: 'admin', label: 'Administrateur' },
              { value: 'pharmacist', label: 'Pharmacien' },
              { value: 'staff', label: 'Personnel' },
            ]}
            style={{ minWidth: 200 }}
          />
        </Group>
      </Paper>

      {/* Users Table */}
      <Paper withBorder>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Email</Table.Th>
              <Table.Th>Rôle</Table.Th>
              <Table.Th>Pharmacie</Table.Th>
              <Table.Th>Statut</Table.Th>
              <Table.Th>Date de création</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredUsers.map((user) => (
              <Table.Tr key={user.id}>
                <Table.Td>
                  <Group gap="sm">
                    <IconUser size={16} />
                    <Text fw={500}>{user.email}</Text>
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Badge color={getRoleBadgeColor(user.role)} variant="light">
                    {user.role}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  {user.pharmacy_name ? (
                    <Group gap="sm">
                      <IconBuilding size={16} />
                      <Text>{user.pharmacy_name}</Text>
                    </Group>
                  ) : (
                    <Text c="dimmed">Aucune</Text>
                  )}
                </Table.Td>
                <Table.Td>
                  <Group gap="sm">
                    {getStatusIcon(user.status)}
                    <Badge color={getStatusBadgeColor(user.status)} variant="light">
                      {user.status}
                    </Badge>
                  </Group>
                </Table.Td>
                <Table.Td>
                  <Text>{new Date(user.created_at).toLocaleDateString('fr-FR')}</Text>
                </Table.Td>
                <Table.Td>
                  <Menu shadow="md" width={200}>
                    <Menu.Target>
                      <ActionIcon variant="subtle" color="gray">
                        <IconDots size={16} />
                      </ActionIcon>
                    </Menu.Target>
                    <Menu.Dropdown>
                      <Menu.Item
                        leftSection={<IconEdit size={14} />}
                        onClick={() => handleEditUser(user)}
                      >
                        Modifier
                      </Menu.Item>
                      <Menu.Item
                        leftSection={<IconShield size={14} />}
                        onClick={() => {/* Handle permissions */}}
                      >
                        Permissions
                      </Menu.Item>
                      <Menu.Divider />
                      <Menu.Item
                        leftSection={<IconTrash size={14} />}
                        color="red"
                        onClick={() => handleDeleteUser(user)}
                      >
                        Supprimer
                      </Menu.Item>
                    </Menu.Dropdown>
                  </Menu>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        {filteredUsers.length === 0 && (
          <Center p="xl">
            <Text c="dimmed">Aucun utilisateur trouvé</Text>
          </Center>
        )}
      </Paper>
    </Stack>
  );
}