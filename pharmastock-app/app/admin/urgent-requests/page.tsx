"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { adminMockServices } from "@/services/mock/admin";
import {
  Paper,
  Title,
  Text,
  Group,
  Stack,
  Grid,
  Card,
  Badge,
  Button,
  Select,
  TextInput,
  Table,
  ActionIcon,
  Tooltip,
  Box,
  Avatar,
  Divider,
  Modal,
  Textarea,
  Tabs,
  Alert,
  Pagination,
  Progress,
  NumberFormatter,
} from '@mantine/core';
import {
  IconAlertTriangle,
  IconSearch,
  IconFilter,
  IconRefresh,
  IconEye,
  IconCheck,
  IconX,
  IconClock,
  IconPhone,
  IconMail,
  IconMapPin,
  IconChartBar,
  IconUsers,
  IconPackage,
} from '@tabler/icons-react';
import { redirect } from "next/navigation";

interface UrgentRequest {
  id: string;
  pharmacy_id: string;
  pharmacy_name: string;
  type: string;
  product_name: string;
  quantity_needed?: number;
  quantity_available?: number;
  urgency_level: string;
  status: string;
  created_at: string;
  expires_at: string;
  description: string;
  contact_info: {
    name: string;
    phone: string;
    email: string;
  };
  responses: Array<{
    pharmacy_id: string;
    pharmacy_name: string;
    response_time: string;
    can_provide: boolean;
    quantity_available: number;
    notes: string;
  }>;
  resolution?: {
    resolved_at: string;
    resolved_by: string;
    notes: string;
  };
}

export default function UrgentRequestsPage() {
  const { user, loading } = useAuth();
  const [requests, setRequests] = useState<UrgentRequest[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [filterUrgency, setFilterUrgency] = useState<string>('all');
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const [resolveModalOpen, setResolveModalOpen] = useState(false);
  const [resolveNotes, setResolveNotes] = useState('');
  const [currentPage, setCurrentPage] = useState(1);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [requestsData, statsData] = await Promise.all([
          adminMockServices.urgentRequests.getAll(),
          adminMockServices.urgentRequests.getStats(),
        ]);

        setRequests(requestsData);
        setStats(statsData);
      } catch (error) {
        console.error("Error fetching urgent requests:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Show loading state while checking session
  if (loading) {
    return (
      <Box
        style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Stack align="center" gap="md">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <Text c="dimmed">Loading...</Text>
        </Stack>
      </Box>
    );
  }

  // Only redirect if we're sure the user is not an admin or owner
  if (!loading && user?.role !== "super_admin") {
    redirect("/unauthorized");
    return null;
  }

  if (isLoading) {
    return (
      <Box
        style={{
          minHeight: '100vh',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
        }}
      >
        <Stack align="center" gap="md">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <Text c="dimmed">Loading urgent requests...</Text>
        </Stack>
      </Box>
    );
  }

  const getUrgencyColor = (level: string) => {
    switch (level.toLowerCase()) {
      case "high":
      case "critical":
        return "red";
      case "medium":
        return "orange";
      default:
        return "blue";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case "resolved":
        return "green";
      case "active":
        return "blue";
      case "expired":
        return "gray";
      default:
        return "yellow";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status.toLowerCase()) {
      case "resolved":
        return <IconCheck size={16} />;
      case "active":
        return <IconClock size={16} />;
      default:
        return <IconAlertTriangle size={16} />;
    }
  };

  const handleResolveRequest = (request: any) => {
    setSelectedRequest(request);
    setResolveModalOpen(true);
  };

  const handleSubmitResolution = () => {
    // Handle resolution logic here
    setResolveModalOpen(false);
    setResolveNotes('');
    setSelectedRequest(null);
  };

  const filteredRequests = requests.filter(request => {
    const matchesSearch = request.product_name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                         request.pharmacy_name.toLowerCase().includes(searchQuery.toLowerCase());
    const matchesUrgency = filterUrgency === 'all' || request.urgency_level === filterUrgency;
    const matchesStatus = filterStatus === 'all' || request.status === filterStatus;
    const matchesTab = selectedTab === 'all' ||
                      (selectedTab === 'active' && request.status === 'active') ||
                      (selectedTab === 'resolved' && request.status === 'resolved') ||
                      (selectedTab === 'critical' && request.urgency_level === 'critical');

    return matchesSearch && matchesUrgency && matchesStatus && matchesTab;
  });

  return (
    <Stack gap="lg">
      {/* Header */}
      <Group justify="space-between" align="center">
        <div>
          <Title order={1} size="h2" c="#00B5FF">
            <Group gap="sm">
              <IconAlertTriangle size={32} />
              Demandes Urgentes Système
            </Group>
          </Title>
          <Text c="dimmed" size="sm" mt="xs">
            Gestion système des demandes urgentes de toutes les pharmacies
          </Text>
        </div>
        <Group gap="sm">
          <Tooltip label="Actualiser">
            <ActionIcon
              variant="light"
              color="#00B5FF"
              size="lg"
              onClick={() => window.location.reload()}
            >
              <IconRefresh size={18} />
            </ActionIcon>
          </Tooltip>
        </Group>
      </Group>

      {/* Stats Overview */}
      <Grid>
        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #00B5FF15, #00B5FF05)' }}>
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>Total Demandes</Text>
                <Text size="xl" fw={700} c="#00B5FF">
                  <NumberFormatter value={stats?.total || 0} />
                </Text>
              </div>
              <IconChartBar size={24} color="#00B5FF" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #e64c3c15, #e64c3c05)' }}>
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>Actives</Text>
                <Text size="xl" fw={700} c="red">
                  <NumberFormatter value={stats?.active || 0} />
                </Text>
              </div>
              <IconClock size={24} color="red" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #28a74515, #28a74505)' }}>
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>Résolues</Text>
                <Text size="xl" fw={700} c="green">
                  <NumberFormatter value={stats?.resolved || 0} />
                </Text>
              </div>
              <IconCheck size={24} color="green" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #fd7e1415, #fd7e1405)' }}>
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed" fw={500}>Temps Moyen</Text>
                <Text size="xl" fw={700} c="orange">
                  {stats?.average_resolution_time || '0h'}
                </Text>
              </div>
              <IconClock size={24} color="orange" opacity={0.6} />
            </Group>
          </Card>
        </Grid.Col>
      </Grid>

      {/* Filters and Search */}
      <Paper p="md" withBorder>
        <Group gap="md" align="end">
          <TextInput
            placeholder="Rechercher des demandes..."
            leftSection={<IconSearch size={16} />}
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Urgence"
            value={filterUrgency}
            onChange={(value) => setFilterUrgency(value || 'all')}
            data={[
              { value: 'all', label: 'Toutes urgences' },
              { value: 'critical', label: 'Critique' },
              { value: 'high', label: 'Haute' },
              { value: 'medium', label: 'Moyenne' },
              { value: 'low', label: 'Basse' },
            ]}
            leftSection={<IconFilter size={16} />}
            style={{ minWidth: 150 }}
          />
          <Select
            placeholder="Statut"
            value={filterStatus}
            onChange={(value) => setFilterStatus(value || 'all')}
            data={[
              { value: 'all', label: 'Tous les statuts' },
              { value: 'active', label: 'Actif' },
              { value: 'resolved', label: 'Résolu' },
              { value: 'expired', label: 'Expiré' },
            ]}
            style={{ minWidth: 150 }}
          />
        </Group>
      </Paper>

      {/* Tabs */}
      <Tabs value={selectedTab} onChange={(value) => setSelectedTab(value || 'all')}>
        <Tabs.List>
          <Tabs.Tab value="all" leftSection={<IconChartBar size={16} />}>
            Toutes ({requests.length})
          </Tabs.Tab>
          <Tabs.Tab value="active" leftSection={<IconClock size={16} />}>
            Actives ({requests.filter(r => r.status === 'active').length})
          </Tabs.Tab>
          <Tabs.Tab value="critical" leftSection={<IconAlertTriangle size={16} />}>
            Critiques ({requests.filter(r => r.urgency_level === 'critical').length})
          </Tabs.Tab>
          <Tabs.Tab value="resolved" leftSection={<IconCheck size={16} />}>
            Résolues ({requests.filter(r => r.status === 'resolved').length})
          </Tabs.Tab>
        </Tabs.List>

        <Tabs.Panel value={selectedTab} pt="md">
          {/* Requests List */}
          <Stack gap="md">
            {filteredRequests.map((request) => (
              <Card key={request.id} withBorder p="lg">
                <Group justify="space-between" align="flex-start" mb="md">
                  <div style={{ flex: 1 }}>
                    <Group gap="sm" mb="xs">
                      {getStatusIcon(request.status)}
                      <Title order={3} size="h4" fw={600}>
                        {request.product_name}
                      </Title>
                      <Badge color={getUrgencyColor(request.urgency_level)} variant="light">
                        {request.urgency_level === 'critical' ? 'Critique' :
                         request.urgency_level === 'high' ? 'Haute' :
                         request.urgency_level === 'medium' ? 'Moyenne' : 'Basse'}
                      </Badge>
                      <Badge color={getStatusColor(request.status)} variant="filled" size="sm">
                        {request.status === 'active' ? 'Actif' :
                         request.status === 'resolved' ? 'Résolu' : 'Expiré'}
                      </Badge>
                    </Group>
                    <Text size="sm" c="dimmed" mb="sm">
                      {request.description}
                    </Text>
                  </div>
                  <div style={{ textAlign: 'right' }}>
                    <Text fw={600} size="sm">
                      {request.pharmacy_name}
                    </Text>
                    <Text size="xs" c="dimmed">
                      Expire: {new Date(request.expires_at).toLocaleDateString()}
                    </Text>
                  </div>
                </Group>

                {/* Request Details */}
                <Grid>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Stack gap="xs">
                      <Text fw={600} size="sm" c="dark">
                        Détails de la demande
                      </Text>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Type:</Text>
                        <Text size="sm" fw={500}>{request.type}</Text>
                      </Group>
                      {request.quantity_needed && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Quantité demandée:</Text>
                          <Text size="sm" fw={500}>{request.quantity_needed}</Text>
                        </Group>
                      )}
                      {request.quantity_available && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Quantité disponible:</Text>
                          <Text size="sm" fw={500}>{request.quantity_available}</Text>
                        </Group>
                      )}
                    </Stack>
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <Stack gap="xs">
                      <Text fw={600} size="sm" c="dark">
                        Informations de contact
                      </Text>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Nom:</Text>
                        <Text size="sm" fw={500}>{request.contact_info.name}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Téléphone:</Text>
                        <Group gap="xs">
                          <IconPhone size={14} color="#00B5FF" />
                          <Text size="sm" fw={500}>{request.contact_info.phone}</Text>
                        </Group>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Email:</Text>
                        <Group gap="xs">
                          <IconMail size={14} color="#00B5FF" />
                          <Text size="sm" fw={500}>{request.contact_info.email}</Text>
                        </Group>
                      </Group>
                    </Stack>
                  </Grid.Col>
                </Grid>

                {/* Responses */}
                {request.responses.length > 0 && (
                  <Box mt="md">
                    <Text fw={600} size="sm" c="dark" mb="sm">
                      Réponses ({request.responses.length})
                    </Text>
                    <Stack gap="sm">
                      {request.responses.map((response, index) => (
                        <Paper key={index} p="md" withBorder style={{ backgroundColor: '#f8fafc' }}>
                          <Group justify="space-between" align="flex-start" mb="xs">
                            <Text fw={600} size="sm">{response.pharmacy_name}</Text>
                            <Text size="xs" c="dimmed">
                              {new Date(response.response_time).toLocaleString()}
                            </Text>
                          </Group>
                          <Stack gap="xs">
                            <Group justify="space-between">
                              <Text size="sm" c="dimmed">Peut fournir:</Text>
                              <Badge color={response.can_provide ? "green" : "red"} variant="light" size="sm">
                                {response.can_provide ? "Oui" : "Non"}
                              </Badge>
                            </Group>
                            {response.can_provide && (
                              <Group justify="space-between">
                                <Text size="sm" c="dimmed">Quantité disponible:</Text>
                                <Text size="sm" fw={500}>{response.quantity_available}</Text>
                              </Group>
                            )}
                            <Text size="sm" c="dimmed" style={{ fontStyle: 'italic' }}>
                              {response.notes}
                            </Text>
                          </Stack>
                        </Paper>
                      ))}
                    </Stack>
                  </Box>
                )}

                {/* Resolution */}
                {request.resolution && (
                  <Paper p="md" withBorder mt="md" style={{ backgroundColor: '#f0f9ff' }}>
                    <Text fw={600} size="sm" c="dark" mb="sm">
                      Résolution
                    </Text>
                    <Stack gap="xs">
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Résolu par:</Text>
                        <Text size="sm" fw={500}>{request.resolution.resolved_by}</Text>
                      </Group>
                      <Group justify="space-between">
                        <Text size="sm" c="dimmed">Résolu le:</Text>
                        <Text size="sm" fw={500}>
                          {new Date(request.resolution.resolved_at).toLocaleString()}
                        </Text>
                      </Group>
                      <Text size="sm" c="dimmed" style={{ fontStyle: 'italic' }}>
                        {request.resolution.notes}
                      </Text>
                    </Stack>
                  </Paper>
                )}

                {/* Actions */}
                {request.status === "active" && (
                  <Group justify="end" mt="md" gap="sm">
                    <Button
                      variant="light"
                      color="blue"
                      leftSection={<IconPhone size={16} />}
                    >
                      Contacter Pharmacie
                    </Button>
                    <Button
                      color="#00B5FF"
                      leftSection={<IconCheck size={16} />}
                      onClick={() => handleResolveRequest(request)}
                    >
                      Marquer comme Résolu
                    </Button>
                  </Group>
                )}
              </Card>
            ))}

            {filteredRequests.length === 0 && (
              <Paper p="xl" ta="center" withBorder>
                <Stack align="center" gap="md">
                  <IconAlertTriangle size={48} color="gray" opacity={0.5} />
                  <Text c="dimmed" size="lg">
                    Aucune demande urgente trouvée
                  </Text>
                  <Text c="dimmed" size="sm">
                    Essayez de modifier vos filtres de recherche
                  </Text>
                </Stack>
              </Paper>
            )}
          </Stack>
        </Tabs.Panel>
      </Tabs>

      {/* Resolve Modal */}
      <Modal
        opened={resolveModalOpen}
        onClose={() => setResolveModalOpen(false)}
        title="Résoudre la demande urgente"
        size="lg"
      >
        <Stack gap="md">
          <Alert color="blue" variant="light">
            <Text size="sm">
              <strong>Demande:</strong> {selectedRequest?.product_name}
            </Text>
            <Text size="sm" mt="xs">
              <strong>Pharmacie:</strong> {selectedRequest?.pharmacy_name}
            </Text>
          </Alert>

          <Textarea
            label="Notes de résolution"
            placeholder="Décrivez comment cette demande a été résolue..."
            value={resolveNotes}
            onChange={(e) => setResolveNotes(e.target.value)}
            minRows={4}
            required
          />

          <Group justify="end">
            <Button variant="light" onClick={() => setResolveModalOpen(false)}>
              Annuler
            </Button>
            <Button
              leftSection={<IconCheck size={16} />}
              onClick={handleSubmitResolution}
              disabled={!resolveNotes.trim()}
            >
              Confirmer la résolution
            </Button>
          </Group>
        </Stack>
      </Modal>
    </Stack>
  );
}