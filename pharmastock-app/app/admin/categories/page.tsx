"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { adminMockServices } from "@/services/mock/admin";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { redirect } from "next/navigation";
import { Plus, Edit, Trash2 } from "lucide-react";

interface Category {
  id: string;
  name: string;
  slug: string;
  description: string;
  icon: string;
  active: boolean;
  subcategories: {
    id: string;
    name: string;
    slug: string;
    active: boolean;
  }[];
  stats: {
    total_products: number;
    active_listings: number;
    total_volume: number;
  };
}

export default function AdminCategoriesPage() {
  const { user, loading } = useAuth();
  const [categories, setCategories] = useState<Category[]>([]);
  const [stats, setStats] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);

  if (!loading && user?.role !== 'super_admin') {
    redirect('/unauthorized');
    return null;
  }

  useEffect(() => {
    const fetchData = async () => {
      try {
        const [categoriesData, statsData] = await Promise.all([
          adminMockServices.categories.getAll(),
          adminMockServices.categories.getStats(),
        ]);

        setCategories(categoriesData);
        setStats(statsData);
      } catch (error) {
        console.error("Error fetching categories:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchData();
  }, []);

  // Show loading state while checking session
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading categories...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-10">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Product Categories</h1>
        <Button>
          <Plus className="h-4 w-4 mr-2" />
          Add Category
        </Button>
      </div>

      {/* Stats Overview */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Total Categories</h3>
          <p className="text-3xl font-bold">{stats?.total_categories}</p>
        </Card>
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Active Listings</h3>
          <p className="text-3xl font-bold">{stats?.active_listings}</p>
        </Card>
        <Card className="p-6">
          <h3 className="font-semibold mb-2">Total Volume</h3>
          <p className="text-3xl font-bold">{stats?.total_volume} DH</p>
        </Card>
      </div>

      {/* Categories List */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {categories.map((category) => (
          <Card key={category.id} className="p-6">
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-xl font-semibold">{category.name}</h3>
                <p className="text-sm text-muted-foreground">{category.description}</p>
              </div>
              <Badge variant={category.active ? "default" : "secondary"}>
                {category.active ? "Active" : "Inactive"}
              </Badge>
            </div>

            {/* Subcategories */}
            <div className="mb-4">
              <h4 className="font-semibold mb-2">Subcategories</h4>
              <div className="flex flex-wrap gap-2">
                {category.subcategories.map((sub) => (
                  <Badge key={sub.id} variant="outline">
                    {sub.name}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Stats */}
            <div className="space-y-2 mb-4">
              <div className="flex justify-between text-sm">
                <span>Total Products:</span>
                <span className="font-semibold">{category.stats.total_products}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Active Listings:</span>
                <span className="font-semibold">{category.stats.active_listings}</span>
              </div>
              <div className="flex justify-between text-sm">
                <span>Total Volume:</span>
                <span className="font-semibold">{category.stats.total_volume} DH</span>
              </div>
            </div>

            {/* Actions */}
            <div className="flex justify-end gap-2">
              <Button variant="outline" size="sm">
                <Edit className="h-4 w-4 mr-2" />
                Edit
              </Button>
              <Button variant="destructive" size="sm">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </Button>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}