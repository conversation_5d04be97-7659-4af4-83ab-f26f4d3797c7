"use client";

import { useState, useEffect, useCallback } from 'react';
import {
  Paper,
  Title,
  Text,
  Group,
  Stack,
  Grid,
  Card,
  Badge,
  Button,
  Select,
  Table,
  Progress,
  ActionIcon,
  Tooltip,
  Box,
  Divider,
  NumberFormatter,
  RingProgress,
  Center,
} from '@mantine/core';
import { DatePickerInput } from '@mantine/dates';
import {
  IconReportAnalytics,
  IconDownload,
  IconCalendar,
  IconTrendingUp,
  IconTrendingDown,
  IconUsers,
  IconBuilding,
  IconShoppingCart,
  IconCurrencyDollar,
  IconFileText,
  IconFilter,
  IconRefresh,
} from '@tabler/icons-react';
import { useAuth } from '@/contexts/auth-context';
import { redirect } from 'next/navigation';
import { getServiceSupabaseClient } from '@/lib/supabaseClient';
import { ExportAsExcel, ExportAsPdf } from '@siamf/react-export';

// Define interfaces for RPC function return types
interface TopPharmacy {
  id: string;
  name: string;
  transactions: number;
  revenue: number;
}

interface CategoryStat {
  category: string;
  transactions: number;
  percentage: number;
}

// Mock data for reports
const systemStats = {
  totalPharmacies: 156,
  activePharmacies: 142,
  totalTransactions: 2847,
  totalRevenue: 1250000,
  monthlyGrowth: 12.5,
  userGrowth: 8.3,
};

const topPharmacies = [
  { id: 1, name: 'Pharmacie Centrale', transactions: 245, revenue: 125000, growth: 15.2 },
  { id: 2, name: 'Pharmacie Al Amal', transactions: 198, revenue: 98000, growth: 8.7 },
  { id: 3, name: 'Pharmacie Moderne', transactions: 167, revenue: 87500, growth: -2.1 },
  { id: 4, name: 'Pharmacie Atlas', transactions: 134, revenue: 76000, growth: 22.3 },
  { id: 5, name: 'Pharmacie Salam', transactions: 112, revenue: 65000, growth: 5.8 },
];

const categoryStats = [
  { category: 'Antibiotiques', transactions: 567, percentage: 35 },
  { category: 'Antalgiques', transactions: 423, percentage: 26 },
  { category: 'Vitamines', transactions: 298, percentage: 18 },
  { category: 'Diabète', transactions: 234, percentage: 14 },
  { category: 'Cardiologie', transactions: 112, percentage: 7 },
];

export default function AdminReportsPage() {
  const { user, loading } = useAuth();
  if (!loading && user?.role !== 'super_admin') {
    redirect('/unauthorized');
    return null;
  }

  const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [reportType, setReportType] = useState<string>('overview');
  const [loadingReports, setLoadingReports] = useState(false);
  const [metrics, setMetrics] = useState<any>(null);
  const [topPharmaciesData, setTopPharmaciesData] = useState<TopPharmacy[]>([]);
  const [categoryStatsData, setCategoryStatsData] = useState<CategoryStat[]>([]);
  const [metricsError, setMetricsError] = useState<string | null>(null);

  const fetchMetrics = useCallback(async () => {
    setLoadingReports(true);
    setMetricsError(null);
    const supabase = getServiceSupabaseClient();
    const [startDate, endDate] = dateRange;
    try {
      if (reportType === 'overview' || reportType === 'pharmacies') {
        // Total pharmacies (filtered by creation date if date range selected)
        let pharmaciesQuery = supabase
          .from('pharmacies')
          .select('id');

        if (startDate) {
          pharmaciesQuery = pharmaciesQuery.gte('created_at', startDate.toISOString());
        }
        if (endDate) {
          pharmaciesQuery = pharmaciesQuery.lte('created_at', endDate.toISOString());
        }

        const { data: pharmacies, error: pharmaciesError } = await pharmaciesQuery;
        if (pharmaciesError) throw pharmaciesError;
        const totalPharmacies = pharmacies.length;

        // Active pharmacies (with at least 1 transaction within the selected date range)
        // If no date range is selected, still defaults to last 30 days logic.
        const effectiveStartDate = startDate || new Date(new Date().setDate(new Date().getDate() - 30));
        const effectiveEndDate = endDate || new Date();

        let recentTxQuery = supabase
          .from('transactions')
          .select('seller_id, buyer_id')
          .gte('created_at', effectiveStartDate.toISOString())
          .lte('created_at', effectiveEndDate.toISOString());

        const { data: recentTx, error: txError } = await recentTxQuery;
        if (txError) throw txError;
        const activePharmacyIds = new Set<string>();
        (recentTx || []).forEach((tx: { seller_id: string; buyer_id: string }) => {
          if (tx.seller_id) activePharmacyIds.add(tx.seller_id);
          if (tx.buyer_id) activePharmacyIds.add(tx.buyer_id);
        });
        const activePharmacies = activePharmacyIds.size;

        setMetrics((prev: any) => ({ ...prev, totalPharmacies, activePharmacies }));
      }

      if (reportType === 'overview' || reportType === 'transactions' || reportType === 'financial') {
        // Total transactions within date range
        let transactionsCountQuery = supabase
          .from('transactions')
          .select('id', { count: 'exact', head: true });

        if (startDate) {
          transactionsCountQuery = transactionsCountQuery.gte('created_at', startDate.toISOString());
        }
        if (endDate) {
          transactionsCountQuery = transactionsCountQuery.lte('created_at', endDate.toISOString());
        }
        const { count: totalTransactions, error: txCountError } = await transactionsCountQuery;
        if (txCountError) throw txCountError;

        // Total revenue within date range
        let revenueQuery = supabase
          .from('transactions')
          .select('total_amount');

        if (startDate) {
          revenueQuery = revenueQuery.gte('created_at', startDate.toISOString());
        }
        if (endDate) {
          revenueQuery = revenueQuery.lte('created_at', endDate.toISOString());
        }
        const { data: revenueRows, error: revenueError } = await revenueQuery;
        if (revenueError) throw revenueError;
        const totalRevenue = (revenueRows || []).reduce((sum: number, row: { total_amount: number }) => sum + Number(row.total_amount), 0);

        setMetrics((prev: any) => ({ ...prev, totalTransactions, totalRevenue }));
      }

      if (reportType === 'overview' || reportType === 'pharmacies') {
        // Top Pharmacies by Transactions and Revenue with date filter
        const { data: topPharmaciesRaw, error: topPharmaciesError } = await supabase.rpc(
          'get_top_pharmacies_by_transactions_and_revenue' as any,
          {
            start_date: startDate ? startDate.toISOString() : null,
            end_date: endDate ? endDate.toISOString() : null,
          }
        );
        if (topPharmaciesError) throw topPharmaciesError;
        setTopPharmaciesData(topPharmaciesRaw as TopPharmacy[] || []);
      } else {
        setTopPharmaciesData([]);
      }

      if (reportType === 'overview' || reportType === 'products') {
        // Transaction Statistics by Category with date filter
        const { data: categoryStatsRaw, error: categoryStatsError } = await supabase.rpc(
          'get_transaction_stats_by_category' as any,
          {
            start_date: startDate ? startDate.toISOString() : null,
            end_date: endDate ? endDate.toISOString() : null,
          }
        );
        if (categoryStatsError) throw categoryStatsError;
        setCategoryStatsData(categoryStatsRaw as CategoryStat[] || []);
      } else {
        setCategoryStatsData([]);
      }

    } catch (err: any) {
      setMetricsError(err.message || 'Erreur lors du chargement des métriques');
    } finally {
      setLoadingReports(false);
    }
  }, [dateRange, reportType]);

  useEffect(() => {
    fetchMetrics();
  }, [fetchMetrics]);

  const handleRefreshData = () => {
    fetchMetrics();
  };

  // Helper to get report data in a generic format for export
  const getExportData = (format: 'excel' | 'pdf') => {
    let data: any[] = [];
    let headers: string[] = [];
    let title = '';

    if (!metrics && !topPharmaciesData.length && !categoryStatsData.length) {
      return null; // No data to export
    }

    switch (reportType) {
      case 'overview':
        title = 'Rapport de Vue d\'ensemble';
        headers = ['Statistique', 'Valeur'];
        data = [
          { Statistique: 'Pharmacies Totales', Valeur: metrics?.totalPharmacies || 0 },
          { Statistique: 'Pharmacies Actives (30j)', Valeur: metrics?.activePharmacies || 0 },
          { Statistique: 'Transactions Totales', Valeur: metrics?.totalTransactions || 0 },
          { Statistique: 'Chiffre d\'affaires Total', Valeur: metrics?.totalRevenue || 0 },
        ];
        break;
      case 'pharmacies':
        title = 'Rapport des Pharmacies';
        headers = ['Nom de la Pharmacie', 'Transactions', 'Chiffre d\'affaires'];
        data = topPharmaciesData.map(p => ({
          'Nom de la Pharmacie': p.name,
          'Transactions': p.transactions,
          'Chiffre d\'affaires': p.revenue,
        }));
        break;
      case 'transactions':
      case 'financial':
        title = 'Rapport des Transactions et Financier';
        headers = ['Transactions Totales', 'Chiffre d\'affaires Total'];
        data = [
          { 'Transactions Totales': metrics?.totalTransactions || 0, 'Chiffre d\'affaires Total': metrics?.totalRevenue || 0 },
        ];
        break;
      case 'products':
        title = 'Rapport des Produits par Catégorie';
        headers = ['Catégorie', 'Transactions', 'Pourcentage'];
        data = categoryStatsData.map(c => ({
          'Catégorie': c.category,
          'Transactions': c.transactions,
          'Pourcentage': `${c.percentage}%`,
        }));
        break;
      default:
        return null;
    }
    return { data, headers, title, fileName: `${title.replace(/ /g, '_')}_${new Date().toISOString().split('T')[0]}` };
  };

  const [exportTrigger, setExportTrigger] = useState<{ format: 'excel' | 'pdf' | null, timestamp: number } | null>(null);

  useEffect(() => {
    if (exportTrigger) {
      const exportData = getExportData(exportTrigger.format as 'excel' | 'pdf');
      if (exportData) {
        if (exportTrigger.format === 'excel') {
          // Trigger Excel export using a ref or direct call
          // For programmatic trigger, the library typically expects a button click.
          // A common pattern for programmatic export is to use a hidden button ref or a direct function call if provided by the library.
          // As a workaround, we can simulate a click on a hidden button.
          const excelButton = document.getElementById('export-excel-button');
          if (excelButton) excelButton.click();
        } else if (exportTrigger.format === 'pdf') {
          const pdfButton = document.getElementById('export-pdf-button');
          if (pdfButton) pdfButton.click();
        }
      }
      setExportTrigger(null); // Reset trigger
      setLoadingReports(false); // Stop loading after attempt
    }
  }, [exportTrigger]);

  const handleExportReport = async (format: 'excel' | 'pdf') => {
    setLoadingReports(true);
    // Set a trigger to be picked up by the useEffect hook
    setExportTrigger({ format, timestamp: Date.now() });
  };

  return (
    <Stack gap="lg">
      {/* Header */}
      <Group justify="space-between" align="center">
        <div>
          <Title order={1} size="h2" c="#00B5FF">
            <Group gap="sm">
              <IconReportAnalytics size={32} />
              Rapports et Analyses
            </Group>
          </Title>
          <Text c="dimmed" size="sm" mt="xs">
            Analyses système et rapports détaillés de performance
          </Text>
        </div>
        <Group gap="sm">
          <Tooltip label="Actualiser les données">
            <ActionIcon
              variant="light"
              color="#00B5FF"
              size="lg"
              loading={loadingReports}
              onClick={handleRefreshData}
            >
              <IconRefresh size={18} />
            </ActionIcon>
          </Tooltip>
          <Button
            leftSection={<IconDownload size={16} />}
            variant="filled"
            color="#00B5FF"
            loading={loadingReports}
            onClick={() => handleExportReport('excel')} // Default to Excel for now
          >
            Exporter Excel
          </Button>
          <Button
            leftSection={<IconDownload size={16} />}
            variant="outline"
            color="#00B5FF"
            loading={loadingReports}
            onClick={() => handleExportReport('pdf')} // Add PDF export button
          >
            Exporter PDF
          </Button>
        </Group>
      </Group>

      {/* Hidden export components */}
      <Box style={{ display: 'none' }}>
        {getExportData('excel') && (
          <ExportAsExcel
            id="export-excel-button"
            data={getExportData('excel')?.data || []}
            headers={getExportData('excel')?.headers || []}
            fileName={getExportData('excel')?.fileName || 'report'}
          >
            <button id="excel-trigger-button">Export Excel</button>
          </ExportAsExcel>
        )}
        {getExportData('pdf') && (
          <ExportAsPdf
            id="export-pdf-button"
            data={getExportData('pdf')?.data || []}
            headers={getExportData('pdf')?.headers || []}
            title={getExportData('pdf')?.title || 'Report'}
            fileName={getExportData('pdf')?.fileName || 'report'}
            orientation="landscape"
            styles={{ cellWidth: 'auto', fontSize: 8 }}
            headerStyles={{ fillColor: '#00B5FF', textColor: 255, fontStyle: 'bold' }}
          >
            <button id="pdf-trigger-button">Export PDF</button>
          </ExportAsPdf>
        )}
      </Box>

      {/* Filters */}
      <Paper p="md" withBorder>
        <Group gap="md" align="end">
          <Select
            label="Type de rapport"
            placeholder="Sélectionner"
            value={reportType}
            onChange={(value) => setReportType(value || 'overview')}
            data={[
              { value: 'overview', label: 'Vue d\'ensemble' },
              { value: 'transactions', label: 'Transactions' },
              { value: 'pharmacies', label: 'Pharmacies' },
              { value: 'products', label: 'Produits' },
              { value: 'financial', label: 'Financier' },
            ]}
            leftSection={<IconFilter size={16} />}
            style={{ minWidth: 200 }}
          />
          <DatePickerInput
            type="range"
            label="Période"
            placeholder="Sélectionner la période"
            value={dateRange}
            onChange={(value) => setDateRange(value as unknown as [Date | null, Date | null])}
            leftSection={<IconCalendar size={16} />}
            style={{ minWidth: 250 }}
          />
        </Group>
      </Paper>

      {/* Key Metrics */}
      {(reportType === 'overview' || reportType === 'pharmacies' || reportType === 'transactions' || reportType === 'financial') && metrics && (
      <Grid>
          {(reportType === 'overview' || reportType === 'pharmacies') && (
        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
          <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #00B5FF15, #00B5FF05)' }}>
            <Group justify="space-between">
              <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Pharmacies Totales
                </Text>
                    <Title order={3} mt="xs">
                      <NumberFormatter value={metrics.totalPharmacies} thousandSeparator=" " />
                    </Title>
              </div>
                  <IconBuilding size={36} style={{ opacity: 0.2 }} />
            </Group>
          </Card>
        </Grid.Col>
          )}

          {(reportType === 'overview' || reportType === 'pharmacies') && (
        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
              <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #00B5FF15, #00B5FF05)' }}>
            <Group justify="space-between">
              <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Pharmacies Actives (30j)
                </Text>
                    <Title order={3} mt="xs">
                      <NumberFormatter value={metrics.activePharmacies} thousandSeparator=" " />
                    </Title>
              </div>
                  <IconUsers size={36} style={{ opacity: 0.2 }} />
            </Group>
          </Card>
        </Grid.Col>
          )}

          {(reportType === 'overview' || reportType === 'transactions') && (
        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
              <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #00B5FF15, #00B5FF05)' }}>
            <Group justify="space-between">
              <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Transactions Totales
                </Text>
                    <Title order={3} mt="xs">
                      <NumberFormatter value={metrics.totalTransactions} thousandSeparator=" " />
                    </Title>
              </div>
                  <IconShoppingCart size={36} style={{ opacity: 0.2 }} />
            </Group>
          </Card>
        </Grid.Col>
          )}

          {(reportType === 'overview' || reportType === 'financial') && (
        <Grid.Col span={{ base: 12, sm: 6, lg: 3 }}>
              <Card withBorder p="lg" style={{ background: 'linear-gradient(135deg, #00B5FF15, #00B5FF05)' }}>
            <Group justify="space-between">
              <div>
                    <Text size="xs" tt="uppercase" fw={700} c="dimmed">
                      Chiffre d'affaires Total
                </Text>
                    <Title order={3} mt="xs">
                      <NumberFormatter value={metrics.totalRevenue} thousandSeparator=" " prefix="DHS " />
                    </Title>
              </div>
                  <IconCurrencyDollar size={36} style={{ opacity: 0.2 }} />
            </Group>
          </Card>
        </Grid.Col>
          )}
      </Grid>
      )}

        {/* Top Pharmacies */}
      {(reportType === 'overview' || reportType === 'pharmacies') && topPharmaciesData.length > 0 && (
        <Paper p="md" withBorder>
          <Title order={3} mb="md">
            Top Pharmacies
          </Title>
          <Table striped highlightOnHover withTableBorder withColumnBorders>
              <Table.Thead>
                <Table.Tr>
                <Table.Th>Nom de la Pharmacie</Table.Th>
                  <Table.Th>Transactions</Table.Th>
                <Table.Th>Chiffre d'affaires</Table.Th>
                </Table.Tr>
              </Table.Thead>
              <Table.Tbody>
              {topPharmaciesData.map((pharmacy) => (
                  <Table.Tr key={pharmacy.id}>
                  <Table.Td>{pharmacy.name}</Table.Td>
                  <Table.Td>{pharmacy.transactions}</Table.Td>
                    <Table.Td>
                    <NumberFormatter value={pharmacy.revenue} thousandSeparator=" " prefix="DHS " />
                    </Table.Td>
                  </Table.Tr>
                ))}
              </Table.Tbody>
            </Table>
          </Paper>
      )}

      {/* Category Statistics */}
      {(reportType === 'overview' || reportType === 'products') && categoryStatsData.length > 0 && (
        <Paper p="md" withBorder>
          <Title order={3} mb="md">
            Statistiques par Catégorie
          </Title>
          <Grid>
            <Grid.Col span={{ base: 12, sm: 6 }}>
              <Center>
                <RingProgress
                  size={200}
                  thickness={20}
                  sections={categoryStatsData.map(cat => ({ value: cat.percentage, color: '#00B5FF' }))}
                  label={
                    <Text c="#00B5FF" fw={700} ta="center" size="xl">
                      Distribution
                    </Text>
                  }
                />
              </Center>
        </Grid.Col>
            <Grid.Col span={{ base: 12, sm: 6 }}>
              <Stack>
                {categoryStatsData.map((stat, index) => (
                  <Group key={stat.category} justify="space-between">
                    <Text>{stat.category}</Text>
                    <Group gap="xs">
                      <Badge size="lg" variant="light" color="#00B5FF">
                        {stat.transactions} transactions
                      </Badge>
                      <Badge size="lg" variant="filled" color="#00B5FF">
                        <NumberFormatter value={stat.percentage} suffix="%" decimalScale={2} />
                      </Badge>
                    </Group>
                  </Group>
              ))}
            </Stack>
        </Grid.Col>
      </Grid>
        </Paper>
      )}
    </Stack>
  );
}
