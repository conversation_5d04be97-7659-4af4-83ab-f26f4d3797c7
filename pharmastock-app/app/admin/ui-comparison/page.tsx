"use client";

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Card as MantineCard,
  Title,
  Text,
  Group,
  Stack,
  Button as MantineButton,
  Badge as MantineBadge,
  Grid,
  Paper,
} from '@mantine/core';
import { IconPill, IconTrendingUp, IconUsers, IconPackage } from '@tabler/icons-react';

export default function UIComparisonPage() {
  const [activeTab, setActiveTab] = useState<'shadcn' | 'mantine'>('shadcn');

  const sampleData = {
    stats: [
      { label: 'Total Pharmacies', value: '1,234', icon: IconPill, trend: '+12%' },
      { label: 'Active Users', value: '5,678', icon: IconUsers, trend: '+8%' },
      { label: 'Products Listed', value: '12,345', icon: IconPackage, trend: '+15%' },
      { label: 'Revenue Growth', value: '23%', icon: IconTrendingUp, trend: '+5%' },
    ],
  };

  return (
    <div className="space-y-8">
      {/* Header */}
      <div>
        <h1 className="text-3xl font-bold text-[#00B5FF]">UI Framework Comparison</h1>
        <p className="text-muted-foreground mt-2">
          Compare ShadCN UI vs Mantine UI components side by side
        </p>
      </div>

      {/* Toggle */}
      <div className="flex gap-2">
        <Button
          variant={activeTab === 'shadcn' ? 'default' : 'outline'}
          onClick={() => setActiveTab('shadcn')}
        >
          ShadCN UI
        </Button>
        <Button
          variant={activeTab === 'mantine' ? 'default' : 'outline'}
          onClick={() => setActiveTab('mantine')}
        >
          Mantine UI
        </Button>
      </div>

      {/* ShadCN UI Example */}
      {activeTab === 'shadcn' && (
        <div className="space-y-6">
          <div>
            <h2 className="text-2xl font-semibold mb-4">ShadCN UI Components</h2>
            <p className="text-muted-foreground mb-6">
              Clean, modern, and highly customizable components with sharp edges
            </p>
          </div>

          {/* Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {sampleData.stats.map((stat, index) => (
              <Card key={index} className="border-0 shadow-sm hover:shadow-md transition-shadow">
                <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                  <CardTitle className="text-sm font-medium text-muted-foreground">
                    {stat.label}
                  </CardTitle>
                  <stat.icon className="h-4 w-4 text-[#00B5FF]" />
                </CardHeader>
                <CardContent>
                  <div className="text-2xl font-bold text-[#00B5FF]">{stat.value}</div>
                  <div className="flex items-center space-x-2 text-xs text-muted-foreground">
                    <Badge variant="secondary" className="text-green-600 bg-green-50">
                      {stat.trend}
                    </Badge>
                    <span>from last month</span>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* Sample Dashboard Card */}
          <Card className="border-0 shadow-sm">
            <CardHeader>
              <CardTitle className="text-[#00B5FF]">Recent Activity</CardTitle>
              <CardDescription>
                Latest transactions and system updates
              </CardDescription>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">New pharmacy registration</p>
                  <p className="text-sm text-muted-foreground">Pharmacie Atlas - Casablanca</p>
                </div>
                <Badge>Pending</Badge>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">Urgent request fulfilled</p>
                  <p className="text-sm text-muted-foreground">Insulin delivery completed</p>
                </div>
                <Badge variant="secondary">Completed</Badge>
              </div>
              <div className="flex items-center justify-between p-4 border rounded-lg">
                <div>
                  <p className="font-medium">System maintenance</p>
                  <p className="text-sm text-muted-foreground">Scheduled for tonight at 2 AM</p>
                </div>
                <Badge variant="outline">Scheduled</Badge>
              </div>
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <div className="flex gap-4">
            <Button className="bg-[#00B5FF] hover:bg-[#0099E6]">
              Primary Action
            </Button>
            <Button variant="outline">
              Secondary Action
            </Button>
            <Button variant="ghost">
              Tertiary Action
            </Button>
          </div>
        </div>
      )}

      {/* Mantine UI Example */}
      {activeTab === 'mantine' && (
        <Stack gap="lg">
          <div>
            <Title order={2} mb="sm">Mantine UI Components</Title>
            <Text c="dimmed" mb="lg">
              Comprehensive component library with built-in theming
            </Text>
          </div>

          {/* Stats Grid */}
          <Grid>
            {sampleData.stats.map((stat, index) => (
              <Grid.Col key={index} span={{ base: 12, sm: 6, lg: 3 }}>
                <MantineCard withBorder p="lg" style={{ background: 'linear-gradient(135deg, #00B5FF15, #00B5FF05)' }}>
                  <Group justify="space-between" mb="xs">
                    <Text size="sm" c="dimmed" fw={500}>{stat.label}</Text>
                    <stat.icon size={20} color="#00B5FF" opacity={0.6} />
                  </Group>
                  <Text size="xl" fw={700} c="#00B5FF">{stat.value}</Text>
                  <Group gap="xs" mt="xs">
                    <MantineBadge color="green" variant="light" size="sm">
                      {stat.trend}
                    </MantineBadge>
                    <Text size="xs" c="dimmed">from last month</Text>
                  </Group>
                </MantineCard>
              </Grid.Col>
            ))}
          </Grid>

          {/* Sample Dashboard Card */}
          <MantineCard withBorder p="lg">
            <Title order={3} c="#00B5FF" mb="xs">Recent Activity</Title>
            <Text c="dimmed" mb="lg">
              Latest transactions and system updates
            </Text>
            <Stack gap="md">
              <Paper p="md" withBorder>
                <Group justify="space-between">
                  <div>
                    <Text fw={500}>New pharmacy registration</Text>
                    <Text size="sm" c="dimmed">Pharmacie Atlas - Casablanca</Text>
                  </div>
                  <MantineBadge color="orange">Pending</MantineBadge>
                </Group>
              </Paper>
              <Paper p="md" withBorder>
                <Group justify="space-between">
                  <div>
                    <Text fw={500}>Urgent request fulfilled</Text>
                    <Text size="sm" c="dimmed">Insulin delivery completed</Text>
                  </div>
                  <MantineBadge color="green">Completed</MantineBadge>
                </Group>
              </Paper>
              <Paper p="md" withBorder>
                <Group justify="space-between">
                  <div>
                    <Text fw={500}>System maintenance</Text>
                    <Text size="sm" c="dimmed">Scheduled for tonight at 2 AM</Text>
                  </div>
                  <MantineBadge variant="outline">Scheduled</MantineBadge>
                </Group>
              </Paper>
            </Stack>
          </MantineCard>

          {/* Action Buttons */}
          <Group gap="md">
            <MantineButton color="#00B5FF">
              Primary Action
            </MantineButton>
            <MantineButton variant="outline" color="#00B5FF">
              Secondary Action
            </MantineButton>
            <MantineButton variant="subtle" color="#00B5FF">
              Tertiary Action
            </MantineButton>
          </Group>
        </Stack>
      )}

      {/* Comparison Notes */}
      <Card className="border-0 shadow-sm bg-slate-50">
        <CardHeader>
          <CardTitle>Key Differences</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <h4 className="font-semibold text-[#00B5FF] mb-2">ShadCN UI Advantages:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>More professional and modern appearance</li>
              <li>Sharp, clean edges that look more enterprise-ready</li>
              <li>Better customization with Tailwind CSS</li>
              <li>Smaller bundle size (copy what you need)</li>
              <li>More aligned with current design trends</li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold text-[#00B5FF] mb-2">Mantine UI Advantages:</h4>
            <ul className="list-disc list-inside space-y-1 text-sm text-muted-foreground">
              <li>More comprehensive component library</li>
              <li>Built-in theming system</li>
              <li>TypeScript-first approach</li>
              <li>Faster development with pre-built components</li>
              <li>Good documentation and examples</li>
            </ul>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
