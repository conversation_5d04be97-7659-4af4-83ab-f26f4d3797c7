"use client";

import { useState, useEffect, useCallback } from 'react';
import { useSupabase } from '@/contexts/supabase-context';
import { useAuth } from '@/contexts/auth-context';
import { redirect } from 'next/navigation';
import {
  Grid,
  Card,
  Text,
  Title,
  Group,
  Progress,
  SimpleGrid,
  Paper,
  ThemeIcon,
  RingProgress,
  Center,
  Loader,
  Alert
} from '@mantine/core';
import {
  IconUsers,
  IconBuilding,
  IconShoppingCart,
  IconAlertTriangle,
  IconTrendingUp,
  IconClock,
  IconCheck,
  IconExclamationMark
} from '@tabler/icons-react';

interface AdminStats {
  totalPharmacies: number;
  totalUsers: number;
  totalTransactions: number;
  urgentRequests: number;
  verifiedPharmacies: number;
  pendingPharmacies: number;
  activeListings: number;
  monthlyGrowth: number;
}

interface StatCardProps {
  title: string;
  value: string | number;
  icon: React.ReactNode;
  color: string;
  description?: string;
  trend?: number;
}

function StatCard({ title, value, icon, color, description, trend }: StatCardProps) {
  return (
    <Paper withBorder p="md" radius="md">
      <Group justify="space-between">
        <div>
          <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
            {title}
          </Text>
          <Text fw={700} fz="xl">
            {value}
          </Text>
          {description && (
            <Text c="dimmed" fz="sm">
              {description}
            </Text>
          )}
          {trend && (
            <Group gap={4} mt={5}>
              <IconTrendingUp size={16} color={trend > 0 ? 'green' : 'red'} />
              <Text c={trend > 0 ? 'green' : 'red'} fz="sm" fw={500}>
                {trend > 0 ? '+' : ''}{trend}%
              </Text>
            </Group>
          )}
        </div>
        <ThemeIcon color={color} variant="light" size={38} radius="md">
          {icon}
        </ThemeIcon>
      </Group>
    </Paper>
  );
}

export default function AdminDashboard() {
  const { supabase } = useSupabase();
  const { user, loading } = useAuth();
  const [stats, setStats] = useState<AdminStats>({
    totalPharmacies: 0,
    totalUsers: 0,
    totalTransactions: 0,
    urgentRequests: 0,
    verifiedPharmacies: 0,
    pendingPharmacies: 0,
    activeListings: 0,
    monthlyGrowth: 0
  });
  const [loadingStats, setLoadingStats] = useState(true);
  const [error, setError] = useState<string | null>(null);

  if (!loading && user?.role !== 'super_admin') {
    redirect('/unauthorized');
    return null;
  }

  const fetchAdminStats = useCallback(async () => {
    try {
      setLoadingStats(true);
      setError(null);

      // Fetch stats from admin API
      const response = await fetch('/api/admin/stats');
      if (!response.ok) {
        throw new Error('Failed to fetch admin stats');
      }

      const data = await response.json();
      setStats(data.stats);
    } catch (error) {
      console.error('Error fetching admin stats:', error);
      setError(error instanceof Error ? error.message : 'Failed to fetch dashboard data');
    } finally {
      setLoadingStats(false);
    }
  }, []);

  useEffect(() => {
    fetchAdminStats();
  }, [fetchAdminStats]);

  const verificationRate = stats.totalPharmacies > 0
    ? Math.round((stats.verifiedPharmacies / stats.totalPharmacies) * 100)
    : 0;

  if (loadingStats) {
    return (
      <div>
        <Title order={1} mb="xl">Admin Dashboard</Title>
        <Center h={200}>
          <Loader size="lg" />
        </Center>
      </div>
    );
  }

  if (error) {
    return (
      <div>
        <Title order={1} mb="xl">Admin Dashboard</Title>
        <Alert
          icon={<IconExclamationMark size={16} />}
          title="Error loading dashboard"
          color="red"
          mb="xl"
        >
          {error}
        </Alert>
      </div>
    );
  }

  return (
    <div>
      <Title order={1} mb="xl">Admin Dashboard</Title>

      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }} spacing="md" mb="xl">
        <StatCard
          title="Total Pharmacies"
          value={stats.totalPharmacies}
          icon={<IconBuilding size={18} />}
          color="blue"
          trend={8.2}
        />

        <StatCard
          title="Total Users"
          value={stats.totalUsers}
          icon={<IconUsers size={18} />}
          color="green"
          trend={15.3}
        />

        <StatCard
          title="Active Listings"
          value={stats.activeListings}
          icon={<IconShoppingCart size={18} />}
          color="violet"
          trend={-2.1}
        />

        <StatCard
          title="Urgent Requests"
          value={stats.urgentRequests}
          icon={<IconAlertTriangle size={18} />}
          color="red"
          description="Requires attention"
        />
      </SimpleGrid>

      <Grid>
        <Grid.Col span={{ base: 12, md: 8 }}>
          <Card withBorder radius="md" p="xl">
            <Title order={3} mb="md">Pharmacy Verification Status</Title>
            <Group justify="space-between" mb="xs">
              <Text fz="sm" c="dimmed">
                Verification Progress
              </Text>
              <Text fz="sm" c="dimmed">
                {verificationRate}%
              </Text>
            </Group>
            <Progress value={verificationRate} mb="md" />

            <Group justify="space-between">
              <div>
                <Group gap="xs">
                  <ThemeIcon color="green" variant="light" size="sm">
                    <IconCheck size={12} />
                  </ThemeIcon>
                  <Text fz="sm">Verified: {stats.verifiedPharmacies}</Text>
                </Group>
              </div>
              <div>
                <Group gap="xs">
                  <ThemeIcon color="orange" variant="light" size="sm">
                    <IconClock size={12} />
                  </ThemeIcon>
                  <Text fz="sm">Pending: {stats.pendingPharmacies}</Text>
                </Group>
              </div>
            </Group>
          </Card>
        </Grid.Col>

        <Grid.Col span={{ base: 12, md: 4 }}>
          <Card withBorder radius="md" p="xl">
            <Title order={3} mb="md">System Health</Title>
            <Center>
              <RingProgress
                size={120}
                thickness={12}
                sections={[
                  { value: 85, color: 'green' },
                  { value: 10, color: 'yellow' },
                  { value: 5, color: 'red' },
                ]}
                label={
                  <Center>
                    <div>
                      <Text ta="center" fz="lg" fw={700}>
                        85%
                      </Text>
                      <Text ta="center" fz="xs" c="dimmed">
                        Healthy
                      </Text>
                    </div>
                  </Center>
                }
              />
            </Center>
          </Card>
        </Grid.Col>
      </Grid>
    </div>
  );
}
