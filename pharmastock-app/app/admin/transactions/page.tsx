"use client";

import { useState, useEffect } from 'react';
import { useSupabase } from '@/contexts/supabase-context';
import { useAuth } from '@/contexts/auth-context';
import { redirect } from 'next/navigation';
import {
  Title,
  Card,
  Table,
  Group,
  Text,
  Badge,
  TextInput,
  Select,
  Button,
  Stack,
  Pagination,
  ActionIcon,
  Tooltip,
  Paper,
  SimpleGrid,
  ThemeIcon
} from '@mantine/core';
import {
  IconSearch,
  IconFilter,
  IconDownload,
  IconEye,
  IconCreditCard,
  IconTrendingUp,
  IconClock,
  IconCheck
} from '@tabler/icons-react';
// import { DatePickerInput } from '@mantine/dates';

interface Transaction {
  id: string;
  type: 'purchase' | 'sale' | 'transfer' | 'refund';
  amount: number;
  currency: string;
  status: 'pending' | 'completed' | 'failed' | 'cancelled';
  pharmacy_name: string;
  user_email: string;
  product_name?: string;
  created_at: string;
  updated_at: string;
}

interface TransactionStats {
  totalTransactions: number;
  totalAmount: number;
  pendingTransactions: number;
  completedTransactions: number;
}

export default function AdminTransactionsPage() {
  const { supabase } = useSupabase();
  const { user, loading } = useAuth();
  if (!loading && user?.role !== 'super_admin') {
    redirect('/unauthorized');
    return null;
  }
  const [transactions, setTransactions] = useState<Transaction[]>([]);
  const [stats, setStats] = useState<TransactionStats>({
    totalTransactions: 0,
    totalAmount: 0,
    pendingTransactions: 0,
    completedTransactions: 0
  });
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [typeFilter, setTypeFilter] = useState<string>('all');
  // const [dateRange, setDateRange] = useState<[Date | null, Date | null]>([null, null]);
  const [currentPage, setCurrentPage] = useState(1);
  const itemsPerPage = 20;
  const [transactionsLoading, setTransactionsLoading] = useState(false);

  useEffect(() => {
    fetchTransactions();
    fetchStats();
  }, [currentPage, searchTerm, statusFilter, typeFilter]);

  const fetchTransactions = async () => {
    try {
      setTransactionsLoading(true);

      // This would be a real query to your transactions table
      // For now, we'll create mock data
      const mockTransactions: Transaction[] = [
        {
          id: '1',
          type: 'purchase',
          amount: 125.50,
          currency: 'EUR',
          status: 'completed',
          pharmacy_name: 'Pharmacie Central',
          user_email: '<EMAIL>',
          product_name: 'Paracetamol 500mg',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        },
        {
          id: '2',
          type: 'sale',
          amount: 89.99,
          currency: 'EUR',
          status: 'pending',
          pharmacy_name: 'Pharmacie du Marché',
          user_email: '<EMAIL>',
          product_name: 'Ibuprofen 400mg',
          created_at: new Date(Date.now() - 86400000).toISOString(),
          updated_at: new Date(Date.now() - 86400000).toISOString()
        }
      ];

      setTransactions(mockTransactions);
    } catch (error) {
      console.error('Error fetching transactions:', error);
    } finally {
      setTransactionsLoading(false);
    }
  };

  const fetchStats = async () => {
    try {
      // Mock stats - replace with real data
      setStats({
        totalTransactions: 1247,
        totalAmount: 45678.90,
        pendingTransactions: 23,
        completedTransactions: 1224
      });
    } catch (error) {
      console.error('Error fetching stats:', error);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'green';
      case 'pending': return 'yellow';
      case 'failed': return 'red';
      case 'cancelled': return 'gray';
      default: return 'blue';
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case 'purchase': return 'blue';
      case 'sale': return 'green';
      case 'transfer': return 'violet';
      case 'refund': return 'orange';
      default: return 'gray';
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('fr-FR', {
      style: 'currency',
      currency: currency
    }).format(amount);
  };

  const filteredTransactions = transactions.filter(transaction => {
    const matchesSearch =
      transaction.pharmacy_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.user_email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      transaction.product_name?.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = statusFilter === 'all' || transaction.status === statusFilter;
    const matchesType = typeFilter === 'all' || transaction.type === typeFilter;

    return matchesSearch && matchesStatus && matchesType;
  });

  return (
    <Stack gap="xl">
      <Group justify="space-between">
        <div>
          <Title order={1}>Transaction Management</Title>
          <Text c="dimmed" mt="xs">
            Monitor and manage all system transactions
          </Text>
        </div>
        <Group>
          <Button leftSection={<IconDownload size={16} />} variant="light">
            Export
          </Button>
        </Group>
      </Group>

      {/* Stats Cards */}
      <SimpleGrid cols={{ base: 1, sm: 2, lg: 4 }}>
        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Total Transactions
              </Text>
              <Text fw={700} fz="xl">
                {stats.totalTransactions.toLocaleString()}
              </Text>
            </div>
            <ThemeIcon color="blue" variant="light" size={38} radius="md">
              <IconCreditCard size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Total Amount
              </Text>
              <Text fw={700} fz="xl">
                {formatCurrency(stats.totalAmount, 'EUR')}
              </Text>
            </div>
            <ThemeIcon color="green" variant="light" size={38} radius="md">
              <IconTrendingUp size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Pending
              </Text>
              <Text fw={700} fz="xl">
                {stats.pendingTransactions}
              </Text>
            </div>
            <ThemeIcon color="yellow" variant="light" size={38} radius="md">
              <IconClock size={18} />
            </ThemeIcon>
          </Group>
        </Paper>

        <Paper withBorder p="md" radius="md">
          <Group justify="space-between">
            <div>
              <Text c="dimmed" tt="uppercase" fw={700} fz="xs">
                Completed
              </Text>
              <Text fw={700} fz="xl">
                {stats.completedTransactions}
              </Text>
            </div>
            <ThemeIcon color="green" variant="light" size={38} radius="md">
              <IconCheck size={18} />
            </ThemeIcon>
          </Group>
        </Paper>
      </SimpleGrid>

      {/* Filters */}
      <Card withBorder>
        <Group>
          <TextInput
            placeholder="Search transactions..."
            leftSection={<IconSearch size={16} />}
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.currentTarget.value)}
            style={{ flex: 1 }}
          />
          <Select
            placeholder="Status"
            data={[
              { value: 'all', label: 'All Status' },
              { value: 'pending', label: 'Pending' },
              { value: 'completed', label: 'Completed' },
              { value: 'failed', label: 'Failed' },
              { value: 'cancelled', label: 'Cancelled' }
            ]}
            value={statusFilter}
            onChange={(value) => setStatusFilter(value || 'all')}
          />
          <Select
            placeholder="Type"
            data={[
              { value: 'all', label: 'All Types' },
              { value: 'purchase', label: 'Purchase' },
              { value: 'sale', label: 'Sale' },
              { value: 'transfer', label: 'Transfer' },
              { value: 'refund', label: 'Refund' }
            ]}
            value={typeFilter}
            onChange={(value) => setTypeFilter(value || 'all')}
          />
        </Group>
      </Card>

      {/* Transactions Table */}
      <Card withBorder>
        <Table striped highlightOnHover>
          <Table.Thead>
            <Table.Tr>
              <Table.Th>Transaction ID</Table.Th>
              <Table.Th>Type</Table.Th>
              <Table.Th>Amount</Table.Th>
              <Table.Th>Pharmacy</Table.Th>
              <Table.Th>User</Table.Th>
              <Table.Th>Product</Table.Th>
              <Table.Th>Status</Table.Th>
              <Table.Th>Date</Table.Th>
              <Table.Th>Actions</Table.Th>
            </Table.Tr>
          </Table.Thead>
          <Table.Tbody>
            {filteredTransactions.map((transaction) => (
              <Table.Tr key={transaction.id}>
                <Table.Td>
                  <Text size="sm" fw={500}>#{transaction.id}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge color={getTypeColor(transaction.type)} variant="light">
                    {transaction.type}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text fw={600}>
                    {formatCurrency(transaction.amount, transaction.currency)}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{transaction.pharmacy_name}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{transaction.user_email}</Text>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">{transaction.product_name || '-'}</Text>
                </Table.Td>
                <Table.Td>
                  <Badge color={getStatusColor(transaction.status)} variant="light">
                    {transaction.status}
                  </Badge>
                </Table.Td>
                <Table.Td>
                  <Text size="sm">
                    {new Date(transaction.created_at).toLocaleDateString()}
                  </Text>
                </Table.Td>
                <Table.Td>
                  <Group gap="xs">
                    <Tooltip label="View Details">
                      <ActionIcon variant="subtle" color="blue">
                        <IconEye size={16} />
                      </ActionIcon>
                    </Tooltip>
                  </Group>
                </Table.Td>
              </Table.Tr>
            ))}
          </Table.Tbody>
        </Table>

        <Group justify="center" mt="md">
          <Pagination
            value={currentPage}
            onChange={setCurrentPage}
            total={Math.ceil(filteredTransactions.length / itemsPerPage)}
          />
        </Group>
      </Card>
    </Stack>
  );
}
