"use client";

import { useEffect, useState } from "react";
import { useAuth } from "@/contexts/auth-context";
import { adminMockServices } from "@/services/mock/admin";
import { Card } from "@/components/ui/card";
import { Switch } from "@/components/ui/switch";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { redirect } from "next/navigation";
import { Settings2, Bell, Shield, Database, Store } from "lucide-react";

interface Settings {
  system_settings: {
    maintenance_mode: boolean;
    registration_enabled: boolean;
    max_failed_login_attempts: number;
    session_timeout_minutes: number;
    password_expiry_days: number;
    require_2fa_for_admin: boolean;
  };
  notification_settings: {
    email_notifications: boolean;
    urgent_request_notifications: boolean;
    system_alert_notifications: boolean;
    maintenance_notifications: boolean;
    notification_email: string;
  };
  security_settings: {
    ip_whitelist: string[];
    allowed_domains: string[];
    minimum_password_length: number;
    require_special_characters: boolean;
    require_numbers: boolean;
    require_uppercase: boolean;
  };
  backup_settings: {
    auto_backup_enabled: boolean;
    backup_frequency: string;
    backup_retention_days: number;
    backup_time: string;
    include_attachments: boolean;
  };
  marketplace_settings: {
    max_listing_duration_days: number;
    min_price: number;
    max_price: number;
    allowed_categories: string[];
    require_expiry_date: boolean;
    min_expiry_months: number;
  };
  verification_settings: {
    auto_verify_trusted_domains: boolean;
    require_license_verification: boolean;
    verification_expiry_days: number;
    allowed_license_formats: string[];
    max_verification_attempts: number;
  };
}

export default function SettingsPage() {
  const { user, loading } = useAuth();
  const [settings, setSettings] = useState<Settings | null>(null);
  const [isLoading, setIsLoading] = useState(true);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const data = await adminMockServices.settings.getAll();
        setSettings(data);
      } catch (error) {
        console.error("Error fetching settings:", error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSettings();
  }, []);

  if (!loading && user?.role !== 'super_admin') {
    redirect('/unauthorized');
    return null;
  }

  // Show loading state while checking session
  if (loading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading...</p>
        </div>
      </div>
    );
  }

  if (isLoading || !settings) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto"></div>
          <p className="mt-4 text-muted-foreground">Loading settings...</p>
        </div>
      </div>
    );
  }

  const handleSettingChange = async (section: string, key: string, value: any) => {
    if (!settings) return;

    const newSettings = { ...settings };
    // @ts-ignore
    newSettings[section][key] = value;
    setSettings(newSettings);

    try {
      await adminMockServices.settings.update(section, { [key]: value });
    } catch (error) {
      console.error("Error updating setting:", error);
      // Revert the change on error
      setSettings(settings);
    }
  };

  return (
    <div className="container mx-auto py-10">
      <h1 className="text-3xl font-bold mb-8">System Settings</h1>

      <div className="space-y-6">
        {/* System Settings */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Settings2 className="h-5 w-5" />
            <h2 className="text-xl font-semibold">System Settings</h2>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Maintenance Mode</p>
                <p className="text-sm text-muted-foreground">
                  Enable maintenance mode to prevent user access
                </p>
              </div>
              <Switch
                checked={settings.system_settings.maintenance_mode}
                onCheckedChange={(value) =>
                  handleSettingChange("system_settings", "maintenance_mode", value)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Registration</p>
                <p className="text-sm text-muted-foreground">Allow new pharmacy registrations</p>
              </div>
              <Switch
                checked={settings.system_settings.registration_enabled}
                onCheckedChange={(value) =>
                  handleSettingChange("system_settings", "registration_enabled", value)
                }
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium mb-2">Max Failed Login Attempts</p>
                <Input
                  type="number"
                  value={settings.system_settings.max_failed_login_attempts}
                  onChange={(e) =>
                    handleSettingChange(
                      "system_settings",
                      "max_failed_login_attempts",
                      parseInt(e.target.value)
                    )
                  }
                />
              </div>
              <div>
                <p className="font-medium mb-2">Session Timeout (minutes)</p>
                <Input
                  type="number"
                  value={settings.system_settings.session_timeout_minutes}
                  onChange={(e) =>
                    handleSettingChange(
                      "system_settings",
                      "session_timeout_minutes",
                      parseInt(e.target.value)
                    )
                  }
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Notification Settings */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Bell className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Notification Settings</h2>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Email Notifications</p>
                <p className="text-sm text-muted-foreground">
                  Receive notifications via email
                </p>
              </div>
              <Switch
                checked={settings.notification_settings.email_notifications}
                onCheckedChange={(value) =>
                  handleSettingChange("notification_settings", "email_notifications", value)
                }
              />
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Urgent Request Notifications</p>
                <p className="text-sm text-muted-foreground">
                  Get notified about urgent requests
                </p>
              </div>
              <Switch
                checked={settings.notification_settings.urgent_request_notifications}
                onCheckedChange={(value) =>
                  handleSettingChange(
                    "notification_settings",
                    "urgent_request_notifications",
                    value
                  )
                }
              />
            </div>
            <div>
              <p className="font-medium mb-2">Notification Email</p>
              <Input
                type="email"
                value={settings.notification_settings.notification_email}
                onChange={(e) =>
                  handleSettingChange(
                    "notification_settings",
                    "notification_email",
                    e.target.value
                  )
                }
              />
            </div>
          </div>
        </Card>

        {/* Security Settings */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Shield className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Security Settings</h2>
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium mb-2">Minimum Password Length</p>
                <Input
                  type="number"
                  value={settings.security_settings.minimum_password_length}
                  onChange={(e) =>
                    handleSettingChange(
                      "security_settings",
                      "minimum_password_length",
                      parseInt(e.target.value)
                    )
                  }
                />
              </div>
              <div>
                <p className="font-medium mb-2">Allowed Domains</p>
                <Input
                  value={settings.security_settings.allowed_domains.join(", ")}
                  onChange={(e) =>
                    handleSettingChange(
                      "security_settings",
                      "allowed_domains",
                      e.target.value.split(", ")
                    )
                  }
                />
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Require Special Characters</p>
                <p className="text-sm text-muted-foreground">
                  Enforce special characters in passwords
                </p>
              </div>
              <Switch
                checked={settings.security_settings.require_special_characters}
                onCheckedChange={(value) =>
                  handleSettingChange("security_settings", "require_special_characters", value)
                }
              />
            </div>
          </div>
        </Card>

        {/* Backup Settings */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Database className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Backup Settings</h2>
          </div>
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Auto Backup</p>
                <p className="text-sm text-muted-foreground">
                  Enable automatic system backups
                </p>
              </div>
              <Switch
                checked={settings.backup_settings.auto_backup_enabled}
                onCheckedChange={(value) =>
                  handleSettingChange("backup_settings", "auto_backup_enabled", value)
                }
              />
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium mb-2">Backup Frequency</p>
                <Input
                  value={settings.backup_settings.backup_frequency}
                  onChange={(e) =>
                    handleSettingChange("backup_settings", "backup_frequency", e.target.value)
                  }
                />
              </div>
              <div>
                <p className="font-medium mb-2">Retention Days</p>
                <Input
                  type="number"
                  value={settings.backup_settings.backup_retention_days}
                  onChange={(e) =>
                    handleSettingChange(
                      "backup_settings",
                      "backup_retention_days",
                      parseInt(e.target.value)
                    )
                  }
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Marketplace Settings */}
        <Card className="p-6">
          <div className="flex items-center gap-2 mb-4">
            <Store className="h-5 w-5" />
            <h2 className="text-xl font-semibold">Marketplace Settings</h2>
          </div>
          <div className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <p className="font-medium mb-2">Max Listing Duration (days)</p>
                <Input
                  type="number"
                  value={settings.marketplace_settings.max_listing_duration_days}
                  onChange={(e) =>
                    handleSettingChange(
                      "marketplace_settings",
                      "max_listing_duration_days",
                      parseInt(e.target.value)
                    )
                  }
                />
              </div>
              <div>
                <p className="font-medium mb-2">Min Expiry Months</p>
                <Input
                  type="number"
                  value={settings.marketplace_settings.min_expiry_months}
                  onChange={(e) =>
                    handleSettingChange(
                      "marketplace_settings",
                      "min_expiry_months",
                      parseInt(e.target.value)
                    )
                  }
                />
              </div>
            </div>
            <div className="flex items-center justify-between">
              <div>
                <p className="font-medium">Require Expiry Date</p>
                <p className="text-sm text-muted-foreground">
                  Enforce expiry date for all listings
                </p>
              </div>
              <Switch
                checked={settings.marketplace_settings.require_expiry_date}
                onCheckedChange={(value) =>
                  handleSettingChange("marketplace_settings", "require_expiry_date", value)
                }
              />
            </div>
          </div>
        </Card>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button size="lg">Save All Changes</Button>
        </div>
      </div>
    </div>
  );
}