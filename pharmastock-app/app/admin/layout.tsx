"use client";

import {
  <PERSON><PERSON><PERSON><PERSON>,
  Burger,
  Group,
  Text,
  ActionIcon,
  Container,
  Box,
  Avatar,
  Menu,
  UnstyledButton,
  rem,
  Di<PERSON>r,
  Badge,
  Indicator,
} from '@mantine/core';
import { useDisclosure } from '@mantine/hooks';
import {
  IconBell,
  IconSearch,
  IconUser,
  IconSettings,
  IconLogout,
  IconChevronDown,
  IconShield,
  IconPill,
} from '@tabler/icons-react';
import { AdminSidebar } from "@/components/admin/AdminSidebar";
import { AccessControl } from "@/components/auth/AccessControl";
import { useAuth } from "@/contexts/auth-context";

export default function AdminLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const [opened, { toggle }] = useDisclosure();
  const { user, signOut } = useAuth();

  // Only allow super_admin to access admin layout
  if (!user || user.role !== 'super_admin') {
    if (typeof window !== 'undefined') {
      window.location.href = '/unauthorized';
    }
    return null;
  }

  const handleLogout = async () => {
    try {
      await signOut();
      window.location.href = '/auth/login';
    } catch (error) {
      console.error('Error during logout:', error);
    }
  };

  return (
    <AccessControl>
      <AppShell
        navbar={{
          width: 280,
          breakpoint: 'sm',
          collapsed: { mobile: !opened },
        }}
        header={{ height: 80 }}
        padding="md"
        styles={{
          header: {
            borderBottom: '1px solid rgba(0, 0, 0, 0.08)',
            backgroundColor: '#ffffff',
            boxShadow: '0 1px 3px rgba(0, 0, 0, 0.05)',
          },
          navbar: {
            borderRight: '1px solid rgba(0, 0, 0, 0.08)',
            backgroundColor: '#ffffff',
          },
          main: {
            backgroundColor: '#f8fafc',
          },
        }}
      >
        <AppShell.Header>
          <Group h="100%" px="lg" justify="space-between">
            {/* Left Section */}
            <Group>
              <Burger
                opened={opened}
                onClick={toggle}
                hiddenFrom="sm"
                size="sm"
                color="#00B5FF"
              />
              <Group gap="sm">
                <IconPill size={28} color="#00B5FF" />
                <Text
                  size="xl"
                  fw={700}
                  c="#00B5FF"
                  style={{ letterSpacing: '-0.5px' }}
                >
                  PharmaStock
                </Text>
                <Badge
                  variant="light"
                  color="#00B5FF"
                  size="sm"
                  style={{ textTransform: 'uppercase', letterSpacing: '0.5px' }}
                >
                  Admin
                </Badge>
              </Group>
            </Group>

            {/* Right Section */}
            <Group gap="md">
              {/* Search */}
              <ActionIcon
                variant="subtle"
                color="gray"
                size="lg"
                style={{
                  border: '1px solid rgba(0, 0, 0, 0.08)',
                  '&:hover': {
                    backgroundColor: 'rgba(0, 181, 255, 0.05)',
                    borderColor: 'rgba(0, 181, 255, 0.2)',
                  },
                }}
              >
                <IconSearch size={18} />
              </ActionIcon>

              {/* Notifications */}
              <Indicator inline color="red" size={8} offset={7}>
                <ActionIcon
                  variant="subtle"
                  color="gray"
                  size="lg"
                  style={{
                    border: '1px solid rgba(0, 0, 0, 0.08)',
                    '&:hover': {
                      backgroundColor: 'rgba(0, 181, 255, 0.05)',
                      borderColor: 'rgba(0, 181, 255, 0.2)',
                    },
                  }}
                >
                  <IconBell size={18} />
                </ActionIcon>
              </Indicator>

              {/* User Menu */}
              <Menu shadow="lg" width={200} position="bottom-end">
                <Menu.Target>
                  <UnstyledButton
                    style={{
                      padding: '8px 12px',
                      border: '1px solid rgba(0, 0, 0, 0.08)',
                      borderRadius: '4px',
                      transition: 'all 0.15s ease',
                      '&:hover': {
                        backgroundColor: 'rgba(0, 181, 255, 0.05)',
                        borderColor: 'rgba(0, 181, 255, 0.2)',
                      },
                    }}
                  >
                    <Group gap="sm">
                      <Avatar size="sm" color="#00B5FF" variant="light">
                        <IconShield size={16} />
                      </Avatar>
                      <div style={{ flex: 1 }}>
                        <Text size="sm" fw={600} c="dark">
                          Super Admin
                        </Text>
                        <Text size="xs" c="dimmed" truncate>
                          {user?.email}
                        </Text>
                      </div>
                      <IconChevronDown size={14} color="gray" />
                    </Group>
                  </UnstyledButton>
                </Menu.Target>

                <Menu.Dropdown>
                  <Menu.Label>Account</Menu.Label>
                  <Menu.Item leftSection={<IconUser size={14} />}>
                    Profile
                  </Menu.Item>
                  <Menu.Item leftSection={<IconSettings size={14} />}>
                    Settings
                  </Menu.Item>
                  <Menu.Divider />
                  <Menu.Item
                    leftSection={<IconLogout size={14} />}
                    color="red"
                    onClick={handleLogout}
                  >
                    Logout
                  </Menu.Item>
                </Menu.Dropdown>
              </Menu>
            </Group>
          </Group>
        </AppShell.Header>

        <AdminSidebar opened={opened} toggle={toggle} />

        <AppShell.Main>
          <Container size="xl" px="lg">
            <Box py="lg">
              {children}
            </Box>
          </Container>
        </AppShell.Main>
      </AppShell>
    </AccessControl>
  );
}