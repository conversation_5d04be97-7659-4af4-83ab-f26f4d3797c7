import "./globals.css";
import type { Metadata } from "next";
import { Inter } from "next/font/google";
import { ThemeProvider } from "@/components/theme-provider";
import { NotificationProvider } from "@/contexts/notification-context";
import { AuthProvider } from "@/contexts/auth-context";
import { SupabaseProvider } from "@/contexts/supabase-context";
import { DatabaseProvider } from "@/contexts/database-context";
import { MantineProvider } from "@/components/providers/MantineProvider";
import { Toaster } from "sonner";

const inter = Inter({ subsets: ["latin"] });

export const viewport = {
  themeColor: "#0f172a",
  width: "device-width",
  initialScale: 1,
  maximumScale: 1,
  userScalable: false,
};

export const metadata: Metadata = {
  title: "PharmaStock",
  description: "Gestion des stocks pharmaceutiques",
  manifest: "/manifest.json",
  appleWebApp: {
    capable: true,
    statusBarStyle: "default",
    title: "PharmaStock",
  },
  icons: {
    icon: "/icons/icon-512x512.png",
    shortcut: "/icons/icon-192x192.png",
    apple: "/icons/icon-192x192.png",
  },
};

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="fr" suppressHydrationWarning>
      <head>
        <link rel="manifest" href="/manifest.json" />
        <meta name="application-name" content="PharmaStock" />
        <meta name="apple-mobile-web-app-capable" content="yes" />
        <meta name="apple-mobile-web-app-status-bar-style" content="default" />
        <meta name="apple-mobile-web-app-title" content="PharmaStock" />
        <meta name="mobile-web-app-capable" content="yes" />
        <meta name="theme-color" content="#0f172a" />
      </head>
      <body className={inter.className}>
        <MantineProvider>
          <AuthProvider>
            <SupabaseProvider>
              <DatabaseProvider>
                <ThemeProvider
                  attribute="class"
                  defaultTheme="system"
                  enableSystem
                  disableTransitionOnChange
                >
                  <NotificationProvider>
                    {children}
                    <Toaster />
                  </NotificationProvider>
                </ThemeProvider>
              </DatabaseProvider>
            </SupabaseProvider>
          </AuthProvider>
        </MantineProvider>
      </body>
    </html>
  );
}
