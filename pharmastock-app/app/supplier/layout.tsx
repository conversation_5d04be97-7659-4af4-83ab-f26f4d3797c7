"use client";

import {
  AppShell as MantineAppShell,
  Group,
  Text,
  UnstyledButton,
  rem,
} from "@mantine/core";
import { IconLogout } from "@tabler/icons-react";
import { useAuth } from "@/contexts/auth-context";
import { Toaster } from "@/components/ui/sonner";

export default function SupplierLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  const { user, signOut } = useAuth();

  if (!user) return null;

  return (
    <MantineAppShell header={{ height: 60 }} padding="md">
      <MantineAppShell.Header>
        <Group h="100%" px="md" justify="space-between">
          <Group>
            <Text size="lg" fw={600} c="#00B5FF">
              PharmaStock - Fournisseur
            </Text>
          </Group>

          <Group>
            <Text size="sm" c="dimmed">
              {user.email}
            </Text>
            <UnstyledButton
              onClick={() => signOut()}
              style={{
                padding: rem(8),
                borderRadius: rem(4),
                display: "flex",
                alignItems: "center",
                gap: rem(8),
              }}
            >
              <IconLogout size={16} />
              <Text size="sm">Déconnexion</Text>
            </UnstyledButton>
          </Group>
        </Group>
      </MantineAppShell.Header>

      <MantineAppShell.Main>{children}</MantineAppShell.Main>
      <Toaster />
    </MantineAppShell>
  );
}
