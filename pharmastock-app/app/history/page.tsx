"use client";

import { TransactionLog } from "@/components/audit/transaction-log";
import {
  Container,
  Card,
  Title,
  Text,
  Stack,
  Group,
  ThemeIcon,
  Box,
  rem,
} from "@mantine/core";
import { IconHistory, IconClock } from "@tabler/icons-react";

export default function HistoryPage() {
  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Box>
          <Group gap="sm" mb="xs">
            <ThemeIcon size="lg" radius="md" color="#00B5FF">
              <IconHistory size={rem(24)} />
            </ThemeIcon>
            <Title order={1} size="h2" c="#00B5FF">
              Historique des Transactions
            </Title>
          </Group>
          <Text c="dimmed" size="sm">
            Consultez l'historique complet des transactions et des interactions
            de votre pharmacie
          </Text>
        </Box>

        {/* Transaction Log Card */}
        <Card withBorder radius="md" p="md" style={{ overflow: "hidden" }}>
          <Group gap="sm" mb="md">
            <ThemeIcon size="md" radius="sm" color="blue" variant="light">
              <IconClock size={rem(18)} />
            </ThemeIcon>
            <Text fw={500} size="lg">
              Journal des Activités
            </Text>
          </Group>
          <Box style={{ overflow: "auto" }}>
            <TransactionLog />
          </Box>
        </Card>
      </Stack>
    </Container>
  );
}
