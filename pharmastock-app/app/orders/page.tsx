"use client";

import { useState, useEffect } from "react";
import {
  Table,
  Badge,
  Button,
  Group,
  Select,
  TextInput,
  Paper,
  Text,
  Title,
  Stack,
  Card,
  Grid,
  ActionIcon,
  Tooltip,
  Container,
  Flex,
  Box,
  ThemeIcon,
  rem,
  Loader,
  Alert,
  Center,
} from "@mantine/core";
import { DatePickerInput, DatesRangeValue } from "@mantine/dates";
import {
  IconFileDownload,
  IconEye,
  IconSearch,
  IconFilter,
  IconShoppingCart,
  IconPackage,
  IconCalendar,
  IconTrendingUp,
} from "@tabler/icons-react";

interface Order {
  id: string;
  product: string;
  quantity: number;
  status: "completed" | "pending" | "cancelled";
  date: string;
  total: number;
  supplier: string;
  category: string;
  type: "sale" | "purchase";
  pricePerUnit: number;
  description: string;
}

const statusColors: Record<Order["status"], string> = {
  completed: "green",
  pending: "yellow",
  cancelled: "red",
};

const statusLabels: Record<Order["status"], string> = {
  completed: "<PERSON>rminée",
  pending: "En attente",
  cancelled: "Annulée",
};

export default function OrdersPage() {
  const [status, setStatus] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [dateRange, setDateRange] = useState<DatesRangeValue>([null, null]);
  const [orders, setOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch orders from API
  useEffect(() => {
    async function fetchOrders() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/orders");
        const data = await res.json();
        if (!res.ok)
          throw new Error(
            data.error || "Erreur lors du chargement des commandes"
          );
        setOrders(data.orders);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchOrders();
  }, []);

  const filteredOrders = orders.filter((order) => {
    const orderDate = new Date(order.date);
    const [startDate, endDate] = dateRange;
    const dateFilter =
      (!startDate || orderDate >= startDate) &&
      (!endDate || orderDate <= endDate);

    return (
      (!status || order.status === status) &&
      (!search || order.product.toLowerCase().includes(search.toLowerCase())) &&
      dateFilter
    );
  });

  // Calculate statistics
  const totalOrders = orders.length;
  const completedOrders = orders.filter((o) => o.status === "completed").length;
  const pendingOrders = orders.filter((o) => o.status === "pending").length;
  const totalValue = orders.reduce((sum, order) => sum + order.total, 0);

  if (loading) {
    return (
      <Container size="xl" py="md">
        <Center h={400}>
          <Loader size="lg" />
        </Center>
      </Container>
    );
  }

  if (error) {
    return (
      <Container size="xl" py="md">
        <Alert color="red" title="Erreur">
          {error}
        </Alert>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Flex justify="space-between" align="center">
          <Box>
            <Title order={1} size="h2" c="#00B5FF" mb="xs">
              <Group gap="sm">
                <ThemeIcon size="lg" radius="md" color="#00B5FF">
                  <IconShoppingCart size={rem(24)} />
                </ThemeIcon>
                Commandes & Transactions
              </Group>
            </Title>
            <Text c="dimmed" size="sm">
              Suivez vos commandes finalisées et l'historique des transactions
            </Text>
            <Text size="xs" c="blue" mt="xs">
              💡 Les réservations acceptées deviennent automatiquement des
              commandes
            </Text>
          </Box>
          <Button
            leftSection={<IconFileDownload size={16} />}
            variant="light"
            color="#00B5FF"
          >
            Exporter
          </Button>
        </Flex>

        {/* Statistics Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #00B5FF 0%, #0099E6 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Total Commandes
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {totalOrders}
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconPackage size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #51cf66 0%, #40c057 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Terminées
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {completedOrders}
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconTrendingUp size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #ffd43b 0%, #fab005 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    En Attente
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {pendingOrders}
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconCalendar size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #845ef7 0%, #7048e8 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Valeur Totale
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {totalValue.toFixed(2)} DH
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconTrendingUp size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Filters */}
        <Card withBorder radius="md" p="md">
          <Group gap="md" align="end">
            <TextInput
              placeholder="Rechercher un produit..."
              value={search}
              onChange={(e) => setSearch(e.currentTarget.value)}
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
            <Select
              placeholder="Filtrer par statut"
              data={[
                { value: "completed", label: "Terminée" },
                { value: "pending", label: "En attente" },
                { value: "cancelled", label: "Annulée" },
              ]}
              value={status}
              onChange={setStatus}
              clearable
              leftSection={<IconFilter size={16} />}
              w={200}
            />
            <DatePickerInput
              type="range"
              placeholder="Filtrer par date"
              value={dateRange}
              onChange={setDateRange}
              clearable
              leftSection={<IconCalendar size={16} />}
              w={250}
            />
          </Group>
        </Card>

        {/* Orders Table */}
        <Card withBorder radius="md" p="md">
          {filteredOrders.length === 0 ? (
            <Paper
              p="xl"
              radius="md"
              style={{ textAlign: "center", background: "#f8f9fa" }}
            >
              <ThemeIcon size="xl" radius="md" color="gray" mx="auto" mb="md">
                <IconPackage size={rem(32)} />
              </ThemeIcon>
              <Text size="lg" fw={500} c="dimmed" mb="xs">
                Aucune commande trouvée
              </Text>
              <Text size="sm" c="dimmed">
                Essayez de modifier vos filtres ou créez une nouvelle commande
              </Text>
            </Paper>
          ) : (
            <Table.ScrollContainer minWidth={800}>
              <Table striped highlightOnHover withTableBorder>
                <Table.Thead>
                  <Table.Tr>
                    <Table.Th>Commande</Table.Th>
                    <Table.Th>Produit</Table.Th>
                    <Table.Th>Fournisseur</Table.Th>
                    <Table.Th>Quantité</Table.Th>
                    <Table.Th>Total</Table.Th>
                    <Table.Th>Statut</Table.Th>
                    <Table.Th>Date</Table.Th>
                    <Table.Th>Actions</Table.Th>
                  </Table.Tr>
                </Table.Thead>
                <Table.Tbody>
                  {filteredOrders.map((order) => (
                    <Table.Tr key={order.id}>
                      <Table.Td>
                        <Text fw={500} c="#00B5FF">
                          {order.id}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Box>
                          <Text fw={500}>{order.product}</Text>
                          <Text size="xs" c="dimmed">
                            {order.category}
                          </Text>
                        </Box>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">{order.supplier}</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge variant="light" color="blue">
                          {order.quantity} unités
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text fw={500}>{order.total.toFixed(2)} DH</Text>
                      </Table.Td>
                      <Table.Td>
                        <Badge
                          color={statusColors[order.status]}
                          variant="light"
                          radius="sm"
                        >
                          {statusLabels[order.status]}
                        </Badge>
                      </Table.Td>
                      <Table.Td>
                        <Text size="sm">
                          {new Date(order.date).toLocaleDateString("fr-FR")}
                        </Text>
                      </Table.Td>
                      <Table.Td>
                        <Group gap="xs">
                          <Tooltip label="Voir les détails">
                            <ActionIcon
                              variant="light"
                              color="#00B5FF"
                              size="sm"
                            >
                              <IconEye size={16} />
                            </ActionIcon>
                          </Tooltip>
                          <Tooltip label="Télécharger">
                            <ActionIcon variant="light" color="green" size="sm">
                              <IconFileDownload size={16} />
                            </ActionIcon>
                          </Tooltip>
                        </Group>
                      </Table.Td>
                    </Table.Tr>
                  ))}
                </Table.Tbody>
              </Table>
            </Table.ScrollContainer>
          )}
        </Card>
      </Stack>
    </Container>
  );
}
