"use client";

import { useState } from "react";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import {
  Paper,
  TextInput,
  PasswordInput,
  Button,
  Title,
  Text,
  Anchor,
  Container,
  Group,
  Stack,
  Alert,
  Loader,
  Box,
  Center,
  Grid,
  Select,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import {
  IconMail,
  IconLock,
  IconAlertCircle,
  IconPill,
  IconPhone,
  IconMapPin,
  IconBuilding,
  IconCertificate,
} from "@tabler/icons-react";

export default function RegisterPage() {
  const { signUp, loading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const form = useForm({
    initialValues: {
      name: "",
      email: "",
      phone: "",
      address: "",
      city: "",
      license_number: "",
      password: "",
      confirmPassword: "",
      role: "pharmacist",
    },
    validate: {
      name: (value) =>
        value.length < 2 ? "Le nom doit contenir au moins 2 caractères" : null,
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Email invalide"),
      phone: (value) =>
        value.length < 10 ? "Numéro de téléphone invalide" : null,
      address: (value) => (value.length < 5 ? "Adresse trop courte" : null),
      city: (value) => (value.length < 2 ? "Ville invalide" : null),
      license_number: (value) =>
        value.length < 3 ? "Numéro de licence invalide" : null,
      password: (value) =>
        value.length < 8
          ? "Le mot de passe doit contenir au moins 8 caractères"
          : null,
      confirmPassword: (value, values) =>
        value !== values.password
          ? "Les mots de passe ne correspondent pas"
          : null,
    },
  });

  const handleSubmit = async (values: typeof form.values) => {
    setIsLoading(true);
    setError(null);

    try {
      const { confirmPassword, ...pharmacyData } = values;
      const result = await signUp({
        ...pharmacyData,
        role: values.role,
        password: values.password,
      });

      if (result?.error) {
        setError(result.error);
        notifications.show({
          title: "Erreur d'inscription",
          message: result.error,
          color: "red",
          icon: <IconAlertCircle size={16} />,
        });
      } else {
        notifications.show({
          title: "Inscription réussie",
          message: "Votre compte a été créé avec succès !",
          color: "green",
        });
        // Redirect to dashboard after successful registration
        setTimeout(() => {
          window.location.href = "/dashboard";
        }, 1000);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "Une erreur est survenue lors de l'inscription";
      setError(errorMessage);
      notifications.show({
        title: "Erreur d'inscription",
        message: errorMessage,
        color: "red",
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Box
      style={{
        minHeight: "100vh",
        display: "flex",
        overflow: "hidden",
      }}
    >
      {/* Left Side - Branding */}
      <Box
        style={{
          flex: 1,
          background:
            "linear-gradient(135deg, #00B5FF 0%, #0099E6 50%, #007ACC 100%)",
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "2rem",
        }}
      >
        {/* Background Pattern */}
        <Box
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%)
            `,
          }}
        />

        <Stack
          align="center"
          gap="xl"
          style={{ position: "relative", zIndex: 1, textAlign: "center" }}
        >
          <Group gap="lg">
            <IconPill size={64} color="white" />
            <Title order={1} size="3rem" fw={700} c="white">
              PharmaStock
            </Title>
          </Group>

          <Stack gap="md" align="center">
            <Title order={2} size="1.5rem" fw={500} c="white" opacity={0.9}>
              Rejoignez Notre Réseau
            </Title>
            <Text size="lg" c="white" opacity={0.8} ta="center" maw={400}>
              Créez votre compte pharmacie et accédez à une plateforme complète
              de gestion des stocks et de collaboration.
            </Text>
          </Stack>

          <Stack gap="sm" align="center" mt="xl">
            <Text size="sm" c="white" opacity={0.7}>
              ✓ Inscription rapide et sécurisée
            </Text>
            <Text size="sm" c="white" opacity={0.7}>
              ✓ Vérification automatique
            </Text>
            <Text size="sm" c="white" opacity={0.7}>
              ✓ Accès immédiat à la marketplace
            </Text>
          </Stack>
        </Stack>
      </Box>

      {/* Right Side - Register Form */}
      <Box
        style={{
          flex: 1,
          backgroundColor: "#ffffff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          padding: "2rem",
          overflowY: "auto",
        }}
      >
        <Container size={500} style={{ width: "100%" }}>
          <Paper
            p={40}
            style={{
              backgroundColor: "#ffffff",
              border: "none",
              boxShadow: "none",
            }}
          >
            <Stack gap="lg">
              {/* Form Header */}
              <Stack gap="xs" align="center">
                <Title order={1} size="2rem" fw={700} c="#00B5FF">
                  Inscription
                </Title>
                <Text c="dimmed" size="md" ta="center">
                  Créez votre compte pharmacie
                </Text>
              </Stack>

              {error && (
                <Alert
                  icon={<IconAlertCircle size={16} />}
                  title="Erreur d'inscription"
                  color="red"
                  variant="light"
                >
                  {error}
                </Alert>
              )}

              <form onSubmit={form.onSubmit(handleSubmit)}>
                <Stack gap="md">
                  <Grid>
                    <Grid.Col span={6}>
                      <TextInput
                        required
                        label="Nom de la pharmacie"
                        placeholder="Pharmacie Centrale"
                        leftSection={<IconBuilding size={16} />}
                        {...form.getInputProps("name")}
                        disabled={isLoading}
                        styles={{
                          input: {
                            "&:focus": {
                              borderColor: "#00B5FF",
                            },
                          },
                        }}
                      />
                    </Grid.Col>
                    <Grid.Col span={6}>
                      <TextInput
                        required
                        label="Numéro de licence"
                        placeholder="LIC-2024-001"
                        leftSection={<IconCertificate size={16} />}
                        {...form.getInputProps("license_number")}
                        disabled={isLoading}
                        styles={{
                          input: {
                            "&:focus": {
                              borderColor: "#00B5FF",
                            },
                          },
                        }}
                      />
                    </Grid.Col>
                  </Grid>

                  <TextInput
                    required
                    label="Adresse email"
                    placeholder="<EMAIL>"
                    leftSection={<IconMail size={16} />}
                    {...form.getInputProps("email")}
                    disabled={isLoading}
                    styles={{
                      input: {
                        "&:focus": {
                          borderColor: "#00B5FF",
                        },
                      },
                    }}
                  />

                  <TextInput
                    required
                    label="Téléphone"
                    placeholder="+212 522 123 456"
                    leftSection={<IconPhone size={16} />}
                    {...form.getInputProps("phone")}
                    disabled={isLoading}
                    styles={{
                      input: {
                        "&:focus": {
                          borderColor: "#00B5FF",
                        },
                      },
                    }}
                  />

                  <Grid>
                    <Grid.Col span={8}>
                      <TextInput
                        required
                        label="Adresse"
                        placeholder="123 Avenue Mohammed V"
                        leftSection={<IconMapPin size={16} />}
                        {...form.getInputProps("address")}
                        disabled={isLoading}
                        styles={{
                          input: {
                            "&:focus": {
                              borderColor: "#00B5FF",
                            },
                          },
                        }}
                      />
                    </Grid.Col>
                    <Grid.Col span={4}>
                      <TextInput
                        required
                        label="Ville"
                        placeholder="Casablanca"
                        {...form.getInputProps("city")}
                        disabled={isLoading}
                        styles={{
                          input: {
                            "&:focus": {
                              borderColor: "#00B5FF",
                            },
                          },
                        }}
                      />
                    </Grid.Col>
                  </Grid>

                  <Select
                    label="Rôle utilisateur"
                    placeholder="Sélectionnez un rôle"
                    data={[
                      { value: "owner", label: "Propriétaire (Owner)" },
                      { value: "pharmacist", label: "Pharmacien(ne)" },
                      { value: "staff", label: "Personnel" },
                      { value: "supplier", label: "Fournisseur" },
                    ]}
                    {...form.getInputProps("role")}
                    required
                  />

                  <PasswordInput
                    required
                    label="Mot de passe"
                    placeholder="Votre mot de passe"
                    leftSection={<IconLock size={16} />}
                    {...form.getInputProps("password")}
                    disabled={isLoading}
                    styles={{
                      input: {
                        "&:focus": {
                          borderColor: "#00B5FF",
                        },
                      },
                    }}
                  />

                  <PasswordInput
                    required
                    label="Confirmer le mot de passe"
                    placeholder="Confirmez votre mot de passe"
                    leftSection={<IconLock size={16} />}
                    {...form.getInputProps("confirmPassword")}
                    disabled={isLoading}
                    styles={{
                      input: {
                        "&:focus": {
                          borderColor: "#00B5FF",
                        },
                      },
                    }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    loading={isLoading}
                    size="md"
                    style={{
                      background: "linear-gradient(45deg, #00B5FF, #0099E6)",
                      "&:hover": {
                        background: "linear-gradient(45deg, #0099E6, #007ACC)",
                      },
                    }}
                  >
                    {isLoading ? <Loader size="sm" /> : "Créer mon compte"}
                  </Button>
                </Stack>
              </form>

              <Group justify="center" mt="md">
                <Text size="sm" c="dimmed">
                  Déjà un compte ?{" "}
                  <Anchor component={Link} href="/auth/login" c="#00B5FF">
                    Se connecter
                  </Anchor>
                </Text>
              </Group>
            </Stack>
          </Paper>
        </Container>
      </Box>
    </Box>
  );
}
