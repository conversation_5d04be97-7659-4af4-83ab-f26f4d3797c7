"use client";

export const dynamic = "force-dynamic";

import { useState, useEffect, useCallback, Suspense } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import Link from "next/link";
import { useAuth } from "@/contexts/auth-context";
import {
  Paper,
  TextInput,
  PasswordInput,
  Button,
  Title,
  Text,
  Anchor,
  Container,
  Group,
  Stack,
  Alert,
  Loader,
  Box,
  Flex,
} from "@mantine/core";
import { useForm } from "@mantine/form";
import { notifications } from "@mantine/notifications";
import {
  IconMail,
  IconLock,
  IconAlertCircle,
  IconUserCircle,
} from "@tabler/icons-react";
import { LogoWhite } from "@/components/ui/logo";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";

function LoginPageContent() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login, user, loading } = useAuth();
  const [isLoading, setIsLoading] = useState(false);
  const [isDemoLoading, setIsDemoLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [isDemoModalOpen, setDemoModalOpen] = useState(false);

  const form = useForm({
    initialValues: {
      email: "",
      password: "",
    },
    validate: {
      email: (value) => (/^\S+@\S+$/.test(value) ? null : "Email invalide"),
      password: (value) =>
        value.length < 6
          ? "Le mot de passe doit contenir au moins 6 caractères"
          : null,
    },
  });

  const handleRedirect = useCallback(() => {
    if (user && !loading) {
      // Determine redirect based on standardized role system
      switch (user.role) {
        case "super_admin":
          router.replace("/admin/dashboard");
          break;
        case "supplier":
          router.replace("/supplier/dashboard");
          break;
        case "owner":
        case "pharmacist":
        case "admin": // Backward compatibility
        case "staff":
        default:
          router.replace("/dashboard");
          break;
      }
      router.refresh();
    }
  }, [user, loading, router]);

  // Auto-fill form from URL parameters
  useEffect(() => {
    const email = searchParams.get("email");
    const password = searchParams.get("password");
    const isDemo = searchParams.get("demo");

    if (email) {
      form.setFieldValue("email", decodeURIComponent(email));
    }
    if (password) {
      form.setFieldValue("password", decodeURIComponent(password));
    }

    // Auto-submit if both email and password are provided and it's a demo
    if (email && password && isDemo === "true") {
      setTimeout(() => {
        handleSubmit({
          email: decodeURIComponent(email),
          password: decodeURIComponent(password),
        });
      }, 500); // Small delay to ensure form is ready
    }
  }, [searchParams]);

  useEffect(() => {
    if (!loading && user) {
      handleRedirect();
    }
  }, [handleRedirect, loading, user]);

  const handleDemoLogin = async (
    email: string,
    password: string,
    description: string
  ) => {
    setIsDemoLoading(true);
    setError(null);

    try {
      const result = await login(email, password);
      if (result?.error) {
        setError(result.error);
        notifications.show({
          title: "Erreur de connexion Démo",
          message: result.error,
          color: "red",
        });
        return;
      }
      notifications.show({
        title: "Connexion Démo réussie",
        message: `Bienvenue ! ${description}`,
        color: "green",
      });
      setDemoModalOpen(false);
    } catch (e) {
      setError("Failed to login as demo user.");
    } finally {
      setIsDemoLoading(false);
    }
  };

  const handleSubmit = async (values: typeof form.values) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await login(values.email, values.password);

      if (result?.error) {
        console.error('Login failed:', result.error);

        setError(result.error);
        notifications.show({
          title: "Erreur de connexion",
          message: result.error,
          color: "red",
          icon: <IconAlertCircle size={16} />,
        });
        return;
      }


      notifications.show({
        title: "Connexion réussie",
        message: "Bienvenue sur PharmaStock !",
        color: "green",
      });

      // Redirect will be handled by useEffect when user state updates
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Erreur de connexion";
      setError(errorMessage);
      notifications.show({
        title: "Erreur de connexion",
        message: errorMessage,
        color: "red",
        icon: <IconAlertCircle size={16} />,
      });
    } finally {
      setIsLoading(false);
    }
  };

  // Show loading state while checking initial session
  if (loading && !isLoading) {
    return (
      <Box
        style={{
          minHeight: "100vh",
          background:
            "linear-gradient(135deg, #00B5FF 0%, #0099E6 50%, #007ACC 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Stack align="center" gap="md">
          <Loader size="lg" color="white" />
          <Text c="white" size="sm">
            Vérification de la session...
          </Text>
        </Stack>
      </Box>
    );
  }

  // Show redirecting state when user is authenticated
  if (user && !loading) {
    return (
      <Box
        style={{
          minHeight: "100vh",
          background:
            "linear-gradient(135deg, #00B5FF 0%, #0099E6 50%, #007ACC 100%)",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
        }}
      >
        <Stack align="center" gap="md">
          <Loader size="lg" color="white" />
          <Text c="white" size="sm">
            Redirection vers le tableau de bord...
          </Text>
        </Stack>
      </Box>
    );
  }

  return (
    <Flex
      direction={{ base: "column", md: "row" }}
      style={{
        minHeight: "100vh",
        overflow: "hidden",
      }}
    >
      {/* Left Side - Branding */}
      <Box
        flex={1}
        style={{
          background:
            "linear-gradient(135deg, #667eea 0%, #764ba2 100%)",
          position: "relative",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "40vh",
        }}
        p={{ base: "md", md: "xl" }}
      >
        {/* Background Pattern */}
        <Box
          style={{
            position: "absolute",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            backgroundImage: `
              radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
              radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%)
            `,
          }}
        />

        <Stack
          align="center"
          gap="xl"
          style={{ position: "relative", zIndex: 1, textAlign: "center" }}
        >
          <Group gap="lg">
            <LogoWhite size={64} />
            <Title
              order={1}
              fw={700}
              c="white"
              style={{ fontSize: "clamp(2rem, 5vw, 3rem)" }}
            >
              PharmaStock
            </Title>
          </Group>

          <Stack gap="md" align="center">
            <Title order={2} size="1.5rem" fw={500} c="white" opacity={0.9}>
              Plateforme Intelligente de Gestion Pharmaceutique
            </Title>
            <Text size="lg" c="white" opacity={0.8} ta="center" maw={400}>
              Connectez-vous pour accéder à votre tableau de bord et gérer vos
              stocks pharmaceutiques en toute sécurité.
            </Text>
          </Stack>

          <Stack gap="sm" align="center" mt="xl">
            <Text size="sm" c="white" opacity={0.7}>
              ✓ Gestion des stocks en temps réel
            </Text>
            <Text size="sm" c="white" opacity={0.7}>
              ✓ Marketplace sécurisée
            </Text>
            <Text size="sm" c="white" opacity={0.7}>
              ✓ Demandes urgentes instantanées
            </Text>
          </Stack>
        </Stack>
      </Box>

      {/* Right Side - Login Form */}
      <Box
        flex={1}
        style={{
          backgroundColor: "#ffffff",
          display: "flex",
          alignItems: "center",
          justifyContent: "center",
          minHeight: "60vh",
        }}
        p={{ base: "md", md: "xl" }}
      >
        <Container size={400} style={{ width: "100%" }}>
          <Paper
            p={40}
            radius="20px"
            style={{
              backgroundColor: "#ffffff",
              border: "none",
              boxShadow: "0 20px 60px rgba(0, 0, 0, 0.3)",
            }}
          >
            <Stack gap="lg">
              {/* Form Header */}
              <Stack gap="xs" align="center">
                <Title order={1} size="28px" fw={700} c="#1e293b" mb="10px">
                  🚀 Connexion
                </Title>
                <Text c="#64748b" size="16px" ta="center">
                  Accédez à votre espace PharmaStock
                </Text>
              </Stack>

              {error && (
                <Alert
                  icon={<IconAlertCircle size={16} />}
                  title="Erreur de connexion"
                  color="red"
                  variant="light"
                >
                  {error}
                </Alert>
              )}

              <form onSubmit={form.onSubmit(handleSubmit)}>
                <Stack gap="md">
                  <TextInput
                    required
                    label="Adresse email"
                    placeholder="<EMAIL>"
                    leftSection={<IconMail size={16} />}
                    {...form.getInputProps("email")}
                    disabled={isLoading}
                    styles={{
                      input: {
                        "&:focus": {
                          borderColor: "#00B5FF",
                        },
                      },
                    }}
                  />

                  <PasswordInput
                    required
                    label="Mot de passe"
                    placeholder="Votre mot de passe"
                    leftSection={<IconLock size={16} />}
                    {...form.getInputProps("password")}
                    disabled={isLoading}
                    styles={{
                      input: {
                        "&:focus": {
                          borderColor: "#00B5FF",
                        },
                      },
                    }}
                  />

                  <Button
                    type="submit"
                    fullWidth
                    loading={isLoading}
                    size="md"
                    radius="12px"
                    style={{
                      background: "linear-gradient(135deg, #667eea, #764ba2)",
                      border: "none",
                      height: "48px",
                      fontSize: "16px",
                      fontWeight: 600,
                    }}
                  >
                    {isLoading ? <Loader size="sm" /> : "Se connecter"}
                  </Button>
                </Stack>
              </form>

              <Group justify="space-between" mt="md">
                <Anchor
                  component={Link}
                  href="/auth/forgot-password"
                  size="sm"
                  c="#00B5FF"
                >
                  Mot de passe oublié ?
                </Anchor>
                <Text size="sm" c="dimmed">
                  Pas de compte ?{" "}
                  <Anchor component={Link} href="/auth/register" c="#00B5FF">
                    S&apos;inscrire
                  </Anchor>
                </Text>
              </Group>

              <Dialog open={isDemoModalOpen} onOpenChange={setDemoModalOpen}>
                <DialogTrigger asChild>
                  <Button
                    variant="outline"
                    fullWidth
                    mt="md"
                    radius="12px"
                    leftSection={<IconUserCircle size={16} />}
                    style={{
                      borderColor: "#667eea",
                      color: "#667eea",
                      height: "48px",
                      fontSize: "16px",
                      fontWeight: 600,
                    }}
                  >
                    <i className="fas fa-play-circle" style={{ marginRight: "8px" }}></i>
                    Accéder à la Démo Live
                  </Button>
                </DialogTrigger>
                <DialogContent style={{ maxWidth: "500px", padding: "40px", borderRadius: "20px" }}>
                  <DialogHeader>
                    <DialogTitle style={{ fontSize: "28px", color: "#1e293b", marginBottom: "10px", textAlign: "center" }}>
                      🚀 Comptes de Démonstration
                    </DialogTitle>
                    <DialogDescription style={{ color: "#64748b", fontSize: "16px", textAlign: "center" }}>
                      Sélectionnez un compte pour découvrir PharmaStock
                    </DialogDescription>
                  </DialogHeader>
                  <Stack mt="md" gap="md">
                    {/* Pharmacy Owner Account */}
                    <Box
                      onClick={() =>
                        handleDemoLogin(
                          "<EMAIL>",
                          "demo123!",
                          "Propriétaire - Pharmacie Camélia"
                        )
                      }
                      style={{
                        background: "linear-gradient(135deg, #f8fafc, #f1f5f9)",
                        border: "2px solid #e2e8f0",
                        borderRadius: "16px",
                        padding: "24px",
                        cursor: "pointer",
                        transition: "all 0.3s ease",
                        textAlign: "center",
                      }}
                    >
                      <div style={{ fontSize: "24px", marginBottom: "12px" }}>
                        <i className="fas fa-store"></i>
                      </div>
                      <Text size="lg" fw={600} c="#1e293b" mb="xs">
                        👨‍⚕️ Propriétaire de Pharmacie
                      </Text>
                      <Text size="sm" c="#64748b" mb="md">
                        Ahmed Benali - Pharmacie Camélia
                      </Text>
                      <Box
                        style={{
                          background: "rgba(59, 130, 246, 0.1)",
                          padding: "8px 12px",
                          borderRadius: "8px",
                          fontSize: "12px",
                          color: "#3b82f6",
                          fontWeight: 600,
                        }}
                      >
                        Accès complet • Gestion d'inventaire • Analytics
                      </Box>
                    </Box>

                    {/* Staff Account */}
                    <Box
                      onClick={() =>
                        handleDemoLogin(
                          "<EMAIL>",
                          "demo123!",
                          "Assistant - Pharmacie Camélia"
                        )
                      }
                      style={{
                        background: "linear-gradient(135deg, #f8fafc, #f1f5f9)",
                        border: "2px solid #e2e8f0",
                        borderRadius: "16px",
                        padding: "24px",
                        cursor: "pointer",
                        transition: "all 0.3s ease",
                        textAlign: "center",
                      }}
                    >
                      <div style={{ fontSize: "24px", marginBottom: "12px" }}>
                        <i className="fas fa-user-md"></i>
                      </div>
                      <Text size="lg" fw={600} c="#1e293b" mb="xs">
                        👩‍💼 Assistant Pharmacie
                      </Text>
                      <Text size="sm" c="#64748b" mb="md">
                        Laila Tazi - Assistant (Casablanca)
                      </Text>
                      <Box
                        style={{
                          background: "rgba(16, 185, 129, 0.1)",
                          padding: "8px 12px",
                          borderRadius: "8px",
                          fontSize: "12px",
                          color: "#10b981",
                          fontWeight: 600,
                        }}
                      >
                        Accès limité • Gestion quotidienne • Demandes urgentes
                      </Box>
                    </Box>
                    {/* Footer */}
                    <Box
                      style={{
                        textAlign: "center",
                        marginTop: "30px",
                        paddingTop: "20px",
                        borderTop: "1px solid #e2e8f0",
                      }}
                    >
                      <Text size="xs" c="#64748b">
                        <strong>Mots de passe:</strong> demo123! pour tous les comptes
                      </Text>
                    </Box>
                  </Stack>
                </DialogContent>
              </Dialog>
            </Stack>
          </Paper>
        </Container>
      </Box>
    </Flex>
  );
}

export default function LoginPage() {
  return (
    <Suspense
      fallback={
        <Box
          style={{
            minHeight: "100vh",
            background:
              "linear-gradient(135deg, #00B5FF 0%, #0099E6 50%, #007ACC 100%)",
            display: "flex",
            alignItems: "center",
            justifyContent: "center",
          }}
        >
          <Stack align="center" gap="md">
            <Loader size="lg" color="white" />
            <Text c="white" size="sm">
              Chargement...
            </Text>
          </Stack>
        </Box>
      }
    >
      <LoginPageContent />
    </Suspense>
  );
}
