"use client";

import { useEffect, useState, useMemo, useCallback } from "react";
import { useDebouncedValue } from "@mantine/hooks";
import {
  Card,
  Badge,
  Button,
  Group,
  TextInput,
  Paper,
  Text,
  SimpleGrid,
  Loader,
  Modal,
  Stack,
  Notification,
  Tabs,
  Title,
  Container,
  ThemeIcon,
  Box,
  Grid,
  Alert,
  Table,
  ActionIcon,
  Tooltip,
  Select,
  Pagination,
  rem,
} from "@mantine/core";
import {
  IconEdit,
  IconTrash,
  IconBox,
  IconPlus,
  IconPackage,
  IconAlertTriangle,
  IconClock,
  IconEye,
  IconArrowsRightLeft,
  IconFilter,
  IconSearch,
  IconShoppingCart,
} from "@tabler/icons-react";
import { StockForm } from "@/components/stocks/stock-form";
import { useAuth } from "@/contexts/auth-context";
import { format } from "date-fns";
import { fr } from "date-fns/locale";
import { useSearchParams } from "next/navigation";

interface Stock {
  id: string;
  pharmacy_id: string;
  name: string;
  category: string;
  quantity: number;
  expiry_date: string | null;
  unit_price: number;
  batch_number?: string | null;
}

interface ExpiringProduct extends Stock {
  days_until_expiry: number;
  total_value: number;
  urgency_level: "critical" | "urgent" | "warning" | "normal";
  supplier?: string;
}

function getStockColor(quantity: number) {
  if (quantity < 5) return "red";
  if (quantity < 20) return "yellow";
  return "green";
}

function getExpiryColor(expiry: string | null) {
  if (!expiry) return "gray";
  const days =
    (new Date(expiry).getTime() - new Date().getTime()) / (1000 * 60 * 60 * 24);
  if (days < 7) return "red";
  if (days < 30) return "yellow";
  return "green";
}

function getDaysUntilExpiry(expiryDate: string | null): number {
  if (!expiryDate) return Infinity;
  const now = new Date();
  const expiry = new Date(expiryDate);
  return Math.ceil((expiry.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
}

function getUrgencyLevel(
  days: number
): "critical" | "urgent" | "warning" | "normal" {
  if (days < 15) return "critical";
  if (days < 60) return "urgent";
  if (days < 90) return "warning";
  return "normal";
}

function getUrgencyColor(
  urgency: "critical" | "urgent" | "warning" | "normal"
): string {
  switch (urgency) {
    case "critical":
      return "red";
    case "urgent":
      return "orange";
    case "warning":
      return "yellow";
    case "normal":
      return "green";
    default:
      return "gray";
  }
}

function getUrgencyText(
  urgency: "critical" | "urgent" | "warning" | "normal"
): string {
  switch (urgency) {
    case "critical":
      return "Critique";
    case "urgent":
      return "Urgent";
    case "warning":
      return "Attention";
    case "normal":
      return "Normal";
    default:
      return "Normal";
  }
}

export default function InventoryPage() {
  const searchParams = useSearchParams();
  const [stocks, setStocks] = useState<Stock[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [search, setSearch] = useState("");
  const [debouncedSearch] = useDebouncedValue(search, 300);

  // Reset pagination when search changes
  useEffect(() => {
    setCurrentPage(1);
  }, [debouncedSearch]);
  const [modalOpen, setModalOpen] = useState(false);
  const [editStock, setEditStock] = useState<Stock | null>(null);
  const [deleteId, setDeleteId] = useState<string | null>(null);
  const [actionLoading, setActionLoading] = useState(false);
  const [notification, setNotification] = useState<string | null>(null);
  const [activeTab, setActiveTab] = useState(
    searchParams?.get("tab") || "overview"
  );
  const [urgencyFilter, setUrgencyFilter] = useState("all");
  const [categoryFilter, setCategoryFilter] = useState("all");
  const [sortBy, setSortBy] = useState("expiry_date");
  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage] = useState(20);
  const { user } = useAuth();
  const canEdit =
    user?.role === "owner" ||
    user?.role === "pharmacist" ||
    user?.role === "super_admin";

  // Fetch inventory
  useEffect(() => {
    async function fetchStocks() {
      setLoading(true);
      setError(null);
      try {
        const res = await fetch("/api/stock");
        const data = await res.json();
        if (!res.ok)
          throw new Error(data.error || "Erreur lors du chargement du stock");
        setStocks(data.stocks);
      } catch (err: any) {
        setError(err.message);
      } finally {
        setLoading(false);
      }
    }
    fetchStocks();
  }, []);

  // Memoized filtered stocks with debounced search and pagination
  const { filtered, paginatedItems, totalPages } = useMemo(() => {
    const filteredStocks = stocks.filter((item) =>
      item.name.toLowerCase().includes(debouncedSearch.toLowerCase())
    );

    const startIndex = (currentPage - 1) * itemsPerPage;
    const endIndex = startIndex + itemsPerPage;
    const paginatedStocks = filteredStocks.slice(startIndex, endIndex);
    const totalPagesCount = Math.ceil(filteredStocks.length / itemsPerPage);

    return {
      filtered: filteredStocks,
      paginatedItems: paginatedStocks,
      totalPages: totalPagesCount,
    };
  }, [stocks, debouncedSearch, currentPage, itemsPerPage]);

  // Optimized expiring products with debounced search and pagination
  const expiringProducts = useMemo(() => {
    if (!stocks.length) return [];

    // Pre-filter stocks with expiry dates
    const stocksWithExpiry = stocks.filter((stock) => stock.expiry_date);

    // Batch process the data transformation
    const enhancedProducts = stocksWithExpiry.map((stock) => {
      const days = getDaysUntilExpiry(stock.expiry_date);
      const urgency = getUrgencyLevel(days);
      return {
        ...stock,
        days_until_expiry: days,
        total_value: stock.quantity * stock.unit_price,
        urgency_level: urgency,
        // TODO: Replace with real supplier data from database
        supplier: "Fournisseur", // Will be replaced with real data
      } as ExpiringProduct;
    });

    // Apply filters efficiently
    let filtered = enhancedProducts;

    if (debouncedSearch) {
      const searchLower = debouncedSearch.toLowerCase();
      filtered = filtered.filter((product) =>
        product.name.toLowerCase().includes(searchLower)
      );
    }

    if (urgencyFilter !== "all") {
      filtered = filtered.filter(
        (product) => product.urgency_level === urgencyFilter
      );
    }

    if (categoryFilter !== "all") {
      filtered = filtered.filter(
        (product) => product.category === categoryFilter
      );
    }

    // Sort efficiently
    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "expiry_date":
          return a.days_until_expiry - b.days_until_expiry;
        case "value":
          return b.total_value - a.total_value;
        case "quantity":
          return b.quantity - a.quantity;
        case "name":
          return a.name.localeCompare(b.name);
        default:
          return a.days_until_expiry - b.days_until_expiry;
      }
    });
  }, [stocks, debouncedSearch, urgencyFilter, categoryFilter, sortBy]);

  // Statistics for expiry management
  const expiryStats = useMemo(() => {
    const critical = expiringProducts.filter(
      (p) => p.urgency_level === "critical"
    ).length;
    const urgent = expiringProducts.filter(
      (p) => p.urgency_level === "urgent"
    ).length;
    const totalValue = expiringProducts.reduce(
      (sum, p) => sum + p.total_value,
      0
    );
    const lowStock = stocks.filter((s) => s.quantity < 20).length;

    return {
      critical,
      urgent,
      totalValue,
      lowStock,
      total: expiringProducts.length,
    };
  }, [expiringProducts, stocks]);

  // Add/Edit stock handler
  const handleSave = async (values: any) => {
    setActionLoading(true);
    setError(null);
    try {
      const method = editStock ? "PUT" : "POST";
      const res = await fetch("/api/stock", {
        method,
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(
          editStock ? { ...values, id: editStock.id } : values
        ),
      });
      const data = await res.json();
      if (!res.ok)
        throw new Error(data.error || "Erreur lors de l'enregistrement");
      setNotification(
        editStock ? "Stock modifié avec succès" : "Produit ajouté avec succès"
      );
      setModalOpen(false);
      setEditStock(null);
      // Refresh list
      setStocks((prev) => {
        if (editStock) {
          return prev.map((s) => (s.id === data.stock.id ? data.stock : s));
        } else {
          return [data.stock, ...prev];
        }
      });
    } catch (err: any) {
      setError(err.message);
    } finally {
      setActionLoading(false);
    }
  };

  // Delete stock handler
  const handleDelete = async (id: string) => {
    setActionLoading(true);
    setError(null);
    try {
      const res = await fetch("/api/stock", {
        method: "DELETE",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({ id }),
      });
      const data = await res.json();
      if (!res.ok)
        throw new Error(data.error || "Erreur lors de la suppression");
      setNotification("Produit supprimé avec succès");
      setDeleteId(null);
      setStocks((prev) => prev.filter((s) => s.id !== id));
    } catch (err: any) {
      setError(err.message);
    } finally {
      setActionLoading(false);
    }
  };

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        {/* Header */}
        <Group justify="space-between">
          <Box>
            <Title order={1} size="h2" c="#00B5FF" mb="xs">
              <Group gap="sm">
                <ThemeIcon size="lg" radius="md" color="#00B5FF">
                  <IconPackage size={rem(24)} />
                </ThemeIcon>
                Gestion de l'Inventaire
              </Group>
            </Title>
            <Text c="dimmed" size="sm">
              Gérez vos stocks, surveillez les péremptions et optimisez votre
              inventaire
            </Text>
          </Box>
          {canEdit && (
            <Button
              leftSection={<IconPlus size={16} />}
              onClick={() => {
                setEditStock(null);
                setModalOpen(true);
              }}
              color="#00B5FF"
            >
              Ajouter un Produit
            </Button>
          )}
        </Group>

        {/* Statistics Cards */}
        <Grid>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #00B5FF 0%, #0099E6 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Total Produits
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {stocks.length}
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconPackage size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Critique
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {expiryStats.critical}
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconAlertTriangle size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #ffd43b 0%, #fab005 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Stock Faible
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {expiryStats.lowStock}
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconClock size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
          <Grid.Col span={{ base: 12, sm: 6, md: 3 }}>
            <Card
              withBorder
              radius="md"
              p="md"
              style={{
                background: "linear-gradient(135deg, #51cf66 0%, #40c057 100%)",
              }}
            >
              <Group justify="space-between">
                <Box>
                  <Text c="white" size="sm" fw={500}>
                    Valeur Totale
                  </Text>
                  <Text c="white" size="xl" fw={700}>
                    {expiryStats.totalValue.toFixed(2)} DH
                  </Text>
                </Box>
                <ThemeIcon size="lg" radius="md" color="rgba(255,255,255,0.2)">
                  <IconPackage size={rem(20)} color="white" />
                </ThemeIcon>
              </Group>
            </Card>
          </Grid.Col>
        </Grid>

        {/* Search and Filters */}
        <Card withBorder radius="md" p="md">
          <Group gap="md" align="end">
            <TextInput
              placeholder="Rechercher un produit..."
              value={search}
              onChange={(e) => setSearch(e.currentTarget.value)}
              leftSection={<IconSearch size={16} />}
              style={{ flex: 1 }}
            />
            {activeTab === "expiry" && (
              <>
                <Select
                  placeholder="Filtrer par urgence"
                  data={[
                    { value: "all", label: "Toutes les urgences" },
                    { value: "critical", label: "Critique" },
                    { value: "urgent", label: "Urgent" },
                    { value: "warning", label: "Attention" },
                    { value: "normal", label: "Normal" },
                  ]}
                  value={urgencyFilter}
                  onChange={(val) => setUrgencyFilter(val || "all")}
                  clearable
                  leftSection={<IconFilter size={16} />}
                  w={200}
                />
                <Select
                  placeholder="Trier par"
                  data={[
                    { value: "expiry_date", label: "Date d'expiration" },
                    { value: "value", label: "Valeur" },
                    { value: "quantity", label: "Quantité" },
                    { value: "name", label: "Nom" },
                  ]}
                  value={sortBy}
                  onChange={(val) => setSortBy(val || "expiry_date")}
                  w={150}
                />
              </>
            )}
          </Group>
        </Card>

        {/* Tabs */}
        <Tabs
          value={activeTab}
          onChange={(val) => setActiveTab(val || "overview")}
        >
          <Tabs.List>
            <Tabs.Tab value="overview" leftSection={<IconPackage size={16} />}>
              Vue d'ensemble
            </Tabs.Tab>
            <Tabs.Tab
              value="expiry"
              leftSection={<IconAlertTriangle size={16} />}
            >
              Alertes Péremption
            </Tabs.Tab>
            <Tabs.Tab value="lowstock" leftSection={<IconClock size={16} />}>
              Stock Faible
            </Tabs.Tab>
          </Tabs.List>

          {/* Overview Tab */}
          <Tabs.Panel value="overview" pt="md">
            {loading ? (
              <Group justify="center" py="xl">
                <Loader />
              </Group>
            ) : error ? (
              <Notification
                color="red"
                title="Erreur"
                onClose={() => setError(null)}
              >
                {error}
              </Notification>
            ) : paginatedItems.length === 0 ? (
              <Paper
                p="xl"
                radius="md"
                style={{ textAlign: "center", background: "#f8f9fa" }}
              >
                <ThemeIcon size="xl" radius="md" color="gray" mx="auto" mb="md">
                  <IconBox size={rem(32)} />
                </ThemeIcon>
                <Text size="lg" fw={500} c="dimmed" mb="xs">
                  Aucun produit en inventaire
                </Text>
                <Text size="sm" c="dimmed">
                  Ajoutez des produits pour commencer à gérer votre stock.
                </Text>
              </Paper>
            ) : (
              <Stack gap="lg">
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
                  {paginatedItems.map((item) => (
                    <Card
                      key={item.id}
                      shadow="sm"
                      padding="lg"
                      radius="md"
                      withBorder
                    >
                      <Group justify="space-between" mb="xs">
                        <Text fw={600}>{item.name}</Text>
                        <Badge color={getStockColor(item.quantity)}>
                          {getStockColor(item.quantity) === "red"
                            ? "Faible"
                            : getStockColor(item.quantity) === "yellow"
                            ? "Moyen"
                            : "OK"}
                        </Badge>
                      </Group>
                      <Text c="dimmed" size="sm">
                        Lot: {item.batch_number || "-"}
                      </Text>
                      <Group gap="xs" mt="xs">
                        <Badge color={getExpiryColor(item.expiry_date)}>
                          {getExpiryColor(item.expiry_date) === "red"
                            ? "Urgent"
                            : getExpiryColor(item.expiry_date) === "yellow"
                            ? "Bientôt"
                            : "OK"}
                        </Badge>
                        <Text size="sm">
                          Péremption:{" "}
                          {item.expiry_date
                            ? new Date(item.expiry_date).toLocaleDateString()
                            : "-"}
                        </Text>
                      </Group>
                      <Text size="sm" mt="xs">
                        Quantité: {item.quantity}
                      </Text>
                      <Text size="sm">Prix: {item.unit_price} DH</Text>
                      {canEdit && (
                        <Group gap="xs" mt="md">
                          <Button
                            size="xs"
                            variant="light"
                            leftSection={<IconEdit size={16} />}
                            onClick={() => {
                              setEditStock(item);
                              setModalOpen(true);
                            }}
                          >
                            Modifier
                          </Button>
                          <Button
                            size="xs"
                            variant="light"
                            color="blue"
                            leftSection={<IconShoppingCart size={16} />}
                            component="a"
                            href={`/marketplace?action=sell&product=${item.id}`}
                          >
                            Vendre
                          </Button>
                          <Button
                            size="xs"
                            variant="light"
                            color="orange"
                            leftSection={<IconArrowsRightLeft size={16} />}
                            component="a"
                            href={`/marketplace?action=exchange&product=${item.id}`}
                          >
                            Échanger
                          </Button>
                          <Button
                            size="xs"
                            variant="light"
                            color="red"
                            leftSection={<IconTrash size={16} />}
                            onClick={() => setDeleteId(item.id)}
                          >
                            Supprimer
                          </Button>
                        </Group>
                      )}
                    </Card>
                  ))}
                </SimpleGrid>

                {/* Pagination Controls */}
                {totalPages > 1 && (
                  <Group justify="center" mt="lg">
                    <Pagination
                      value={currentPage}
                      onChange={setCurrentPage}
                      total={totalPages}
                      size="sm"
                    />
                    <Text size="sm" c="dimmed">
                      Affichage de {(currentPage - 1) * itemsPerPage + 1} à{" "}
                      {Math.min(currentPage * itemsPerPage, filtered.length)}{" "}
                      sur {filtered.length} produits
                    </Text>
                  </Group>
                )}
              </Stack>
            )}
          </Tabs.Panel>

          {/* Expiry Management Tab */}
          <Tabs.Panel value="expiry" pt="md">
            {expiryStats.critical > 0 && (
              <Alert color="red" icon={<IconAlertTriangle size={16} />} mb="md">
                <Text fw={500}>Attention !</Text>
                <Text size="sm">
                  Vous avez {expiryStats.critical} produit
                  {expiryStats.critical > 1 ? "s" : ""} qui expire
                  {expiryStats.critical > 1 ? "nt" : ""} dans moins de 15 jours.
                  Action immédiate recommandée pour éviter les pertes
                  financières.
                </Text>
              </Alert>
            )}

            <Card withBorder radius="md" p="md">
              {expiringProducts.length === 0 ? (
                <Paper
                  p="xl"
                  radius="md"
                  style={{ textAlign: "center", background: "#f8f9fa" }}
                >
                  <ThemeIcon
                    size="xl"
                    radius="md"
                    color="green"
                    mx="auto"
                    mb="md"
                  >
                    <IconPackage size={rem(32)} />
                  </ThemeIcon>
                  <Text size="lg" fw={500} c="dimmed" mb="xs">
                    Aucun produit en péremption critique
                  </Text>
                  <Text size="sm" c="dimmed">
                    Tous vos produits ont des dates d'expiration acceptables
                  </Text>
                </Paper>
              ) : (
                <Table.ScrollContainer minWidth={800}>
                  <Table striped highlightOnHover withTableBorder>
                    <Table.Thead>
                      <Table.Tr>
                        <Table.Th>Produit</Table.Th>
                        <Table.Th>Lot</Table.Th>
                        <Table.Th>Expiration</Table.Th>
                        <Table.Th>Quantité</Table.Th>
                        <Table.Th>Valeur</Table.Th>
                        <Table.Th>Urgence</Table.Th>
                        <Table.Th>Actions</Table.Th>
                      </Table.Tr>
                    </Table.Thead>
                    <Table.Tbody>
                      {expiringProducts.map((product) => (
                        <Table.Tr key={product.id}>
                          <Table.Td>
                            <Box>
                              <Text fw={500}>{product.name}</Text>
                              <Text size="xs" c="dimmed">
                                {product.category}
                              </Text>
                            </Box>
                          </Table.Td>
                          <Table.Td>
                            <Text size="sm" style={{ fontFamily: "monospace" }}>
                              {product.batch_number || "-"}
                            </Text>
                          </Table.Td>
                          <Table.Td>
                            <Box>
                              <Text fw={500}>
                                {product.expiry_date
                                  ? format(
                                      new Date(product.expiry_date),
                                      "dd/MM/yyyy",
                                      { locale: fr }
                                    )
                                  : "-"}
                              </Text>
                              <Text size="sm" c="dimmed">
                                {product.days_until_expiry > 0
                                  ? `Dans ${product.days_until_expiry} jours`
                                  : `Expiré depuis ${Math.abs(
                                      product.days_until_expiry
                                    )} jours`}
                              </Text>
                            </Box>
                          </Table.Td>
                          <Table.Td>
                            <Box>
                              <Text fw={500}>{product.quantity}</Text>
                              <Text size="sm" c="dimmed">
                                {product.unit_price.toFixed(2)} DH/unité
                              </Text>
                            </Box>
                          </Table.Td>
                          <Table.Td>
                            <Text fw={500}>
                              {product.total_value.toFixed(2)} DH
                            </Text>
                          </Table.Td>
                          <Table.Td>
                            <Badge
                              color={getUrgencyColor(product.urgency_level)}
                              variant="light"
                              radius="sm"
                            >
                              {getUrgencyText(product.urgency_level)}
                            </Badge>
                          </Table.Td>
                          <Table.Td>
                            <Group gap="xs">
                              <Tooltip label="Voir les détails">
                                <ActionIcon
                                  variant="light"
                                  color="#00B5FF"
                                  size="sm"
                                >
                                  <IconEye size={16} />
                                </ActionIcon>
                              </Tooltip>
                              <Tooltip label="Vendre ce produit">
                                <ActionIcon
                                  variant="light"
                                  color="blue"
                                  size="sm"
                                  component="a"
                                  href={`/marketplace?action=sell&product=${product.id}`}
                                >
                                  <IconShoppingCart size={16} />
                                </ActionIcon>
                              </Tooltip>
                              <Tooltip label="Proposer un échange">
                                <ActionIcon
                                  variant="light"
                                  color="orange"
                                  size="sm"
                                  component="a"
                                  href={`/marketplace?action=exchange&product=${product.id}`}
                                >
                                  <IconArrowsRightLeft size={16} />
                                </ActionIcon>
                              </Tooltip>
                            </Group>
                          </Table.Td>
                        </Table.Tr>
                      ))}
                    </Table.Tbody>
                  </Table>
                </Table.ScrollContainer>
              )}
            </Card>
          </Tabs.Panel>

          {/* Low Stock Tab */}
          <Tabs.Panel value="lowstock" pt="md">
            <Card withBorder radius="md" p="md">
              {stocks.filter((s) => s.quantity < 20).length === 0 ? (
                <Paper
                  p="xl"
                  radius="md"
                  style={{ textAlign: "center", background: "#f8f9fa" }}
                >
                  <ThemeIcon
                    size="xl"
                    radius="md"
                    color="green"
                    mx="auto"
                    mb="md"
                  >
                    <IconPackage size={rem(32)} />
                  </ThemeIcon>
                  <Text size="lg" fw={500} c="dimmed" mb="xs">
                    Aucun produit en stock faible
                  </Text>
                  <Text size="sm" c="dimmed">
                    Tous vos produits ont des niveaux de stock acceptables
                  </Text>
                </Paper>
              ) : (
                <SimpleGrid cols={{ base: 1, sm: 2, md: 3 }} spacing="lg">
                  {stocks
                    .filter((s) => s.quantity < 20)
                    .map((item) => (
                      <Card
                        key={item.id}
                        shadow="sm"
                        padding="lg"
                        radius="md"
                        withBorder
                        style={{
                          borderColor:
                            item.quantity < 5 ? "#ff6b6b" : "#ffd43b",
                        }}
                      >
                        <Group justify="space-between" mb="xs">
                          <Text fw={600}>{item.name}</Text>
                          <Badge color={getStockColor(item.quantity)}>
                            {item.quantity < 5 ? "CRITIQUE" : "FAIBLE"}
                          </Badge>
                        </Group>
                        <Text c="dimmed" size="sm">
                          Lot: {item.batch_number || "-"}
                        </Text>
                        <Text
                          size="lg"
                          fw={700}
                          c={item.quantity < 5 ? "red" : "orange"}
                          mt="xs"
                        >
                          {item.quantity} unités restantes
                        </Text>
                        <Text size="sm" c="dimmed">
                          Prix: {item.unit_price} DH
                        </Text>
                        {canEdit && (
                          <Group gap="xs" mt="md">
                            <Button
                              size="xs"
                              variant="light"
                              leftSection={<IconEdit size={16} />}
                              onClick={() => {
                                setEditStock(item);
                                setModalOpen(true);
                              }}
                            >
                              Réapprovisionner
                            </Button>
                            <Button
                              size="xs"
                              variant="light"
                              color="blue"
                              leftSection={<IconShoppingCart size={16} />}
                              component="a"
                              href={`/marketplace?action=sell&product=${item.id}`}
                            >
                              Vendre
                            </Button>
                            <Button
                              size="xs"
                              variant="light"
                              color="orange"
                              leftSection={<IconArrowsRightLeft size={16} />}
                              component="a"
                              href={`/marketplace?action=exchange&product=${item.id}`}
                            >
                              Échanger
                            </Button>
                          </Group>
                        )}
                      </Card>
                    ))}
                </SimpleGrid>
              )}
            </Card>
          </Tabs.Panel>
        </Tabs>
      </Stack>

      {/* Modals */}
      <Modal
        opened={modalOpen}
        onClose={() => {
          setModalOpen(false);
          setEditStock(null);
        }}
        title={editStock ? "Modifier le stock" : "Ajouter un produit"}
      >
        <StockForm
          initialValues={editStock || undefined}
          onSubmit={handleSave}
          loading={actionLoading}
        />
      </Modal>
      <Modal
        opened={!!deleteId}
        onClose={() => setDeleteId(null)}
        title="Confirmer la suppression"
      >
        <Stack>
          <Text>
            Êtes-vous sûr de vouloir supprimer ce produit ? Cette action est
            irréversible.
          </Text>
          <Group justify="end">
            <Button variant="default" onClick={() => setDeleteId(null)}>
              Annuler
            </Button>
            <Button
              color="red"
              loading={actionLoading}
              onClick={() => deleteId && handleDelete(deleteId)}
            >
              Supprimer
            </Button>
          </Group>
        </Stack>
      </Modal>
      {notification && (
        <Notification
          color="green"
          title="Succès"
          onClose={() => setNotification(null)}
        >
          {notification}
        </Notification>
      )}
    </Container>
  );
}
