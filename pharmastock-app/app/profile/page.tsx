"use client";

import { useState, useEffect } from "react";
import {
  <PERSON><PERSON>er,
  Card,
  Button,
  TextInput,
  Tabs,
  Divider,
  Switch,
  Group,
  Stack,
  Text,
  Title,
  ThemeIcon,
  Box,
  Grid,
  Skeleton,
  PasswordInput,
  Flex,
  Badge,
  rem,
} from "@mantine/core";
import {
  IconBuilding,
  IconMapPin,
  IconPhone,
  IconMail,
  IconUser,
  IconLock,
  IconBell,
  IconShield,
  IconClock,
  IconEdit,
  IconCheck,
  IconX,
} from "@tabler/icons-react";
import { useAuth } from "@/contexts/auth-context";
import { notifications } from "@mantine/notifications";

export default function ProfilePage() {
  const [isEditing, setIsEditing] = useState(false);
  const [mounted, setMounted] = useState(false);
  const { user, pharmacy, loading, updatePharmacy } = useAuth();
  const [formData, setFormData] = useState({
    name: "",
    license_number: "",
    address: "",
    city: "",
    phone: "",
    email: "",
  });

  // Update form data when pharmacy data is loaded
  useEffect(() => {
    if (pharmacy) {
      setFormData({
        name: pharmacy.name || "",
        license_number: pharmacy.license_number || "",
        address: pharmacy.address || "",
        city: pharmacy.city || "",
        phone: pharmacy.phone || "",
        email: user?.email || "",
      });
    }
  }, [pharmacy, user]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData((prev) => ({ ...prev, [name]: value }));
  };

  const handleSave = async () => {
    try {
      await updatePharmacy(formData);
      setIsEditing(false);
      notifications.show({
        title: "Succès",
        message: "Informations mises à jour avec succès",
        color: "green",
        icon: <IconCheck size={16} />,
      });
    } catch (error) {
      console.error("Error updating pharmacy:", error);
      notifications.show({
        title: "Erreur",
        message: "Erreur lors de la mise à jour",
        color: "red",
        icon: <IconX size={16} />,
      });
    }
  };

  // Handle hydration mismatch by only rendering after mount
  if (typeof window !== "undefined") {
    if (!mounted) {
      setMounted(true);
      return null;
    }
  }

  if (loading) {
    return (
      <Container size="xl" py="md">
        <Stack gap="lg">
          <Box>
            <Skeleton height={32} width={200} mb="xs" />
            <Skeleton height={16} width={400} />
          </Box>
          <Card withBorder radius="md" p="md">
            <Stack gap="lg">
              <Grid>
                {[...Array(6)].map((_, i) => (
                  <Grid.Col key={i} span={{ base: 12, md: 6 }}>
                    <Stack gap="xs">
                      <Skeleton height={16} width={100} />
                      <Skeleton height={40} />
                    </Stack>
                  </Grid.Col>
                ))}
              </Grid>
            </Stack>
          </Card>
        </Stack>
      </Container>
    );
  }

  return (
    <Container size="xl" py="md">
      <Stack gap="lg">
        <Box>
          <Title order={1} size="h2" c="#00B5FF" mb="xs">
            <Group gap="sm">
              <ThemeIcon size="lg" radius="md" color="#00B5FF">
                <IconUser size={rem(24)} />
              </ThemeIcon>
              Profil
            </Group>
          </Title>
          <Text c="dimmed" size="sm">
            Gérez vos informations personnelles et les paramètres de votre
            pharmacie
          </Text>
        </Box>

        <Card withBorder radius="md" p="md">
          <Tabs defaultValue="pharmacy">
            <Tabs.List>
              <Tabs.Tab
                value="pharmacy"
                leftSection={<IconBuilding size={16} />}
              >
                Pharmacie
              </Tabs.Tab>
              <Tabs.Tab value="personal" leftSection={<IconUser size={16} />}>
                Personnel
              </Tabs.Tab>
              <Tabs.Tab value="security" leftSection={<IconShield size={16} />}>
                Sécurité
              </Tabs.Tab>
              <Tabs.Tab
                value="notifications"
                leftSection={<IconBell size={16} />}
              >
                Notifications
              </Tabs.Tab>
            </Tabs.List>

            <Tabs.Panel value="pharmacy" pt="md">
              <Stack gap="lg">
                <Group justify="space-between">
                  <Box>
                    <Title order={3}>Informations de la pharmacie</Title>
                    <Text c="dimmed" size="sm">
                      Gérez les informations de votre pharmacie
                    </Text>
                  </Box>
                  <Button
                    variant={isEditing ? "outline" : "filled"}
                    onClick={() => setIsEditing(!isEditing)}
                    leftSection={<IconEdit size={16} />}
                  >
                    {isEditing ? "Annuler" : "Modifier"}
                  </Button>
                </Group>

                <Grid>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Nom de la pharmacie"
                      name="name"
                      value={formData.name}
                      onChange={(e) => handleChange(e)}
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Numéro de licence"
                      name="license_number"
                      value={formData.license_number}
                      onChange={(e) => handleChange(e)}
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Adresse"
                      name="address"
                      value={formData.address}
                      onChange={(e) => handleChange(e)}
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Ville"
                      name="city"
                      value={formData.city}
                      onChange={(e) => handleChange(e)}
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Téléphone"
                      name="phone"
                      value={formData.phone}
                      onChange={(e) => handleChange(e)}
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Email"
                      name="email"
                      value={formData.email}
                      onChange={(e) => handleChange(e)}
                      disabled={!isEditing}
                    />
                  </Grid.Col>
                </Grid>

                <Divider />

                <Box>
                  <Title order={4} mb="md">
                    Horaires d'ouverture
                  </Title>
                  <Grid>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                      <Text size="sm" fw={500} mb="xs">
                        Lundi - Vendredi
                      </Text>
                      <Group>
                        <TextInput
                          type="time"
                          defaultValue="09:00"
                          disabled={!isEditing}
                          style={{ flex: 1 }}
                        />
                        <TextInput
                          type="time"
                          defaultValue="19:00"
                          disabled={!isEditing}
                          style={{ flex: 1 }}
                        />
                      </Group>
                    </Grid.Col>
                    <Grid.Col span={{ base: 12, md: 6 }}>
                      <Text size="sm" fw={500} mb="xs">
                        Samedi
                      </Text>
                      <Group>
                        <TextInput
                          type="time"
                          defaultValue="09:00"
                          disabled={!isEditing}
                          style={{ flex: 1 }}
                        />
                        <TextInput
                          type="time"
                          defaultValue="13:00"
                          disabled={!isEditing}
                          style={{ flex: 1 }}
                        />
                      </Group>
                    </Grid.Col>
                  </Grid>
                </Box>

                {isEditing && (
                  <Group justify="flex-end">
                    <Button
                      variant="outline"
                      onClick={() => setIsEditing(false)}
                    >
                      Annuler
                    </Button>
                    <Button onClick={handleSave}>Enregistrer</Button>
                  </Group>
                )}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="personal" pt="md">
              <Stack gap="lg">
                <Group justify="space-between">
                  <Box>
                    <Title order={3}>Informations personnelles</Title>
                    <Text c="dimmed" size="sm">
                      Gérez vos informations personnelles
                    </Text>
                  </Box>
                  <Button
                    variant={isEditing ? "outline" : "filled"}
                    onClick={() => setIsEditing(!isEditing)}
                    leftSection={<IconEdit size={16} />}
                  >
                    {isEditing ? "Annuler" : "Modifier"}
                  </Button>
                </Group>

                <Grid>
                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Nom"
                      defaultValue="Mohammed"
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Prénom"
                      defaultValue="El Amrani"
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Email personnel"
                      defaultValue="<EMAIL>"
                      disabled={!isEditing}
                    />
                  </Grid.Col>

                  <Grid.Col span={{ base: 12, md: 6 }}>
                    <TextInput
                      label="Téléphone personnel"
                      defaultValue="0661234567"
                      disabled={!isEditing}
                    />
                  </Grid.Col>
                </Grid>

                {isEditing && (
                  <Group justify="flex-end">
                    <Button
                      variant="outline"
                      onClick={() => setIsEditing(false)}
                    >
                      Annuler
                    </Button>
                    <Button>Enregistrer les modifications</Button>
                  </Group>
                )}
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="security" pt="md">
              <Stack gap="lg">
                <Box>
                  <Title order={3}>Sécurité</Title>
                  <Text c="dimmed" size="sm">
                    Gérez vos paramètres de sécurité et de connexion
                  </Text>
                </Box>

                <Stack gap="lg">
                  <Box>
                    <Title order={4} mb="md">
                      Changer le mot de passe
                    </Title>
                    <Stack gap="md">
                      <PasswordInput
                        label="Mot de passe actuel"
                        placeholder="Entrez votre mot de passe actuel"
                      />
                      <PasswordInput
                        label="Nouveau mot de passe"
                        placeholder="Entrez votre nouveau mot de passe"
                      />
                      <PasswordInput
                        label="Confirmer le nouveau mot de passe"
                        placeholder="Confirmez votre nouveau mot de passe"
                      />
                      <Group justify="flex-start">
                        <Button leftSection={<IconLock size={16} />}>
                          Mettre à jour le mot de passe
                        </Button>
                      </Group>
                    </Stack>
                  </Box>

                  <Divider />

                  <Box>
                    <Title order={4} mb="xs">
                      Authentification à deux facteurs
                    </Title>
                    <Text size="sm" c="dimmed" mb="md">
                      Ajoutez une couche de sécurité supplémentaire à votre
                      compte
                    </Text>
                    <Button
                      variant="outline"
                      leftSection={<IconShield size={16} />}
                    >
                      Configurer l'authentification à deux facteurs
                    </Button>
                  </Box>
                </Stack>
              </Stack>
            </Tabs.Panel>

            <Tabs.Panel value="notifications" pt="md">
              <Stack gap="lg">
                <Box>
                  <Title order={3}>Préférences de notification</Title>
                  <Text c="dimmed" size="sm">
                    Gérez vos préférences de notification
                  </Text>
                </Box>

                <Stack gap="lg">
                  <Box>
                    <Title order={4} mb="md">
                      Notifications par email
                    </Title>
                    <Stack gap="md">
                      <Group justify="space-between">
                        <Box>
                          <Text fw={500}>Nouvelles demandes urgentes</Text>
                          <Text size="sm" c="dimmed">
                            Recevez un email pour les nouvelles demandes
                            urgentes
                          </Text>
                        </Box>
                        <Switch defaultChecked />
                      </Group>
                      <Group justify="space-between">
                        <Box>
                          <Text fw={500}>Mises à jour des offres</Text>
                          <Text size="sm" c="dimmed">
                            Recevez un email pour les mises à jour des offres
                            suivies
                          </Text>
                        </Box>
                        <Switch defaultChecked />
                      </Group>
                    </Stack>
                  </Box>

                  <Divider />

                  <Box>
                    <Title order={4} mb="md">
                      Notifications push
                    </Title>
                    <Stack gap="md">
                      <Group justify="space-between">
                        <Box>
                          <Text fw={500}>Messages instantanés</Text>
                          <Text size="sm" c="dimmed">
                            Recevez des notifications pour les nouveaux messages
                          </Text>
                        </Box>
                        <Switch defaultChecked />
                      </Group>
                      <Group justify="space-between">
                        <Box>
                          <Text fw={500}>Alertes de stock</Text>
                          <Text size="sm" c="dimmed">
                            Recevez des notifications pour les alertes de stock
                          </Text>
                        </Box>
                        <Switch defaultChecked />
                      </Group>
                    </Stack>
                  </Box>
                </Stack>
              </Stack>
            </Tabs.Panel>
          </Tabs>
        </Card>
      </Stack>
    </Container>
  );
}
