"use client";

import { createContext, useContext, useState } from "react";
import { toast } from "sonner";

export type NotificationType =
  | "price_change"
  | "expiry_alert"
  | "urgent_request"
  | "interest_shown"
  | "message_received"
  | "offer_update"
  | "system_alert"
  | "stock_alert"
  | "order_status"
  | "payment_status"
  | "verification_status"
  | "market_update"
  | "maintenance_alert"
  | "security_alert"
  | "product_recall"
  | "new_feature";

export type NotificationPriority = "low" | "medium" | "high" | "urgent" | "critical";

interface NotificationContextType {
  showNotification: (
    message: string,
    type?: "success" | "error" | "info"
  ) => void;
  notifications: Notification[];
  markAsRead: (id: string) => void;
  clearAll: () => void;
}

interface Notification {
  id: string;
  message: string;
  type: NotificationType;
  priority: NotificationPriority;
  read: boolean;
  timestamp: Date;
}

const NotificationContext = createContext<NotificationContextType | undefined>(
  undefined
);

export function NotificationProvider({
  children,
}: {
  children: React.ReactNode;
}) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  const showNotification = (
    message: string,
    type: "success" | "error" | "info" = "info"
  ) => {
    switch (type) {
      case "success":
        toast.success(message);
        break;
      case "error":
        toast.error(message);
        break;
      default:
        toast(message);
    }
  };

  const markAsRead = (id: string) => {
    setNotifications(prev =>
      prev.map(notif =>
        notif.id === id ? { ...notif, read: true } : notif
      )
    );
  };

  const clearAll = () => {
    setNotifications([]);
  };

  return (
    <NotificationContext.Provider value={{ showNotification, notifications, markAsRead, clearAll }}>
      {children}
    </NotificationContext.Provider>
  );
}

export const useNotification = () => {
  const context = useContext(NotificationContext);
  if (context === undefined) {
    throw new Error(
      "useNotification must be used within a NotificationProvider"
    );
  }
  return context;
};

// Add useNotifications as an alias for useNotification for backward compatibility
export const useNotifications = useNotification;
