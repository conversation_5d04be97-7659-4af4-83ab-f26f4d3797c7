"use client";

import { createContext, useContext, useState, useEffect } from "react";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "sonner";

async function getSession() {
  try {
    const response = await fetch("/api/auth/session", {
      headers: {
        Accept: "application/json",
      },
      credentials: "include",
      cache: "no-store",
    });

    if (!response.ok) {
      if (response.status === 401) {
        return { user: null };
      }
      throw new Error(`Session fetch failed: ${response.status}`);
    }

    const data = await response.json();
    if (!data || !data.user) {
      return { user: null };
    }

    return data;
  } catch (error) {
    console.error("Session fetch error:", error);
    return { user: null };
  }
}

export interface AuthContextType {
  user: {
    id: string;
    email: string;
    role: string;
    pharmacyId?: string;
  } | null;
  pharmacy: {
    id: string;
    name: string;
    address: string;
    phone: string;
    latitude: number;
    longitude: number;
  } | null;
  loading: boolean;
  error: string | null;
  isPharmacy: boolean;
  login: (
    email: string,
    password: string
  ) => Promise<{ error: string | null; user: any }>;
  signOut: () => Promise<void>;
  signUp: (credentials: any) => Promise<{ error: string | null }>;
  resetPassword: (email: string) => Promise<{ error: string | null }>;
  updatePharmacy: (data: any) => Promise<{ error: string | null }>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const router = useRouter();
  const pathname = usePathname();
  const [state, setState] = useState<
    Omit<
      AuthContextType,
      "login" | "signOut" | "signUp" | "resetPassword" | "updatePharmacy"
    >
  >({
    user: null,
    pharmacy: null,
    loading: true,
    error: null,
    isPharmacy: false,
  });

  const fetchPharmacyData = async (pharmacyId: string) => {
    try {
      console.log("Fetching pharmacy data for:", pharmacyId);
      const response = await fetch(`/api/pharmacy/${pharmacyId}`, {
        headers: {
          Accept: "application/json",
        },
        credentials: "include",
      });

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        console.error("Pharmacy fetch failed:", {
          status: response.status,
          statusText: response.statusText,
          error: errorData.error,
        });
        throw new Error(
          errorData.error || `Failed to fetch pharmacy data: ${response.status}`
        );
      }

      const data = await response.json();
      console.log("Pharmacy data fetched successfully:", data.pharmacy);
      return data.pharmacy;
    } catch (error) {
      console.error("Error fetching pharmacy data:", error);
      return null;
    }
  };

  const checkSession = async () => {
    try {
      console.log("Starting session check...");
      setState((prev) => ({ ...prev, loading: true }));
      const { user } = await getSession();

      console.log("Session check result:", { user });

      if (!user) {
        console.log("No user found, clearing state");
        setState((prev) => ({
          ...prev,
          user: null,
          pharmacy: null,
          loading: false,
        }));
        return;
      }

      let pharmacy = null;
      // Only fetch pharmacy data for non-super_admin users
      if (user.role !== "super_admin" && user.pharmacyId) {
        pharmacy = await fetchPharmacyData(user.pharmacyId);
      }

      console.log("Updating state with user and pharmacy:", { user, pharmacy });
      setState((prev) => ({
        ...prev,
        user,
        pharmacy,
        loading: false,
        error: null,
        isPharmacy:
          user?.role === "admin" ||
          user?.role === "pharmacist" ||
          user?.role === "owner",
      }));

      // Wait for state to be updated
      await new Promise((resolve) => setTimeout(resolve, 500));
    } catch (error) {
      console.error("Session check error:", error);
      setState((prev) => ({
        ...prev,
        user: null,
        pharmacy: null,
        loading: false,
        error: error instanceof Error ? error.message : "Session check failed",
        isPharmacy: false,
      }));
    }
  };

  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      if (!mounted) return;
      await checkSession();
    };

    console.log("Auth provider mounted, initializing...");
    initializeAuth();

    return () => {
      mounted = false;
    };
  }, []);

  const login = async (email: string, password: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await fetch("/api/auth/login", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        credentials: "include",
        body: JSON.stringify({ email, password }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Identifiants invalides");
      }

      // Use pharmacy data from login response if available
      let pharmacy = data.pharmacy || null;

      // Update state with user and pharmacy data
      setState((prev) => ({
        ...prev,
        user: data.user,
        pharmacy,
        loading: false,
        error: null,
        isPharmacy:
          data.user.role === "admin" ||
          data.user.role === "pharmacist" ||
          data.user.role === "owner",
      }));

      // Wait for state to be updated
      await new Promise((resolve) => setTimeout(resolve, 100));

      // Debug: print cookies after login
      if (typeof document !== "undefined") {
        console.log("Cookies after login:", document.cookie);
      }
      console.log("User after login:", data.user);

      // Add a short delay to ensure cookie is set before session check
      await new Promise((resolve) => setTimeout(resolve, 300));
      await checkSession();

      // Generate demo notifications for demo accounts
      if (email === "<EMAIL>" && pharmacy?.id) {
        try {
          console.log("🔔 Generating demo notifications for admin user...");
          await fetch("/api/demo/generate-notifications", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            credentials: "include",
            body: JSON.stringify({
              userId: data.user.id,
              pharmacyId: pharmacy.id,
            }),
          });
        } catch (error) {
          console.log("Note: Could not generate demo notifications:", error);
          // Don't fail login if notifications fail
        }
      }

      // Return success with user data
      return { error: null, user: data.user };
    } catch (error) {
      setState((prev) => ({
        ...prev,
        loading: false,
        error: error instanceof Error ? error.message : "Erreur de connexion",
      }));
      return {
        error: error instanceof Error ? error.message : "Erreur de connexion",
        user: null,
      };
    }
  };

  const logout = async () => {
    try {
      // Clear local state first
      setState((prev) => ({
        ...prev,
        user: null,
        pharmacy: null,
        error: null,
        loading: true,
      }));

      // Call logout endpoint
      const response = await fetch("/api/auth/logout", {
        method: "POST",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error("Logout failed");
      }

      // Clear any local storage or other client state
      localStorage.removeItem("pharmacy-id");
      localStorage.removeItem("user-role");
      sessionStorage.clear();

      // Use window.location for a full page reload after logout
      window.location.href = "/auth/login";
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("Failed to logout. Please try again.");
    } finally {
      setState((prev) => ({ ...prev, loading: false }));
    }
  };

  const signUp = async (credentials: any) => {
    try {
      const response = await fetch("/api/auth/register", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(credentials),
      });

      let responseData;
      try {
        responseData = await response.json();
      } catch (parseError) {
        console.error("Failed to parse response JSON:", parseError);
        throw new Error("Invalid response from server");
      }

      if (!response.ok) {
        console.error("Registration error response:", responseData);
        const errorMessage =
          responseData?.error ||
          responseData?.message ||
          `Registration failed (${response.status})`;
        throw new Error(errorMessage);
      }

      if (responseData.user) {
        await checkSession();
        return { error: null };
      } else {
        throw new Error(
          responseData.message || "Registration failed - no user data returned"
        );
      }
    } catch (error) {
      console.error("Registration error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Registration failed";
      return { error: errorMessage };
    }
  };

  const resetPassword = async (email: string) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await fetch("/api/auth/reset-password", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        body: JSON.stringify({ email }),
      });

      const data = await response.json();

      if (!response.ok) {
        throw new Error(data.error || "Password reset failed");
      }

      return { error: null };
    } catch (error) {
      console.error("Password reset error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Password reset failed";
      setState((prev) => ({ ...prev, error: errorMessage, loading: false }));
      return { error: errorMessage };
    }
  };

  const updatePharmacy = async (data: any) => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: null }));
      const response = await fetch(`/api/pharmacy/${data.id}`, {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Accept: "application/json",
        },
        credentials: "include",
        body: JSON.stringify(data),
      });

      const responseData = await response.json();

      if (!response.ok) {
        throw new Error(responseData.error || "Update failed");
      }

      // Update local pharmacy data
      setState((prev) => ({
        ...prev,
        pharmacy: { ...prev.pharmacy, ...data },
        loading: false,
        error: null,
      }));

      return { error: null };
    } catch (error) {
      console.error("Update pharmacy error:", error);
      const errorMessage =
        error instanceof Error ? error.message : "Update failed";
      setState((prev) => ({ ...prev, error: errorMessage, loading: false }));
      return { error: errorMessage };
    }
  };

  return (
    <AuthContext.Provider
      value={{
        ...state,
        login,
        signOut: logout,
        signUp,
        resetPassword,
        updatePharmacy,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
