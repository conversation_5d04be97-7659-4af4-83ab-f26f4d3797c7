"use client";

import React, { createContext, use<PERSON>ontext, PropsWithChildren } from "react";

// Placeholder context. Do not use for direct DB access from client.
const DatabaseContext = createContext<undefined>(undefined);

export const DatabaseProvider: React.FC<PropsWithChildren> = ({ children }) => {
  // No value provided; server DB access is not available on client.
  return (
    <DatabaseContext.Provider value={undefined}>
      {children}
    </DatabaseContext.Provider>
  );
};

export function useDatabase() {
  throw new Error("useDatabase is not available on the client. Use API routes or server components for DB access.");
}
