"use client";

import { createContext, useContext, useEffect, useState, useMemo } from "react";
import { createClient, SupabaseClient } from "@supabase/supabase-js";
import { useAuth } from "./auth-context";

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://shptfbdcuwaxyosyjsas.supabase.co';
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocHRmYmRjdXdheHlvc3lqc2FzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MTgyNDMsImV4cCI6MjA2NjM5NDI0M30.tvoMbGEU3nL0tZvqib_jrITKzRpf1DE3A2CIk9czTd0';
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.***********************************************************************************.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU';

interface SupabaseContextType {
  supabase: SupabaseClient;
  pharmacy: {
    id: string;
    name: string;
    address: string;
    phone: string;
    latitude: number;
    longitude: number;
  } | null;
}

const SupabaseContext = createContext<SupabaseContextType | undefined>(undefined);

export function SupabaseProvider({ children }: { children: React.ReactNode }) {
  const supabase = useMemo(() => createClient(supabaseUrl, supabaseAnonKey), []);
  const adminClient = useMemo(() => createClient(supabaseUrl, supabaseServiceKey), []);
  const { user } = useAuth();
  const [pharmacy, setPharmacy] = useState<SupabaseContextType['pharmacy']>(null);

  useEffect(() => {
    const fetchPharmacyData = async () => {
      console.log('Starting pharmacy data fetch...', {
        user,
        pharmacyId: user?.pharmacyId,
        timestamp: new Date().toISOString()
      });

      if (!user?.pharmacyId) {
        console.log('No pharmacy ID found in user data');
        setPharmacy(null);
        return;
      }

      try {
        // Use admin client to bypass RLS
        const { data, error } = await adminClient
          .from('pharmacies')
          .select(`
            id,
            name,
            address,
            phone,
            location
          `)
          .eq('id', user.pharmacyId)
          .single();

        if (error) {
          console.error('Error fetching pharmacy:', {
            error: {
              message: error.message,
              details: error.details,
              hint: error.hint,
              code: error.code
            },
            context: {
              pharmacyId: user.pharmacyId,
              userId: user.id,
              timestamp: new Date().toISOString()
            },
            query: {
              table: 'pharmacies',
              columns: ['id', 'name', 'address', 'phone', 'location'],
              filter: `id = ${user.pharmacyId}`
            }
          });
          setPharmacy(null);
          return;
        }

        if (!data) {
          console.warn('No pharmacy data found:', {
            pharmacyId: user.pharmacyId,
            userId: user.id,
            timestamp: new Date().toISOString()
          });
          setPharmacy(null);
          return;
        }

        console.log('Pharmacy data fetched successfully:', {
          pharmacyId: data.id,
          hasLocation: !!data.location,
          timestamp: new Date().toISOString()
        });

        // Extract latitude and longitude from PostGIS point
        const location = data.location ? {
          latitude: data.location.coordinates[1],
          longitude: data.location.coordinates[0]
        } : { latitude: 0, longitude: 0 };

        setPharmacy({
          id: data.id,
          name: data.name,
          address: data.address,
          phone: data.phone,
          ...location
        });
      } catch (error) {
        console.error('Unexpected error in fetchPharmacyData:', {
          error: error instanceof Error ? {
            name: error.name,
            message: error.message,
            stack: error.stack
          } : error,
          context: {
            pharmacyId: user.pharmacyId,
            userId: user.id,
            timestamp: new Date().toISOString()
          }
        });
        setPharmacy(null);
      }
    };

    fetchPharmacyData();
  }, [adminClient, user?.pharmacyId, user?.id]);

  const value = useMemo(() => ({ supabase, pharmacy }), [supabase, pharmacy]);

  return (
    <SupabaseContext.Provider value={value}>
      {children}
    </SupabaseContext.Provider>
  );
}

export function useSupabase() {
  const context = useContext(SupabaseContext);
  if (context === undefined) {
    throw new Error('useSupabase must be used within a SupabaseProvider');
  }
  return context;
} 