import { NextResponse } from 'next/server';

export type ApiHandler = (
  req: Request,
  params?: { [key: string]: string }
) => Promise<NextResponse>;

export function createApiHandler(handler: ApiHandler) {
  return async function POST(req: Request, { params }: { params?: { [key: string]: string } } = {}) {
    try {
      return await handler(req, params);
    } catch (error) {
      console.error('API Error:', error);
      return NextResponse.json(
        { error: error instanceof Error ? error.message : 'Internal Server Error' },
        { status: 500 }
      );
    }
  };
} 