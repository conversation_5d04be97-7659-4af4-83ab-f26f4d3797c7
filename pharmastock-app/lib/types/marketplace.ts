import type { Database } from "@/types/supabase";

// Database type utilities
type Tables = Database['public']['Tables']

// Core listing type representing a product listing in the marketplace
export interface Listing {
  id: string;
  product_id: string;
  pharmacy_id: string;
  quantity: number;
  price_per_unit: number;
  minimum_order: number;
  status: 'active' | 'inactive' | 'sold';
  expires_at: string | null;
  created_at: string;
  updated_at: string;
}

// Database types for related entities
export interface Pharmacy {
  id: string;
  name: string;
  distance?: number;
  created_at?: string;
  email?: string;
  phone?: string;
  address?: string;
  city?: string;
  license_number?: string;
  is_verified?: boolean;
  is_admin?: boolean;
  latitude?: number;
  longitude?: number;
}

// Base product type from database
export interface BaseProduct {
  id: string;
  name: string;
  description?: string;
  manufacturer: string;
  category: string;
  expiry_date: string;
  original_price: number;
  batch_number?: string;
  unit_price?: number;
  dosage?: string;
  form?: string;
  active_ingredients?: string;
  created_at: string;
  updated_at: string;
  quantity?: number;
  pharmacy_id?: string;
  packaging_state?: string;
  condition?: string;
  barcode?: string;
}

// Extended product type with relations
export interface Product extends BaseProduct {
  pharmacy: Pharmacy;
  listing: Listing;
}

// Search result type from the database function
export interface SearchResult extends BaseProduct {
  pharmacy_id: string;
  pharmacy_name: string;
  distance: number;
  similarity: number;
  listing_id: string;
  quantity: number;
  price_per_unit: number;
  minimum_order: number;
  expires_at: string | null;
}

// Type for nearby pharmacy from database function
export interface NearbyPharmacy {
  id: string;
  distance: number;
}

// Filter types
export type FilterType = 'all' | 'expiring' | 'discounted' | 'new'; 