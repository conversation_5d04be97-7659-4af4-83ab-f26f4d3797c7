interface WorkboxEvent extends Event {
  type: string;
  target: any;
}

export async function registerServiceWorker() {
  if (
    typeof window !== 'undefined' &&
    'serviceWorker' in navigator &&
    window.workbox !== undefined
  ) {
    const wb = window.workbox;
    
    // Add event listeners
    wb.addEventListener('installed', (event: WorkboxEvent) => {
      console.log(`PWA installed: ${event.type}`);
    });

    wb.addEventListener('controlling', (event: WorkboxEvent) => {
      console.log(`PWA controlling: ${event.type}`);
    });

    wb.addEventListener('activated', (event: WorkboxEvent) => {
      console.log(`PWA activated: ${event.type}`);
    });

    // Register the service worker after all event listeners are added
    wb.register();

    // Request notification permission
    try {
      const status = await Notification.requestPermission();
      if (status === 'granted') {
        console.log('Notification permission granted');
      } else {
        console.log('Notification permission denied');
      }
    } catch (err) {
      console.error('Error requesting notification permission:', err);
    }
  }
}

export async function subscribeToPushNotifications(registration: ServiceWorkerRegistration) {
  try {
    const subscription = await registration.pushManager.subscribe({
      userVisibleOnly: true,
      applicationServerKey: process.env.NEXT_PUBLIC_VAPID_PUBLIC_KEY
    });

    console.log('Push notification subscription:', subscription);
    return subscription;
  } catch (err) {
    console.error('Error subscribing to push notifications:', err);
    return null;
  }
}

// Add to types.d.ts
declare global {
  interface Window {
    workbox: {
      addEventListener: (event: string, callback: (event: WorkboxEvent) => void) => void;
      register: () => Promise<void>;
    };
  }
} 