import { createTheme, MantineColorsTuple } from '@mantine/core';

// Custom color palette based on #00B5FF
const primaryColor: MantineColorsTuple = [
  '#e6f7ff',
  '#bae7ff',
  '#91d5ff',
  '#69c0ff',
  '#40a9ff',
  '#1890ff',
  '#096dd9',
  '#0050b3',
  '#003a8c',
  '#002766'
];

// Extended color palette for the pharmacy theme
const pharmacyBlue: MantineColorsTuple = [
  '#e6f7ff',
  '#bae7ff',
  '#91d5ff',
  '#69c0ff',
  '#40a9ff',
  '#00B5FF', // Your specified color
  '#0099e6',
  '#007acc',
  '#005cb3',
  '#003d99'
];

const pharmacyGreen: MantineColorsTuple = [
  '#f6ffed',
  '#d9f7be',
  '#b7eb8f',
  '#95de64',
  '#73d13d',
  '#52c41a',
  '#389e0d',
  '#237804',
  '#135200',
  '#092b00'
];

export const mantineTheme = createTheme({
  primaryColor: 'pharmacy-blue',
  colors: {
    'pharmacy-blue': pharmacyBlue,
    'pharmacy-green': pharmacyGreen,
    'primary': primaryColor,
  },
  fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
  headings: {
    fontFamily: 'Inter, -apple-system, BlinkMacSystemFont, Segoe UI, Roboto, sans-serif',
    fontWeight: '700',
    sizes: {
      h1: { fontSize: '2.125rem', lineHeight: '1.2' },
      h2: { fontSize: '1.625rem', lineHeight: '1.3' },
      h3: { fontSize: '1.375rem', lineHeight: '1.4' },
      h4: { fontSize: '1.125rem', lineHeight: '1.45' },
    },
  },
  defaultRadius: 'xs', // Changed from 'lg' to 'xs' for sharp edges
  cursorType: 'pointer',
  focusRing: 'auto',
  respectReducedMotion: true,
  autoContrast: true,

  // Component-specific styles for professional, sharp interface
  components: {
    Button: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        size: 'md',
      },
      styles: {
        root: {
          fontWeight: 600,
          transition: 'all 0.15s ease',
          border: '1px solid transparent',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: '0 4px 12px rgba(0, 181, 255, 0.2)',
          },
        },
      },
    },

    Card: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        shadow: 'sm',
        withBorder: true, // Add borders for definition
        padding: 'lg',
      },
      styles: {
        root: {
          transition: 'all 0.15s ease',
          border: '1px solid rgba(0, 0, 0, 0.08)',
          '&:hover': {
            transform: 'translateY(-1px)',
            boxShadow: '0 8px 20px rgba(0, 181, 255, 0.08)',
            borderColor: 'rgba(0, 181, 255, 0.2)',
          },
        },
      },
    },

    Paper: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        shadow: 'xs',
        padding: 'md',
        withBorder: true,
      },
      styles: {
        root: {
          border: '1px solid rgba(0, 0, 0, 0.08)',
          backgroundColor: '#ffffff',
        },
      },
    },

    Modal: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        shadow: 'xl',
        centered: true,
        overlayProps: { backgroundOpacity: 0.55, blur: 3 },
      },
    },

    TextInput: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        size: 'md',
      },
      styles: {
        input: {
          border: '1px solid rgba(0, 0, 0, 0.15)',
          transition: 'all 0.15s ease',
          '&:focus': {
            borderColor: '#00B5FF',
            boxShadow: '0 0 0 2px rgba(0, 181, 255, 0.1)',
          },
          '&:hover': {
            borderColor: 'rgba(0, 181, 255, 0.3)',
          },
        },
      },
    },

    PasswordInput: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        size: 'md',
      },
      styles: {
        input: {
          border: '1px solid rgba(0, 0, 0, 0.15)',
          transition: 'all 0.15s ease',
          '&:focus': {
            borderColor: '#00B5FF',
            boxShadow: '0 0 0 2px rgba(0, 181, 255, 0.1)',
          },
          '&:hover': {
            borderColor: 'rgba(0, 181, 255, 0.3)',
          },
        },
      },
    },

    Select: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        size: 'md',
      },
      styles: {
        input: {
          border: '1px solid rgba(0, 0, 0, 0.15)',
          transition: 'all 0.15s ease',
          '&:focus': {
            borderColor: '#00B5FF',
            boxShadow: '0 0 0 2px rgba(0, 181, 255, 0.1)',
          },
        },
      },
    },

    Textarea: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        size: 'md',
      },
      styles: {
        input: {
          border: '1px solid rgba(0, 0, 0, 0.15)',
          transition: 'all 0.15s ease',
          '&:focus': {
            borderColor: '#00B5FF',
            boxShadow: '0 0 0 2px rgba(0, 181, 255, 0.1)',
          },
        },
      },
    },

    Badge: {
      defaultProps: {
        radius: 'xs', // Sharp corners
        size: 'sm',
      },
      styles: {
        root: {
          fontWeight: 600,
          textTransform: 'uppercase',
          fontSize: '0.75rem',
          letterSpacing: '0.5px',
        },
      },
    },

    Notification: {
      defaultProps: {
        radius: 'xs', // Sharp corners
      },
    },

    AppShell: {
      styles: {
        main: {
          backgroundColor: 'rgba(248, 250, 252, 0.5)',
          minHeight: '100vh',
        },
      },
    },

    NavLink: {
      defaultProps: {
        radius: 'xs', // Sharp corners
      },
      styles: {
        root: {
          borderRadius: '0px', // Completely sharp
          margin: '2px 0',
          transition: 'all 0.15s ease',
          border: '1px solid transparent',
          '&:hover': {
            backgroundColor: 'rgba(0, 181, 255, 0.08)',
            borderColor: 'rgba(0, 181, 255, 0.2)',
            transform: 'translateX(2px)',
          },
        },
      },
    },
  },

  // Spacing scale
  spacing: {
    xs: '0.5rem',
    sm: '0.75rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem',
  },

  // Shadow scale
  shadows: {
    xs: '0 1px 3px rgba(0, 0, 0, 0.05), 0 1px 2px rgba(0, 0, 0, 0.1)',
    sm: '0 1px 3px rgba(0, 0, 0, 0.05), 0 4px 6px rgba(0, 0, 0, 0.1)',
    md: '0 4px 6px rgba(0, 0, 0, 0.05), 0 10px 15px rgba(0, 0, 0, 0.1)',
    lg: '0 10px 15px rgba(0, 0, 0, 0.05), 0 20px 25px rgba(0, 0, 0, 0.1)',
    xl: '0 20px 25px rgba(0, 0, 0, 0.05), 0 25px 50px rgba(0, 0, 0, 0.25)',
  },

  // Breakpoints for responsive design
  breakpoints: {
    xs: '30em',
    sm: '48em',
    md: '64em',
    lg: '74em',
    xl: '90em',
  },
});
