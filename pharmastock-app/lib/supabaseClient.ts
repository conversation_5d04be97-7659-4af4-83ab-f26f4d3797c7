import { createClient, type AuthChangeEvent, type Session, type SupabaseClientOptions as SupabaseOptions } from '@supabase/supabase-js';
import { supabaseConfig } from './config';
import type { Database } from '@/types/supabase';

// Extend the Supabase options to include global headers
type SupabaseClientOptions = SupabaseOptions<'public'> & {
  auth?: {
    autoRefreshToken?: boolean;
    persistSession?: boolean;
    detectSessionInUrl?: boolean;
    flowType?: 'implicit' | 'pkce';
  };
  global?: {
    headers?: Record<string, string>;
  };
};

// Create a custom fetch function that works in all environments
const customFetch = async (input: RequestInfo | URL, init?: RequestInit): Promise<Response> => {
  try {
    const url = new URL(input.toString());

    // Merge headers safely using the Headers API
    const headers = new Headers(init?.headers || {});
    headers.set('Content-Type', 'application/json');
    // Debug: log all outgoing headers
    // console.log('Outgoing headers:', Array.from(headers.entries()));

    const response = await fetch(url.toString(), {
      ...init,
      headers,
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      console.error('API Error:', {
        status: response.status,
        statusText: response.statusText,
        url: url.toString(),
        error: errorData,
      });
    }

    return response;
  } catch (error) {
    console.error('Fetch error:', error);
    throw error;
  }
};

// Options for the Supabase client
const supabaseOptions: SupabaseClientOptions = {
  auth: {
    autoRefreshToken: true,
    persistSession: false,
    detectSessionInUrl: true,
    flowType: 'pkce',
  },
  global: {
    headers: {
      'Content-Type': 'application/json',
      'apikey': supabaseConfig.serviceKey,
    },
  },
} as const;

export function getAnonSupabaseClient() {
  return createClient<Database>(
    supabaseConfig.url,
    supabaseConfig.anonKey,
    {
      ...supabaseOptions,
      global: {
        fetch: customFetch,
      },
    }
  );
}

export function getServiceSupabaseClient() {
  return createClient<Database>(
    supabaseConfig.url,
    supabaseConfig.serviceKey,
    {
      ...supabaseOptions,
      auth: {
        ...supabaseOptions.auth,
        autoRefreshToken: false,
        persistSession: false,
      },
      global: {
        fetch: customFetch,
      },
    }
  );
}

export function parseSession(sessionStr: string): Session | null {
  try {
    const session = JSON.parse(sessionStr);
    // Basic validation that it looks like a session
    if (session?.access_token && session?.refresh_token && session?.user) {
      return session as Session;
    }
    return null;
  } catch (error) {
    console.error('Failed to parse session:', error);
    return null;
  }
}

// Helper function to handle auth state changes
export function onAuthStateChange(
  callback: (event: AuthChangeEvent, session: Session | null) => void
) {
  const { data: { subscription } } = getAnonSupabaseClient().auth.onAuthStateChange(callback);
  return subscription;
}