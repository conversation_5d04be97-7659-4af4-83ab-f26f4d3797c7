import { z } from 'zod';
import { router, publicProcedure, protectedProcedure } from '../server';
import { hash, compare } from 'bcrypt';
import { TRPCError } from '@trpc/server';

const userSchema = z.object({
  email: z.string().email(),
  password: z.string().min(8),
  role: z.enum(['owner', 'admin', 'pharmacist', 'assistant']).default('assistant'),
  pharmacy_id: z.string().optional(),
});

export const authRouter = router({
  register: publicProcedure
    .input(userSchema)
    .mutation(async ({ ctx, input }) => {
      const { email, password, role, pharmacy_id } = input;
      const exists = await ctx.prisma.user.findUnique({ where: { email } });
      
      if (exists) {
        throw new TRPCError({
          code: 'CONFLICT',
          message: 'User already exists',
        });
      }

      const hashedPassword = await hash(password, 10);
      
      const user = await ctx.prisma.user.create({
        data: {
          email,
          password: hashedPassword,
          role,
          pharmacy_id,
        },
      });

      return { user };
    }),

  login: publicProcedure
    .input(z.object({
      email: z.string().email(),
      password: z.string(),
    }))
    .mutation(async ({ ctx, input }) => {
      const { email, password } = input;
      const user = await ctx.prisma.user.findUnique({ where: { email } });

      if (!user) {
        throw new TRPCError({
          code: 'NOT_FOUND',
          message: 'User not found',
        });
      }

      const valid = await compare(password, user.password);

      if (!valid) {
        throw new TRPCError({
          code: 'UNAUTHORIZED',
          message: 'Invalid password',
        });
      }

      return { user };
    }),

  me: protectedProcedure
    .query(async ({ ctx }) => {
      const user = await ctx.prisma.user.findUnique({
        where: { id: ctx.user.id },
        include: {
          pharmacy: true,
          team_members: true,
        },
      });

      return { user };
    }),
});