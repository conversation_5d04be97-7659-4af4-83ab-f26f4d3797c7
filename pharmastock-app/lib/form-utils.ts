import { z } from 'zod';

export function createFormSubmitHandler<T extends z.ZodType>(
  schema: T,
  onSubmitHandler: (values: z.infer<T>) => Promise<void>,
  options: {
    successMessage?: string;
    errorMessage?: string;
    onSuccess?: () => void;
    onError?: (error: unknown) => void;
  } = {}
) {
  return async function onSubmit(values: z.infer<T>) {
    try {
      // Validate the form data
      const validatedData = schema.parse(values);
      
      // Call the handler
      await onSubmitHandler(validatedData);
      
      // Call success callback
      options.onSuccess?.();
    } catch (error) {
      console.error('Form submission error:', error);
      
      // Call error callback
      options.onError?.(error);
    }
  };
} 