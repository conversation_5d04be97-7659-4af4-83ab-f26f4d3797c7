// This file is deprecated and replaced with Supabase client usage
// All database operations should now use the Supabase client from @/lib/supabaseClient

import { getServiceSupabaseClient } from './supabaseClient';

// Legacy compatibility - this should not be used in new code
// Use getServiceSupabaseClient() directly instead
export const db = {
  async query(text: string, params?: any[]) {
    console.warn('DEPRECATED: Direct SQL queries via db.query() are deprecated. Use Supabase client instead.');
    throw new Error('Direct SQL queries are no longer supported. Please use Supabase client operations.');
  },
};

// Helper function to get Supabase client (for migration purposes)
export function getSupabaseClient() {
  return getServiceSupabaseClient();
}
