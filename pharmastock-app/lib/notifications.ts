import { getServiceSupabaseClient } from "@/lib/supabaseClient";
import { NotificationType, NotificationPriority } from "@/contexts/notification-context";

interface SendNotificationOptions {
  recipientId: string;
  senderId?: string;
  type: NotificationType;
  priority?: NotificationPriority;
  title: string;
  message: string;
  data?: Record<string, any>;
  expiresAt?: Date;
}

export async function sendNotification({
  recipientId,
  senderId,
  type,
  priority = "medium",
  title,
  message,
  data,
  expiresAt,
}: SendNotificationOptions) {
  try {
    const supabase = getServiceSupabaseClient();
    
    const notificationData = {
      recipient_id: recipientId,
      type,
      priority,
      title,
      message,
      data,
      expires_at: expiresAt?.toISOString(),
      ...(senderId && { sender_id: senderId }),
    };

    const { data: notification, error } = await supabase
      .from('notifications')
      .insert(notificationData)
      .select()
      .single();

    if (error) {
      console.error("Error sending notification:", error);
      throw error;
    }

    return notification;
  } catch (error) {
    console.error("Error sending notification:", error);
    throw error;
  }
}

export async function sendPriceChangeNotification({
  recipientId,
  productName,
  oldPrice,
  newPrice,
  productId,
}: {
  recipientId: string;
  productName: string;
  oldPrice: number;
  newPrice: number;
  productId: string;
}) {
  return sendNotification({
    recipientId,
    type: "price_change",
    priority: "medium",
    title: "Changement de prix",
    message: `Le prix de ${productName} a changé de ${oldPrice} DH à ${newPrice} DH`,
    data: { productName, oldPrice, newPrice, productId },
  });
}

export async function sendExpiryAlertNotification({
  recipientId,
  productName,
  expiryDate,
  productId,
}: {
  recipientId: string;
  productName: string;
  expiryDate: string;
  productId: string;
}) {
  return sendNotification({
    recipientId,
    type: "expiry_alert",
    priority: "high",
    title: "Alerte de péremption",
    message: `${productName} expire le ${expiryDate}`,
    data: { productName, expiryDate, productId },
  });
}

export async function sendUrgentRequestNotification({
  recipientId,
  senderId,
  pharmacyName,
  productName,
  requestId,
}: {
  recipientId: string;
  senderId: string;
  pharmacyName: string;
  productName: string;
  requestId: string;
}) {
  return sendNotification({
    recipientId,
    senderId,
    type: "urgent_request",
    priority: "urgent",
    title: "Demande urgente",
    message: `${pharmacyName} recherche ${productName} en urgence`,
    data: { pharmacyName, productName, requestId },
  });
}

export async function sendInterestNotification({
  recipientId,
  senderId,
  pharmacyName,
  productName,
  productId,
}: {
  recipientId: string;
  senderId: string;
  pharmacyName: string;
  productName: string;
  productId: string;
}) {
  return sendNotification({
    recipientId,
    senderId,
    type: "interest_shown",
    priority: "medium",
    title: "Intérêt pour votre produit",
    message: `${pharmacyName} est intéressé(e) par votre ${productName}`,
    data: { pharmacyName, productName, productId },
  });
}

export async function sendMessageNotification({
  recipientId,
  senderId,
  senderName,
  messagePreview,
  messageId,
}: {
  recipientId: string;
  senderId: string;
  senderName: string;
  messagePreview: string;
  messageId: string;
}) {
  return sendNotification({
    recipientId,
    senderId,
    type: "message_received",
    priority: "medium",
    title: "Nouveau message",
    message: `${senderName}: ${messagePreview}`,
    data: { senderName, messagePreview, messageId },
  });
}

export async function sendStockAlertNotification({
  recipientId,
  productName,
  status,
  currentQuantity,
  threshold,
  productId,
}: {
  recipientId: string;
  productName: string;
  status: string;
  currentQuantity: number;
  threshold: number;
  productId: string;
}) {
  return sendNotification({
    recipientId,
    type: "stock_alert",
    priority: "high",
    title: "Alerte de stock",
    message: `Le stock de ${productName} est ${status}`,
    data: { productName, status, currentQuantity, threshold, productId },
  });
}

export async function sendOrderStatusNotification({
  recipientId,
  orderId,
  status,
  details,
}: {
  recipientId: string;
  orderId: string;
  status: string;
  details: string;
}) {
  return sendNotification({
    recipientId,
    type: "order_status",
    priority: "medium",
    title: "Statut de commande",
    message: `Commande #${orderId} : ${status}`,
    data: { orderId, status, details },
  });
}

export async function sendPaymentStatusNotification({
  recipientId,
  orderId,
  status,
  amount,
}: {
  recipientId: string;
  orderId: string;
  status: string;
  amount: number;
}) {
  return sendNotification({
    recipientId,
    type: "payment_status",
    priority: "high",
    title: "Statut de paiement",
    message: `Paiement ${status} pour la commande #${orderId}`,
    data: { orderId, status, amount },
  });
}

export async function sendVerificationStatusNotification({
  recipientId,
  status,
  details,
}: {
  recipientId: string;
  status: string;
  details: string;
}) {
  return sendNotification({
    recipientId,
    type: "verification_status",
    priority: "high",
    title: "Statut de vérification",
    message: `Votre compte est maintenant ${status}`,
    data: { status, details },
  });
}

export async function sendMarketUpdateNotification({
  recipientId,
  title,
  summary,
  category,
}: {
  recipientId: string;
  title: string;
  summary: string;
  category: string;
}) {
  return sendNotification({
    recipientId,
    type: "market_update",
    priority: "low",
    title: "Mise à jour du marché",
    message: `${title} - ${summary}`,
    data: { title, summary, category },
  });
}

export async function sendMaintenanceAlertNotification({
  recipientId,
  title,
  scheduledTime,
  duration,
  impact,
}: {
  recipientId: string;
  title: string;
  scheduledTime: string;
  duration: string;
  impact: string;
}) {
  return sendNotification({
    recipientId,
    type: "maintenance_alert",
    priority: "medium",
    title: "Maintenance prévue",
    message: `${title} - ${scheduledTime}`,
    data: { title, scheduledTime, duration, impact },
  });
}

export async function sendSecurityAlertNotification({
  recipientId,
  title,
  details,
  actionRequired,
}: {
  recipientId: string;
  title: string;
  details: string;
  actionRequired: boolean;
}) {
  return sendNotification({
    recipientId,
    type: "security_alert",
    priority: "critical",
    title: "Alerte de sécurité",
    message: title,
    data: { title, details, actionRequired },
  });
}

export async function sendProductRecallNotification({
  recipientId,
  productName,
  batchNumber,
  reason,
  actionRequired,
}: {
  recipientId: string;
  productName: string;
  batchNumber: string;
  reason: string;
  actionRequired: string;
}) {
  return sendNotification({
    recipientId,
    type: "product_recall",
    priority: "critical",
    title: "Rappel de produit",
    message: `Rappel urgent : ${productName}`,
    data: { productName, batchNumber, reason, actionRequired },
  });
}

export async function sendNewFeatureNotification({
  recipientId,
  title,
  description,
  learnMoreLink,
}: {
  recipientId: string;
  title: string;
  description: string;
  learnMoreLink: string;
}) {
  return sendNotification({
    recipientId,
    type: "new_feature",
    priority: "low",
    title: "Nouvelle fonctionnalité",
    message: title,
    data: { title, description, learnMoreLink },
  });
}