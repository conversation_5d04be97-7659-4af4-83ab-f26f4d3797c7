import pino from 'pino';

const isDevelopment = process.env.NODE_ENV === 'development';

// Store logs in memory for development
const logStore: Array<{ level: string; message: string; timestamp: string }> = [];

const logger = pino({
  level: isDevelopment ? 'debug' : 'info',
  // Temporarily disable pino-pretty to avoid worker thread issues
  // transport: isDevelopment
  //   ? {
  //       target: 'pino-pretty',
  //       options: {
  //         colorize: true,
  //         translateTime: 'SYS:standard',
  //         ignore: 'pid,hostname',
  //       },
  //     }
  //   : undefined,
  hooks: {
    logMethod(inputArgs, method) {
      if (isDevelopment) {
        const timestamp = new Date().toISOString();
        const level = method.name;
        const message = inputArgs.join(' ');
        logStore.push({ level, message, timestamp });
        // Keep only last 1000 logs
        if (logStore.length > 1000) {
          logStore.shift();
        }
      }
      return method.apply(this, inputArgs);
    },
  },
});

// Add getLogs method to logger
const getLogs = (filter?: string) => {
  if (!isDevelopment) return [];
  if (!filter) return logStore;
  const lowercaseFilter = filter.toLowerCase();
  return logStore.filter(
    log =>
      log.message.toLowerCase().includes(lowercaseFilter) ||
      log.level.toLowerCase().includes(lowercaseFilter)
  );
};

export { logger, getLogs };
export default logger;