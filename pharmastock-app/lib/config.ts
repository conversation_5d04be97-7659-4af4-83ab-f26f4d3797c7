// Environment configuration
const env = process.env.NODE_ENV || 'development';
const isDevelopment = env === 'development';

// Supabase configuration
export const supabaseConfig = {
  url: process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://shptfbdcuwaxyosyjsas.supabase.co',
  anonKey: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocHRmYmRjdXdheHlvc3lqc2FzIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MTgyNDMsImV4cCI6MjA2NjM5NDI0M30.tvoMbGEU3nL0tZvqib_jrITKzRpf1DE3A2CIk9czTd0',
  serviceKey: process.env.SUPABASE_SERVICE_ROLE_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InNocHRmYmRjdXdheHlvc3lqc2FzIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImlhdCI6MTc1MDgxODI0MywiZXhwIjoyMDY2Mzk0MjQzfQ.HBU_zcarbOtM2jHUrSSSpUi-3vfWf7kSRidLMvFPNjI'
};

// Database configuration
export const dbConfig = {
  host: process.env.DB_HOST || '127.0.0.1',
  port: parseInt(process.env.DB_PORT || '54352'), // Match the port in .env
  database: process.env.DB_NAME || 'postgres',
  user: process.env.DB_USER || 'postgres',
  password: process.env.DB_PASSWORD || 'postgres',
  ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false
};

// Application configuration
export const appConfig = {
  url: process.env.NEXT_PUBLIC_APP_URL || 'http://localhost:3001',
  wsUrl: process.env.NEXT_PUBLIC_WS_URL || 'ws://localhost:3001',
  apiUrl: process.env.NEXT_PUBLIC_API_URL || 'http://127.0.0.1:54351',
  debug: process.env.DEBUG === 'true',
  enableApiLogs: process.env.ENABLE_API_LOGS === 'true'
};

// Auth configuration
export const authConfig = {
  cookieName: 'sb-session',
  secure: !isDevelopment,
  maxAge: 60 * 60 * 24 * 7, // 1 week
  sameSite: 'lax' as const,
};

// Storage configuration
export const storageConfig = {
  url: process.env.NEXT_PUBLIC_STORAGE_URL || 'http://127.0.0.1:54352/storage/v1/s3',
  accessKey: process.env.STORAGE_S3_ACCESS_KEY,
  secretKey: process.env.STORAGE_S3_SECRET_KEY,
  region: process.env.STORAGE_S3_REGION || 'local'
};

// Export environment helpers
export const isProduction = env === 'production';
export const isTest = env === 'test';
export { isDevelopment };

// Export a default config object
export default {
  env,
  supabase: supabaseConfig,
  db: dbConfig,
  app: appConfig,
  auth: authConfig,
  storage: storageConfig
};