# Provider Architecture

This document outlines the provider hierarchy and dependencies in our application.

## Provider Order

Our application uses the following provider nesting order:

```jsx
<AuthProvider>
  <SupabaseProvider>
    <DatabaseProvider>
      <ThemeProvider>
        <NotificationProvider>
          <App />
        </NotificationProvider>
      </ThemeProvider>
    </DatabaseProvider>
  </SupabaseProvider>
</AuthProvider>
```

## Provider Dependencies

### 1. AuthProvider
- **Position:** Outermost provider
- **Dependencies:** None
- **Provides:** User authentication state and methods
- **Used by:** SupabaseProvider, DatabaseProvider
- **Reason:** Must be first as it manages the global authentication state that other providers depend on

### 2. SupabaseProvider
- **Position:** Second level
- **Dependencies:** AuthProvider
- **Provides:** Supabase client and pharmacy data
- **Used by:** DatabaseProvider, application components
- **Reason:** Needs authentication state from AuthProvider to fetch pharmacy data

### 3. DatabaseProvider
- **Position:** Third level
- **Dependencies:** Auth<PERSON>rovider, Su<PERSON><PERSON><PERSON>rovider
- **Provides:** Database connection and methods
- **Used by:** Application components
- **Reason:** Requires both auth state and Supabase client for operations

### 4. ThemeProvider
- **Position:** Fourth level
- **Dependencies:** None (independent)
- **Provides:** Theme context and utilities
- **Used by:** All UI components
- **Reason:** Can be placed here as it doesn't depend on data providers

### 5. NotificationProvider
- **Position:** Innermost provider
- **Dependencies:** All above providers
- **Provides:** Toast notifications and alerts
- **Used by:** All components
- **Reason:** Needs access to all other contexts for comprehensive error handling

## Usage Guidelines

1. **Context Access:**
   - Always use the appropriate hook to access context (e.g., `useAuth()`, `useSupabase()`)
   - Never directly import provider components except in the root layout

2. **Error Handling:**
   - Each provider implements its own error boundary
   - Errors are propagated up through the NotificationProvider

3. **State Management:**
   - AuthProvider manages global authentication state
   - SupabaseProvider caches pharmacy data
   - DatabaseProvider handles database connections
   - ThemeProvider manages UI theme
   - NotificationProvider manages transient UI states

4. **Performance Considerations:**
   - Each provider implements proper memoization
   - State updates are batched where possible
   - Heavy operations are deferred using `useEffect`

## Example Usage

```typescript
function MyComponent() {
  const { user } = useAuth();
  const { pharmacy } = useSupabase();
  const { db } = useDatabase();
  const { theme } = useTheme();
  const { notify } = useNotifications();

  // Component logic here
}
``` 