# Roles and Permissions

## Overview

PharmaStock implements a hierarchical role-based access control (RBAC) system with the following roles:
- <PERSON> <PERSON><PERSON> (Application Manager)
- Pharmacist (Pharmacy Owner)
- Staff

## Role Hierarchy

```
Super Admin (Application Manager)
└── Pharmacist (Pharmacy Owner)
    └── Staff
```

## Role Definitions

### 1. Super Admin (Application Manager)
The super admin has complete control over the entire application.

**Capabilities:**
- Full access to all system features
- Manage all pharmacies
- Access to system-wide analytics
- Configure global settings
- Monitor system health
- Access audit logs
- Manage system-wide notifications
- Override any restrictions

### 2. <PERSON>armacist (Pharmacy Owner)
The licensed pharmacist who owns and manages the pharmacy.

**Capabilities:**
- Full access to pharmacy features
- Team management (create/modify/disable staff accounts)
- Access to pharmacy reports and analytics
- Marketplace operations (create/edit/delete listings)
- Create and respond to urgent requests
- Manage pharmacy profile and settings
- View and manage contact requests

### 3. Staff
Support staff members working under the pharmacist's supervision.

**Capabilities:**
- View marketplace listings (if granted)
- Basic profile management
- View assigned tasks
- Handle contact requests (if granted)

## Permission Matrix

| Feature/Action                  | Super Admin | Pharmacist | Staff |
|--------------------------------|-------------|------------|--------|
| **System Management**          |             |            |        |
| Access all pharmacies          | ✅         | ❌         | ❌     |
| System configuration           | ✅         | ❌         | ❌     |
| View audit logs               | ✅         | ❌         | ❌     |
| **Pharmacy Management**        |             |            |        |
| Create staff accounts          | ✅         | ✅         | ❌     |
| Modify staff permissions       | ✅         | ✅         | ❌     |
| Disable staff accounts         | ✅         | ✅         | ❌     |
| **Marketplace**                |             |            |        |
| View listings                  | ✅         | ✅         | ⚪     |
| Create listings               | ✅         | ✅         | ⚪     |
| Edit/Delete listings          | ✅         | ✅         | ⚪     |
| **Urgent Requests**            |             |            |        |
| Create requests               | ✅         | ✅         | ⚪     |
| Accept/Reject requests        | ✅         | ✅         | ⚪     |
| **Contact Management**         |             |            |        |
| Initiate contact              | ✅         | ✅         | ⚪     |
| Respond to inquiries          | ✅         | ✅         | ⚪     |
| **Reports & Analytics**        |             |            |        |
| View system-wide reports      | ✅         | ❌         | ❌     |
| View pharmacy reports         | ✅         | ✅         | ❌     |
| Export data                   | ✅         | ✅         | ❌     |

Legend:
- ✅ Always allowed
- ⚪ Configurable by pharmacist
- ❌ Never allowed

## Permission Management

### Granting Permissions
1. Super admin can grant permissions to any user
2. Pharmacists can only grant permissions to their staff
3. All permission changes are logged for audit purposes

### Permission Inheritance
- Staff members inherit base permissions from their role
- Additional permissions can be granted by their pharmacist
- Removing a permission returns it to the role's default state

### Security Considerations
1. All permission changes require re-authentication
2. Permission changes are logged with timestamp and admin/pharmacist ID
3. Users are notified of permission changes
4. Sessions are invalidated when critical permissions change

## Implementation Notes

### Database Schema
```sql
CREATE TYPE user_role AS ENUM ('super_admin', 'pharmacist', 'staff');

CREATE TABLE user_permissions (
    user_id UUID REFERENCES auth.users(id),
    pharmacy_id UUID REFERENCES pharmacies(id),
    role user_role NOT NULL,
    custom_permissions JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    updated_by UUID REFERENCES auth.users(id),
    PRIMARY KEY (user_id, pharmacy_id)
);

-- Add super admin specific table
CREATE TABLE super_admin_access (
    admin_id UUID REFERENCES auth.users(id) PRIMARY KEY,
    access_level TEXT NOT NULL,
    restrictions JSONB,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### Permission Check Flow
1. Check if user is super admin
2. If not, check user's base role permissions
3. Check for custom granted permissions
4. Apply any temporary restrictions
5. Validate against business rules
6. Log access attempt 

### Canonical roles: `super_admin`, `pharmacist`, `staff`
- Legacy mapping: `admin` → `super_admin`, `owner` → `pharmacist`, `assistant` → `staff`
- **Note:** Test users must be created via Supabase Auth or the dedicated test user migration (`20240610000001_create_test_user_accounts.sql`), not via `seed.sql`, due to ownership restrictions on the `auth.users` table. 