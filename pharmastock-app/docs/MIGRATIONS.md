# Database Migrations

This document outlines the structure and organization of our database migrations.

## Directory Structure

```
supabase/
├── migrations/
│   ├── 20240322000001_create_schema.sql         # Base schema creation
│   ├── 20240322000002_auth_setup.sql            # Authentication setup
│   ├── 20240322000003_marketplace_functions.sql  # Marketplace functionality
│   ├── 20240322000004_sessions.sql              # Session management
│   ├── 20240322000005_session_cleanup.sql       # Session cleanup
│   ├── 20240322000006_enhance_role_system.sql   # Role system enhancement
│   ├── 20240322000007_enhance_auth_system.sql   # Auth system enhancement
│   └── ... future migrations
├── seed.sql                                      # Development seed data
├── functions/                                    # Database functions
│   ├── search_products.sql
│   └── ... other function files
└── policies/                                     # RLS policies
    ├── pharmacies.sql
    ├── products.sql
    └── ... other policy files
```

## Migration Naming Convention

Migrations follow the format: `YYYYMMDD[HHMM]_description.sql`

Example: `20240322000001_create_schema.sql`

## Migration Types

1. **Schema Migrations** (000001-099999)
   - Base table creation
   - Schema modifications
   - Index creation

2. **Auth Migrations** (100000-199999)
   - User authentication setup
   - Role definitions
   - Permission systems

3. **Function Migrations** (200000-299999)
   - Stored procedures
   - Custom functions
   - Triggers

4. **Policy Migrations** (300000-399999)
   - Row Level Security (RLS)
   - Access control
   - Security policies

## Best Practices

1. **Idempotency**
   - All migrations should be idempotent
   - Use `CREATE TABLE IF NOT EXISTS`
   - Use `DO $$ BEGIN ... EXCEPTION WHEN duplicate_object THEN null; END $$;`

2. **Rollbacks**
   - Include `DOWN` migrations where possible
   - Document manual rollback steps if automated rollback isn't possible

3. **Dependencies**
   - Clearly document dependencies between migrations
   - Maintain proper ordering through numbering

4. **Testing**
   - Test migrations in development first
   - Use development-only seed data
   - Verify RLS policies work as expected

5. **Environment Awareness**
   - Use environment checks for development-specific operations
   - Keep production and development data separate
   - Document test accounts and data

## Example Migration

```sql
-- =============================================================================
-- Migration: Create Products Table
-- Description: Creates the base products table with indexes and policies
-- =============================================================================

-- Up Migration
CREATE TABLE IF NOT EXISTS products (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Down Migration
-- DROP TABLE IF EXISTS products;

-- Indexes
CREATE INDEX IF NOT EXISTS idx_products_name ON products(name);

-- RLS
ALTER TABLE products ENABLE ROW LEVEL SECURITY;

-- Policies
CREATE POLICY "Users can view all products"
    ON products FOR SELECT
    USING (true);
```

## Development Data

Test accounts and development data are managed in `seed.sql` and are only created in development environments. **Note:** Test users must be created via Supabase Auth or the dedicated test user migration (`20240610000001_create_test_user_accounts.sql`), not via `seed.sql`, due to ownership restrictions on the `auth.users` table.

- Development pharmacies
- Test user accounts (via migration)
- Team member associations

To ensure development data doesn't leak into production:
1. All inserts are wrapped in environment checks
2. Test accounts use `.test` domain
3. Clear documentation of test data
4. Consistent naming conventions for test entities

### Role Mapping
- Canonical roles: `super_admin`, `pharmacist`, `staff`
- Legacy mapping: `admin` → `super_admin`, `owner` → `pharmacist`, `assistant` → `staff`

## Running Migrations

Use the Supabase CLI to run migrations:

```bash
# Apply all migrations
npx supabase db reset

# Create a new migration
npx supabase migration new create_users_table

# Verify migration status
npx supabase db status
```

## Security Considerations

1. Never include production credentials in migrations
2. Use environment variables for sensitive values
3. Test all RLS policies thoroughly
4. Document all security-related changes
5. Keep test accounts isolated from production 