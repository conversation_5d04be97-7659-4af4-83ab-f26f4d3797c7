# Role Standardization Plan

## Current Issues
- Multiple incompatible role systems exist across the codebase
- TypeScript types, database schemas, and business logic are inconsistent
- Role-based redirects are hardcoded and inflexible

## Proposed Standardized Role System

### 1. Three-Tier Role Hierarchy
```
super_admin (System Administrator)
└── pharmacist (Licensed Pharmacist - Pharmacy Owner/Manager)
    └── staff (Pharmacy Staff)
```

### 2. Role Definitions

#### **super_admin**
- **Purpose**: System-wide administrator
- **Access**: Full access to all pharmacies and system features
- **Dashboard**: `/admin/dashboard`
- **Permissions**: All actions on all resources

#### **pharmacist** 
- **Purpose**: Licensed pharmacist who owns/manages a pharmacy
- **Access**: Full control over their pharmacy
- **Dashboard**: `/dashboard` 
- **Permissions**: All actions within their pharmacy scope

#### **staff**
- **Purpose**: Pharmacy employees with limited access
- **Access**: Basic operations within their pharmacy
- **Dashboard**: `/dashboard`
- **Permissions**: Read/create/update products, read other resources

### 3. Migration Strategy

#### Phase 1: Update Type Definitions
- [x] Update `lib/types/auth.ts` UserRole type
- [ ] Update RolePermissions object
- [ ] Update helper functions

#### Phase 2: Update Database Schema
- [ ] Create migration to standardize role enum
- [ ] Update existing user roles in database
- [ ] Update RLS policies

#### Phase 3: Update Application Logic
- [ ] Update access control rules
- [ ] Update middleware role validation
- [ ] Update redirect logic
- [ ] Update UI role checks

#### Phase 4: Update Documentation
- [ ] Update business documentation
- [ ] Update API documentation
- [ ] Update user guides

### 4. Backward Compatibility
- Map old roles to new roles during transition:
  - `owner` → `super_admin`
  - `admin` → `pharmacist`
  - `assistant` → `staff`

**Note:** Test users must be created via Supabase Auth or the dedicated test user migration (`20240610000001_create_test_user_accounts.sql`), not via `seed.sql`, due to ownership restrictions on the `auth.users` table.

### 5. Testing Strategy
- [ ] Update test data with new roles
- [ ] Test role-based access control
- [ ] Test dashboard redirects
- [ ] Test permission checks
