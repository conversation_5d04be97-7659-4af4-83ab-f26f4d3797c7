# API Documentation

## Overview

The PharmaStock API is built using Next.js API routes with Edge Runtime support. All routes are protected by authentication and proper role-based access control.

## Base URL

```
Development: http://localhost:3000/api
Production: https://[your-domain]/api
```

## Authentication

All API requests must include a valid session cookie (`sb-session`). This is handled automatically by the frontend authentication system.

### Authentication Endpoints

#### POST /api/auth/login
```typescript
Request:
{
  email: string;
  password: string;
}

Response:
{
  user: {
    id: string;
    email: string;
    role: string;
    pharmacyId?: string;
  }
}
```

#### POST /api/auth/logout
```typescript
Response:
{
  message: string;
}
```

#### GET /api/auth/session
```typescript
Response:
{
  user: {
    id: string;
    email: string;
    role: string;
    pharmacyId?: string;
  } | null;
}
```

## Marketplace API

### Listings

#### GET /api/listings
```typescript
Query Parameters:
{
  page?: number;
  limit?: number;
  search?: string;
  category?: string;
  distance?: number;
  lat?: number;
  lng?: number;
}

Response:
{
  listings: Array<{
    id: string;
    product: {
      name: string;
      description: string;
      category: string;
      expiry_date: string;
    };
    quantity: number;
    price_per_unit: number;
    minimum_order: number;
    pharmacy: {
      name: string;
      distance: number;
    };
  }>;
  total: number;
  hasMore: boolean;
}
```

#### POST /api/listings
```typescript
Request:
{
  product_id: string;
  quantity: number;
  price_per_unit: number;
  minimum_order: number;
  expires_at?: string;
}

Response:
{
  listing: {
    id: string;
    // ... listing details
  }
}
```

### Reservations

#### POST /api/reservations
```typescript
Request:
{
  listing_id: string;
  quantity: number;
  message?: string;
}

Response:
{
  reservation: {
    id: string;
    status: 'pending' | 'accepted' | 'rejected';
    // ... other details
  }
}
```

#### PUT /api/reservations/:id
```typescript
Request:
{
  status: 'accepted' | 'rejected';
  message?: string;
}

Response:
{
  reservation: {
    id: string;
    status: string;
    // ... updated details
  }
}
```

## Pharmacy Management

### Staff Management

#### GET /api/pharmacy/staff
```typescript
Response:
{
  staff: Array<{
    id: string;
    email: string;
    role: string;
    status: string;
    permissions: string[];
  }>;
}
```

#### POST /api/pharmacy/staff
```typescript
Request:
{
  email: string;
  role: 'pharmacist' | 'staff';
  permissions: string[];
}

Response:
{
  member: {
    id: string;
    // ... member details
  }
}
```

## Error Handling

All API endpoints follow a consistent error response format:

```typescript
{
  error: {
    code: string;
    message: string;
    details?: any;
  }
}
```

Common error codes:
- `auth/unauthorized`: Not authenticated
- `auth/forbidden`: Insufficient permissions
- `validation/invalid`: Invalid input data
- `not_found`: Resource not found
- `server_error`: Internal server error

## Rate Limiting

API requests are rate-limited based on the following rules:
- 500 requests per 5 minutes per IP
- 1000 requests per 5 minutes per authenticated user

## Versioning

The API is currently at v1. All endpoints are prefixed with `/api`.

Future versions will be prefixed with `/api/v2`, `/api/v3`, etc.

## WebSocket API

Real-time updates are handled through WebSocket connections:

```typescript
const ws = new WebSocket('ws://localhost:3000/ws');

// Message Types
type WSMessage = {
  type: 'new_listing' | 'reservation_update' | 'urgent_request';
  payload: any;
};

// Example subscription
ws.onmessage = (event) => {
  const message: WSMessage = JSON.parse(event.data);
  switch (message.type) {
    case 'new_listing':
      // Handle new listing
      break;
    case 'reservation_update':
      // Handle reservation update
      break;
    case 'urgent_request':
      // Handle urgent request
      break;
  }
};
``` 