#!/bin/bash

# Deploy Next.js app to server alongside website
# This script sets up the app in /app subdirectory

set -e

SERVER_IP="**************"
SSH_USER="deploy"
WEBSITE_DIR="/var/www/pharmastock"
APP_DIR="/var/www/pharmastock-app"
DOMAIN="pharmastock.ma"

echo "🚀 Deploying PharmaStock App to production..."

# Step 1: Skip local build - we'll build on server with correct environment
echo "📦 Skipping local build - will build on server with production environment..."

# Step 2: Create app directory on server
echo "📁 Setting up app directory on server..."
ssh -tt $SSH_USER@$SERVER_IP "sudo mkdir -p $APP_DIR && sudo chown $SSH_USER:$SSH_USER $APP_DIR"

# Step 3: Upload source files (excluding build artifacts)
echo "📤 Uploading source files..."
rsync -avz --progress --delete \
  --exclude='.git' \
  --exclude='node_modules' \
  --exclude='.next' \
  --exclude='build' \
  --exclude='dist' \
  ./ $SSH_USER@$SERVER_IP:$APP_DIR/

# Step 3.5: Set up production environment on server BEFORE building
echo "🔧 Setting up production environment on server..."
if [ -f ".env.production" ]; then
    # Clean all environment files and set up production config
    ssh $SSH_USER@$SERVER_IP "cd $APP_DIR && rm -f .env.local .env.development .env"
    scp .env.production $SSH_USER@$SERVER_IP:$APP_DIR/.env
    echo "✅ Production environment uploaded as .env for build-time variables"
else
    echo "❌ No .env.production found - this is required!"
    exit 1
fi

# Step 4: Build and start the app on server with production environment
echo "🏗️ Building and starting application on server..."
ssh -tt $SSH_USER@$SERVER_IP << 'EOF'
cd /var/www/pharmastock-app

echo "🧹 Cleaning any existing build artifacts..."
rm -rf .next node_modules/.cache

echo "📦 Installing dependencies..."
npm install --production=false

echo "🔍 Verifying production environment variables..."
echo "NEXT_PUBLIC_SUPABASE_URL: $(grep NEXT_PUBLIC_SUPABASE_URL .env | head -1)"

echo "🔧 Building Next.js application with production environment..."
NODE_ENV=production npm run build

echo "⚙️ Setting up PM2 process manager..."
if ! command -v pm2 &> /dev/null; then
    echo "Installing PM2 globally..."
    sudo npm install -g pm2
fi

echo "🚀 Starting application with PM2..."
pm2 delete pharmastock-app 2>/dev/null || true
pm2 start npm --name "pharmastock-app" -- start

echo "💾 Saving PM2 configuration..."
pm2 save

echo "✅ Deployment completed successfully!"
pm2 status
EOF

echo "✅ App deployment complete!"
echo "📍 App running on port 3006"
echo "🔗 Next step: Update Nginx configuration"
