#!/bin/bash

# Deploy Next.js app to server alongside website
# This script sets up the app in /app subdirectory

set -e

SERVER_IP="**************"
SSH_USER="deploy"
WEBSITE_DIR="/var/www/pharmastock"
APP_DIR="/var/www/pharmastock-app"
DOMAIN="pharmastock.ma"

echo "🚀 Deploying PharmaStock App to production..."

# Step 1: Build the Next.js app locally with production environment
echo "📦 Building Next.js application..."

# Temporarily use production environment for build
if [ -f ".env.production" ]; then
    echo "🔧 Building with production environment variables..."
    cp .env.production .env.local.backup 2>/dev/null || true
    cp .env.production .env.local
    npm run build
    # Restore original .env.local if it existed
    if [ -f ".env.local.backup" ]; then
        mv .env.local.backup .env.local
    else
        rm -f .env.local
    fi
else
    echo "🔧 Building with existing environment..."
    npm run build
fi

# Step 2: Create app directory on server
echo "📁 Setting up app directory on server..."
ssh -tt $SSH_USER@$SERVER_IP "sudo mkdir -p $APP_DIR && sudo chown $SSH_USER:$SSH_USER $APP_DIR"

# Step 3: Upload the built app
echo "📤 Uploading application files..."
rsync -avz --progress --delete \
  --exclude='.git' \
  --exclude='node_modules' \
  --exclude='.next/cache' \
  ./ $SSH_USER@$SERVER_IP:$APP_DIR/

# Step 3.5: Set up production environment on server
echo "🔧 Setting up production environment on server..."
if [ -f ".env.production" ]; then
    # Remove any existing .env.local file and upload production config
    ssh $SSH_USER@$SERVER_IP "cd $APP_DIR && rm -f .env.local .env.development .env"
    scp .env.production $SSH_USER@$SERVER_IP:$APP_DIR/.env.local
    echo "✅ Production environment uploaded and local env files cleaned"
else
    echo "⚠️  No .env.production found, using server's existing environment"
fi

# Step 4: Install dependencies on server
echo "📦 Installing dependencies on server..."
ssh $SSH_USER@$SERVER_IP "cd $APP_DIR && npm ci --only=production"

# Step 5: Set up PM2 process manager
echo "⚙️ Setting up PM2 process manager..."
ssh -tt $SSH_USER@$SERVER_IP "
  # Install PM2 globally if not installed
  sudo npm install -g pm2

  # Create PM2 ecosystem file
  cd $APP_DIR
  cat > ecosystem.config.cjs << 'EOF'
module.exports = {
  apps: [{
    name: 'pharmastock-app',
    script: 'npm',
    args: 'start',
    cwd: '$APP_DIR',
    instances: 1,
    autorestart: true,
    watch: false,
    max_memory_restart: '1G',
    env: {
      NODE_ENV: 'production',
      PORT: 3006
    }
  }]
}
EOF

  # Start the app with PM2
  pm2 start ecosystem.config.cjs
  pm2 save
  pm2 startup
"

echo "✅ App deployment complete!"
echo "📍 App running on port 3006"
echo "🔗 Next step: Update Nginx configuration"
