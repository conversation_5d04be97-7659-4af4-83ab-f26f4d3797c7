#!/bin/bash

echo "🚀 Deploying NUCLEAR REDIRECT FIX..."

# Create force-redirect directory on server
ssh deploy@************** "mkdir -p /var/www/pharmastock-app/app/force-redirect"

# Upload force redirect page
echo "📁 Uploading force redirect page..."
scp app/force-redirect/page.tsx deploy@**************:/var/www/pharmastock-app/app/force-redirect/

# Upload updated login page
echo "📁 Uploading updated login page..."
scp app/auth/login/page.tsx deploy@**************:/var/www/pharmastock-app/app/auth/login/

# Restart PM2
echo "🔄 Restarting PM2..."
ssh deploy@************** "cd /var/www/pharmastock-app && pm2 restart pharmastock-app"

echo "✅ NUCLEAR REDIRECT FIX DEPLOYED!"
echo ""
echo "🧪 Test the fix:"
echo "1. Go to https://pharmastock.ma/auth/login"
echo "2. <PERSON><PERSON> should redirect to /force-redirect"
echo "3. Force redirect page should try multiple methods to reach dashboard"
echo "4. If automatic redirect fails, click the manual button"
echo ""
echo "🔧 Alternative test:"
echo "Visit https://pharmastock.ma/force-redirect directly to test redirect methods"
