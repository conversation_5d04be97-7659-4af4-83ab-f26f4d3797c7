#!/bin/bash

echo "🚀 FORCING COMPLETE REBUILD..."

# Stop PM2
echo "🛑 Stopping PM2..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && pm2 stop pharmastock-app"

# Upload the file again
echo "📁 Re-uploading login page..."
scp app/auth/login/page.tsx deploy@216.238.99.198:/var/www/pharmastock-app/app/auth/login/

# Clear all caches
echo "🧹 Clearing all caches..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && rm -rf .next && rm -rf node_modules/.cache"

# Force rebuild
echo "🔨 Force rebuilding..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && npm run build"

# Start PM2
echo "🚀 Starting PM2..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && pm2 start pharmastock-app"

echo "✅ COMPLETE REBUILD FINISHED!"
