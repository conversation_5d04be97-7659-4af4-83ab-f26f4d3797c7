#!/bin/bash

# Finish deployment script for PharmaStock App
echo "🔧 Finishing PharmaStock App deployment..."

# Server details
SERVER_USER="deploy"
SERVER_HOST="**************"
APP_DIR="/var/www/pharmastock-app"

echo "📦 Installing dependencies on server..."
ssh ${SERVER_USER}@${SERVER_HOST} << 'EOF'
cd /var/www/pharmastock-app
echo "Installing npm dependencies..."
npm install --production --silent
echo "Dependencies installed successfully!"
EOF

echo "⚙️ Setting up PM2 process manager..."
ssh ${SERVER_USER}@${SERVER_HOST} << 'EOF'
cd /var/www/pharmastock-app
echo "Restarting PM2 process..."
pm2 restart pharmastock-app || pm2 start npm --name "pharmastock-app" -- start
pm2 save
pm2 list
echo "PM2 setup complete!"
EOF

echo "✅ Deployment finished successfully!"
echo "🚀 Application should now be running on the server"
echo ""
echo "📋 Next steps:"
echo "1. Test the demo login at your application URL"
echo "2. Check the Supabase test page at /supabase-test"
echo "3. Verify the login <NAME_EMAIL> / demo123!"
