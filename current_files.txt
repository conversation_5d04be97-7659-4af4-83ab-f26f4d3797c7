./.next/server/app/_not-found/page_client-reference-manifest.js
./.next/server/app/_not-found/page.js
./.next/server/app/api/dashboard/consolidated/route_client-reference-manifest.js
./.next/server/app/api/dashboard/consolidated/route.js
./.next/server/app/api/marketplace/exchanges/route_client-reference-manifest.js
./.next/server/app/api/marketplace/exchanges/route.js
./.next/server/app/api/marketplace/reservations/route_client-reference-manifest.js
./.next/server/app/api/marketplace/reservations/route.js
./.next/server/app/api/network/route_client-reference-manifest.js
./.next/server/app/api/network/route.js
./.next/server/app/api/orders/route_client-reference-manifest.js
./.next/server/app/api/orders/route.js
./.next/server/app/api/settings/route_client-reference-manifest.js
./.next/server/app/api/settings/route.js
./.next/server/app/api/stock/route_client-reference-manifest.js
./.next/server/app/api/stock/route.js
./.next/server/app/api/urgent-requests/route_client-reference-manifest.js
./.next/server/app/api/urgent-requests/route.js
./.next/server/app/dashboard/page_client-reference-manifest.js
./.next/server/app/dashboard/page.js
./.next/server/app/history/page_client-reference-manifest.js
./.next/server/app/history/page.js
./.next/server/app/inventory/page_client-reference-manifest.js
./.next/server/app/inventory/page.js
./.next/server/app/marketplace/page_client-reference-manifest.js
./.next/server/app/marketplace/page.js
./.next/server/app/network/page_client-reference-manifest.js
./.next/server/app/network/page.js
./.next/server/app/orders/page_client-reference-manifest.js
./.next/server/app/orders/page.js
./.next/server/app/prescription-reader/page_client-reference-manifest.js
./.next/server/app/prescription-reader/page.js
./.next/server/app/profile/page_client-reference-manifest.js
./.next/server/app/profile/page.js
./.next/server/app/settings/page_client-reference-manifest.js
./.next/server/app/settings/page.js
./.next/server/app/team/page_client-reference-manifest.js
./.next/server/app/team/page.js
./.next/server/app/urgent-requests/page_client-reference-manifest.js
./.next/server/app/urgent-requests/page.js
./.next/server/edge-runtime-webpack.js
./.next/server/interception-route-rewrite-manifest.js
./.next/server/middleware-build-manifest.js
./.next/server/middleware-react-loadable-manifest.js
./.next/server/middleware.js
./.next/server/next-font-manifest.js
./.next/server/server-reference-manifest.js
./.next/server/vendor-chunks/@babel.js
./.next/server/vendor-chunks/@floating-ui.js
./.next/server/vendor-chunks/@hookform.js
./.next/server/vendor-chunks/@mantine.js
./.next/server/vendor-chunks/@radix-ui.js
./.next/server/vendor-chunks/@supabase.js
./.next/server/vendor-chunks/@swc.js
./.next/server/vendor-chunks/@tabler.js
./.next/server/vendor-chunks/aria-hidden.js
./.next/server/vendor-chunks/atomic-sleep.js
./.next/server/vendor-chunks/class-variance-authority.js
./.next/server/vendor-chunks/clsx.js
./.next/server/vendor-chunks/date-fns.js
./.next/server/vendor-chunks/dayjs.js
./.next/server/vendor-chunks/fast-redact.js
./.next/server/vendor-chunks/get-nonce.js
./.next/server/vendor-chunks/isows.js
./.next/server/vendor-chunks/jose.js
./.next/server/vendor-chunks/lucide-react.js
./.next/server/vendor-chunks/next-themes.js
./.next/server/vendor-chunks/next.js
./.next/server/vendor-chunks/object-assign.js
./.next/server/vendor-chunks/on-exit-leak-free.js
./.next/server/vendor-chunks/pino-std-serializers.js
./.next/server/vendor-chunks/pino.js
./.next/server/vendor-chunks/prop-types.js
./.next/server/vendor-chunks/quick-format-unescaped.js
./.next/server/vendor-chunks/react-hook-form.js
./.next/server/vendor-chunks/react-is.js
./.next/server/vendor-chunks/react-remove-scroll-bar.js
./.next/server/vendor-chunks/react-remove-scroll.js
./.next/server/vendor-chunks/react-style-singleton.js
./.next/server/vendor-chunks/react-transition-group.js
./.next/server/vendor-chunks/safe-stable-stringify.js
./.next/server/vendor-chunks/set-cookie-parser.js
./.next/server/vendor-chunks/sonic-boom.js
./.next/server/vendor-chunks/sonner.js
./.next/server/vendor-chunks/tabbable.js
./.next/server/vendor-chunks/tailwind-merge.js
./.next/server/vendor-chunks/thread-stream.js
./.next/server/vendor-chunks/tr46.js
./.next/server/vendor-chunks/tslib.js
./.next/server/vendor-chunks/use-callback-ref.js
./.next/server/vendor-chunks/use-sidecar.js
./.next/server/vendor-chunks/webidl-conversions.js
./.next/server/vendor-chunks/whatwg-url.js
./.next/server/vendor-chunks/ws.js
./.next/server/vendor-chunks/zod.js
./.next/server/webpack-runtime.js
./.next/static/chunks/app-pages-internals.js
./.next/static/chunks/app/_not-found/page.js
./.next/static/chunks/app/api/dashboard/consolidated/route.js
./.next/static/chunks/app/api/marketplace/exchanges/route.js
./.next/static/chunks/app/api/marketplace/reservations/route.js
./.next/static/chunks/app/api/network/route.js
./.next/static/chunks/app/api/orders/route.js
./.next/static/chunks/app/api/settings/route.js
./.next/static/chunks/app/api/stock/route.js
./.next/static/chunks/app/api/urgent-requests/route.js
./.next/static/chunks/app/dashboard/layout.js
./.next/static/chunks/app/dashboard/page.js
./.next/static/chunks/app/history/layout.js
./.next/static/chunks/app/history/page.js
./.next/static/chunks/app/inventory/layout.js
./.next/static/chunks/app/inventory/page.js
./.next/static/chunks/app/layout.js
./.next/static/chunks/app/marketplace/page.js
./.next/static/chunks/app/network/page.js
./.next/static/chunks/app/orders/layout.js
./.next/static/chunks/app/orders/page.js
./.next/static/chunks/app/prescription-reader/page.js
./.next/static/chunks/app/profile/layout.js
./.next/static/chunks/app/profile/page.js
./.next/static/chunks/app/settings/page.js
./.next/static/chunks/app/team/layout.js
./.next/static/chunks/app/team/page.js
./.next/static/chunks/app/urgent-requests/layout.js
./.next/static/chunks/app/urgent-requests/page.js
./.next/static/chunks/main-app.js
./.next/static/chunks/polyfills.js
./.next/static/chunks/webpack.js
./.next/static/development/_buildManifest.js
./.next/static/development/_ssgManifest.js
./.next/types/app/api/dashboard/consolidated/route.ts
./.next/types/app/api/marketplace/exchanges/route.ts
./.next/types/app/api/marketplace/reservations/route.ts
./.next/types/app/api/network/route.ts
./.next/types/app/api/orders/route.ts
./.next/types/app/api/settings/route.ts
./.next/types/app/api/stock/route.ts
./.next/types/app/api/urgent-requests/route.ts
./.next/types/app/dashboard/layout.ts
./.next/types/app/dashboard/page.ts
./.next/types/app/history/layout.ts
./.next/types/app/history/page.ts
./.next/types/app/inventory/layout.ts
./.next/types/app/inventory/page.ts
./.next/types/app/layout.ts
./.next/types/app/marketplace/page.ts
./.next/types/app/network/page.ts
./.next/types/app/orders/layout.ts
./.next/types/app/orders/page.ts
./.next/types/app/prescription-reader/page.ts
./.next/types/app/profile/layout.ts
./.next/types/app/profile/page.ts
./.next/types/app/settings/page.ts
./.next/types/app/team/layout.ts
./.next/types/app/team/page.ts
./.next/types/app/urgent-requests/layout.ts
./.next/types/app/urgent-requests/page.ts
./.next/types/cache-life.d.ts
./app/admin/analytics/page.tsx
./app/admin/audit-logs/page.tsx
./app/admin/categories/page.tsx
./app/admin/dashboard/page.tsx
./app/admin/layout.tsx
./app/admin/marketplace/page.tsx
./app/admin/messages/page.tsx
./app/admin/page.tsx
./app/admin/pharmacies/page.tsx
./app/admin/reports/page.tsx
./app/admin/settings/page.tsx
./app/admin/transactions/page.tsx
./app/admin/ui-comparison/page.tsx
./app/admin/urgent-requests/page.tsx
./app/admin/users/page.tsx
./app/alerts/page.tsx
./app/api/admin/messages/route.ts
./app/api/admin/reports/route.ts
./app/api/admin/stats/route.ts
./app/api/admin/users/route.ts
./app/api/alerts/route.ts
./app/api/analyze-image/route.ts
./app/api/analyze-prescription/route.ts
./app/api/auth/create-demo-user/route.ts
./app/api/auth/login/route.ts
./app/api/auth/logout/route.ts
./app/api/auth/pharmacy/route.ts
./app/api/auth/refresh-session/route.ts
./app/api/auth/register/route.ts
./app/api/auth/session/route.ts
./app/api/auth/simple-register/route.ts
./app/api/dashboard/activity/route.ts
./app/api/dashboard/consolidated/route.ts
./app/api/dashboard/stats/route.ts
./app/api/demo/generate-notifications/route.ts
./app/api/enhance-prescription/route.ts
./app/api/marketplace/consolidated/route.ts
./app/api/marketplace/exchanges/route.ts
./app/api/marketplace/listings/route.ts
./app/api/marketplace/reservations/route.ts
./app/api/medication-lookup/route.ts
./app/api/network/contact.ts
./app/api/network/invite.ts
./app/api/network/route.ts
./app/api/orders/route.ts
./app/api/pharmacy/[id]/route.ts
./app/api/pharmacy/drug-requests/route.ts
./app/api/settings/route.ts
./app/api/stock/route.ts
./app/api/suppliers/route.ts
./app/api/urgent-requests/route.ts
./app/auth/forgot-password/page.tsx
./app/auth/login/page.tsx
./app/auth/register/page.tsx
./app/dashboard/layout.tsx
./app/dashboard/listings/page.tsx
./app/dashboard/page.tsx
./app/history/layout.tsx
./app/history/page.tsx
./app/inventory/layout.tsx
./app/inventory/page.tsx
./app/layout.tsx
./app/marketplace/list/page.tsx
./app/marketplace/page.tsx
./app/marketplace/reservations/layout.tsx
./app/metadata.ts
./app/network/page.tsx
./app/notifications/page.tsx
./app/orders/layout.tsx
./app/orders/page.tsx
./app/page.tsx
./app/prescription-reader/page.tsx
./app/profile/layout.tsx
./app/profile/page.tsx
./app/providers.tsx
./app/settings/page.tsx
./app/supplier/dashboard/page.tsx
./app/supplier/layout.tsx
./app/team/layout.tsx
./app/team/page.tsx
./app/test-access/page.tsx
./app/unauthorized/page.tsx
./app/urgent-requests/layout.tsx
./app/urgent-requests/page.tsx
./archive/configuration/page.tsx
./archive/dashboard-old-design.tsx
./archive/expiring/page.tsx
./archive/routes/page.tsx
./archive/scan/page.tsx
./archive/settings/notifications/test/page.tsx
./archive/sharing/page.tsx
./archive/stocks/page.tsx
./check-enum-values.js
./check-existing-users.js
./check-registration.js
./check-tables.js
./check-users.js
./clear-auth-users.js
./components/admin/AdminSidebar.tsx
./components/admin/alert-system.tsx
./components/admin/moderation-panel.tsx
./components/admin/pharmacy-management.tsx
./components/admin/sidebar.tsx
./components/admin/user-management.tsx
./components/audit/transaction-log.tsx
./components/auth/AccessControl.tsx
./components/dashboard/recent-transactions.tsx
./components/dashboard/stats-card.tsx
./components/dashboard/stats/active-listings.tsx
./components/dashboard/stats/category-distribution.tsx
./components/dashboard/stats/local-offers.tsx
./components/dashboard/stats/most-requested-meds.tsx
./components/dashboard/stats/stock-evolution.tsx
./components/dashboard/stock-alert-table.tsx
./components/debug/debug-panel.tsx
./components/error-boundary.tsx
./components/layout/app-shell.tsx
./components/layout/header.tsx
./components/layout/navbar.tsx
./components/layout/root-wrapper.tsx
./components/layout/sidebar.tsx
./components/marketplace/advanced-filters.tsx
./components/marketplace/barcode-scanner.tsx
./components/marketplace/drug-recognition.tsx
./components/marketplace/filters.tsx
./components/marketplace/list-for-sale-form.tsx
./components/marketplace/list-for-sale-modal.tsx
./components/marketplace/product-card.tsx
./components/marketplace/product-detail-modal.tsx
./components/marketplace/reservations-list.tsx
./components/marketplace/reserve-product-modal.tsx
./components/marketplace/sell-product-modal.tsx
./components/network/contact-pharmacy-modal.tsx
./components/notifications/notification-analytics.tsx
./components/notifications/notification-list.tsx
./components/notifications/notification-preferences.tsx
./components/notifications/notification-test.tsx
./components/profile/pharmacy-documents.tsx
./components/profile/pharmacy-profile-view.tsx
./components/profile/pharmacy-profile.tsx
./components/profile/pharmacy-settings.tsx
./components/Providers.tsx
./components/providers/MantineProvider.tsx
./components/pwa/pwa-init.tsx
./components/share-button.tsx
./components/sidebar.tsx
./components/stocks/stock-form.tsx
./components/stocks/stock-table.tsx
./components/theme-provider.tsx
./components/theme-toggle.tsx
./components/ui/accordion.tsx
./components/ui/alert-dialog.tsx
./components/ui/alert.tsx
./components/ui/aspect-ratio.tsx
./components/ui/avatar.tsx
./components/ui/badge.tsx
./components/ui/breadcrumb.tsx
./components/ui/button.tsx
./components/ui/calendar.tsx
./components/ui/card.tsx
./components/ui/carousel.tsx
./components/ui/chart.tsx
./components/ui/checkbox.tsx
./components/ui/collapsible.tsx
./components/ui/command.tsx
./components/ui/context-menu.tsx
./components/ui/custom-badge.tsx
./components/ui/dialog.tsx
./components/ui/drawer.tsx
./components/ui/dropdown-menu.tsx
./components/ui/expiry-progress.tsx
./components/ui/form.tsx
./components/ui/hover-card.tsx
./components/ui/input-otp.tsx
./components/ui/input.tsx
./components/ui/label.tsx
./components/ui/logo.tsx
./components/ui/menubar.tsx
./components/ui/navigation-menu.tsx
./components/ui/pagination.tsx
./components/ui/popover.tsx
./components/ui/popup-provider.tsx
./components/ui/progress.tsx
./components/ui/radio-group.tsx
./components/ui/resizable.tsx
./components/ui/scroll-area.tsx
./components/ui/select.tsx
./components/ui/separator.tsx
./components/ui/sheet.tsx
./components/ui/skeleton.tsx
./components/ui/slider.tsx
./components/ui/sonner.tsx
./components/ui/switch.tsx
./components/ui/table.tsx
./components/ui/tabs.tsx
./components/ui/textarea.tsx
./components/ui/toast.tsx
./components/ui/toaster.tsx
./components/ui/toggle-group.tsx
./components/ui/toggle.tsx
./components/ui/tooltip.tsx
./components/ui/use-toast.ts
./components/urgent-requests/urgent-request-form.tsx
./components/urgent-requests/urgent-request-modal.tsx
./components/user-nav.tsx
./contexts/auth-client.tsx
./contexts/auth-context.tsx
./contexts/database-context.tsx
./contexts/notification-context.tsx
./contexts/supabase-context.tsx
./create-all-demo-accounts.js
./create-demo-notifications.js
./create-demo-users.js
./create-final-demo-users.js
./create-production-demo-users.js
./create-test-user.js
./create-working-demo-users.js
./demo-summary.js
./fix-demo-users-with-existing-ids.js
./hooks/use-product-interest.ts
./hooks/use-realtime-notifications.ts
./hooks/use-team.ts
./hooks/use-toast.ts
./hooks/useAccessControl.ts
./lib/api-utils.ts
./lib/auth-helpers.ts
./lib/auth-server.ts
./lib/auth.ts
./lib/config.ts
./lib/db.ts
./lib/form-utils.ts
./lib/logger.ts
./lib/mantine-theme.ts
./lib/notifications.ts
./lib/prisma.ts
./lib/prisma/index.ts
./lib/pwa.ts
./lib/supabase/admin.ts
./lib/supabase/client.ts
./lib/trpc/context.ts
./lib/trpc/routers/auth.ts
./lib/trpc/server.ts
./lib/type-guards.ts
./lib/types/auth.ts
./lib/types/marketplace.ts
./lib/utils.ts
./middleware.ts
./next-env.d.ts
./playwright-report/trace/assets/codeMirrorModule-CyuxU5C-.js
./playwright-report/trace/assets/defaultSettingsView-5nVJRt0A.js
./playwright-report/trace/index.qVn2ZnpC.js
./playwright-report/trace/sw.bundle.js
./playwright-report/trace/uiMode.m4IPRPOd.js
./playwright.config.ts
./public/sw.js
./public/workbox-e43f5367.js
./reset-demo.js
./scripts/analyze-code.js
./scripts/check-db.js
./scripts/check-db.ts
./scripts/check-schema.js
./scripts/check-users.ts
./scripts/clear-auth-tokens.js
./scripts/create-auth-users.js
./scripts/create-local-test-user.js
./scripts/create-pharmacy-owner.js
./scripts/create-supabase-users.js
./scripts/create-super-admin.js
./scripts/create-team-member.js
./scripts/create-test-user.js
./scripts/create-test-users-properly.js
./scripts/create-test-users.ts
./scripts/create-users-with-api.js
./scripts/fix-passwords.js
./scripts/fix-pharma-test-user.js
./scripts/generate-icons.js
./scripts/refresh-user-session.js
./scripts/reset-auth-schema.js
./scripts/reset-pharmacy-owner-password.js
./scripts/reset-user-passwords.js
./scripts/run-tests.ts
./scripts/setup-database.js
./scripts/setup-test-environment.js
./scripts/setup-test-pharmacy.js
./scripts/setup-test-users-admin.js
./scripts/setup-test-users-final.js
./scripts/setup-test-users-simple.js
./scripts/setup-test-users.js
./scripts/setup-users-properly.js
./scripts/test-auth-simple.js
./scripts/test-auth.js
./scripts/test-session-api.js
./scripts/test-session-profile-query.js
./scripts/test-user-roles.js
./scripts/update-admin-password.js
./scripts/update-user-passwords.js
./scripts/utils.js
./scripts/utils/check-app.ts
./scripts/utils/test-setup.ts
./scripts/utils/test-utils.ts
./seed-pharmacy-demo.js
./services/mock/admin.ts
./services/mock/index.ts
./setup-complete-demo.js
./setup-demo-pharmacy.js
./setup-pharmastock-demo.js
./src/components/PWAInstallPrompt.tsx
./src/components/SubscriptionStatus.tsx
./src/components/supplier/SupplierAlerts.tsx
./src/components/supplier/SupplierAnalytics.tsx
./src/components/supplier/SupplierDashboard.tsx
./src/components/supplier/SupplierMainDashboard.tsx
./src/components/supplier/SupplierNavigation.tsx
./src/components/supplier/SupplierNetwork.tsx
./src/components/supplier/SupplierOpportunities.tsx
./src/components/supplier/SupplierTerritory.tsx
./src/hooks/useFreemium.ts
./src/services/pushNotificationService.ts
./src/services/supabase.ts
./src/utils/useIsClient.ts
./supabase/functions/notify-suppliers-excess/index.ts
./supabase/functions/send-push-notification/index.ts
./tailwind.config.ts
./test-auth.js
./test-demo-accounts.js
./test-gemini.js
./test-single-user.js
./tests/admin/dashboard.spec.ts
./tests/auth/login.spec.ts
./tests/example.test.ts
./tests/global-setup.ts
./tests/global-teardown.ts
./tests/prescription-reader.test.js
./tests/setup/globalSetup.ts
./tests/setup/testData.ts
./tests/utils/auth.ts
./tests/utils/check-app.ts
./types/index.ts
./types/supabase.ts
./update-demo-user-roles.js
./update-demo-user-to-pro.js
./website/js/main.js
