import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables - try production first, then local
if (process.env.NODE_ENV === 'production' || process.argv.includes('--production')) {
  dotenv.config({ path: '.env.production' });
  console.log('🏭 Using production environment');
} else {
  dotenv.config({ path: '.env.local' });
  console.log('🏠 Using local environment');
}

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixDemoAccounts() {
  try {
    console.log('🔧 Fixing demo accounts...\n');

    // Demo account details
    const demoAccounts = [
      {
        email: '<EMAIL>',
        password: 'demo123!',
        role: 'owner',
        name: '<PERSON> Benali'
      },
      {
        email: '<EMAIL>',
        password: 'demo123!',
        role: 'staff',
        name: 'Laila Tazi'
      }
    ];

    // 1. Ensure demo users exist in auth
    console.log('1. Ensuring demo users exist...');
    const userIds = {};
    
    for (const account of demoAccounts) {
      // Check if user exists
      const { data: existingUsers } = await supabase.auth.admin.listUsers();
      let user = existingUsers.users.find(u => u.email === account.email);
      
      if (!user) {
        console.log(`Creating user: ${account.email}`);
        const { data: newUser, error: createError } = await supabase.auth.admin.createUser({
          email: account.email,
          password: account.password,
          email_confirm: true,
          user_metadata: { 
            role: account.role,
            full_name: account.name
          }
        });
        
        if (createError) {
          console.error(`❌ Failed to create ${account.email}:`, createError.message);
          continue;
        }
        user = newUser.user;
      } else {
        console.log(`✅ User exists: ${account.email}`);
        
        // Update user metadata if needed
        if (user.user_metadata?.role !== account.role) {
          console.log(`Updating role for ${account.email}`);
          await supabase.auth.admin.updateUserById(user.id, {
            user_metadata: { 
              role: account.role,
              full_name: account.name
            }
          });
        }
      }
      
      userIds[account.email] = user.id;
    }

    // 2. Ensure demo pharmacy exists
    console.log('\n2. Ensuring demo pharmacy exists...');
    const { data: pharmacy, error: pharmacyError } = await supabase
      .from('pharmacies')
      .upsert({
        name: 'Pharmacie Camélia',
        email: '<EMAIL>',
        phone: '+212 522 123 456',
        address: '123 Boulevard Hassan II',
        city: 'Casablanca',
        license_number: 'DEMO-2024-001',
        is_verified: true
      }, { 
        onConflict: 'email',
        ignoreDuplicates: false 
      })
      .select()
      .single();

    if (pharmacyError) {
      console.error('❌ Failed to create/update pharmacy:', pharmacyError.message);
      return;
    }

    console.log(`✅ Demo pharmacy ready: ${pharmacy.name} (${pharmacy.id})`);

    // 3. Ensure profiles exist with pharmacy_id
    console.log('\n3. Ensuring profiles exist...');
    for (const account of demoAccounts) {
      const userId = userIds[account.email];
      if (!userId) continue;

      const { error: profileError } = await supabase
        .from('profiles')
        .upsert({
          id: userId,
          email: account.email,
          full_name: account.name,
          role: account.role,
          pharmacy_id: pharmacy.id
        }, { 
          onConflict: 'id',
          ignoreDuplicates: false 
        });

      if (profileError) {
        console.error(`❌ Failed to create/update profile for ${account.email}:`, profileError.message);
      } else {
        console.log(`✅ Profile updated: ${account.email}`);
      }
    }

    // 4. Ensure team memberships exist
    console.log('\n4. Ensuring team memberships exist...');
    for (const account of demoAccounts) {
      const userId = userIds[account.email];
      if (!userId) continue;

      const { error: teamError } = await supabase
        .from('pharmacy_team_members')
        .upsert({
          pharmacy_id: pharmacy.id,
          user_id: userId,
          role: account.role,
          status: 'active'
        }, { 
          onConflict: 'pharmacy_id,user_id',
          ignoreDuplicates: false 
        });

      if (teamError) {
        console.error(`❌ Failed to create/update team membership for ${account.email}:`, teamError.message);
      } else {
        console.log(`✅ Team membership updated: ${account.email}`);
      }
    }

    // 5. Test authentication
    console.log('\n5. Testing authentication...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123!'
    });

    if (loginError) {
      console.error(`❌ Login test failed: ${loginError.message}`);
    } else {
      console.log(`✅ Login test successful for: ${loginData.user.email}`);
      
      // Test getting team membership
      const { data: teamMember, error: teamError } = await supabase
        .from('pharmacy_team_members')
        .select('pharmacy_id')
        .eq('user_id', loginData.user.id)
        .single();

      if (teamError) {
        console.error(`❌ Team membership lookup failed: ${teamError.message}`);
      } else {
        console.log(`✅ Team membership found: pharmacy_id=${teamMember.pharmacy_id}`);
      }
    }

    console.log('\n🎉 Demo accounts fix completed!');
    console.log('📋 Demo Credentials:');
    console.log('👤 Owner: <EMAIL> / demo123!');
    console.log('👤 Staff: <EMAIL> / demo123!');
    console.log(`🏥 Pharmacy: Pharmacie Camélia (${pharmacy.id})`);

  } catch (error) {
    console.error('💥 Fix error:', error.message);
  }
}

fixDemoAccounts();
