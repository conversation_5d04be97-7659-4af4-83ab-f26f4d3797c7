import { 
  MOROCCO_SEASONAL_PATTERNS, 
  MOROCCO_WEATHER_PATTERNS, 
  MOROCCO_HOLIDAY_PATTERNS,
  getCurrentSeasonalPatterns,
  getDemandMultiplier 
} from './morocco-patterns';

export interface DemandForecast {
  date: string;
  category: string;
  baselineDemand: number;
  forecastedDemand: number;
  multiplier: number;
  confidence: number; // 0-100%
  factors: ForecastFactor[];
  recommendations: string[];
}

export interface ForecastFactor {
  type: 'seasonal' | 'weather' | 'holiday' | 'trend' | 'historical';
  name: string;
  impact: number; // -1 to 1
  confidence: number;
  description: string;
}

export interface CategoryDemand {
  category: string;
  categoryName: string;
  currentDemand: number;
  forecastedDemand: number;
  trend: 'increasing' | 'decreasing' | 'stable';
  trendPercentage: number;
  peakDate?: string;
  recommendations: string[];
}

export interface SeasonalInsight {
  period: string;
  type: 'opportunity' | 'warning' | 'preparation';
  title: string;
  description: string;
  affectedCategories: string[];
  actionItems: string[];
  priority: 'low' | 'medium' | 'high' | 'critical';
  daysUntil: number;
}

// Pharmaceutical categories with Arabic names
export const PHARMACEUTICAL_CATEGORIES = {
  'cold_flu_medicine': 'أدوية البرد والإنفلونزا',
  'digestive_health': 'صحة الجهاز الهضمي',
  'diabetes_management': 'إدارة السكري',
  'vitamins_supplements': 'الفيتامينات والمكملات',
  'children_medicine': 'أدوية الأطفال',
  'pain_relief': 'مسكنات الألم',
  'antibiotics': 'المضادات الحيوية',
  'respiratory_care': 'العناية بالجهاز التنفسي',
  'skin_care': 'العناية بالبشرة',
  'antihistamines': 'مضادات الهيستامين',
  'hydration_solutions': 'محاليل الترطيب',
  'sunscreen': 'واقي الشمس',
  'first_aid': 'الإسعافات الأولية',
  'immune_boosters': 'معززات المناعة',
  'vaccines': 'اللقاحات',
  'chronic_disease_management': 'إدارة الأمراض المزمنة'
};

export class DemandForecastingEngine {
  private historicalData: Map<string, number[]> = new Map();
  
  constructor() {
    this.initializeHistoricalData();
  }

  // Initialize with mock historical data
  private initializeHistoricalData() {
    Object.keys(PHARMACEUTICAL_CATEGORIES).forEach(category => {
      // Generate 12 months of mock historical data
      const data = Array.from({ length: 12 }, (_, i) => {
        const baseValue = 100 + Math.random() * 50;
        const seasonalVariation = this.getSeasonalVariation(category, i + 1);
        return Math.round(baseValue * seasonalVariation);
      });
      this.historicalData.set(category, data);
    });
  }

  // Get seasonal variation for a category and month
  private getSeasonalVariation(category: string, month: number): number {
    const date = new Date(2024, month - 1, 15); // Mid-month
    return getDemandMultiplier(category, date);
  }

  // Generate demand forecast for a specific date and category
  generateForecast(category: string, targetDate: Date, baselineDemand: number = 100): DemandForecast {
    const factors = this.analyzeForecastFactors(category, targetDate);
    const multiplier = this.calculateTotalMultiplier(factors);
    const forecastedDemand = Math.round(baselineDemand * multiplier);
    const confidence = this.calculateConfidence(factors);
    const recommendations = this.generateRecommendations(category, multiplier, factors);

    return {
      date: targetDate.toISOString().split('T')[0],
      category,
      baselineDemand,
      forecastedDemand,
      multiplier,
      confidence,
      factors,
      recommendations
    };
  }

  // Analyze all factors affecting demand
  private analyzeForecastFactors(category: string, date: Date): ForecastFactor[] {
    const factors: ForecastFactor[] = [];

    // Seasonal factors
    const activePatterns = getCurrentSeasonalPatterns(date);
    activePatterns.forEach(pattern => {
      if (pattern.affectedCategories.includes(category)) {
        factors.push({
          type: 'seasonal',
          name: pattern.name,
          impact: (pattern.demandMultiplier - 1) * 0.8, // Scale impact
          confidence: pattern.isVariable ? 75 : 90,
          description: pattern.description
        });
      }
    });

    // Weather factors
    const season = this.getCurrentSeason(date);
    const weatherPattern = MOROCCO_WEATHER_PATTERNS.find(p => p.season === season);
    if (weatherPattern) {
      const weatherCategory = weatherPattern.demandCategories.find(c => c.category === category);
      if (weatherCategory) {
        factors.push({
          type: 'weather',
          name: `Conditions ${season}`,
          impact: (weatherCategory.multiplier - 1) * 0.6,
          confidence: 80,
          description: weatherCategory.reason
        });
      }
    }

    // Holiday factors
    const holidayImpact = this.getHolidayImpact(category, date);
    if (holidayImpact) {
      factors.push(holidayImpact);
    }

    // Historical trend
    const trendFactor = this.calculateTrendFactor(category, date);
    if (trendFactor) {
      factors.push(trendFactor);
    }

    return factors;
  }

  // Calculate total demand multiplier from all factors
  private calculateTotalMultiplier(factors: ForecastFactor[]): number {
    let totalImpact = 0;
    let weightSum = 0;

    factors.forEach(factor => {
      const weight = factor.confidence / 100;
      totalImpact += factor.impact * weight;
      weightSum += weight;
    });

    const averageImpact = weightSum > 0 ? totalImpact / weightSum : 0;
    return Math.max(0.1, 1 + averageImpact); // Minimum 10% of baseline
  }

  // Calculate forecast confidence
  private calculateConfidence(factors: ForecastFactor[]): number {
    if (factors.length === 0) return 50;
    
    const avgConfidence = factors.reduce((sum, f) => sum + f.confidence, 0) / factors.length;
    const factorBonus = Math.min(factors.length * 5, 20); // More factors = higher confidence
    
    return Math.min(95, Math.round(avgConfidence + factorBonus));
  }

  // Generate recommendations based on forecast
  private generateRecommendations(category: string, multiplier: number, factors: ForecastFactor[]): string[] {
    const recommendations: string[] = [];
    const categoryName = PHARMACEUTICAL_CATEGORIES[category as keyof typeof PHARMACEUTICAL_CATEGORIES];

    if (multiplier > 1.5) {
      recommendations.push(`📈 Forte demande prévue pour ${categoryName} - Augmenter les stocks de 50-70%`);
      recommendations.push(`⏰ Commander dès maintenant pour éviter les ruptures`);
    } else if (multiplier > 1.2) {
      recommendations.push(`📊 Demande modérée pour ${categoryName} - Augmenter les stocks de 20-30%`);
    } else if (multiplier < 0.8) {
      recommendations.push(`📉 Demande réduite pour ${categoryName} - Réduire les commandes`);
    }

    // Factor-specific recommendations
    factors.forEach(factor => {
      if (factor.type === 'seasonal' && factor.impact > 0.3) {
        recommendations.push(`🗓️ Préparer pour ${factor.name} - Stock supplémentaire recommandé`);
      }
      if (factor.type === 'weather' && factor.impact > 0.2) {
        recommendations.push(`🌡️ Conditions météo favorables à la demande`);
      }
    });

    return recommendations.slice(0, 4); // Limit to 4 recommendations
  }

  // Get holiday impact for a specific date
  private getHolidayImpact(category: string, date: Date): ForecastFactor | null {
    // Simplified holiday detection - in real implementation, would calculate Islamic calendar dates
    const month = date.getMonth() + 1;
    const day = date.getDate();
    
    for (const holiday of MOROCCO_HOLIDAY_PATTERNS) {
      if (holiday.isFixed && holiday.fixedDate) {
        const [holidayMonth, holidayDay] = holiday.fixedDate.split('-').map(Number);
        const daysDiff = Math.abs((month * 30 + day) - (holidayMonth * 30 + holidayDay));
        
        if (daysDiff <= holiday.duration) {
          const impact = holiday.demandImpact.find(i => i.category === category);
          if (impact) {
            return {
              type: 'holiday',
              name: holiday.name,
              impact: (impact.multiplier - 1) * 0.7,
              confidence: 85,
              description: `Impact de ${holiday.name} sur la demande`
            };
          }
        }
      }
    }
    
    return null;
  }

  // Calculate trend factor based on historical data
  private calculateTrendFactor(category: string, date: Date): ForecastFactor | null {
    const historical = this.historicalData.get(category);
    if (!historical || historical.length < 3) return null;

    const recent = historical.slice(-3);
    const trend = (recent[2] - recent[0]) / recent[0];

    if (Math.abs(trend) > 0.1) {
      return {
        type: 'trend',
        name: 'Tendance historique',
        impact: trend * 0.3, // Scale down trend impact
        confidence: 70,
        description: trend > 0 ? 'Tendance à la hausse' : 'Tendance à la baisse'
      };
    }

    return null;
  }

  // Generate category demand overview
  generateCategoryOverview(targetDate: Date = new Date()): CategoryDemand[] {
    return Object.keys(PHARMACEUTICAL_CATEGORIES).map(category => {
      const baseline = 100;
      const forecast = this.generateForecast(category, targetDate, baseline);
      const historical = this.historicalData.get(category) || [];
      const lastMonth = historical[historical.length - 1] || baseline;
      
      const trendPercentage = ((forecast.forecastedDemand - lastMonth) / lastMonth) * 100;
      const trend = trendPercentage > 5 ? 'increasing' : 
                   trendPercentage < -5 ? 'decreasing' : 'stable';

      return {
        category,
        categoryName: PHARMACEUTICAL_CATEGORIES[category as keyof typeof PHARMACEUTICAL_CATEGORIES],
        currentDemand: lastMonth,
        forecastedDemand: forecast.forecastedDemand,
        trend,
        trendPercentage: Math.round(trendPercentage),
        recommendations: forecast.recommendations
      };
    });
  }

  // Generate seasonal insights
  generateSeasonalInsights(daysAhead: number = 30): SeasonalInsight[] {
    const insights: SeasonalInsight[] = [];
    const today = new Date();

    // Check upcoming seasonal patterns
    MOROCCO_SEASONAL_PATTERNS.forEach(pattern => {
      const patternDate = this.getPatternDate(pattern, today);
      if (patternDate) {
        const daysUntil = Math.ceil((patternDate.getTime() - today.getTime()) / (1000 * 60 * 60 * 24));
        
        if (daysUntil <= daysAhead && daysUntil >= -pattern.preparationDays) {
          const priority = pattern.demandMultiplier > 1.8 ? 'critical' :
                          pattern.demandMultiplier > 1.5 ? 'high' :
                          pattern.demandMultiplier > 1.2 ? 'medium' : 'low';

          insights.push({
            period: pattern.name,
            type: daysUntil > 0 ? 'preparation' : 'opportunity',
            title: daysUntil > 0 ? `Préparer pour ${pattern.name}` : `${pattern.name} en cours`,
            description: pattern.description,
            affectedCategories: pattern.affectedCategories,
            actionItems: this.generateActionItems(pattern),
            priority: priority as any,
            daysUntil: Math.max(0, daysUntil)
          });
        }
      }
    });

    return insights.sort((a, b) => a.daysUntil - b.daysUntil);
  }

  // Helper methods
  private getCurrentSeason(date: Date): 'winter' | 'spring' | 'summer' | 'autumn' {
    const month = date.getMonth() + 1;
    if (month >= 12 || month <= 2) return 'winter';
    if (month >= 3 && month <= 5) return 'spring';
    if (month >= 6 && month <= 8) return 'summer';
    return 'autumn';
  }

  private getPatternDate(pattern: any, referenceDate: Date): Date | null {
    if (!pattern.isVariable && pattern.startDate) {
      const [month, day] = pattern.startDate.split('-').map(Number);
      const year = referenceDate.getFullYear();
      const patternDate = new Date(year, month - 1, day);
      
      // If the date has passed this year, check next year
      if (patternDate < referenceDate) {
        return new Date(year + 1, month - 1, day);
      }
      return patternDate;
    }
    return null; // Variable dates need Islamic calendar calculation
  }

  private generateActionItems(pattern: any): string[] {
    const items: string[] = [];
    
    if (pattern.demandMultiplier > 1.5) {
      items.push(`Augmenter les stocks de ${Math.round((pattern.demandMultiplier - 1) * 100)}%`);
    }
    
    items.push(`Commencer les préparatifs ${pattern.preparationDays} jours avant`);
    
    if (pattern.category === 'religious') {
      items.push('Coordonner avec les fournisseurs pour les livraisons');
    }
    
    if (pattern.affectedCategories.length > 3) {
      items.push('Vérifier les stocks de toutes les catégories affectées');
    }

    return items;
  }
}

// Export singleton instance
export const demandForecastingEngine = new DemandForecastingEngine();
