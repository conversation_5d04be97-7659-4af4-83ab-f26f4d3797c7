// Morocco-specific seasonal demand patterns for pharmaceutical products

export interface SeasonalPattern {
  id: string;
  name: string;
  category: 'religious' | 'seasonal' | 'weather' | 'social' | 'health';
  startDate: string; // MM-DD format
  endDate: string;
  isVariable: boolean; // For dates that change yearly (like Ramadan)
  demandMultiplier: number; // 1.0 = normal, >1.0 = increased demand
  affectedCategories: string[];
  description: string;
  preparationDays: number; // Days before to start stocking
}

export interface WeatherPattern {
  season: 'winter' | 'spring' | 'summer' | 'autumn';
  months: number[];
  conditions: string[];
  demandCategories: {
    category: string;
    multiplier: number;
    reason: string;
  }[];
}

export interface HolidayPattern {
  id: string;
  name: string;
  type: 'religious' | 'national' | 'cultural';
  isFixed: boolean;
  fixedDate?: string; // MM-DD for fixed dates
  calculationMethod?: string; // For variable dates
  duration: number; // Days
  demandImpact: {
    category: string;
    multiplier: number;
    peakDays: number[]; // Which days of the holiday have peak demand
  }[];
}

// Morocco-specific seasonal patterns
export const MOROCCO_SEASONAL_PATTERNS: SeasonalPattern[] = [
  {
    id: 'ramadan',
    name: '<PERSON><PERSON>',
    category: 'religious',
    startDate: '03-11', // Approximate, varies yearly
    endDate: '04-09',
    isVariable: true,
    demandMultiplier: 1.8,
    affectedCategories: [
      'digestive_health',
      'diabetes_management',
      'vitamins_supplements',
      'hydration_solutions',
      'sleep_aids',
      'headache_relief'
    ],
    description: 'Mois de jeûne avec changements alimentaires et horaires',
    preparationDays: 14
  },
  {
    id: 'eid_al_fitr',
    name: 'Aïd Al-Fitr',
    category: 'religious',
    startDate: '04-10', // After Ramadan
    endDate: '04-12',
    isVariable: true,
    demandMultiplier: 2.2,
    affectedCategories: [
      'digestive_health',
      'children_medicine',
      'pain_relief',
      'first_aid',
      'diabetes_management'
    ],
    description: 'Fête de fin de Ramadan avec repas copieux',
    preparationDays: 7
  },
  {
    id: 'eid_al_adha',
    name: 'Aïd Al-Adha',
    category: 'religious',
    startDate: '06-17', // Approximate
    endDate: '06-20',
    isVariable: true,
    demandMultiplier: 2.0,
    affectedCategories: [
      'digestive_health',
      'antibiotics',
      'wound_care',
      'pain_relief',
      'food_poisoning'
    ],
    description: 'Fête du sacrifice avec consommation de viande',
    preparationDays: 10
  },
  {
    id: 'winter_season',
    name: 'Saison Hivernale',
    category: 'seasonal',
    startDate: '11-01',
    endDate: '03-31',
    isVariable: false,
    demandMultiplier: 1.6,
    affectedCategories: [
      'cold_flu_medicine',
      'cough_syrups',
      'throat_lozenges',
      'fever_reducers',
      'immune_boosters',
      'respiratory_care'
    ],
    description: 'Saison des rhumes et grippes',
    preparationDays: 21
  },
  {
    id: 'back_to_school',
    name: 'Rentrée Scolaire',
    category: 'social',
    startDate: '09-01',
    endDate: '09-30',
    isVariable: false,
    demandMultiplier: 1.4,
    affectedCategories: [
      'children_medicine',
      'vitamins_supplements',
      'vaccines',
      'head_lice_treatment',
      'first_aid',
      'immune_boosters'
    ],
    description: 'Retour à l\'école et exposition aux infections',
    preparationDays: 14
  },
  {
    id: 'summer_heat',
    name: 'Chaleur Estivale',
    category: 'weather',
    startDate: '06-01',
    endDate: '09-15',
    isVariable: false,
    demandMultiplier: 1.3,
    affectedCategories: [
      'sunscreen',
      'hydration_solutions',
      'heat_rash_treatment',
      'digestive_health',
      'insect_repellent',
      'cooling_gels'
    ],
    description: 'Forte chaleur et risques liés',
    preparationDays: 10
  },
  {
    id: 'allergy_season',
    name: 'Saison des Allergies',
    category: 'seasonal',
    startDate: '03-01',
    endDate: '05-31',
    isVariable: false,
    demandMultiplier: 1.5,
    affectedCategories: [
      'antihistamines',
      'nasal_sprays',
      'eye_drops',
      'respiratory_care',
      'skin_allergies'
    ],
    description: 'Pollens et allergies printanières',
    preparationDays: 14
  },
  {
    id: 'pilgrimage_season',
    name: 'Saison du Hajj',
    category: 'religious',
    startDate: '05-01',
    endDate: '08-31',
    isVariable: true,
    demandMultiplier: 1.2,
    affectedCategories: [
      'vaccines',
      'travel_medicine',
      'antibiotics',
      'digestive_health',
      'first_aid',
      'chronic_disease_management'
    ],
    description: 'Préparation et retour du pèlerinage',
    preparationDays: 30
  }
];

// Weather-based demand patterns
export const MOROCCO_WEATHER_PATTERNS: WeatherPattern[] = [
  {
    season: 'winter',
    months: [12, 1, 2, 3],
    conditions: ['cold', 'rain', 'humidity'],
    demandCategories: [
      { category: 'cold_flu_medicine', multiplier: 2.0, reason: 'Rhumes et grippes saisonniers' },
      { category: 'respiratory_care', multiplier: 1.8, reason: 'Problèmes respiratoires dus au froid' },
      { category: 'joint_pain_relief', multiplier: 1.4, reason: 'Douleurs articulaires accrues' },
      { category: 'skin_care', multiplier: 1.3, reason: 'Peau sèche due au froid' }
    ]
  },
  {
    season: 'spring',
    months: [3, 4, 5],
    conditions: ['pollen', 'temperature_variation', 'wind'],
    demandCategories: [
      { category: 'antihistamines', multiplier: 2.2, reason: 'Allergies aux pollens' },
      { category: 'eye_drops', multiplier: 1.6, reason: 'Irritations oculaires' },
      { category: 'nasal_sprays', multiplier: 1.8, reason: 'Congestion nasale allergique' }
    ]
  },
  {
    season: 'summer',
    months: [6, 7, 8, 9],
    conditions: ['extreme_heat', 'dehydration_risk', 'sun_exposure'],
    demandCategories: [
      { category: 'hydration_solutions', multiplier: 2.5, reason: 'Risque de déshydratation' },
      { category: 'sunscreen', multiplier: 3.0, reason: 'Protection solaire essentielle' },
      { category: 'digestive_health', multiplier: 1.6, reason: 'Troubles digestifs dus à la chaleur' },
      { category: 'cooling_products', multiplier: 2.0, reason: 'Produits rafraîchissants' }
    ]
  },
  {
    season: 'autumn',
    months: [9, 10, 11],
    conditions: ['temperature_drop', 'back_to_school', 'immune_system_stress'],
    demandCategories: [
      { category: 'immune_boosters', multiplier: 1.5, reason: 'Renforcement immunitaire' },
      { category: 'vitamins_supplements', multiplier: 1.4, reason: 'Supplémentation préventive' },
      { category: 'children_medicine', multiplier: 1.6, reason: 'Retour à l\'école et infections' }
    ]
  }
];

// Holiday-specific patterns
export const MOROCCO_HOLIDAY_PATTERNS: HolidayPattern[] = [
  {
    id: 'new_year',
    name: 'Nouvel An',
    type: 'national',
    isFixed: true,
    fixedDate: '01-01',
    duration: 2,
    demandImpact: [
      { category: 'digestive_health', multiplier: 1.8, peakDays: [1, 2] },
      { category: 'pain_relief', multiplier: 1.5, peakDays: [1] },
      { category: 'hangover_remedies', multiplier: 2.0, peakDays: [1] }
    ]
  },
  {
    id: 'independence_day',
    name: 'Fête de l\'Indépendance',
    type: 'national',
    isFixed: true,
    fixedDate: '11-18',
    duration: 1,
    demandImpact: [
      { category: 'first_aid', multiplier: 1.3, peakDays: [1] },
      { category: 'digestive_health', multiplier: 1.2, peakDays: [1] }
    ]
  },
  {
    id: 'throne_day',
    name: 'Fête du Trône',
    type: 'national',
    isFixed: true,
    fixedDate: '07-30',
    duration: 1,
    demandImpact: [
      { category: 'digestive_health', multiplier: 1.4, peakDays: [1] },
      { category: 'first_aid', multiplier: 1.2, peakDays: [1] }
    ]
  },
  {
    id: 'mawlid',
    name: 'Mawlid An-Nabawi',
    type: 'religious',
    isFixed: false,
    calculationMethod: 'islamic_calendar',
    duration: 1,
    demandImpact: [
      { category: 'digestive_health', multiplier: 1.6, peakDays: [1] },
      { category: 'children_medicine', multiplier: 1.3, peakDays: [1] }
    ]
  }
];

// Function to get current seasonal patterns
export function getCurrentSeasonalPatterns(date: Date = new Date()): SeasonalPattern[] {
  const currentMonth = date.getMonth() + 1;
  const currentDay = date.getDate();
  const currentDateStr = `${currentMonth.toString().padStart(2, '0')}-${currentDay.toString().padStart(2, '0')}`;
  
  return MOROCCO_SEASONAL_PATTERNS.filter(pattern => {
    if (pattern.isVariable) {
      // For variable dates, we'd need to calculate based on Islamic calendar
      // For now, return approximate matches
      return true; // Simplified for demo
    }
    
    const startMonth = parseInt(pattern.startDate.split('-')[0]);
    const startDay = parseInt(pattern.startDate.split('-')[1]);
    const endMonth = parseInt(pattern.endDate.split('-')[0]);
    const endDay = parseInt(pattern.endDate.split('-')[1]);
    
    // Handle year-crossing patterns (e.g., winter)
    if (startMonth > endMonth) {
      return (currentMonth >= startMonth || currentMonth <= endMonth) ||
             (currentMonth === startMonth && currentDay >= startDay) ||
             (currentMonth === endMonth && currentDay <= endDay);
    }
    
    return (currentMonth > startMonth || (currentMonth === startMonth && currentDay >= startDay)) &&
           (currentMonth < endMonth || (currentMonth === endMonth && currentDay <= endDay));
  });
}

// Function to get demand multiplier for a category
export function getDemandMultiplier(category: string, date: Date = new Date()): number {
  const activePatterns = getCurrentSeasonalPatterns(date);
  const currentSeason = getCurrentSeason(date);
  const weatherPattern = MOROCCO_WEATHER_PATTERNS.find(p => p.season === currentSeason);
  
  let maxMultiplier = 1.0;
  
  // Check seasonal patterns
  activePatterns.forEach(pattern => {
    if (pattern.affectedCategories.includes(category)) {
      maxMultiplier = Math.max(maxMultiplier, pattern.demandMultiplier);
    }
  });
  
  // Check weather patterns
  if (weatherPattern) {
    const weatherCategory = weatherPattern.demandCategories.find(c => c.category === category);
    if (weatherCategory) {
      maxMultiplier = Math.max(maxMultiplier, weatherCategory.multiplier);
    }
  }
  
  return maxMultiplier;
}

// Helper function to get current season
function getCurrentSeason(date: Date): 'winter' | 'spring' | 'summer' | 'autumn' {
  const month = date.getMonth() + 1;
  
  if (month >= 12 || month <= 2) return 'winter';
  if (month >= 3 && month <= 5) return 'spring';
  if (month >= 6 && month <= 8) return 'summer';
  return 'autumn';
}

// Function to get upcoming patterns for preparation
export function getUpcomingPatterns(daysAhead: number = 30): SeasonalPattern[] {
  const today = new Date();
  const futureDate = new Date(today.getTime() + daysAhead * 24 * 60 * 60 * 1000);
  
  return MOROCCO_SEASONAL_PATTERNS.filter(pattern => {
    const preparationDate = new Date();
    // Calculate when to start preparing (pattern start date - preparation days)
    // Simplified calculation for demo
    return pattern.preparationDays <= daysAhead;
  });
}
