// Role and Permission Types
// Standardized role system:
// - super_admin: System-wide administrator (replaces 'owner')
// - pharmacist: Licensed pharmacist who owns/manages pharmacy (replaces 'admin')
// - staff: Pharmacy staff with limited permissions
export type UserRole = 'super_admin' | 'pharmacist' | 'staff' | 'owner' | 'supplier' | 'admin';

export type Action = 'create' | 'read' | 'update' | 'delete' | 'manage';
export type Resource = 'listings' | 'products' | 'team' | 'pharmacy' | 'transactions' | 'reports' | 'settings';

export interface Permission {
  action: Action;
  resource: Resource;
}

// Role Permissions Configuration
export const RolePermissions: Record<UserRole, Permission[]> = {
  super_admin: [
    // Full access for system-wide administrators
    { action: 'manage', resource: 'listings' },
    { action: 'manage', resource: 'products' },
    { action: 'manage', resource: 'team' },
    { action: 'manage', resource: 'pharmacy' },
    { action: 'manage', resource: 'transactions' },
    { action: 'manage', resource: 'reports' },
    { action: 'manage', resource: 'settings' },
  ],
  owner: [
    // Pharmacy owner: manage their own pharmacy, products, team, transactions, and settings
    { action: 'manage', resource: 'products' },
    { action: 'manage', resource: 'team' },
    { action: 'manage', resource: 'pharmacy' },
    { action: 'manage', resource: 'transactions' },
    { action: 'manage', resource: 'settings' },
    { action: 'read', resource: 'listings' },
    // No access to system-wide reports or admin features
  ],
  admin: [
    // Administrative access (can be used for specific admin-level roles within a pharmacy if 'pharmacist' is the owner)
    { action: 'create', resource: 'listings' },
    { action: 'read', resource: 'listings' },
    { action: 'update', resource: 'listings' },
    { action: 'delete', resource: 'listings' },
    { action: 'create', resource: 'products' },
    { action: 'read', resource: 'products' },
    { action: 'update', resource: 'products' },
    { action: 'delete', resource: 'products' },
    { action: 'create', resource: 'team' },
    { action: 'read', resource: 'team' },
    { action: 'update', resource: 'team' },
    { action: 'read', resource: 'pharmacy' },
    { action: 'update', resource: 'pharmacy' },
    { action: 'create', resource: 'transactions' },
    { action: 'read', resource: 'transactions' },
    { action: 'update', resource: 'transactions' },
    { action: 'read', resource: 'reports' },
    { action: 'update', resource: 'settings' },
  ],
  pharmacist: [
    // Pharmacy management
    { action: 'create', resource: 'listings' },
    { action: 'read', resource: 'listings' },
    { action: 'update', resource: 'listings' },
    { action: 'create', resource: 'products' },
    { action: 'read', resource: 'products' },
    { action: 'update', resource: 'products' },
    { action: 'read', resource: 'team' },
    { action: 'read', resource: 'pharmacy' },
    { action: 'create', resource: 'transactions' },
    { action: 'read', resource: 'transactions' },
    { action: 'read', resource: 'reports' },
  ],
  supplier: [
    // Supplier/Distributor permissions for market intelligence and pharmacy relationships
    { action: 'read', resource: 'listings' },
    { action: 'read', resource: 'products' },
    { action: 'read', resource: 'pharmacy' },
    { action: 'read', resource: 'transactions' },
    { action: 'read', resource: 'reports' },
  ],
  staff: [
    // Basic access
    { action: 'read', resource: 'listings' },
    { action: 'read', resource: 'products' },
    { action: 'create', resource: 'products' },
    { action: 'update', resource: 'products' },
    { action: 'read', resource: 'team' },
    { action: 'read', resource: 'pharmacy' },
    { action: 'read', resource: 'transactions' },
  ],
};

// User Interface
export interface User {
  id: string;
  email: string;
  role: UserRole;
  pharmacyId?: string;
  status: 'active' | 'pending' | 'blocked';
  createdAt: Date;
  updatedAt: Date;
}

// Permission Check Functions
export function hasPermission(user: User, action: Action, resource: Resource): boolean {
  if (!user || !user.role) return false;

  // Debug logging for permission checks
  if (typeof window !== 'undefined' && window.location.hostname === 'localhost') {
    console.log('Permission check:', { user: user.role, action, resource });
  }

  const permissions = RolePermissions[user.role as UserRole];
  if (!permissions) {
    console.warn('No permissions found for role:', user.role);
    return false;
  }

  return permissions.some(p =>
    (p.action === action && p.resource === resource) ||
    (p.action === 'manage' && p.resource === resource)
  );
}

// Helper Types
export type PermissionCheck = (action: Action, resource: Resource) => boolean;

export interface UsePermissionsReturn {
  can: PermissionCheck;
  isOwner: () => boolean;
  isAdmin: () => boolean;
  isPharmacist: () => boolean;
  isStaff: () => boolean;
}

// Hook Return Type
export function usePermissions(user: User | null): UsePermissionsReturn {
  return {
    can: (action: Action, resource: Resource) => {
      if (!user) return false;
      try {
        return hasPermission(user, action, resource);
      } catch (error) {
        console.error('Permission check error:', error, { user, action, resource });
        return false;
      }
    },
    isOwner: () => user?.role === 'owner',
    isAdmin: () => user?.role === 'super_admin',
    isPharmacist: () => user?.role === 'pharmacist',
    isStaff: () => user?.role === 'staff',
  };
}