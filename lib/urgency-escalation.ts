import { getSupabaseAdmin } from "@/lib/supabase/admin";
import { logger } from "@/lib/logger";

interface EscalationRule {
  urgencyLevel: 'low' | 'normal' | 'high' | 'critical';
  initialRadius: number; // km
  escalationSteps: {
    afterMinutes: number;
    newRadius: number;
    notificationFrequency: number; // minutes between notifications
    actions: ('expand_radius' | 'increase_frequency' | 'notify_admin' | 'auto_expire')[];
  }[];
  maxRadius: number; // km
  autoExpireAfter: number; // hours
}

// Escalation rules based on urgency level
const ESCALATION_RULES: Record<string, EscalationRule> = {
  critical: {
    urgencyLevel: 'critical',
    initialRadius: 10,
    escalationSteps: [
      {
        afterMinutes: 15,
        newRadius: 25,
        notificationFrequency: 5,
        actions: ['expand_radius', 'increase_frequency']
      },
      {
        afterMinutes: 30,
        newRadius: 50,
        notificationFrequency: 3,
        actions: ['expand_radius', 'increase_frequency', 'notify_admin']
      },
      {
        afterMinutes: 60,
        newRadius: 100,
        notificationFrequency: 2,
        actions: ['expand_radius', 'increase_frequency']
      }
    ],
    maxRadius: 100,
    autoExpireAfter: 4 // 4 hours for critical
  },
  high: {
    urgencyLevel: 'high',
    initialRadius: 15,
    escalationSteps: [
      {
        afterMinutes: 30,
        newRadius: 35,
        notificationFrequency: 10,
        actions: ['expand_radius', 'increase_frequency']
      },
      {
        afterMinutes: 120,
        newRadius: 75,
        notificationFrequency: 5,
        actions: ['expand_radius', 'increase_frequency']
      }
    ],
    maxRadius: 75,
    autoExpireAfter: 12 // 12 hours for high
  },
  normal: {
    urgencyLevel: 'normal',
    initialRadius: 20,
    escalationSteps: [
      {
        afterMinutes: 120,
        newRadius: 40,
        notificationFrequency: 30,
        actions: ['expand_radius']
      },
      {
        afterMinutes: 480,
        newRadius: 60,
        notificationFrequency: 60,
        actions: ['expand_radius']
      }
    ],
    maxRadius: 60,
    autoExpireAfter: 48 // 48 hours for normal
  },
  low: {
    urgencyLevel: 'low',
    initialRadius: 25,
    escalationSteps: [
      {
        afterMinutes: 480,
        newRadius: 50,
        notificationFrequency: 120,
        actions: ['expand_radius']
      }
    ],
    maxRadius: 50,
    autoExpireAfter: 72 // 72 hours for low
  }
};

export interface EscalationStatus {
  currentRadius: number;
  currentStep: number;
  nextEscalationAt: Date | null;
  notificationsSent: number;
  lastNotificationAt: Date | null;
  escalationHistory: {
    timestamp: Date;
    action: string;
    oldRadius: number;
    newRadius: number;
    reason: string;
  }[];
}

export class UrgencyEscalationService {
  private supabase = getSupabaseAdmin();

  /**
   * Initialize escalation tracking for a new urgent request
   */
  async initializeEscalation(urgentRequestId: string, urgencyLevel: string): Promise<void> {
    try {
      const rule = ESCALATION_RULES[urgencyLevel];
      if (!rule) {
        logger.warn(`No escalation rule found for urgency level: ${urgencyLevel}`);
        return;
      }

      const now = new Date();
      const nextEscalation = rule.escalationSteps.length > 0 
        ? new Date(now.getTime() + rule.escalationSteps[0].afterMinutes * 60000)
        : null;

      const escalationStatus: EscalationStatus = {
        currentRadius: rule.initialRadius,
        currentStep: 0,
        nextEscalationAt: nextEscalation,
        notificationsSent: 0,
        lastNotificationAt: null,
        escalationHistory: [{
          timestamp: now,
          action: 'initialized',
          oldRadius: 0,
          newRadius: rule.initialRadius,
          reason: `Initial radius set for ${urgencyLevel} urgency`
        }]
      };

      // Store escalation status in database
      await this.supabase
        .from('urgent_request_escalations')
        .insert([{
          urgent_request_id: urgentRequestId,
          urgency_level: urgencyLevel,
          current_radius: escalationStatus.currentRadius,
          current_step: escalationStatus.currentStep,
          next_escalation_at: nextEscalation?.toISOString(),
          notifications_sent: 0,
          escalation_data: escalationStatus,
          created_at: now.toISOString(),
          updated_at: now.toISOString()
        }]);

      logger.info(`Escalation initialized for urgent request ${urgentRequestId}`, {
        urgencyLevel,
        initialRadius: rule.initialRadius,
        nextEscalation: nextEscalation?.toISOString()
      });

    } catch (error) {
      logger.error(`Failed to initialize escalation for request ${urgentRequestId}`, { error });
    }
  }

  /**
   * Process escalation for requests that need it
   */
  async processEscalations(): Promise<void> {
    try {
      const now = new Date();

      // Get requests that need escalation
      const { data: requestsToEscalate, error } = await this.supabase
        .from('urgent_request_escalations')
        .select(`
          *,
          urgent_request:urgent_requests!urgent_request_escalations_urgent_request_id_fkey (
            id,
            status,
            product_name,
            urgency_level,
            pharmacy_id
          )
        `)
        .lte('next_escalation_at', now.toISOString())
        .eq('urgent_request.status', 'active');

      if (error) {
        throw error;
      }

      if (!requestsToEscalate || requestsToEscalate.length === 0) {
        return;
      }

      logger.info(`Processing ${requestsToEscalate.length} escalations`);

      for (const escalationRecord of requestsToEscalate) {
        await this.escalateRequest(escalationRecord);
      }

    } catch (error) {
      logger.error('Failed to process escalations', { error });
    }
  }

  /**
   * Escalate a specific request
   */
  private async escalateRequest(escalationRecord: any): Promise<void> {
    try {
      const urgentRequest = escalationRecord.urgent_request;
      const rule = ESCALATION_RULES[urgentRequest.urgency_level];
      
      if (!rule) {
        logger.warn(`No escalation rule for urgency: ${urgentRequest.urgency_level}`);
        return;
      }

      const currentStep = escalationRecord.current_step;
      const nextStepIndex = currentStep + 1;

      // Check if we have more escalation steps
      if (nextStepIndex >= rule.escalationSteps.length) {
        // No more escalation steps, check for auto-expiry
        const createdAt = new Date(urgentRequest.created_at);
        const hoursElapsed = (Date.now() - createdAt.getTime()) / (1000 * 60 * 60);
        
        if (hoursElapsed >= rule.autoExpireAfter) {
          await this.expireRequest(urgentRequest.id, 'Auto-expired after maximum escalation time');
        }
        return;
      }

      const nextStep = rule.escalationSteps[nextStepIndex];
      const now = new Date();

      // Update escalation status
      const escalationStatus: EscalationStatus = escalationRecord.escalation_data || {
        currentRadius: escalationRecord.current_radius,
        currentStep: escalationRecord.current_step,
        nextEscalationAt: null,
        notificationsSent: escalationRecord.notifications_sent,
        lastNotificationAt: null,
        escalationHistory: []
      };

      // Apply escalation actions
      let newRadius = escalationStatus.currentRadius;
      
      if (nextStep.actions.includes('expand_radius')) {
        newRadius = Math.min(nextStep.newRadius, rule.maxRadius);
        
        escalationStatus.escalationHistory.push({
          timestamp: now,
          action: 'expand_radius',
          oldRadius: escalationStatus.currentRadius,
          newRadius: newRadius,
          reason: `Step ${nextStepIndex + 1} escalation after ${nextStep.afterMinutes} minutes`
        });
      }

      if (nextStep.actions.includes('notify_admin')) {
        await this.notifyAdmin(urgentRequest);
        
        escalationStatus.escalationHistory.push({
          timestamp: now,
          action: 'notify_admin',
          oldRadius: newRadius,
          newRadius: newRadius,
          reason: 'Admin notification triggered'
        });
      }

      // Calculate next escalation time
      const nextEscalationAt = nextStepIndex + 1 < rule.escalationSteps.length
        ? new Date(now.getTime() + rule.escalationSteps[nextStepIndex + 1].afterMinutes * 60000)
        : null;

      // Update escalation status
      escalationStatus.currentRadius = newRadius;
      escalationStatus.currentStep = nextStepIndex;
      escalationStatus.nextEscalationAt = nextEscalationAt;

      // Update database
      await this.supabase
        .from('urgent_request_escalations')
        .update({
          current_radius: newRadius,
          current_step: nextStepIndex,
          next_escalation_at: nextEscalationAt?.toISOString(),
          escalation_data: escalationStatus,
          updated_at: now.toISOString()
        })
        .eq('urgent_request_id', urgentRequest.id);

      logger.info(`Escalated request ${urgentRequest.id}`, {
        step: nextStepIndex + 1,
        newRadius,
        nextEscalation: nextEscalationAt?.toISOString(),
        actions: nextStep.actions
      });

      // Send enhanced notifications to nearby pharmacies
      if (nextStep.actions.includes('expand_radius') || nextStep.actions.includes('increase_frequency')) {
        await this.sendEscalatedNotifications(urgentRequest, newRadius, nextStep.notificationFrequency);
      }

    } catch (error) {
      logger.error(`Failed to escalate request ${escalationRecord.urgent_request_id}`, { error });
    }
  }

  /**
   * Send escalated notifications to pharmacies in expanded radius
   */
  private async sendEscalatedNotifications(urgentRequest: any, radius: number, frequency: number): Promise<void> {
    try {
      // This would integrate with the push notification service
      // For now, we'll log the escalation
      logger.info(`Sending escalated notifications for request ${urgentRequest.id}`, {
        radius,
        frequency,
        urgency: urgentRequest.urgency_level,
        product: urgentRequest.product_name
      });

      // TODO: Implement actual notification sending
      // This would call the push notification service to send notifications
      // to all pharmacies within the new radius

    } catch (error) {
      logger.error(`Failed to send escalated notifications for request ${urgentRequest.id}`, { error });
    }
  }

  /**
   * Notify admin about critical escalation
   */
  private async notifyAdmin(urgentRequest: any): Promise<void> {
    try {
      logger.warn(`Admin notification: Critical urgent request needs attention`, {
        requestId: urgentRequest.id,
        product: urgentRequest.product_name,
        urgency: urgentRequest.urgency_level,
        pharmacyId: urgentRequest.pharmacy_id
      });

      // TODO: Implement admin notification system
      // This could send email, SMS, or push notification to admin users

    } catch (error) {
      logger.error(`Failed to notify admin about request ${urgentRequest.id}`, { error });
    }
  }

  /**
   * Expire a request automatically
   */
  private async expireRequest(requestId: string, reason: string): Promise<void> {
    try {
      await this.supabase
        .from('urgent_requests')
        .update({
          status: 'expired',
          updated_at: new Date().toISOString()
        })
        .eq('id', requestId);

      logger.info(`Request ${requestId} expired: ${reason}`);

    } catch (error) {
      logger.error(`Failed to expire request ${requestId}`, { error });
    }
  }

  /**
   * Get escalation status for a request
   */
  async getEscalationStatus(urgentRequestId: string): Promise<EscalationStatus | null> {
    try {
      const { data, error } = await this.supabase
        .from('urgent_request_escalations')
        .select('escalation_data')
        .eq('urgent_request_id', urgentRequestId)
        .single();

      if (error || !data) {
        return null;
      }

      return data.escalation_data as EscalationStatus;

    } catch (error) {
      logger.error(`Failed to get escalation status for request ${urgentRequestId}`, { error });
      return null;
    }
  }
}

// Export singleton instance
export const urgencyEscalationService = new UrgencyEscalationService();
