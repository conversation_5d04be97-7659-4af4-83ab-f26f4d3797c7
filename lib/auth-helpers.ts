// Centralized authentication helpers for API routes
import { NextRequest } from "next/server";
import { UserRole } from "@/lib/types/auth";

export interface AuthenticatedUser {
  id: string;
  email: string;
  role: UserRole;
  pharmacyId: string | null;
}

export interface AuthResult {
  user: AuthenticatedUser | null;
  error: string | null;
}

/**
 * Get authenticated user for API routes
 * Uses Supabase session validation for production
 */
export async function getAuthenticatedUser(request: NextRequest): Promise<AuthResult> {
  try {
    // For local development, use mock user data
    if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
      const userData: AuthenticatedUser = {
        id: '550e8400-e29b-41d4-a716-446655440001',
        email: '<EMAIL>',
        role: 'owner',
        pharmacyId: '550e8400-e29b-41d4-a716-446655440000',
      };

      return { user: userData, error: null };
    }

    // Production: Use Supabase session validation
    const { createRouteHandlerClient } = await import('@supabase/auth-helpers-nextjs');
    const { cookies } = await import('next/headers');

    const cookieStore = await cookies();
    const supabase = createRouteHandlerClient({ cookies: () => cookieStore });

    // Get user from Supabase session
    const { data: { user }, error: userError } = await supabase.auth.getUser();

    if (userError || !user) {
      return { user: null, error: "Unauthorized" };
    }

    // Get the actual user role from user_metadata, fallback to user.role
    const actualRole = user.user_metadata?.role || user.role;

    // Get pharmacy association
    const { data: teamMember } = await supabase
      .from("pharmacy_team_members")
      .select("pharmacy_id")
      .eq("user_id", user.id)
      .single();



    const userData: AuthenticatedUser = {
      id: user.id,
      email: user.email || '',
      role: actualRole as UserRole,
      pharmacyId: teamMember?.pharmacy_id || null,
    };

    return { user: userData, error: null };

  } catch (error) {
    console.error('Authentication error:', error);
    return { user: null, error: "Authentication failed" };
  }
}

/**
 * Check if user has required role
 */
export function hasRequiredRole(user: AuthenticatedUser, allowedRoles: UserRole[]): boolean {
  return allowedRoles.includes(user.role);
}

/**
 * Common roles for different operations
 */
export const ROLES = {
  ADMIN: ['super_admin'] as UserRole[],
  PHARMACY_ADMIN: ['owner', 'pharmacist', 'super_admin'] as UserRole[],
  PHARMACY_STAFF: ['owner', 'pharmacist', 'staff', 'super_admin'] as UserRole[],
  ALL_AUTHENTICATED: ['owner', 'pharmacist', 'staff', 'super_admin', 'supplier'] as UserRole[],
} as const;

/**
 * Middleware for API routes that require authentication
 */
export async function requireAuth(
  request: NextRequest, 
  allowedRoles: UserRole[] = ROLES.ALL_AUTHENTICATED
): Promise<AuthResult> {
  const { user, error } = await getAuthenticatedUser(request);
  
  if (error || !user) {
    return { user: null, error: error || "Unauthorized" };
  }

  if (!hasRequiredRole(user, allowedRoles)) {
    return { user: null, error: "Insufficient permissions" };
  }

  return { user, error: null };
}
