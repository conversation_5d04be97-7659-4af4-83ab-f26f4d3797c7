{"name": "nextjs", "version": "0.1.0", "private": true, "type": "module", "scripts": {"dev": "NODE_ENV=test next dev -p 3006", "dev:local": "cp .env.local.docker .env.local && NODE_ENV=development next dev -p 3006", "build": "next build", "build:local": "cp .env.local.docker .env.local && NODE_ENV=development next build", "start": "next start", "lint": "next lint", "test:local": "bash scripts/test-local-optimizations.sh", "test:local:simple": "bash scripts/test-local-simple.sh", "db:local:setup": "psql postgresql://postgres:postgres@127.0.0.1:54352/postgres -f scripts/apply-local-optimizations.sql", "db:local:reset": "supabase db reset --local", "db:local:migrate": "supabase db push --local", "check-users": "node --loader ts-node/esm scripts/check-users.ts", "create-test-users": "node --loader ts-node/esm scripts/create-test-users.ts", "ws-server": "node --loader ts-node/esm server/start-ws-server.ts", "test": "playwright test", "test:ui": "playwright test --ui", "test:debug": "playwright test --debug", "test:headed": "playwright test --headed", "test:auth": "playwright test tests/auth/", "test:admin": "playwright test tests/admin/", "test:safe": "node --loader ts-node/esm scripts/run-tests.ts", "test:admin:safe": "node --loader ts-node/esm scripts/run-tests.ts tests/admin/", "test:auth:safe": "node --loader ts-node/esm scripts/run-tests.ts tests/auth/", "test:prescription": "node tests/prescription-reader.test.js", "test:api": "node tests/prescription-reader.test.js"}, "dependencies": {"@ericblade/quagga2": "^1.8.4", "@faker-js/faker": "^9.8.0", "@google/generative-ai": "^0.24.1", "@hookform/resolvers": "^3.9.0", "@mantine/carousel": "^8.0.1", "@mantine/charts": "^8.0.1", "@mantine/core": "^8.0.1", "@mantine/dates": "^8.0.1", "@mantine/dropzone": "^8.0.1", "@mantine/form": "^8.0.1", "@mantine/hooks": "^8.0.1", "@mantine/modals": "^8.0.1", "@mantine/notifications": "^8.0.1", "@mantine/spotlight": "^8.0.1", "@next/swc-wasm-nodejs": "13.5.1", "@prisma/client": "^6.9.0", "@radix-ui/react-accordion": "^1.2.0", "@radix-ui/react-alert-dialog": "^1.1.1", "@radix-ui/react-aspect-ratio": "^1.1.0", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.1.1", "@radix-ui/react-collapsible": "^1.1.0", "@radix-ui/react-context-menu": "^2.2.1", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.1", "@radix-ui/react-hover-card": "^1.1.1", "@radix-ui/react-label": "^2.1.0", "@radix-ui/react-menubar": "^1.1.1", "@radix-ui/react-navigation-menu": "^1.2.0", "@radix-ui/react-popover": "^1.1.1", "@radix-ui/react-progress": "^1.1.0", "@radix-ui/react-radio-group": "^1.2.0", "@radix-ui/react-scroll-area": "^1.1.0", "@radix-ui/react-select": "^2.1.1", "@radix-ui/react-separator": "^1.1.0", "@radix-ui/react-slider": "^1.2.0", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.0", "@radix-ui/react-tabs": "^1.1.0", "@radix-ui/react-toast": "^1.2.4", "@radix-ui/react-toggle": "^1.1.0", "@radix-ui/react-toggle-group": "^1.1.0", "@radix-ui/react-tooltip": "^1.1.2", "@siamf/react-export": "^4.1.1", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/supabase-js": "^2.49.8", "@tabler/icons-react": "^3.33.0", "@types/bcrypt": "^5.0.2", "@types/bcryptjs": "^2.4.6", "@types/react": "18.2.22", "@types/react-dom": "18.2.7", "@types/socket.io-client": "^1.4.36", "@types/ws": "^8.5.13", "autoprefixer": "10.4.15", "bcrypt": "^5.1.1", "bcryptjs": "^2.4.3", "class-variance-authority": "^0.7.0", "clsx": "^2.1.0", "critters": "^0.0.25", "date-fns": "^3.6.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.0.0", "jsonwebtoken": "^9.0.2", "lucide-react": "^0.344.0", "next": "^15.1.6", "next-pwa": "^5.6.0", "next-themes": "^0.2.1", "node-fetch": "^2.7.0", "openai": "^5.8.2", "pino": "^9.6.0", "pino-pretty": "^13.0.0", "postcss": "8.4.29", "react": "18.2.0", "react-day-picker": "^9.5.1", "react-dom": "18.2.0", "react-hook-form": "^7.50.1", "recharts": "^2.15.1", "socket.io-client": "^4.7.4", "sonner": "^1.4.3", "tailwind-merge": "^2.2.1", "tailwindcss": "3.3.3", "tailwindcss-animate": "^1.0.7", "ws": "^8.16.0", "zod": "^3.22.4"}, "devDependencies": {"@playwright/test": "^1.50.1", "@types/jsonwebtoken": "^9.0.8", "@types/node": "^20.17.18", "@types/node-fetch": "^2.6.12", "@types/pg": "^8.11.11", "eslint": "8.49.0", "eslint-config-next": "13.5.1", "pg": "^8.11.3", "pg-native": "^3.2.0", "prisma": "^6.9.0", "ts-node": "^10.9.2", "typescript": "^5.7.3"}}