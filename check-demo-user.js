#!/usr/bin/env node

import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.production' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  console.log('NEXT_PUBLIC_SUPABASE_URL:', !!supabaseUrl);
  console.log('SUPABASE_SERVICE_ROLE_KEY:', !!supabaseServiceKey);
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function checkDemoUser() {
  try {
    console.log('🔍 Checking demo user in production database...\n');
    
    // Check if demo user exists
    const { data: users, error: usersError } = await supabase.auth.admin.listUsers();
    
    if (usersError) {
      console.error('❌ Error fetching users:', usersError.message);
      return;
    }
    
    console.log(`📊 Total users in database: ${users.users.length}`);
    
    // Look for demo users
    const demoOwner = users.users.find(u => u.email === '<EMAIL>');
    const demoStaff = users.users.find(u => u.email === '<EMAIL>');
    
    console.log('\n👤 Demo User Status:');
    console.log(`   Owner (<EMAIL>): ${demoOwner ? '✅ EXISTS' : '❌ NOT FOUND'}`);
    console.log(`   Staff (<EMAIL>): ${demoStaff ? '✅ EXISTS' : '❌ NOT FOUND'}`);
    
    if (demoOwner) {
      console.log('\n📋 Demo Owner Details:');
      console.log(`   ID: ${demoOwner.id}`);
      console.log(`   Email: ${demoOwner.email}`);
      console.log(`   Created: ${demoOwner.created_at}`);
      console.log(`   Email Confirmed: ${demoOwner.email_confirmed_at ? 'YES' : 'NO'}`);
      console.log(`   Role: ${demoOwner.user_metadata?.role || 'Not set'}`);
      
      // Test login
      console.log('\n🧪 Testing demo login...');
      const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
        email: '<EMAIL>',
        password: 'demo123!'
      });
      
      if (loginError) {
        console.log('❌ Login failed:', loginError.message);
        console.log('   Error code:', loginError.code);
        console.log('   Error status:', loginError.status);
      } else {
        console.log('✅ Login successful!');
        console.log('   User ID:', loginData.user.id);
        console.log('   Session valid:', !!loginData.session);
      }
    }
    
    // Check pharmacy association
    if (demoOwner) {
      console.log('\n🏥 Checking pharmacy association...');
      const { data: pharmacy, error: pharmacyError } = await supabase
        .from('pharmacies')
        .select('*')
        .eq('email', '<EMAIL>')
        .single();
        
      if (pharmacyError) {
        console.log('❌ No pharmacy found for demo user');
      } else {
        console.log('✅ Pharmacy found:', pharmacy.name);
        
        // Check team membership
        const { data: teamMember, error: teamError } = await supabase
          .from('pharmacy_team_members')
          .select('*')
          .eq('user_id', demoOwner.id)
          .eq('pharmacy_id', pharmacy.id)
          .single();
          
        if (teamError) {
          console.log('❌ No team membership found');
        } else {
          console.log('✅ Team membership found:', teamMember.role);
        }
      }
    }
    
    // If demo user doesn't exist, offer to create it
    if (!demoOwner) {
      console.log('\n🔧 Demo user not found. Creating demo user...');
      await createDemoUser();
    }
    
  } catch (error) {
    console.error('💥 Error:', error.message);
  }
}

async function createDemoUser() {
  try {
    console.log('\n👤 Creating demo owner user...');
    
    // Create demo owner user
    const { data: authUser, error: authError } = await supabase.auth.admin.createUser({
      email: '<EMAIL>',
      password: 'demo123!',
      email_confirm: true,
      user_metadata: { 
        role: 'owner',
        full_name: 'Demo Owner'
      }
    });
    
    if (authError) {
      console.error('❌ Failed to create demo user:', authError.message);
      return;
    }
    
    console.log('✅ Demo user created:', authUser.user.email);
    
    // Create pharmacy
    console.log('\n🏥 Creating demo pharmacy...');
    const { data: pharmacy, error: pharmacyError } = await supabase
      .from('pharmacies')
      .upsert([{
        name: 'Pharmacie Camélia - Démo',
        email: '<EMAIL>',
        phone: '+212 522 123 456',
        address: '123 Boulevard Hassan II',
        city: 'Casablanca',
        license_number: 'DEMO-2024-001',
        is_verified: true
      }], { onConflict: 'email' })
      .select()
      .single();
      
    if (pharmacyError) {
      console.error('❌ Failed to create pharmacy:', pharmacyError.message);
      return;
    }
    
    console.log('✅ Demo pharmacy created:', pharmacy.name);
    
    // Create team membership
    console.log('\n👥 Creating team membership...');
    const { error: teamError } = await supabase
      .from('pharmacy_team_members')
      .upsert([{
        pharmacy_id: pharmacy.id,
        user_id: authUser.user.id,
        role: 'owner',
        status: 'active'
      }], { onConflict: 'pharmacy_id,user_id' });
      
    if (teamError) {
      console.error('❌ Failed to create team membership:', teamError.message);
      return;
    }
    
    console.log('✅ Team membership created');
    
    // Test the new user
    console.log('\n🧪 Testing new demo user login...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123!'
    });
    
    if (loginError) {
      console.log('❌ Login test failed:', loginError.message);
    } else {
      console.log('✅ Login test successful!');
    }
    
    console.log('\n🎉 Demo user setup complete!');
    console.log('\n📋 Demo Credentials:');
    console.log('   Email: <EMAIL>');
    console.log('   Password: demo123!');
    console.log('   Pharmacy: Pharmacie Camélia - Démo');
    
  } catch (error) {
    console.error('💥 Error creating demo user:', error.message);
  }
}

// Run the check
checkDemoUser();
