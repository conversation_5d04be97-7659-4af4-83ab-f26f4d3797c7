"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Clock,
  Package,
  Zap,
  Sun,
  Cloud,
  Snowflake,
  Leaf,
  Moon,
  Star,
} from "lucide-react";
import { format, addDays, startOfMonth, endOfMonth, eachDayOfInterval, isSameMonth, isToday } from "date-fns";
import { fr } from "date-fns/locale";
import { demandForecastingEngine, type CategoryDemand, type SeasonalInsight } from "@/lib/seasonal-demand/forecasting-engine";
import { MOROCCO_SEASONAL_PATTERNS } from "@/lib/seasonal-demand/morocco-patterns";

interface CalendarDay {
  date: Date;
  isCurrentMonth: boolean;
  isToday: boolean;
  events: CalendarEvent[];
  demandLevel: 'low' | 'medium' | 'high' | 'critical';
}

interface CalendarEvent {
  id: string;
  title: string;
  type: 'seasonal' | 'religious' | 'weather' | 'holiday';
  impact: 'low' | 'medium' | 'high' | 'critical';
  categories: string[];
}

const seasonIcons = {
  winter: Snowflake,
  spring: Leaf,
  summer: Sun,
  autumn: Cloud
};

const eventTypeColors = {
  seasonal: 'bg-blue-100 text-blue-800 border-blue-200',
  religious: 'bg-purple-100 text-purple-800 border-purple-200',
  weather: 'bg-green-100 text-green-800 border-green-200',
  holiday: 'bg-orange-100 text-orange-800 border-orange-200'
};

const demandLevelColors = {
  low: 'bg-gray-50',
  medium: 'bg-yellow-50',
  high: 'bg-orange-50',
  critical: 'bg-red-50'
};

export function DemandCalendar() {
  const [currentDate, setCurrentDate] = useState(new Date());
  const [selectedCategory, setSelectedCategory] = useState<string>('all');
  const [calendarDays, setCalendarDays] = useState<CalendarDay[]>([]);
  const [categoryOverview, setCategoryOverview] = useState<CategoryDemand[]>([]);
  const [seasonalInsights, setSeasonalInsights] = useState<SeasonalInsight[]>([]);
  const [selectedDay, setSelectedDay] = useState<CalendarDay | null>(null);

  // Generate calendar data
  useEffect(() => {
    generateCalendarData();
    generateOverviewData();
  }, [currentDate, selectedCategory]);

  const generateCalendarData = () => {
    const monthStart = startOfMonth(currentDate);
    const monthEnd = endOfMonth(currentDate);
    const calendarStart = addDays(monthStart, -monthStart.getDay());
    const calendarEnd = addDays(monthEnd, 6 - monthEnd.getDay());
    
    const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd });
    
    const calendarData: CalendarDay[] = days.map(day => {
      const events = generateEventsForDay(day);
      const demandLevel = calculateDemandLevel(day, events);
      
      return {
        date: day,
        isCurrentMonth: isSameMonth(day, currentDate),
        isToday: isToday(day),
        events,
        demandLevel
      };
    });
    
    setCalendarDays(calendarData);
  };

  const generateOverviewData = () => {
    const overview = demandForecastingEngine.generateCategoryOverview(currentDate);
    setCategoryOverview(overview);
    
    const insights = demandForecastingEngine.generateSeasonalInsights(60);
    setSeasonalInsights(insights);
  };

  const generateEventsForDay = (date: Date): CalendarEvent[] => {
    const events: CalendarEvent[] = [];
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const dateStr = `${month.toString().padStart(2, '0')}-${day.toString().padStart(2, '0')}`;
    
    // Check seasonal patterns
    MOROCCO_SEASONAL_PATTERNS.forEach(pattern => {
      if (!pattern.isVariable && pattern.startDate === dateStr) {
        events.push({
          id: `${pattern.id}-start`,
          title: `Début: ${pattern.name}`,
          type: pattern.category as any,
          impact: pattern.demandMultiplier > 1.8 ? 'critical' :
                  pattern.demandMultiplier > 1.5 ? 'high' :
                  pattern.demandMultiplier > 1.2 ? 'medium' : 'low',
          categories: pattern.affectedCategories
        });
      }
      
      if (!pattern.isVariable && pattern.endDate === dateStr) {
        events.push({
          id: `${pattern.id}-end`,
          title: `Fin: ${pattern.name}`,
          type: pattern.category as any,
          impact: 'low',
          categories: pattern.affectedCategories
        });
      }
    });

    // Add weather-based events
    const season = getCurrentSeason(date);
    if (day === 1) { // First day of month
      events.push({
        id: `weather-${month}`,
        title: `Saison ${season}`,
        type: 'weather',
        impact: 'medium',
        categories: []
      });
    }

    return events;
  };

  const calculateDemandLevel = (date: Date, events: CalendarEvent[]): 'low' | 'medium' | 'high' | 'critical' => {
    if (events.some(e => e.impact === 'critical')) return 'critical';
    if (events.some(e => e.impact === 'high')) return 'high';
    if (events.some(e => e.impact === 'medium')) return 'medium';
    return 'low';
  };

  const getCurrentSeason = (date: Date): string => {
    const month = date.getMonth() + 1;
    if (month >= 12 || month <= 2) return 'hiver';
    if (month >= 3 && month <= 5) return 'printemps';
    if (month >= 6 && month <= 8) return 'été';
    return 'automne';
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newDate = new Date(currentDate);
    newDate.setMonth(currentDate.getMonth() + (direction === 'next' ? 1 : -1));
    setCurrentDate(newDate);
  };

  const getEventIcon = (type: string) => {
    switch (type) {
      case 'religious': return Moon;
      case 'seasonal': return Star;
      case 'weather': return Sun;
      case 'holiday': return Calendar;
      default: return Calendar;
    }
  };

  const filteredOverview = selectedCategory === 'all' 
    ? categoryOverview 
    : categoryOverview.filter(cat => cat.category === selectedCategory);

  return (
    <div className="space-y-6">
      {/* Header Controls */}
      <div className="flex flex-wrap items-center justify-between gap-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => navigateMonth('prev')}>
              ←
            </Button>
            <h2 className="text-xl font-semibold min-w-[200px] text-center">
              {format(currentDate, 'MMMM yyyy', { locale: fr })}
            </h2>
            <Button variant="outline" onClick={() => navigateMonth('next')}>
              →
            </Button>
          </div>
          
          <Button
            variant="outline"
            onClick={() => setCurrentDate(new Date())}
          >
            Aujourd'hui
          </Button>
        </div>

        <div className="flex items-center gap-2">
          <span className="text-sm font-medium">Catégorie:</span>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-48">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes les catégories</SelectItem>
              <SelectItem value="cold_flu_medicine">Rhume & Grippe</SelectItem>
              <SelectItem value="digestive_health">Santé Digestive</SelectItem>
              <SelectItem value="diabetes_management">Diabète</SelectItem>
              <SelectItem value="vitamins_supplements">Vitamines</SelectItem>
              <SelectItem value="children_medicine">Pédiatrie</SelectItem>
              <SelectItem value="pain_relief">Antidouleur</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Calendar */}
        <div className="lg:col-span-2">
          <Card className="p-6">
            <div className="mb-4">
              <h3 className="text-lg font-semibold flex items-center gap-2">
                <Calendar className="w-5 h-5 text-blue-600" />
                Calendrier de Demande Saisonnière
              </h3>
            </div>

            {/* Calendar Grid */}
            <div className="grid grid-cols-7 gap-1 mb-2">
              {['Dim', 'Lun', 'Mar', 'Mer', 'Jeu', 'Ven', 'Sam'].map(day => (
                <div key={day} className="p-2 text-center text-sm font-medium text-muted-foreground">
                  {day}
                </div>
              ))}
            </div>

            <div className="grid grid-cols-7 gap-1">
              {calendarDays.map((day, index) => (
                <div
                  key={index}
                  className={`
                    min-h-[80px] p-1 border rounded cursor-pointer transition-colors
                    ${day.isCurrentMonth ? 'bg-white' : 'bg-gray-50'}
                    ${day.isToday ? 'ring-2 ring-blue-500' : ''}
                    ${demandLevelColors[day.demandLevel]}
                    hover:bg-blue-50
                  `}
                  onClick={() => setSelectedDay(day)}
                >
                  <div className={`
                    text-sm font-medium mb-1
                    ${day.isCurrentMonth ? 'text-gray-900' : 'text-gray-400'}
                    ${day.isToday ? 'text-blue-600' : ''}
                  `}>
                    {format(day.date, 'd')}
                  </div>
                  
                  <div className="space-y-1">
                    {day.events.slice(0, 2).map(event => {
                      const Icon = getEventIcon(event.type);
                      return (
                        <div
                          key={event.id}
                          className={`
                            text-xs p-1 rounded border
                            ${eventTypeColors[event.type]}
                          `}
                        >
                          <div className="flex items-center gap-1">
                            <Icon className="w-3 h-3" />
                            <span className="truncate">{event.title}</span>
                          </div>
                        </div>
                      );
                    })}
                    {day.events.length > 2 && (
                      <div className="text-xs text-muted-foreground">
                        +{day.events.length - 2} autres
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>

            {/* Legend */}
            <div className="mt-4 flex flex-wrap gap-4 text-xs">
              <div className="flex items-center gap-2">
                <span className="font-medium">Niveau de demande:</span>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-gray-50 border rounded"></div>
                  <span>Faible</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-yellow-50 border rounded"></div>
                  <span>Moyen</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-orange-50 border rounded"></div>
                  <span>Élevé</span>
                </div>
                <div className="flex items-center gap-1">
                  <div className="w-3 h-3 bg-red-50 border rounded"></div>
                  <span>Critique</span>
                </div>
              </div>
            </div>
          </Card>
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Seasonal Insights */}
          <Card className="p-4">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <AlertTriangle className="w-4 h-4 text-orange-600" />
              Alertes Saisonnières
            </h3>
            
            <div className="space-y-3">
              {seasonalInsights.slice(0, 3).map((insight, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded-lg">
                  <div className="flex items-start justify-between mb-2">
                    <div className="font-medium text-sm">{insight.title}</div>
                    <Badge 
                      variant={insight.priority === 'critical' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {insight.daysUntil === 0 ? 'Maintenant' : `${insight.daysUntil}j`}
                    </Badge>
                  </div>
                  <div className="text-xs text-muted-foreground mb-2">
                    {insight.description}
                  </div>
                  <div className="text-xs">
                    <span className="font-medium">Catégories:</span> {insight.affectedCategories.slice(0, 2).join(', ')}
                    {insight.affectedCategories.length > 2 && ` +${insight.affectedCategories.length - 2}`}
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Category Overview */}
          <Card className="p-4">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <TrendingUp className="w-4 h-4 text-green-600" />
              Aperçu des Catégories
            </h3>
            
            <div className="space-y-3">
              {filteredOverview.slice(0, 5).map((category, index) => (
                <div key={index} className="flex items-center justify-between">
                  <div className="flex-1">
                    <div className="text-sm font-medium truncate">
                      {category.categoryName}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Demande: {category.forecastedDemand}
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {category.trend === 'increasing' && (
                      <TrendingUp className="w-4 h-4 text-green-600" />
                    )}
                    {category.trend === 'decreasing' && (
                      <TrendingDown className="w-4 h-4 text-red-600" />
                    )}
                    <span className={`text-xs font-medium ${
                      category.trend === 'increasing' ? 'text-green-600' : 
                      category.trend === 'decreasing' ? 'text-red-600' : 
                      'text-gray-600'
                    }`}>
                      {category.trendPercentage > 0 ? '+' : ''}{category.trendPercentage}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </Card>

          {/* Selected Day Details */}
          {selectedDay && (
            <Card className="p-4">
              <h3 className="font-semibold mb-3 flex items-center gap-2">
                <Clock className="w-4 h-4 text-blue-600" />
                {format(selectedDay.date, 'dd MMMM yyyy', { locale: fr })}
              </h3>
              
              {selectedDay.events.length > 0 ? (
                <div className="space-y-2">
                  {selectedDay.events.map(event => {
                    const Icon = getEventIcon(event.type);
                    return (
                      <div key={event.id} className="p-2 bg-gray-50 rounded">
                        <div className="flex items-center gap-2 mb-1">
                          <Icon className="w-4 h-4" />
                          <span className="text-sm font-medium">{event.title}</span>
                          <Badge variant="outline" className="text-xs">
                            {event.impact}
                          </Badge>
                        </div>
                        {event.categories.length > 0 && (
                          <div className="text-xs text-muted-foreground">
                            Affecte: {event.categories.slice(0, 3).join(', ')}
                            {event.categories.length > 3 && ` +${event.categories.length - 3}`}
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-sm text-muted-foreground">
                  Aucun événement saisonnier prévu
                </div>
              )}
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
