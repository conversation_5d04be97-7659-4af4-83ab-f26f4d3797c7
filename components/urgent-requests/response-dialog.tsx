"use client";

import { useState } from "react";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import * as z from "zod";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON><PERSON>eader,
  <PERSON><PERSON>Tit<PERSON>,
  Di<PERSON>Footer,
} from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Card } from "@/components/ui/card";
import {
  MapPin,
  Clock,
  Phone,
  Mail,
  User,
  Package,
  DollarSign,
  MessageSquare,
  Send,
  AlertCircle,
} from "lucide-react";
import { format } from "date-fns";
import { fr } from "date-fns/locale";

const responseSchema = z.object({
  quantityAvailable: z.string().min(1, "Quantité requise").refine(
    (val) => !isNaN(Number(val)) && Number(val) > 0,
    "Quantité doit être un nombre positif"
  ),
  pricePerUnit: z.string().optional().refine(
    (val) => !val || (!isNaN(Number(val)) && Number(val) >= 0),
    "Prix doit être un nombre positif ou vide"
  ),
  message: z.string().optional(),
  contactName: z.string().min(2, "Nom de contact requis"),
  contactPhone: z.string().min(10, "Numéro de téléphone requis"),
  contactEmail: z.string().email("Email invalide").optional().or(z.literal("")),
});

interface UrgentRequest {
  id: string;
  medication: string;
  quantity: number;
  urgency: "critique" | "urgent" | "normal" | "faible";
  status: "en attente" | "accepté" | "refusé" | "expiré";
  timestamp: string;
  notes?: string;
  distance?: number;
  expiresAt?: string;
  pharmacy?: {
    id: string;
    name: string;
    address: string;
    phone: string;
    latitude: number;
    longitude: number;
  };
  contact?: {
    name?: string;
    phone?: string;
    email?: string;
  };
}

interface ResponseDialogProps {
  open: boolean;
  onClose: () => void;
  request: UrgentRequest | null;
  onSubmit: (requestId: string, responseData: any) => Promise<void>;
}

const urgencyStyles = {
  critique: "bg-red-100 text-red-800 dark:bg-red-900/50 animate-pulse border-red-200",
  urgent: "bg-orange-100 text-orange-800 dark:bg-orange-900/50 border-orange-200",
  normal: "bg-blue-100 text-blue-800 dark:bg-blue-900/50 border-blue-200",
  faible: "bg-gray-100 text-gray-800 dark:bg-gray-900/50 border-gray-200",
};

const urgencyOptions = {
  critique: "🚨 CRITIQUE",
  urgent: "⚡ URGENT",
  normal: "📋 Normal",
  faible: "📝 Faible",
};

export function ResponseDialog({ open, onClose, request, onSubmit }: ResponseDialogProps) {
  const [isSubmitting, setIsSubmitting] = useState(false);

  const form = useForm<z.infer<typeof responseSchema>>({
    resolver: zodResolver(responseSchema),
    defaultValues: {
      quantityAvailable: "",
      pricePerUnit: "",
      message: "",
      contactName: "",
      contactPhone: "",
      contactEmail: "",
    },
  });

  const handleSubmit = async (values: z.infer<typeof responseSchema>) => {
    if (!request) return;

    setIsSubmitting(true);
    try {
      await onSubmit(request.id, {
        urgentRequestId: request.id,
        quantityAvailable: values.quantityAvailable,
        pricePerUnit: values.pricePerUnit || null,
        message: values.message || null,
        contactName: values.contactName,
        contactPhone: values.contactPhone,
        contactEmail: values.contactEmail || null,
      });

      form.reset();
      onClose();
    } catch (error) {
      console.error("Error submitting response:", error);
    } finally {
      setIsSubmitting(false);
    }
  };

  if (!request) return null;

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Send className="w-5 h-5 text-blue-600" />
            Répondre à la demande urgente
          </DialogTitle>
        </DialogHeader>

        {/* Request Details */}
        <Card className="p-4 bg-gray-50">
          <div className="space-y-3">
            <div className="flex items-start justify-between">
              <div>
                <h3 className="font-semibold text-lg">{request.medication}</h3>
                <p className="text-sm text-muted-foreground">
                  {request.quantity} unités demandées
                </p>
              </div>
              <Badge className={urgencyStyles[request.urgency]}>
                {urgencyOptions[request.urgency]}
              </Badge>
            </div>

            {request.notes && (
              <div className="bg-white p-3 rounded border">
                <div className="flex items-start gap-2">
                  <MessageSquare className="w-4 h-4 text-muted-foreground mt-0.5" />
                  <div>
                    <div className="text-sm font-medium">Détails:</div>
                    <div className="text-sm text-muted-foreground">{request.notes}</div>
                  </div>
                </div>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div className="flex items-center gap-2">
                <MapPin className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="font-medium">{request.pharmacy?.name}</div>
                  <div className="text-muted-foreground">{request.pharmacy?.address}</div>
                  {request.distance && (
                    <div className="text-blue-600">{request.distance.toFixed(1)}km de distance</div>
                  )}
                </div>
              </div>
              <div className="flex items-center gap-2">
                <Clock className="w-4 h-4 text-muted-foreground" />
                <div>
                  <div className="font-medium">Publié</div>
                  <div className="text-muted-foreground">
                    {format(new Date(request.timestamp), "PPp", { locale: fr })}
                  </div>
                </div>
              </div>
            </div>

            {request.contact && (
              <div className="bg-white p-3 rounded border">
                <div className="text-sm font-medium mb-2">Contact demandeur:</div>
                <div className="space-y-1 text-sm">
                  {request.contact.name && (
                    <div className="flex items-center gap-2">
                      <User className="w-3 h-3 text-muted-foreground" />
                      <span>{request.contact.name}</span>
                    </div>
                  )}
                  {request.contact.phone && (
                    <div className="flex items-center gap-2">
                      <Phone className="w-3 h-3 text-muted-foreground" />
                      <span>{request.contact.phone}</span>
                    </div>
                  )}
                  {request.contact.email && (
                    <div className="flex items-center gap-2">
                      <Mail className="w-3 h-3 text-muted-foreground" />
                      <span>{request.contact.email}</span>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </Card>

        {/* Response Form */}
        <Form {...form}>
          <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <FormField
                control={form.control}
                name="quantityAvailable"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <Package className="w-4 h-4" />
                      Quantité disponible *
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: 5"
                        {...field}
                        type="number"
                        min="1"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />

              <FormField
                control={form.control}
                name="pricePerUnit"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel className="flex items-center gap-2">
                      <DollarSign className="w-4 h-4" />
                      Prix unitaire (MAD)
                    </FormLabel>
                    <FormControl>
                      <Input
                        placeholder="Ex: 25.50"
                        {...field}
                        type="number"
                        step="0.01"
                        min="0"
                      />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <FormField
              control={form.control}
              name="message"
              render={({ field }) => (
                <FormItem>
                  <FormLabel className="flex items-center gap-2">
                    <MessageSquare className="w-4 h-4" />
                    Message (optionnel)
                  </FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Informations supplémentaires, conditions de vente, délai de préparation..."
                      {...field}
                      rows={3}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />

            <div className="border-t pt-4">
              <h4 className="font-medium mb-3 flex items-center gap-2">
                <User className="w-4 h-4" />
                Vos informations de contact
              </h4>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  control={form.control}
                  name="contactName"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Nom de contact *</FormLabel>
                      <FormControl>
                        <Input placeholder="Votre nom" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="contactPhone"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Téléphone *</FormLabel>
                      <FormControl>
                        <Input placeholder="06 XX XX XX XX" {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />
              </div>

              <FormField
                control={form.control}
                name="contactEmail"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Email (optionnel)</FormLabel>
                    <FormControl>
                      <Input placeholder="<EMAIL>" {...field} type="email" />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>

            <div className="bg-blue-50 p-3 rounded-lg">
              <div className="flex items-start gap-2">
                <AlertCircle className="w-4 h-4 text-blue-600 mt-0.5" />
                <div className="text-sm text-blue-800">
                  <div className="font-medium">Information importante:</div>
                  <div>
                    Votre réponse sera transmise directement à la pharmacie demandeuse. 
                    Assurez-vous que vos informations de contact sont correctes.
                  </div>
                </div>
              </div>
            </div>

            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Annuler
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
                className="bg-blue-600 hover:bg-blue-700"
              >
                {isSubmitting ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                    Envoi en cours...
                  </>
                ) : (
                  <>
                    <Send className="w-4 h-4 mr-2" />
                    Envoyer la réponse
                  </>
                )}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  );
}
