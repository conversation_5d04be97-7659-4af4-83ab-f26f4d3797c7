"use client";

import { useState, useEffect, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Slider } from "@/components/ui/slider";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  MapPin,
  Radar,
  Zap,
  TrendingUp,
  Users,
  Package,
  AlertCircle,
  RefreshCw,
  Settings,
  Eye,
  EyeOff,
} from "lucide-react";

interface PharmacyLocation {
  id: string;
  name: string;
  latitude: number;
  longitude: number;
  distance?: number;
  demandLevel: 'low' | 'medium' | 'high' | 'critical';
  supplyLevel: 'low' | 'medium' | 'high';
  urgentRequests: number;
  activeListings: number;
  lastActivity: string;
  isAnonymous: boolean;
}

interface DemandSupplyIndicator {
  latitude: number;
  longitude: number;
  demandIntensity: number; // 0-100
  supplyIntensity: number; // 0-100
  urgentCount: number;
  listingCount: number;
  radius: number; // Area of influence in km
}

interface MapInterfaceProps {
  centerLat?: number;
  centerLng?: number;
  initialRadius?: number;
}

const demandColors = {
  low: '#10b981',     // Green
  medium: '#f59e0b',  // Yellow
  high: '#ef4444',    // Red
  critical: '#dc2626' // Dark Red
};

const supplyColors = {
  low: '#ef4444',     // Red (low supply = bad)
  medium: '#f59e0b',  // Yellow
  high: '#10b981'     // Green (high supply = good)
};

export function MapInterface({ centerLat = 33.5731, centerLng = -7.5898, initialRadius = 20 }: MapInterfaceProps) {
  const [pharmacies, setPharmacies] = useState<PharmacyLocation[]>([]);
  const [indicators, setIndicators] = useState<DemandSupplyIndicator[]>([]);
  const [radius, setRadius] = useState([initialRadius]);
  const [viewMode, setViewMode] = useState<'pharmacies' | 'heatmap' | 'both'>('both');
  const [showAnonymous, setShowAnonymous] = useState(true);
  const [loading, setLoading] = useState(false);
  const [lastUpdate, setLastUpdate] = useState<Date | null>(null);
  const [autoRefresh, setAutoRefresh] = useState(true);

  // Simulated map container (in real implementation, this would be Leaflet/MapBox)
  const [mapCenter, setMapCenter] = useState({ lat: centerLat, lng: centerLng });
  const [mapZoom, setMapZoom] = useState(12);

  // Fetch pharmacy radar data
  const fetchRadarData = useCallback(async () => {
    setLoading(true);
    try {
      const params = new URLSearchParams({
        radius: radius[0].toString(),
        anonymous: showAnonymous.toString(),
        mode: viewMode
      });

      const response = await fetch(`/api/pharmacy-radar?${params}`, {
        method: "GET",
        credentials: "include",
      });

      if (!response.ok) {
        throw new Error(`Failed to fetch radar data: ${response.statusText}`);
      }

      const data = await response.json();

      if (data.error) {
        throw new Error(data.error);
      }

      setPharmacies(data.pharmacies || []);
      setIndicators(data.indicators || []);
      setLastUpdate(new Date());

    } catch (error) {
      console.error('Error fetching radar data:', error);

      // Fallback to mock data for demonstration
      const mockPharmacies: PharmacyLocation[] = [
        {
          id: '1',
          name: 'Pharmacie Al Andalous',
          latitude: 33.5731 + (Math.random() - 0.5) * 0.02,
          longitude: -7.5898 + (Math.random() - 0.5) * 0.02,
          distance: Math.random() * radius[0],
          demandLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
          supplyLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
          urgentRequests: Math.floor(Math.random() * 5),
          activeListings: Math.floor(Math.random() * 20),
          lastActivity: new Date(Date.now() - Math.random() * 3600000).toISOString(),
          isAnonymous: Math.random() > 0.3
        },
        {
          id: '2',
          name: 'Pharmacie Centrale',
          latitude: 33.5731 + (Math.random() - 0.5) * 0.02,
          longitude: -7.5898 + (Math.random() - 0.5) * 0.02,
          distance: Math.random() * radius[0],
          demandLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
          supplyLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
          urgentRequests: Math.floor(Math.random() * 5),
          activeListings: Math.floor(Math.random() * 20),
          lastActivity: new Date(Date.now() - Math.random() * 3600000).toISOString(),
          isAnonymous: Math.random() > 0.3
        },
        {
          id: '3',
          name: 'Pharmacie du Quartier',
          latitude: 33.5731 + (Math.random() - 0.5) * 0.02,
          longitude: -7.5898 + (Math.random() - 0.5) * 0.02,
          distance: Math.random() * radius[0],
          demandLevel: ['low', 'medium', 'high', 'critical'][Math.floor(Math.random() * 4)] as any,
          supplyLevel: ['low', 'medium', 'high'][Math.floor(Math.random() * 3)] as any,
          urgentRequests: Math.floor(Math.random() * 5),
          activeListings: Math.floor(Math.random() * 20),
          lastActivity: new Date(Date.now() - Math.random() * 3600000).toISOString(),
          isAnonymous: Math.random() > 0.3
        }
      ];

      // Generate demand/supply indicators
      const mockIndicators: DemandSupplyIndicator[] = Array.from({ length: 8 }, (_, i) => ({
        latitude: 33.5731 + (Math.random() - 0.5) * 0.03,
        longitude: -7.5898 + (Math.random() - 0.5) * 0.03,
        demandIntensity: Math.random() * 100,
        supplyIntensity: Math.random() * 100,
        urgentCount: Math.floor(Math.random() * 10),
        listingCount: Math.floor(Math.random() * 50),
        radius: 2 + Math.random() * 3
      }));

      setPharmacies(mockPharmacies);
      setIndicators(mockIndicators);
      setLastUpdate(new Date());
    } finally {
      setLoading(false);
    }
  }, [radius, showAnonymous, viewMode]);

  // Initial load and auto-refresh
  useEffect(() => {
    fetchRadarData();
  }, [fetchRadarData]);

  useEffect(() => {
    if (!autoRefresh) return;

    const interval = setInterval(fetchRadarData, 30000); // Refresh every 30 seconds
    return () => clearInterval(interval);
  }, [autoRefresh, fetchRadarData]);

  // Filter pharmacies based on settings
  const filteredPharmacies = pharmacies.filter(pharmacy => {
    if (!showAnonymous && pharmacy.isAnonymous) return false;
    return pharmacy.distance ? pharmacy.distance <= radius[0] : true;
  });

  // Calculate network statistics
  const networkStats = {
    totalPharmacies: filteredPharmacies.length,
    urgentRequests: filteredPharmacies.reduce((sum, p) => sum + p.urgentRequests, 0),
    activeListings: filteredPharmacies.reduce((sum, p) => sum + p.activeListings, 0),
    highDemandAreas: filteredPharmacies.filter(p => p.demandLevel === 'high' || p.demandLevel === 'critical').length,
    lowSupplyAreas: filteredPharmacies.filter(p => p.supplyLevel === 'low').length
  };

  return (
    <div className="space-y-6">
      {/* Controls */}
      <Card className="p-4">
        <div className="flex flex-wrap items-center gap-4">
          <div className="flex items-center gap-2">
            <Radar className="w-4 h-4 text-blue-600" />
            <span className="font-medium">Rayon:</span>
            <div className="w-32">
              <Slider
                value={radius}
                onValueChange={setRadius}
                max={100}
                min={5}
                step={5}
                className="w-full"
              />
            </div>
            <span className="text-sm text-muted-foreground">{radius[0]}km</span>
          </div>

          <div className="flex items-center gap-2">
            <Eye className="w-4 h-4 text-muted-foreground" />
            <span className="text-sm font-medium">Vue:</span>
            <Select value={viewMode} onValueChange={(value: any) => setViewMode(value)}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="pharmacies">Pharmacies</SelectItem>
                <SelectItem value="heatmap">Heatmap</SelectItem>
                <SelectItem value="both">Les Deux</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setShowAnonymous(!showAnonymous)}
          >
            {showAnonymous ? <Eye className="w-4 h-4 mr-1" /> : <EyeOff className="w-4 h-4 mr-1" />}
            Anonymes
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={fetchRadarData}
            disabled={loading}
          >
            <RefreshCw className={`w-4 h-4 mr-1 ${loading ? 'animate-spin' : ''}`} />
            Actualiser
          </Button>

          <Button
            variant="outline"
            size="sm"
            onClick={() => setAutoRefresh(!autoRefresh)}
          >
            <Settings className="w-4 h-4 mr-1" />
            Auto: {autoRefresh ? 'ON' : 'OFF'}
          </Button>
        </div>
      </Card>

      {/* Network Statistics */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="p-3">
          <div className="flex items-center gap-2">
            <Users className="w-4 h-4 text-blue-600" />
            <div>
              <div className="text-sm text-muted-foreground">Pharmacies</div>
              <div className="text-lg font-semibold">{networkStats.totalPharmacies}</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-2">
            <Zap className="w-4 h-4 text-red-600" />
            <div>
              <div className="text-sm text-muted-foreground">Urgences</div>
              <div className="text-lg font-semibold">{networkStats.urgentRequests}</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-2">
            <Package className="w-4 h-4 text-green-600" />
            <div>
              <div className="text-sm text-muted-foreground">Offres</div>
              <div className="text-lg font-semibold">{networkStats.activeListings}</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4 text-orange-600" />
            <div>
              <div className="text-sm text-muted-foreground">Forte Demande</div>
              <div className="text-lg font-semibold">{networkStats.highDemandAreas}</div>
            </div>
          </div>
        </Card>
        
        <Card className="p-3">
          <div className="flex items-center gap-2">
            <AlertCircle className="w-4 h-4 text-red-600" />
            <div>
              <div className="text-sm text-muted-foreground">Faible Stock</div>
              <div className="text-lg font-semibold">{networkStats.lowSupplyAreas}</div>
            </div>
          </div>
        </Card>
      </div>

      {/* Map Container */}
      <Card className="p-6">
        <div className="flex items-center justify-between mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2">
            <MapPin className="w-5 h-5 text-blue-600" />
            Radar Pharmaceutique - Casablanca
          </h3>
          {lastUpdate && (
            <div className="text-sm text-muted-foreground">
              Dernière mise à jour: {lastUpdate.toLocaleTimeString()}
            </div>
          )}
        </div>

        {/* Simulated Map Interface */}
        <div className="relative bg-gray-100 rounded-lg h-96 overflow-hidden">
          {/* Map Background */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-50 to-green-50">
            <div className="absolute inset-0 opacity-20">
              {/* Grid pattern to simulate map */}
              <div className="grid grid-cols-8 grid-rows-6 h-full">
                {Array.from({ length: 48 }).map((_, i) => (
                  <div key={i} className="border border-gray-300"></div>
                ))}
              </div>
            </div>
          </div>

          {/* Pharmacy Markers */}
          {(viewMode === 'pharmacies' || viewMode === 'both') && filteredPharmacies.map((pharmacy) => {
            const x = ((pharmacy.longitude + 7.5898) * 1000) % 100;
            const y = ((pharmacy.latitude - 33.5731) * 1000) % 100;
            
            return (
              <div
                key={pharmacy.id}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 cursor-pointer group"
                style={{
                  left: `${20 + x * 0.6}%`,
                  top: `${20 + y * 0.6}%`
                }}
              >
                <div
                  className="w-4 h-4 rounded-full border-2 border-white shadow-lg"
                  style={{ backgroundColor: demandColors[pharmacy.demandLevel] }}
                >
                  <div
                    className="w-2 h-2 rounded-full absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
                    style={{ backgroundColor: supplyColors[pharmacy.supplyLevel] }}
                  ></div>
                </div>
                
                {/* Tooltip */}
                <div className="absolute bottom-full left-1/2 transform -translate-x-1/2 mb-2 opacity-0 group-hover:opacity-100 transition-opacity bg-black text-white text-xs rounded px-2 py-1 whitespace-nowrap z-10">
                  <div className="font-medium">{pharmacy.isAnonymous ? 'Pharmacie Anonyme' : pharmacy.name}</div>
                  <div>Demande: {pharmacy.demandLevel} | Stock: {pharmacy.supplyLevel}</div>
                  <div>Urgences: {pharmacy.urgentRequests} | Offres: {pharmacy.activeListings}</div>
                  {pharmacy.distance && <div>Distance: {pharmacy.distance.toFixed(1)}km</div>}
                </div>
              </div>
            );
          })}

          {/* Demand/Supply Heat Indicators */}
          {(viewMode === 'heatmap' || viewMode === 'both') && indicators.map((indicator, index) => {
            const x = ((indicator.longitude + 7.5898) * 1000) % 100;
            const y = ((indicator.latitude - 33.5731) * 1000) % 100;
            const intensity = (indicator.demandIntensity + indicator.supplyIntensity) / 2;
            
            return (
              <div
                key={index}
                className="absolute transform -translate-x-1/2 -translate-y-1/2 pointer-events-none"
                style={{
                  left: `${15 + x * 0.7}%`,
                  top: `${15 + y * 0.7}%`
                }}
              >
                <div
                  className="rounded-full opacity-30"
                  style={{
                    width: `${indicator.radius * 8}px`,
                    height: `${indicator.radius * 8}px`,
                    backgroundColor: intensity > 70 ? '#ef4444' : intensity > 40 ? '#f59e0b' : '#10b981'
                  }}
                ></div>
              </div>
            );
          })}

          {/* Center Marker (User Location) */}
          <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2">
            <div className="w-6 h-6 bg-blue-600 rounded-full border-4 border-white shadow-lg flex items-center justify-center">
              <div className="w-2 h-2 bg-white rounded-full"></div>
            </div>
            <div className="absolute top-full left-1/2 transform -translate-x-1/2 mt-1 text-xs font-medium text-blue-600">
              Vous
            </div>
          </div>

          {/* Radius Circle */}
          <div
            className="absolute border-2 border-blue-400 border-dashed rounded-full opacity-50 pointer-events-none"
            style={{
              width: `${radius[0] * 4}px`,
              height: `${radius[0] * 4}px`,
              top: '50%',
              left: '50%',
              transform: 'translate(-50%, -50%)'
            }}
          ></div>

          {/* Loading Overlay */}
          {loading && (
            <div className="absolute inset-0 bg-white bg-opacity-75 flex items-center justify-center">
              <div className="flex items-center gap-2">
                <RefreshCw className="w-5 h-5 animate-spin text-blue-600" />
                <span className="text-blue-600">Mise à jour du radar...</span>
              </div>
            </div>
          )}
        </div>

        {/* Legend */}
        <div className="mt-4 flex flex-wrap gap-4 text-xs">
          <div className="flex items-center gap-2">
            <span className="font-medium">Demande:</span>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: demandColors.low }}></div>
              <span>Faible</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: demandColors.medium }}></div>
              <span>Moyenne</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: demandColors.high }}></div>
              <span>Forte</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: demandColors.critical }}></div>
              <span>Critique</span>
            </div>
          </div>
          
          <div className="flex items-center gap-2">
            <span className="font-medium">Stock:</span>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: supplyColors.low }}></div>
              <span>Faible</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: supplyColors.medium }}></div>
              <span>Moyen</span>
            </div>
            <div className="flex items-center gap-1">
              <div className="w-3 h-3 rounded-full" style={{ backgroundColor: supplyColors.high }}></div>
              <span>Élevé</span>
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
