"use client";

import { useState, useEffect } from "react";
import { cn } from "@/lib/utils";
import { useAuth } from "@/contexts/auth-context";
import { Button } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  LayoutDashboard,
  Package,
  History,
  MessageSquare,
  Heart,
  Store,
  Users,
  Settings,
  LogOut,
  Menu,
  X,
  AlertCircle,
  Clock,
  CalendarClock,
  User,
  Building2,
  ShoppingCart,
  Map,
  CreditCard,
  AlertTriangle,
  BarChart3,
  FileText,
  Radar,
  Brain,
} from "lucide-react";
import Link from "next/link";
import { usePathname, useRouter } from "next/navigation";
import Image from "next/image";
import { usePermissions } from "@/lib/types/auth";
import type { User as AuthUser } from "@/lib/types/auth";

interface RouteGroup {
  label: string;
  routes: {
    label: string;
    icon: any;
    href: string;
    adminOnly?: boolean;
    requiresPharmacy?: boolean;
    permission?: { action: string; resource: string };
    roleRestriction?: string[]; // Only show for specific roles
  }[];
}

// Each route now includes a permission property for access control.
// Only 'super_admin' sees admin links. 'owner' sees only pharmacy management links.
const routeGroups: RouteGroup[] = [
  {
    label: "Principal",
    routes: [
      {
        label: "Tableau de bord",
        icon: LayoutDashboard,
        href: "/dashboard",
        permission: { action: "read", resource: "listings" },
      },
      {
        label: "Inventaire",
        icon: Package,
        href: "/inventory",
        requiresPharmacy: true,
        permission: { action: "read", resource: "products" },
      },
      {
        label: "Lecteur d'Ordonnances",
        icon: FileText,
        href: "/prescription-reader",
        permission: { action: "read", resource: "listings" },
      },
    ],
  },
  {
    label: "Échange",
    routes: [
      {
        label: "Marché",
        icon: Store,
        href: "/marketplace",
        requiresPharmacy: true,
        permission: { action: "read", resource: "products" },
      },
      {
        label: "Commandes",
        icon: ShoppingCart,
        href: "/orders",
        requiresPharmacy: true,
        permission: { action: "read", resource: "transactions" },
      },
      {
        label: "Réservations",
        icon: CalendarClock,
        href: "/marketplace?tab=reservations",
        requiresPharmacy: true,
        permission: { action: "read", resource: "transactions" },
      },
      {
        label: "Demandes urgentes",
        icon: AlertCircle,
        href: "/urgent-requests",
        requiresPharmacy: true,
        permission: { action: "read", resource: "transactions" },
      },
      {
        label: "Pharmacy Radar",
        icon: Radar,
        href: "/pharmacy-radar",
        requiresPharmacy: true,
        permission: { action: "read", resource: "transactions" },
      },
      {
        label: "Demande Saisonnière",
        icon: CalendarClock,
        href: "/seasonal-demand",
        requiresPharmacy: true,
        permission: { action: "read", resource: "transactions" },
      },
    ],
  },
  {
    label: "Intelligence Distributeur",
    routes: [
      {
        label: "Intelligence IA",
        icon: Brain,
        href: "/distributor-intelligence",
        permission: { action: "read", resource: "transactions" },
        roleRestriction: ["supplier"], // Only for suppliers/distributors
      },
    ],
  },
  {
    label: "Gestion",
    routes: [
      {
        label: "Équipe",
        icon: Users,
        href: "/team",
        requiresPharmacy: true,
        permission: { action: "read", resource: "team" },
      },
      {
        label: "Historique",
        icon: History,
        href: "/history",
        requiresPharmacy: true,
        permission: { action: "read", resource: "transactions" },
      },
    ],
  },
  {
    label: "Paramètres",
    routes: [
      {
        label: "Profil",
        icon: User,
        href: "/profile",
        permission: { action: "read", resource: "settings" },
      },
      {
        label: "Configuration",
        icon: Settings,
        href: "/settings",
        requiresPharmacy: true,
        permission: { action: "read", resource: "settings" },
      },
    ],
  },
];

const COLLAPSE_TIMEOUT = 3000;

interface SidebarProps {
  className?: string;
}

export default function Sidebar() {
  const [isCollapsed, setIsCollapsed] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const pathname = usePathname();
  const router = useRouter();

  let collapseTimeout: NodeJS.Timeout;

  const handleMouseEnter = () => {
    clearTimeout(collapseTimeout);
    setIsCollapsed(false);
  };

  const handleMouseLeave = () => {
    collapseTimeout = setTimeout(() => {
      setIsCollapsed(true);
    }, COLLAPSE_TIMEOUT);
  };

  useEffect(() => {
    return () => {
      clearTimeout(collapseTimeout);
    };
  }, []);

  const { signOut, user, isPharmacy } = useAuth();

  const mappedUser: AuthUser | null = user
    ? {
        ...user,
        role: user.role as import("@/lib/types/auth").UserRole,
        status: "active" as "active",
        createdAt: new Date(),
        updatedAt: new Date(),
      }
    : null;
  const permissions = usePermissions(mappedUser);

  // Only show links the user can access
  const filteredRoutes = routeGroups.flatMap((group: RouteGroup) =>
    group.routes.filter((route) => {
      // Check role restrictions first
      if (route.roleRestriction && user?.role) {
        if (!route.roleRestriction.includes(user.role)) {
          return false;
        }
      }

      // For owner, ignore requiresPharmacy if user is owner
      if (
        route.requiresPharmacy &&
        user?.role !== "owner" &&
        !user?.pharmacyId
      ) {
        return false;
      }
      return (
        route.permission &&
        permissions.can(
          route.permission.action as any,
          route.permission.resource as any
        )
      );
    })
  );

  // Responsive: always expanded when mobile menu is open
  const isSidebarExpanded = isMobileMenuOpen || (!isMobileMenuOpen && !isCollapsed);

  const handleLogout = async () => {
    try {
      console.log("Logging out...");
      await signOut();
      router.push("/auth/login");
    } catch (error) {
      console.error("Error logging out:", error);
    }
  };

  return (
    <>
      {/* Hamburger menu for mobile */}
      <button
        className="sm:hidden fixed top-4 left-4 z-50 bg-white rounded-full p-2 shadow-md"
        onClick={() => setIsMobileMenuOpen((open) => !open)}
        aria-label={isMobileMenuOpen ? "Fermer le menu" : "Ouvrir le menu"}
      >
        {isMobileMenuOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
      </button>
      {/* Sidebar */}
      <aside
        className={cn(
          "fixed left-0 top-0 z-40 h-screen transition-all duration-300 bg-background",
          isSidebarExpanded ? "w-[250px]" : "w-[80px]",
          isMobileMenuOpen ? "block sm:block" : "hidden sm:block"
        )}
        onMouseEnter={() => !isMobileMenuOpen && setIsCollapsed(false)}
        onMouseLeave={() => !isMobileMenuOpen && setIsCollapsed(true)}
      >
        <div className="flex h-full flex-col">
          <div className="flex h-[60px] items-center justify-between px-4">
            <Link href="/" className="flex items-center space-x-2">
              <Image
                src="/logo.png"
                alt="Logo"
                width={32}
                height={32}
                className="h-8 w-8"
              />
              <span
                className={cn(
                  "font-bold transition-all duration-300",
                  isSidebarExpanded ? "inline" : "hidden"
                )}
              >
                PharmaStock
              </span>
            </Link>
          </div>

          <ScrollArea className="flex-1 py-2">
            <nav className="space-y-2 px-2" role="navigation">
              {filteredRoutes.map((route) => (
                <Link
                  key={route.href}
                  href={route.href}
                  className={cn(
                    "flex items-center rounded-lg px-3 py-2 text-sm font-medium",
                    "hover:bg-accent hover:text-accent-foreground",
                    "transition-colors duration-200"
                  )}
                  onClick={() => setIsMobileMenuOpen(false)}
                >
                  <route.icon className="h-4 w-4" />
                  <span
                    className={cn(
                      "ml-3 transition-all duration-300",
                      isSidebarExpanded ? "inline" : "hidden"
                    )}
                  >
                    {route.label}
                  </span>
                </Link>
              ))}
            </nav>
          </ScrollArea>

          <div className="mt-auto border-t py-2">
            <Button
              variant="ghost"
              className="w-full justify-start px-3"
              onClick={async () => { await signOut(); setIsMobileMenuOpen(false); }}
            >
              <LogOut className="h-4 w-4 mr-2" />
              <span
                className={cn(
                  "transition-all duration-300",
                  isSidebarExpanded ? "inline" : "hidden"
                )}
              >
                Sign out
              </span>
            </Button>
          </div>
        </div>
      </aside>
    </>
  );
}
