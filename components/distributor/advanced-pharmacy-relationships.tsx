"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Users,
  Heart,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Star,
  Phone,
  Mail,
  Calendar,
  Package,
  DollarSign,
  Clock,
  Target,
  Zap,
  MessageSquare,
  Gift,
  Award,
} from "lucide-react";

interface PharmacyRelationship {
  id: string;
  name: string;
  owner: string;
  city: string;
  relationshipScore: number;
  loyaltyLevel: 'bronze' | 'silver' | 'gold' | 'platinum';
  monthlyVolume: number;
  paymentTerms: string;
  lastOrder: string;
  preferredCategories: string[];
  communicationPreference: 'phone' | 'email' | 'whatsapp' | 'visit';
  riskLevel: 'low' | 'medium' | 'high';
  opportunities: string[];
  notes: string;
  nextAction: {
    type: string;
    date: string;
    description: string;
  };
}

interface RelationshipInsight {
  type: 'opportunity' | 'risk' | 'celebration' | 'action_needed';
  pharmacy: string;
  title: string;
  description: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  suggestedAction: string;
  potentialImpact: string;
}

export function AdvancedPharmacyRelationships() {
  const [activeTab, setActiveTab] = useState("overview");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterLoyalty, setFilterLoyalty] = useState("all");
  const [filterRisk, setFilterRisk] = useState("all");
  const [relationships, setRelationships] = useState<PharmacyRelationship[]>([]);
  const [insights, setInsights] = useState<RelationshipInsight[]>([]);

  useEffect(() => {
    loadRelationshipData();
  }, []);

  const loadRelationshipData = () => {
    // Mock data - in real implementation, this would come from CRM analysis
    const mockRelationships: PharmacyRelationship[] = [
      {
        id: '1',
        name: 'Pharmacie Al Andalous',
        owner: 'Dr. Amina Benali',
        city: 'Casablanca',
        relationshipScore: 92,
        loyaltyLevel: 'platinum',
        monthlyVolume: 45000,
        paymentTerms: '30 jours',
        lastOrder: '2024-01-15',
        preferredCategories: ['Cardiologie', 'Diabète', 'Pédiatrie'],
        communicationPreference: 'phone',
        riskLevel: 'low',
        opportunities: ['Expansion gamme dermatologie', 'Programme fidélité VIP'],
        notes: 'Partenaire stratégique depuis 5 ans. Très satisfait du service.',
        nextAction: {
          type: 'Visite commerciale',
          date: '2024-01-25',
          description: 'Présentation nouvelle gamme printemps'
        }
      },
      {
        id: '2',
        name: 'Pharmacie Centrale',
        owner: 'Dr. Mohamed Alami',
        city: 'Rabat',
        relationshipScore: 67,
        loyaltyLevel: 'silver',
        monthlyVolume: 28000,
        paymentTerms: '45 jours',
        lastOrder: '2024-01-10',
        preferredCategories: ['Généraliste', 'OTC'],
        communicationPreference: 'email',
        riskLevel: 'medium',
        opportunities: ['Améliorer délais paiement', 'Formation équipe'],
        notes: 'Relation stable mais potentiel d\'amélioration sur la fidélité.',
        nextAction: {
          type: 'Appel de suivi',
          date: '2024-01-22',
          description: 'Discuter des nouveaux termes de paiement'
        }
      },
      {
        id: '3',
        name: 'Pharmacie du Quartier',
        owner: 'Dr. Fatima Zahra',
        city: 'Marrakech',
        relationshipScore: 45,
        loyaltyLevel: 'bronze',
        monthlyVolume: 12000,
        paymentTerms: '60 jours',
        lastOrder: '2023-12-28',
        preferredCategories: ['Basique', 'Génériques'],
        communicationPreference: 'whatsapp',
        riskLevel: 'high',
        opportunities: ['Plan de récupération', 'Offres spéciales'],
        notes: 'Relation en déclin. Concurrence agressive dans la zone.',
        nextAction: {
          type: 'Visite urgente',
          date: '2024-01-20',
          description: 'Plan de reconquête et offres spéciales'
        }
      }
    ];

    const mockInsights: RelationshipInsight[] = [
      {
        type: 'opportunity',
        pharmacy: 'Pharmacie Al Andalous',
        title: 'Opportunité d\'Expansion',
        description: 'Client platinum avec potentiel d\'augmentation de 25% sur la dermatologie',
        priority: 'high',
        suggestedAction: 'Proposer gamme dermatologie premium',
        potentialImpact: '+11K MAD/mois'
      },
      {
        type: 'risk',
        pharmacy: 'Pharmacie du Quartier',
        title: 'Risque de Perte',
        description: 'Aucune commande depuis 3 semaines, score relationnel en baisse',
        priority: 'urgent',
        suggestedAction: 'Visite immédiate avec offres de reconquête',
        potentialImpact: '-12K MAD/mois'
      },
      {
        type: 'celebration',
        pharmacy: 'Pharmacie Centrale',
        title: 'Anniversaire Partenariat',
        description: '3 ans de collaboration, moment idéal pour renforcer la relation',
        priority: 'medium',
        suggestedAction: 'Organiser événement de remerciement',
        potentialImpact: 'Fidélisation renforcée'
      }
    ];

    setRelationships(mockRelationships);
    setInsights(mockInsights);
  };

  const getLoyaltyColor = (level: string) => {
    switch (level) {
      case 'platinum': return 'bg-purple-100 text-purple-800 border-purple-200';
      case 'gold': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'silver': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'bronze': return 'bg-orange-100 text-orange-800 border-orange-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const getInsightIcon = (type: string) => {
    switch (type) {
      case 'opportunity': return TrendingUp;
      case 'risk': return AlertTriangle;
      case 'celebration': return Gift;
      case 'action_needed': return Clock;
      default: return Target;
    }
  };

  const filteredRelationships = relationships.filter(rel => {
    const matchesSearch = rel.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         rel.owner.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesLoyalty = filterLoyalty === 'all' || rel.loyaltyLevel === filterLoyalty;
    const matchesRisk = filterRisk === 'all' || rel.riskLevel === filterRisk;
    
    return matchesSearch && matchesLoyalty && matchesRisk;
  });

  const relationshipStats = {
    totalRelationships: relationships.length,
    avgScore: Math.round(relationships.reduce((sum, r) => sum + r.relationshipScore, 0) / relationships.length),
    totalVolume: relationships.reduce((sum, r) => sum + r.monthlyVolume, 0),
    highRisk: relationships.filter(r => r.riskLevel === 'high').length,
    platinum: relationships.filter(r => r.loyaltyLevel === 'platinum').length
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Heart className="w-6 h-6 text-red-500" />
            Relations Pharmacies Avancées
          </h2>
          <p className="text-muted-foreground">
            Gestion intelligente des relations et optimisation de la fidélité
          </p>
        </div>
        <Button className="bg-blue-600 hover:bg-blue-700">
          <MessageSquare className="w-4 h-4 mr-2" />
          Nouveau Contact
        </Button>
      </div>

      {/* Relationship KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
        <Card className="p-4">
          <div className="flex items-center gap-2">
            <Users className="w-5 h-5 text-blue-600" />
            <div>
              <div className="text-sm text-muted-foreground">Relations Totales</div>
              <div className="text-2xl font-bold">{relationshipStats.totalRelationships}</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <Star className="w-5 h-5 text-yellow-600" />
            <div>
              <div className="text-sm text-muted-foreground">Score Moyen</div>
              <div className="text-2xl font-bold">{relationshipStats.avgScore}/100</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <DollarSign className="w-5 h-5 text-green-600" />
            <div>
              <div className="text-sm text-muted-foreground">Volume Mensuel</div>
              <div className="text-2xl font-bold">{(relationshipStats.totalVolume / 1000).toFixed(0)}K</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <Award className="w-5 h-5 text-purple-600" />
            <div>
              <div className="text-sm text-muted-foreground">Clients Platinum</div>
              <div className="text-2xl font-bold">{relationshipStats.platinum}</div>
            </div>
          </div>
        </Card>

        <Card className="p-4">
          <div className="flex items-center gap-2">
            <AlertTriangle className="w-5 h-5 text-red-600" />
            <div>
              <div className="text-sm text-muted-foreground">Risque Élevé</div>
              <div className="text-2xl font-bold">{relationshipStats.highRisk}</div>
            </div>
          </div>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="overview" className="flex items-center gap-2">
            <Users className="w-4 h-4" />
            Vue d'Ensemble
          </TabsTrigger>
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Insights IA
          </TabsTrigger>
          <TabsTrigger value="actions" className="flex items-center gap-2">
            <Clock className="w-4 h-4" />
            Actions
          </TabsTrigger>
          <TabsTrigger value="loyalty" className="flex items-center gap-2">
            <Award className="w-4 h-4" />
            Fidélité
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-4">
          {/* Filters */}
          <Card className="p-4">
            <div className="flex flex-wrap gap-4">
              <div className="flex-1 min-w-[200px]">
                <Input
                  placeholder="Rechercher pharmacie ou propriétaire..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              <Select value={filterLoyalty} onValueChange={setFilterLoyalty}>
                <SelectTrigger className="w-40">
                  <SelectValue placeholder="Niveau fidélité" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous niveaux</SelectItem>
                  <SelectItem value="platinum">Platinum</SelectItem>
                  <SelectItem value="gold">Gold</SelectItem>
                  <SelectItem value="silver">Silver</SelectItem>
                  <SelectItem value="bronze">Bronze</SelectItem>
                </SelectContent>
              </Select>
              <Select value={filterRisk} onValueChange={setFilterRisk}>
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="Risque" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">Tous</SelectItem>
                  <SelectItem value="low">Faible</SelectItem>
                  <SelectItem value="medium">Moyen</SelectItem>
                  <SelectItem value="high">Élevé</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </Card>

          {/* Relationships List */}
          <div className="space-y-4">
            {filteredRelationships.map((rel) => (
              <Card key={rel.id} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <Users className="w-6 h-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-lg">{rel.name}</h3>
                      <p className="text-muted-foreground">{rel.owner} • {rel.city}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getLoyaltyColor(rel.loyaltyLevel)}>
                      {rel.loyaltyLevel.toUpperCase()}
                    </Badge>
                    <div className="text-right">
                      <div className="text-2xl font-bold">{rel.relationshipScore}</div>
                      <div className="text-xs text-muted-foreground">Score</div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Volume Mensuel</div>
                    <div className="font-semibold">{rel.monthlyVolume.toLocaleString()} MAD</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Paiement</div>
                    <div className="font-semibold">{rel.paymentTerms}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Dernière Commande</div>
                    <div className="font-semibold">{rel.lastOrder}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Risque</div>
                    <div className={`font-semibold ${getRiskColor(rel.riskLevel)}`}>
                      {rel.riskLevel.toUpperCase()}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                  <div>
                    <div className="text-sm font-medium mb-2">Catégories Préférées:</div>
                    <div className="flex flex-wrap gap-1">
                      {rel.preferredCategories.map((cat, i) => (
                        <Badge key={i} variant="outline" className="text-xs">
                          {cat}
                        </Badge>
                      ))}
                    </div>
                  </div>
                  <div>
                    <div className="text-sm font-medium mb-2">Opportunités:</div>
                    <ul className="text-sm space-y-1">
                      {rel.opportunities.map((opp, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <span className="text-green-600">•</span>
                          <span>{opp}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <Calendar className="w-4 h-4" />
                    <span>Prochaine action: {rel.nextAction.type} - {rel.nextAction.date}</span>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Phone className="w-4 h-4 mr-1" />
                      Contacter
                    </Button>
                    <Button size="sm" variant="outline">
                      <Package className="w-4 h-4 mr-1" />
                      Historique
                    </Button>
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      <Target className="w-4 h-4 mr-1" />
                      Actions
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="insights" className="space-y-4">
          <div className="space-y-4">
            {insights.map((insight, index) => {
              const InsightIcon = getInsightIcon(insight.type);
              return (
                <Card key={index} className="p-6 border-l-4" style={{
                  borderLeftColor: insight.priority === 'urgent' ? '#ef4444' :
                                  insight.priority === 'high' ? '#f97316' :
                                  insight.priority === 'medium' ? '#eab308' : '#3b82f6'
                }}>
                  <div className="flex items-start justify-between mb-3">
                    <div className="flex items-center gap-3">
                      <InsightIcon className="w-6 h-6 text-blue-600" />
                      <div>
                        <h3 className="font-semibold">{insight.title}</h3>
                        <p className="text-sm text-muted-foreground">{insight.pharmacy}</p>
                      </div>
                    </div>
                    <Badge variant="outline" className={
                      insight.priority === 'urgent' ? 'bg-red-50 text-red-700' :
                      insight.priority === 'high' ? 'bg-orange-50 text-orange-700' :
                      insight.priority === 'medium' ? 'bg-yellow-50 text-yellow-700' :
                      'bg-blue-50 text-blue-700'
                    }>
                      {insight.priority.toUpperCase()}
                    </Badge>
                  </div>

                  <p className="text-sm mb-4">{insight.description}</p>

                  <div className="grid grid-cols-2 gap-4 mb-4">
                    <div>
                      <div className="text-sm font-medium">Action Suggérée:</div>
                      <div className="text-sm text-muted-foreground">{insight.suggestedAction}</div>
                    </div>
                    <div>
                      <div className="text-sm font-medium">Impact Potentiel:</div>
                      <div className="text-sm font-semibold text-green-600">{insight.potentialImpact}</div>
                    </div>
                  </div>

                  <div className="flex gap-2">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      Agir Maintenant
                    </Button>
                    <Button size="sm" variant="outline">
                      Programmer
                    </Button>
                    <Button size="sm" variant="outline">
                      Ignorer
                    </Button>
                  </div>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="actions" className="space-y-4">
          <Card className="p-6">
            <div className="text-center py-8">
              <Clock className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Gestionnaire d'Actions</h3>
              <p className="text-muted-foreground mb-4">
                Planification et suivi des actions commerciales automatisées
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Rappels automatiques de suivi</div>
                <div>• Planification des visites commerciales</div>
                <div>• Suivi des engagements clients</div>
                <div>• Alertes de renouvellement</div>
              </div>
              <Button className="mt-4" disabled>
                <Calendar className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="loyalty" className="space-y-4">
          <Card className="p-6">
            <div className="text-center py-8">
              <Award className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Programme de Fidélité IA</h3>
              <p className="text-muted-foreground mb-4">
                Système de fidélisation intelligent basé sur l'analyse comportementale
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Scoring automatique de fidélité</div>
                <div>• Récompenses personnalisées</div>
                <div>• Prédiction du risque de départ</div>
                <div>• Programmes de rétention ciblés</div>
              </div>
              <Button className="mt-4" disabled>
                <Gift className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
