"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Brain,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  Target,
  Zap,
  Clock,
  MapPin,
  Package,
  Users,
  BarChart3,
  Eye,
  Lightbulb,
} from "lucide-react";

interface MarketInsight {
  id: string;
  type: 'demand_surge' | 'supply_shortage' | 'price_opportunity' | 'seasonal_trend' | 'competitive_threat';
  title: string;
  description: string;
  impact: 'low' | 'medium' | 'high' | 'critical';
  confidence: number;
  timeframe: string;
  affectedCategories: string[];
  actionItems: string[];
  potentialRevenue: number;
  pharmaciesAffected: number;
}

interface PredictiveAlert {
  id: string;
  category: string;
  prediction: string;
  probability: number;
  timeframe: string;
  recommendedAction: string;
  potentialImpact: string;
}

interface CompetitorActivity {
  competitor: string;
  activity: string;
  impact: 'positive' | 'negative' | 'neutral';
  region: string;
  timestamp: string;
}

export function MarketIntelligence() {
  const [activeTab, setActiveTab] = useState("insights");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedTimeframe, setSelectedTimeframe] = useState("30d");
  const [insights, setInsights] = useState<MarketInsight[]>([]);
  const [predictiveAlerts, setPredictiveAlerts] = useState<PredictiveAlert[]>([]);
  const [competitorActivity, setCompetitorActivity] = useState<CompetitorActivity[]>([]);

  useEffect(() => {
    loadMarketIntelligence();
  }, [selectedRegion, selectedTimeframe]);

  const loadMarketIntelligence = () => {
    // Mock data - in real implementation, this would come from AI analysis
    const mockInsights: MarketInsight[] = [
      {
        id: '1',
        type: 'demand_surge',
        title: 'Pic de Demande Prévu - Antibiotiques',
        description: 'Analyse prédictive indique une augmentation de 35% de la demande d\'antibiotiques dans la région de Casablanca dans les 2 prochaines semaines.',
        impact: 'high',
        confidence: 87,
        timeframe: '2 semaines',
        affectedCategories: ['Antibiotiques', 'Anti-inflammatoires'],
        actionItems: [
          'Augmenter les stocks d\'amoxicilline de 40%',
          'Contacter 12 pharmacies prioritaires',
          'Préparer campagne promotionnelle ciblée'
        ],
        potentialRevenue: 45000,
        pharmaciesAffected: 23
      },
      {
        id: '2',
        type: 'supply_shortage',
        title: 'Pénurie Imminente - Insuline',
        description: 'Rupture de stock prévue chez le fournisseur principal d\'insuline. Opportunité de repositionnement avec fournisseurs alternatifs.',
        impact: 'critical',
        confidence: 92,
        timeframe: '1 semaine',
        affectedCategories: ['Diabète', 'Endocrinologie'],
        actionItems: [
          'Sécuriser stocks alternatifs immédiatement',
          'Négocier prix préférentiels',
          'Alerter pharmacies partenaires'
        ],
        potentialRevenue: 78000,
        pharmaciesAffected: 45
      },
      {
        id: '3',
        type: 'seasonal_trend',
        title: 'Tendance Saisonnière - Allergies',
        description: 'Début de la saison des allergies avec 3 semaines d\'avance. Demande d\'antihistaminiques en hausse de 28%.',
        impact: 'medium',
        confidence: 79,
        timeframe: '3 semaines',
        affectedCategories: ['Allergologie', 'ORL'],
        actionItems: [
          'Anticiper commandes antihistaminiques',
          'Proposer packs allergie aux pharmacies',
          'Organiser formation produits'
        ],
        potentialRevenue: 32000,
        pharmaciesAffected: 67
      }
    ];

    const mockPredictiveAlerts: PredictiveAlert[] = [
      {
        id: '1',
        category: 'Cardiovasculaire',
        prediction: 'Augmentation demande +22% dans 10 jours',
        probability: 84,
        timeframe: '10 jours',
        recommendedAction: 'Augmenter stocks préventifs',
        potentialImpact: '+15K MAD revenus'
      },
      {
        id: '2',
        category: 'Pédiatrie',
        prediction: 'Pic saisonnier bronchiolite prévu',
        probability: 91,
        timeframe: '2 semaines',
        recommendedAction: 'Préparer stocks sirops enfants',
        potentialImpact: '+28K MAD revenus'
      }
    ];

    const mockCompetitorActivity: CompetitorActivity[] = [
      {
        competitor: 'Pharma Distribution Maroc',
        activity: 'Nouvelle promotion -15% sur gamme diabète',
        impact: 'negative',
        region: 'Casablanca',
        timestamp: 'Il y a 2 heures'
      },
      {
        competitor: 'MediSupply',
        activity: 'Rupture stock antibiotiques majeurs',
        impact: 'positive',
        region: 'Rabat',
        timestamp: 'Il y a 4 heures'
      }
    ];

    setInsights(mockInsights);
    setPredictiveAlerts(mockPredictiveAlerts);
    setCompetitorActivity(mockCompetitorActivity);
  };

  const getImpactColor = (impact: string) => {
    switch (impact) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'demand_surge': return TrendingUp;
      case 'supply_shortage': return AlertTriangle;
      case 'price_opportunity': return Target;
      case 'seasonal_trend': return Clock;
      case 'competitive_threat': return Zap;
      default: return Brain;
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Brain className="w-6 h-6 text-purple-600" />
            Intelligence Marché IA
          </h2>
          <p className="text-muted-foreground">
            Analyses prédictives et insights stratégiques pour optimiser vos opportunités
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedRegion} onValueChange={setSelectedRegion}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes régions</SelectItem>
              <SelectItem value="casablanca">Casablanca</SelectItem>
              <SelectItem value="rabat">Rabat</SelectItem>
              <SelectItem value="marrakech">Marrakech</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedTimeframe} onValueChange={setSelectedTimeframe}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 jours</SelectItem>
              <SelectItem value="30d">30 jours</SelectItem>
              <SelectItem value="90d">90 jours</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Insights IA</h3>
              <p className="text-2xl font-bold text-purple-800">{insights.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <Target className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Opportunités</h3>
              <p className="text-2xl font-bold text-green-800">
                {insights.reduce((sum, i) => sum + i.potentialRevenue, 0).toLocaleString()}
              </p>
              <p className="text-xs text-green-600">MAD potentiels</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Alertes Critiques</h3>
              <p className="text-2xl font-bold text-orange-800">
                {insights.filter(i => i.impact === 'critical').length}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Pharmacies Impactées</h3>
              <p className="text-2xl font-bold text-blue-800">
                {insights.reduce((sum, i) => sum + i.pharmaciesAffected, 0)}
              </p>
            </div>
          </div>
        </Card>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="insights" className="flex items-center gap-2">
            <Lightbulb className="w-4 h-4" />
            Insights IA
          </TabsTrigger>
          <TabsTrigger value="predictions" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Prédictions
          </TabsTrigger>
          <TabsTrigger value="competitors" className="flex items-center gap-2">
            <Eye className="w-4 h-4" />
            Concurrence
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2">
            <BarChart3 className="w-4 h-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        <TabsContent value="insights" className="space-y-4">
          <div className="space-y-4">
            {insights.map((insight) => {
              const TypeIcon = getTypeIcon(insight.type);
              return (
                <Card key={insight.id} className="p-6 border-l-4" style={{
                  borderLeftColor: insight.impact === 'critical' ? '#ef4444' :
                                  insight.impact === 'high' ? '#f97316' :
                                  insight.impact === 'medium' ? '#eab308' : '#3b82f6'
                }}>
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex items-center gap-3">
                      <TypeIcon className="w-6 h-6 text-blue-600" />
                      <div>
                        <h3 className="font-semibold text-lg">{insight.title}</h3>
                        <p className="text-sm text-muted-foreground">
                          Confiance: {insight.confidence}% • {insight.timeframe}
                        </p>
                      </div>
                    </div>
                    <Badge className={getImpactColor(insight.impact)}>
                      {insight.impact.toUpperCase()}
                    </Badge>
                  </div>

                  <p className="text-sm mb-4">{insight.description}</p>

                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                    <div className="flex items-center gap-2">
                      <Package className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <div className="text-sm font-medium">Revenus Potentiels</div>
                        <div className="text-lg font-bold text-green-600">
                          {insight.potentialRevenue.toLocaleString()} MAD
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Users className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <div className="text-sm font-medium">Pharmacies Affectées</div>
                        <div className="text-lg font-bold text-blue-600">
                          {insight.pharmaciesAffected}
                        </div>
                      </div>
                    </div>
                    <div className="flex items-center gap-2">
                      <Clock className="w-4 h-4 text-muted-foreground" />
                      <div>
                        <div className="text-sm font-medium">Fenêtre d'Action</div>
                        <div className="text-lg font-bold text-orange-600">
                          {insight.timeframe}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <div className="text-sm font-medium mb-2">Catégories Affectées:</div>
                      <div className="flex flex-wrap gap-1">
                        {insight.affectedCategories.map((cat, i) => (
                          <Badge key={i} variant="outline" className="text-xs">
                            {cat}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div>
                      <div className="text-sm font-medium mb-2">Actions Recommandées:</div>
                      <ul className="text-sm space-y-1">
                        {insight.actionItems.slice(0, 3).map((action, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <span className="text-blue-600">•</span>
                            <span>{action}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="mt-4 pt-4 border-t flex gap-2">
                    <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                      Agir Maintenant
                    </Button>
                    <Button size="sm" variant="outline">
                      Programmer Rappel
                    </Button>
                    <Button size="sm" variant="outline">
                      Voir Détails
                    </Button>
                  </div>
                </Card>
              );
            })}
          </div>
        </TabsContent>

        <TabsContent value="predictions" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <TrendingUp className="w-5 h-5 text-green-600" />
              Alertes Prédictives
            </h3>
            
            <div className="space-y-4">
              {predictiveAlerts.map((alert) => (
                <div key={alert.id} className="p-4 bg-gray-50 rounded-lg border">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{alert.category}</h4>
                      <p className="text-sm text-muted-foreground">{alert.prediction}</p>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      {alert.probability}% confiance
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div>
                      <span className="font-medium">Délai:</span> {alert.timeframe}
                    </div>
                    <div>
                      <span className="font-medium">Action:</span> {alert.recommendedAction}
                    </div>
                    <div>
                      <span className="font-medium">Impact:</span> {alert.potentialImpact}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="competitors" className="space-y-4">
          <Card className="p-6">
            <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
              <Eye className="w-5 h-5 text-orange-600" />
              Activité Concurrentielle
            </h3>
            
            <div className="space-y-3">
              {competitorActivity.map((activity, index) => (
                <div key={index} className="p-3 bg-gray-50 rounded border-l-4" style={{
                  borderLeftColor: activity.impact === 'positive' ? '#10b981' :
                                  activity.impact === 'negative' ? '#ef4444' : '#6b7280'
                }}>
                  <div className="flex items-start justify-between">
                    <div>
                      <div className="font-medium">{activity.competitor}</div>
                      <div className="text-sm text-muted-foreground">{activity.activity}</div>
                    </div>
                    <div className="text-right">
                      <Badge variant="outline" className={
                        activity.impact === 'positive' ? 'bg-green-50 text-green-700' :
                        activity.impact === 'negative' ? 'bg-red-50 text-red-700' :
                        'bg-gray-50 text-gray-700'
                      }>
                        {activity.impact}
                      </Badge>
                      <div className="text-xs text-muted-foreground mt-1">
                        {activity.region} • {activity.timestamp}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </Card>
        </TabsContent>

        <TabsContent value="analytics" className="space-y-4">
          <Card className="p-6">
            <div className="text-center py-8">
              <BarChart3 className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Analytics Avancées IA</h3>
              <p className="text-muted-foreground mb-4">
                Tableaux de bord interactifs et analyses prédictives détaillées
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Modèles de machine learning pour prédiction de demande</div>
                <div>• Analyse de sentiment des pharmacies partenaires</div>
                <div>• Optimisation des prix basée sur l'IA</div>
                <div>• Scoring prédictif des opportunités</div>
              </div>
              <Button className="mt-4" disabled>
                <Brain className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
