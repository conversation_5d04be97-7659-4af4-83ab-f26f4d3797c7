"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  MapPin,
  Target,
  TrendingUp,
  Zap,
  Route,
  Building,
  Users,
  Package,
  DollarSign,
  Clock,
  AlertTriangle,
  CheckCircle,
  Brain,
  Compass,
  Flag,
} from "lucide-react";

interface TerritoryOptimization {
  region: string;
  currentCoverage: number;
  potentialCoverage: number;
  competitorPresence: number;
  marketSaturation: number;
  growthPotential: number;
  investmentRequired: number;
  expectedROI: number;
  timeToBreakeven: number;
  riskLevel: 'low' | 'medium' | 'high';
  strategicPriority: 'low' | 'medium' | 'high' | 'critical';
  keyOpportunities: string[];
  challenges: string[];
  recommendedActions: string[];
}

interface RouteOptimization {
  route: string;
  currentEfficiency: number;
  optimizedEfficiency: number;
  timeSavings: number;
  costSavings: number;
  pharmaciesVisited: number;
  totalDistance: number;
  fuelCost: number;
  recommendations: string[];
}

interface CompetitorAnalysis {
  competitor: string;
  marketShare: number;
  strengths: string[];
  weaknesses: string[];
  threats: string[];
  opportunities: string[];
  strategicResponse: string;
}

export function StrategicTerritoryOptimization() {
  const [activeTab, setActiveTab] = useState("optimization");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [investmentBudget, setInvestmentBudget] = useState([100000]);
  const [timeHorizon, setTimeHorizon] = useState("12");
  const [territoryData, setTerritoryData] = useState<TerritoryOptimization[]>([]);
  const [routeData, setRouteData] = useState<RouteOptimization[]>([]);
  const [competitorData, setCompetitorData] = useState<CompetitorAnalysis[]>([]);

  useEffect(() => {
    loadOptimizationData();
  }, [selectedRegion, investmentBudget, timeHorizon]);

  const loadOptimizationData = () => {
    // Mock data - in real implementation, this would come from AI optimization algorithms
    const mockTerritoryData: TerritoryOptimization[] = [
      {
        region: 'Casablanca-Settat',
        currentCoverage: 85,
        potentialCoverage: 95,
        competitorPresence: 70,
        marketSaturation: 80,
        growthPotential: 85,
        investmentRequired: 75000,
        expectedROI: 145,
        timeToBreakeven: 8,
        riskLevel: 'low',
        strategicPriority: 'high',
        keyOpportunities: [
          'Expansion vers zones périphériques',
          'Partenariats avec nouvelles pharmacies',
          'Optimisation logistique'
        ],
        challenges: [
          'Concurrence établie',
          'Coûts immobiliers élevés'
        ],
        recommendedActions: [
          'Investir dans 3 nouveaux points de distribution',
          'Recruter 2 commerciaux spécialisés',
          'Lancer programme de fidélisation renforcé'
        ]
      },
      {
        region: 'Marrakech-Safi',
        currentCoverage: 45,
        potentialCoverage: 75,
        competitorPresence: 40,
        marketSaturation: 50,
        growthPotential: 95,
        investmentRequired: 120000,
        expectedROI: 180,
        timeToBreakeven: 12,
        riskLevel: 'medium',
        strategicPriority: 'critical',
        keyOpportunities: [
          'Marché sous-exploité',
          'Faible présence concurrentielle',
          'Croissance démographique forte'
        ],
        challenges: [
          'Distance logistique',
          'Habitudes d\'achat locales',
          'Réseau de distribution à construire'
        ],
        recommendedActions: [
          'Établir hub logistique régional',
          'Recruter équipe commerciale locale',
          'Partenariats avec distributeurs locaux'
        ]
      },
      {
        region: 'Fès-Meknès',
        currentCoverage: 35,
        potentialCoverage: 65,
        competitorPresence: 60,
        marketSaturation: 65,
        growthPotential: 70,
        investmentRequired: 90000,
        expectedROI: 125,
        timeToBreakeven: 15,
        riskLevel: 'high',
        strategicPriority: 'medium',
        keyOpportunities: [
          'Universités et centres de santé',
          'Tourisme médical',
          'Spécialisation produits traditionnels'
        ],
        challenges: [
          'Concurrence historique forte',
          'Préférences pour fournisseurs locaux',
          'Marges réduites'
        ],
        recommendedActions: [
          'Stratégie de niche sur produits spécialisés',
          'Partenariats avec institutions médicales',
          'Programme de formation continue'
        ]
      }
    ];

    const mockRouteData: RouteOptimization[] = [
      {
        route: 'Casablanca Nord',
        currentEfficiency: 65,
        optimizedEfficiency: 85,
        timeSavings: 2.5,
        costSavings: 1200,
        pharmaciesVisited: 12,
        totalDistance: 45,
        fuelCost: 180,
        recommendations: [
          'Réorganiser ordre des visites',
          'Grouper livraisons par zone',
          'Utiliser GPS optimisé'
        ]
      },
      {
        route: 'Rabat-Salé',
        currentEfficiency: 70,
        optimizedEfficiency: 90,
        timeSavings: 3.2,
        costSavings: 1800,
        pharmaciesVisited: 15,
        totalDistance: 52,
        fuelCost: 210,
        recommendations: [
          'Nouveau point de départ optimal',
          'Éviter heures de pointe',
          'Livraisons groupées mercredi'
        ]
      }
    ];

    const mockCompetitorData: CompetitorAnalysis[] = [
      {
        competitor: 'Pharma Distribution Maroc',
        marketShare: 35,
        strengths: ['Réseau établi', 'Prix compétitifs', 'Logistique efficace'],
        weaknesses: ['Service client limité', 'Innovation lente'],
        threats: ['Expansion agressive', 'Guerre des prix'],
        opportunities: ['Digitalisation', 'Services à valeur ajoutée'],
        strategicResponse: 'Différenciation par le service et l\'innovation'
      },
      {
        competitor: 'MediSupply',
        marketShare: 25,
        strengths: ['Spécialisation', 'Relations privilégiées'],
        weaknesses: ['Couverture limitée', 'Capacité financière'],
        threats: ['Niche market capture'],
        opportunities: ['Partenariats', 'Acquisition'],
        strategicResponse: 'Surveillance et contre-offensive ciblée'
      }
    ];

    setTerritoryData(mockTerritoryData);
    setRouteData(mockRouteData);
    setCompetitorData(mockCompetitorData);
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  const totalInvestment = territoryData.reduce((sum, t) => sum + t.investmentRequired, 0);
  const avgROI = territoryData.reduce((sum, t) => sum + t.expectedROI, 0) / territoryData.length;
  const totalPotentialGrowth = territoryData.reduce((sum, t) => sum + (t.potentialCoverage - t.currentCoverage), 0);

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Compass className="w-6 h-6 text-blue-600" />
            Optimisation Territoriale Stratégique
          </h2>
          <p className="text-muted-foreground">
            Intelligence artificielle pour l'expansion et l'optimisation des territoires
          </p>
        </div>
        <div className="flex gap-2">
          <Select value={selectedRegion} onValueChange={setSelectedRegion}>
            <SelectTrigger className="w-40">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes régions</SelectItem>
              <SelectItem value="casablanca">Casablanca-Settat</SelectItem>
              <SelectItem value="marrakech">Marrakech-Safi</SelectItem>
              <SelectItem value="fes">Fès-Meknès</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeHorizon} onValueChange={setTimeHorizon}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="6">6 mois</SelectItem>
              <SelectItem value="12">12 mois</SelectItem>
              <SelectItem value="24">24 mois</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Strategic KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Investissement Total</h3>
              <p className="text-2xl font-bold text-blue-800">
                {(totalInvestment / 1000).toFixed(0)}K MAD
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">ROI Moyen</h3>
              <p className="text-2xl font-bold text-green-800">{avgROI.toFixed(0)}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <Target className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Croissance Potentielle</h3>
              <p className="text-2xl font-bold text-orange-800">+{totalPotentialGrowth.toFixed(0)}%</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Brain className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">IA Optimisation</h3>
              <p className="text-2xl font-bold text-purple-800">94%</p>
              <p className="text-xs text-purple-600">Précision</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Investment Budget Slider */}
      <Card className="p-4">
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <DollarSign className="w-4 h-4 text-muted-foreground" />
            <span className="font-medium">Budget d'investissement:</span>
          </div>
          <div className="flex-1 max-w-md">
            <Slider
              value={investmentBudget}
              onValueChange={setInvestmentBudget}
              max={500000}
              min={50000}
              step={25000}
              className="w-full"
            />
          </div>
          <span className="text-sm font-medium">{investmentBudget[0].toLocaleString()} MAD</span>
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="optimization" className="flex items-center gap-2">
            <Target className="w-4 h-4" />
            Optimisation
          </TabsTrigger>
          <TabsTrigger value="routes" className="flex items-center gap-2">
            <Route className="w-4 h-4" />
            Routes
          </TabsTrigger>
          <TabsTrigger value="competitors" className="flex items-center gap-2">
            <Zap className="w-4 h-4" />
            Concurrence
          </TabsTrigger>
          <TabsTrigger value="strategy" className="flex items-center gap-2">
            <Flag className="w-4 h-4" />
            Stratégie
          </TabsTrigger>
        </TabsList>

        <TabsContent value="optimization" className="space-y-4">
          <div className="space-y-4">
            {territoryData.map((territory, index) => (
              <Card key={index} className="p-6 border-l-4" style={{
                borderLeftColor: territory.strategicPriority === 'critical' ? '#ef4444' :
                                territory.strategicPriority === 'high' ? '#f97316' :
                                territory.strategicPriority === 'medium' ? '#eab308' : '#3b82f6'
              }}>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <MapPin className="w-6 h-6 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{territory.region}</h3>
                      <p className="text-sm text-muted-foreground">
                        Couverture: {territory.currentCoverage}% → {territory.potentialCoverage}%
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getPriorityColor(territory.strategicPriority)}>
                      {territory.strategicPriority.toUpperCase()}
                    </Badge>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">{territory.expectedROI}%</div>
                      <div className="text-xs text-muted-foreground">ROI</div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Investissement</div>
                    <div className="font-semibold">{territory.investmentRequired.toLocaleString()} MAD</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Retour sur Investissement</div>
                    <div className="font-semibold">{territory.timeToBreakeven} mois</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Potentiel de Croissance</div>
                    <div className="font-semibold">{territory.growthPotential}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Niveau de Risque</div>
                    <div className={`font-semibold ${getRiskColor(territory.riskLevel)}`}>
                      {territory.riskLevel.toUpperCase()}
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                  <div>
                    <div className="text-sm font-medium mb-2">Opportunités Clés:</div>
                    <ul className="text-sm space-y-1">
                      {territory.keyOpportunities.map((opp, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <CheckCircle className="w-3 h-3 text-green-600 mt-0.5" />
                          <span>{opp}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium mb-2">Défis:</div>
                    <ul className="text-sm space-y-1">
                      {territory.challenges.map((challenge, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <AlertTriangle className="w-3 h-3 text-orange-600 mt-0.5" />
                          <span>{challenge}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                  
                  <div>
                    <div className="text-sm font-medium mb-2">Actions Recommandées:</div>
                    <ul className="text-sm space-y-1">
                      {territory.recommendedActions.map((action, i) => (
                        <li key={i} className="flex items-start gap-2">
                          <Target className="w-3 h-3 text-blue-600 mt-0.5" />
                          <span>{action}</span>
                        </li>
                      ))}
                    </ul>
                  </div>
                </div>

                <div className="flex gap-2 pt-4 border-t">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    <Flag className="w-4 h-4 mr-1" />
                    Lancer Stratégie
                  </Button>
                  <Button size="sm" variant="outline">
                    <Brain className="w-4 h-4 mr-1" />
                    Analyse Détaillée
                  </Button>
                  <Button size="sm" variant="outline">
                    <Clock className="w-4 h-4 mr-1" />
                    Planifier
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="routes" className="space-y-4">
          <div className="space-y-4">
            {routeData.map((route, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Route className="w-6 h-6 text-green-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{route.route}</h3>
                      <p className="text-sm text-muted-foreground">
                        Efficacité: {route.currentEfficiency}% → {route.optimizedEfficiency}%
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-green-600">
                      -{route.costSavings.toLocaleString()} MAD
                    </div>
                    <div className="text-xs text-muted-foreground">Économies/mois</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Pharmacies Visitées</div>
                    <div className="font-semibold">{route.pharmaciesVisited}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Distance Totale</div>
                    <div className="font-semibold">{route.totalDistance} km</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Gain de Temps</div>
                    <div className="font-semibold">{route.timeSavings}h/jour</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Coût Carburant</div>
                    <div className="font-semibold">{route.fuelCost} MAD</div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-sm font-medium mb-2">Recommandations d'Optimisation:</div>
                  <ul className="text-sm space-y-1">
                    {route.recommendations.map((rec, i) => (
                      <li key={i} className="flex items-start gap-2">
                        <CheckCircle className="w-3 h-3 text-green-600 mt-0.5" />
                        <span>{rec}</span>
                      </li>
                    ))}
                  </ul>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="bg-green-600 hover:bg-green-700">
                    <Route className="w-4 h-4 mr-1" />
                    Appliquer Optimisation
                  </Button>
                  <Button size="sm" variant="outline">
                    <MapPin className="w-4 h-4 mr-1" />
                    Voir Carte
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="competitors" className="space-y-4">
          <div className="space-y-4">
            {competitorData.map((competitor, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Zap className="w-6 h-6 text-orange-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{competitor.competitor}</h3>
                      <p className="text-sm text-muted-foreground">
                        Part de marché: {competitor.marketShare}%
                      </p>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-4">
                  <div className="space-y-4">
                    <div>
                      <div className="text-sm font-medium mb-2 text-green-600">Forces:</div>
                      <ul className="text-sm space-y-1">
                        {competitor.strengths.map((strength, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <CheckCircle className="w-3 h-3 text-green-600 mt-0.5" />
                            <span>{strength}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <div className="text-sm font-medium mb-2 text-blue-600">Opportunités:</div>
                      <ul className="text-sm space-y-1">
                        {competitor.opportunities.map((opp, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <Target className="w-3 h-3 text-blue-600 mt-0.5" />
                            <span>{opp}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <div className="text-sm font-medium mb-2 text-red-600">Faiblesses:</div>
                      <ul className="text-sm space-y-1">
                        {competitor.weaknesses.map((weakness, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <AlertTriangle className="w-3 h-3 text-red-600 mt-0.5" />
                            <span>{weakness}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <div className="text-sm font-medium mb-2 text-orange-600">Menaces:</div>
                      <ul className="text-sm space-y-1">
                        {competitor.threats.map((threat, i) => (
                          <li key={i} className="flex items-start gap-2">
                            <Zap className="w-3 h-3 text-orange-600 mt-0.5" />
                            <span>{threat}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  </div>
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <div className="text-sm font-medium mb-2">Réponse Stratégique Recommandée:</div>
                  <div className="text-sm">{competitor.strategicResponse}</div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="strategy" className="space-y-4">
          <Card className="p-6">
            <div className="text-center py-8">
              <Flag className="w-12 h-12 mx-auto text-muted-foreground/50 mb-4" />
              <h3 className="text-lg font-semibold mb-2">Planificateur Stratégique IA</h3>
              <p className="text-muted-foreground mb-4">
                Génération automatique de plans stratégiques basés sur l'analyse de données
              </p>
              <div className="space-y-2 text-sm text-muted-foreground">
                <div>• Plans d'expansion territoriaux personnalisés</div>
                <div>• Modélisation de scénarios d'investissement</div>
                <div>• Optimisation multi-objectifs (ROI, risque, temps)</div>
                <div>• Simulation de réponses concurrentielles</div>
              </div>
              <Button className="mt-4" disabled>
                <Brain className="w-4 h-4 mr-2" />
                En cours de développement
              </Button>
            </div>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
