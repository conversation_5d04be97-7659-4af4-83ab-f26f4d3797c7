"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import {
  Package,
  TrendingUp,
  AlertTriangle,
  Clock,
  MapPin,
  DollarSign,
  Users,
  Zap,
  Target,
  ShoppingCart,
  Calendar,
  Filter,
  Eye,
  Phone,
} from "lucide-react";

interface ExcessInventoryOpportunity {
  id: string;
  pharmacyName: string;
  pharmacyCity: string;
  medicationName: string;
  category: string;
  quantity: number;
  expiryDate: string;
  daysUntilExpiry: number;
  originalPrice: number;
  discountedPrice: number;
  potentialProfit: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  contactInfo: string;
}

interface DemandPattern {
  category: string;
  region: string;
  requestCount: number;
  avgQuantity: number;
  trendDirection: 'up' | 'down' | 'stable';
  trendPercentage: number;
  topMedications: string[];
  peakDemandTime: string;
}

interface BulkOpportunity {
  id: string;
  medicationName: string;
  category: string;
  totalQuantityNeeded: number;
  pharmaciesCount: number;
  regions: string[];
  avgPriceOffered: number;
  potentialRevenue: number;
  deadline: string;
  coordinationComplexity: 'low' | 'medium' | 'high';
}

export function MarketplaceIntelligence() {
  const [activeTab, setActiveTab] = useState("excess");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [timeRange, setTimeRange] = useState("7d");
  const [searchTerm, setSearchTerm] = useState("");

  const [excessOpportunities, setExcessOpportunities] = useState<ExcessInventoryOpportunity[]>([]);
  const [demandPatterns, setDemandPatterns] = useState<DemandPattern[]>([]);
  const [bulkOpportunities, setBulkOpportunities] = useState<BulkOpportunity[]>([]);

  useEffect(() => {
    loadMarketplaceData();
  }, [selectedRegion, selectedCategory, timeRange]);

  const loadMarketplaceData = () => {
    // Mock data - in real implementation, this would come from your marketplace API
    const mockExcessOpportunities: ExcessInventoryOpportunity[] = [
      {
        id: '1',
        pharmacyName: 'Pharmacie Al Andalous',
        pharmacyCity: 'Casablanca',
        medicationName: 'Amoxicilline 500mg',
        category: 'Antibiotiques',
        quantity: 150,
        expiryDate: '2024-03-15',
        daysUntilExpiry: 45,
        originalPrice: 12.50,
        discountedPrice: 8.75,
        potentialProfit: 562.50,
        urgencyLevel: 'medium',
        contactInfo: '+212522123456'
      },
      {
        id: '2',
        pharmacyName: 'Pharmacie Centrale',
        pharmacyCity: 'Rabat',
        medicationName: 'Paracétamol 1g',
        category: 'Antalgiques',
        quantity: 300,
        expiryDate: '2024-02-28',
        daysUntilExpiry: 28,
        originalPrice: 5.20,
        discountedPrice: 3.50,
        potentialProfit: 510.00,
        urgencyLevel: 'high',
        contactInfo: '+212537987654'
      },
      {
        id: '3',
        pharmacyName: 'Pharmacie du Quartier',
        pharmacyCity: 'Marrakech',
        medicationName: 'Insuline Lantus',
        category: 'Diabète',
        quantity: 25,
        expiryDate: '2024-02-15',
        daysUntilExpiry: 15,
        originalPrice: 145.00,
        discountedPrice: 95.00,
        potentialProfit: 1250.00,
        urgencyLevel: 'critical',
        contactInfo: '+212524456789'
      }
    ];

    const mockDemandPatterns: DemandPattern[] = [
      {
        category: 'Antibiotiques',
        region: 'Casablanca-Settat',
        requestCount: 45,
        avgQuantity: 85,
        trendDirection: 'up',
        trendPercentage: 23,
        topMedications: ['Amoxicilline', 'Azithromycine', 'Cefixime'],
        peakDemandTime: 'Lundi matin'
      },
      {
        category: 'Antalgiques',
        region: 'Rabat-Salé-Kénitra',
        requestCount: 67,
        avgQuantity: 120,
        trendDirection: 'up',
        trendPercentage: 15,
        topMedications: ['Paracétamol', 'Ibuprofène', 'Aspirine'],
        peakDemandTime: 'Vendredi après-midi'
      },
      {
        category: 'Cardiovasculaire',
        region: 'Marrakech-Safi',
        requestCount: 28,
        avgQuantity: 45,
        trendDirection: 'stable',
        trendPercentage: 2,
        topMedications: ['Amlodipine', 'Enalapril', 'Atorvastatine'],
        peakDemandTime: 'Mercredi matin'
      }
    ];

    const mockBulkOpportunities: BulkOpportunity[] = [
      {
        id: '1',
        medicationName: 'Vaccin Grippe Saisonnière',
        category: 'Vaccins',
        totalQuantityNeeded: 500,
        pharmaciesCount: 12,
        regions: ['Casablanca', 'Rabat', 'Salé'],
        avgPriceOffered: 85.00,
        potentialRevenue: 42500,
        deadline: '2024-02-20',
        coordinationComplexity: 'medium'
      },
      {
        id: '2',
        medicationName: 'Masques Chirurgicaux',
        category: 'Dispositifs Médicaux',
        totalQuantityNeeded: 2000,
        pharmaciesCount: 8,
        regions: ['Marrakech', 'Agadir'],
        avgPriceOffered: 2.50,
        potentialRevenue: 5000,
        deadline: '2024-02-10',
        coordinationComplexity: 'low'
      }
    ];

    setExcessOpportunities(mockExcessOpportunities);
    setDemandPatterns(mockDemandPatterns);
    setBulkOpportunities(mockBulkOpportunities);
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full"></div>;
    }
  };

  const filteredExcessOpportunities = excessOpportunities.filter(opp => {
    const matchesRegion = selectedRegion === 'all' || opp.pharmacyCity.toLowerCase().includes(selectedRegion.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || opp.category === selectedCategory;
    const matchesSearch = searchTerm === '' || 
      opp.medicationName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      opp.pharmacyName.toLowerCase().includes(searchTerm.toLowerCase());
    
    return matchesRegion && matchesCategory && matchesSearch;
  });

  const totalPotentialProfit = filteredExcessOpportunities.reduce((sum, opp) => sum + opp.potentialProfit, 0);
  const criticalOpportunities = filteredExcessOpportunities.filter(opp => opp.urgencyLevel === 'critical').length;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Package className="w-6 h-6 text-blue-600" />
            Intelligence Marketplace
          </h2>
          <p className="text-muted-foreground">
            Opportunités d'achat, patterns de demande et sourcing en gros
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Eye className="w-4 h-4 mr-2" />
            Voir Carte
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Target className="w-4 h-4 mr-2" />
            Nouvelles Opportunités
          </Button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Profit Potentiel</h3>
              <p className="text-2xl font-bold text-green-800">
                {totalPotentialProfit.toLocaleString()} MAD
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <AlertTriangle className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Opportunités Critiques</h3>
              <p className="text-2xl font-bold text-orange-800">{criticalOpportunities}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Package className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Stock Excédentaire</h3>
              <p className="text-2xl font-bold text-blue-800">{filteredExcessOpportunities.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Pharmacies Actives</h3>
              <p className="text-2xl font-bold text-purple-800">
                {new Set(filteredExcessOpportunities.map(o => o.pharmacyName)).size}
              </p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-wrap gap-4">
          <div className="flex-1 min-w-[200px]">
            <Input
              placeholder="Rechercher médicament ou pharmacie..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <Select value={selectedRegion} onValueChange={setSelectedRegion}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Région" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes régions</SelectItem>
              <SelectItem value="casablanca">Casablanca</SelectItem>
              <SelectItem value="rabat">Rabat</SelectItem>
              <SelectItem value="marrakech">Marrakech</SelectItem>
              <SelectItem value="fes">Fès</SelectItem>
            </SelectContent>
          </Select>
          <Select value={selectedCategory} onValueChange={setSelectedCategory}>
            <SelectTrigger className="w-40">
              <SelectValue placeholder="Catégorie" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Toutes catégories</SelectItem>
              <SelectItem value="Antibiotiques">Antibiotiques</SelectItem>
              <SelectItem value="Antalgiques">Antalgiques</SelectItem>
              <SelectItem value="Diabète">Diabète</SelectItem>
              <SelectItem value="Cardiovasculaire">Cardiovasculaire</SelectItem>
            </SelectContent>
          </Select>
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue placeholder="Période" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">7 jours</SelectItem>
              <SelectItem value="30d">30 jours</SelectItem>
              <SelectItem value="90d">90 jours</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="excess" className="flex items-center gap-2">
            <Package className="w-4 h-4" />
            Stock Excédentaire
          </TabsTrigger>
          <TabsTrigger value="demand" className="flex items-center gap-2">
            <TrendingUp className="w-4 h-4" />
            Patterns Demande
          </TabsTrigger>
          <TabsTrigger value="bulk" className="flex items-center gap-2">
            <ShoppingCart className="w-4 h-4" />
            Opportunités Gros
          </TabsTrigger>
        </TabsList>

        <TabsContent value="excess" className="space-y-4">
          <div className="space-y-4">
            {filteredExcessOpportunities.map((opportunity) => (
              <Card key={opportunity.id} className="p-6 border-l-4" style={{
                borderLeftColor: opportunity.urgencyLevel === 'critical' ? '#ef4444' :
                                opportunity.urgencyLevel === 'high' ? '#f97316' :
                                opportunity.urgencyLevel === 'medium' ? '#eab308' : '#3b82f6'
              }}>
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <Package className="w-6 h-6 text-blue-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{opportunity.medicationName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {opportunity.pharmacyName} • {opportunity.pharmacyCity}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getUrgencyColor(opportunity.urgencyLevel)}>
                      {opportunity.urgencyLevel.toUpperCase()}
                    </Badge>
                    <div className="text-right">
                      <div className="text-lg font-bold text-green-600">
                        +{opportunity.potentialProfit.toLocaleString()} MAD
                      </div>
                      <div className="text-xs text-muted-foreground">Profit potentiel</div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Quantité</div>
                    <div className="font-semibold">{opportunity.quantity} unités</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Prix Original</div>
                    <div className="font-semibold">{opportunity.originalPrice} MAD</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Prix Réduit</div>
                    <div className="font-semibold text-green-600">{opportunity.discountedPrice} MAD</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Expiration</div>
                    <div className="font-semibold text-orange-600">{opportunity.daysUntilExpiry} jours</div>
                  </div>
                </div>

                <div className="flex items-center justify-between pt-4 border-t">
                  <div className="flex items-center gap-2">
                    <Badge variant="outline">{opportunity.category}</Badge>
                    <span className="text-sm text-muted-foreground">
                      Expire le {opportunity.expiryDate}
                    </span>
                  </div>
                  <div className="flex gap-2">
                    <Button size="sm" variant="outline">
                      <Phone className="w-4 h-4 mr-1" />
                      Contacter
                    </Button>
                    <Button size="sm" className="bg-green-600 hover:bg-green-700">
                      <ShoppingCart className="w-4 h-4 mr-1" />
                      Négocier
                    </Button>
                  </div>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="demand" className="space-y-4">
          <div className="space-y-4">
            {demandPatterns.map((pattern, index) => (
              <Card key={index} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <TrendingUp className="w-6 h-6 text-green-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{pattern.category}</h3>
                      <p className="text-sm text-muted-foreground">{pattern.region}</p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    {getTrendIcon(pattern.trendDirection)}
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">
                        {pattern.requestCount} demandes
                      </div>
                      <div className="text-xs text-muted-foreground">Cette semaine</div>
                    </div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Quantité Moyenne</div>
                    <div className="font-semibold">{pattern.avgQuantity} unités</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Tendance</div>
                    <div className={`font-semibold ${
                      pattern.trendDirection === 'up' ? 'text-green-600' : 
                      pattern.trendDirection === 'down' ? 'text-red-600' : 'text-gray-600'
                    }`}>
                      {pattern.trendDirection === 'up' ? '+' : pattern.trendDirection === 'down' ? '-' : ''}
                      {pattern.trendPercentage}%
                    </div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Pic de Demande</div>
                    <div className="font-semibold">{pattern.peakDemandTime}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Région</div>
                    <div className="font-semibold">{pattern.region}</div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-sm font-medium mb-2">Médicaments les plus demandés:</div>
                  <div className="flex flex-wrap gap-1">
                    {pattern.topMedications.map((med, i) => (
                      <Badge key={i} variant="outline" className="text-xs">
                        {med}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="bg-blue-600 hover:bg-blue-700">
                    <Target className="w-4 h-4 mr-1" />
                    Analyser Opportunité
                  </Button>
                  <Button size="sm" variant="outline">
                    <MapPin className="w-4 h-4 mr-1" />
                    Voir sur Carte
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="bulk" className="space-y-4">
          <div className="space-y-4">
            {bulkOpportunities.map((bulk) => (
              <Card key={bulk.id} className="p-6">
                <div className="flex items-start justify-between mb-4">
                  <div className="flex items-center gap-3">
                    <ShoppingCart className="w-6 h-6 text-purple-600" />
                    <div>
                      <h3 className="font-semibold text-lg">{bulk.medicationName}</h3>
                      <p className="text-sm text-muted-foreground">
                        {bulk.pharmaciesCount} pharmacies • {bulk.regions.join(', ')}
                      </p>
                    </div>
                  </div>
                  <div className="text-right">
                    <div className="text-lg font-bold text-purple-600">
                      {bulk.potentialRevenue.toLocaleString()} MAD
                    </div>
                    <div className="text-xs text-muted-foreground">Revenus potentiels</div>
                  </div>
                </div>

                <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                  <div>
                    <div className="text-sm text-muted-foreground">Quantité Totale</div>
                    <div className="font-semibold">{bulk.totalQuantityNeeded} unités</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Prix Moyen Offert</div>
                    <div className="font-semibold">{bulk.avgPriceOffered} MAD</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Échéance</div>
                    <div className="font-semibold text-orange-600">{bulk.deadline}</div>
                  </div>
                  <div>
                    <div className="text-sm text-muted-foreground">Complexité</div>
                    <div className={`font-semibold ${
                      bulk.coordinationComplexity === 'low' ? 'text-green-600' :
                      bulk.coordinationComplexity === 'medium' ? 'text-yellow-600' : 'text-red-600'
                    }`}>
                      {bulk.coordinationComplexity.toUpperCase()}
                    </div>
                  </div>
                </div>

                <div className="mb-4">
                  <div className="text-sm font-medium mb-2">Régions concernées:</div>
                  <div className="flex flex-wrap gap-1">
                    {bulk.regions.map((region, i) => (
                      <Badge key={i} variant="outline" className="text-xs">
                        {region}
                      </Badge>
                    ))}
                  </div>
                </div>

                <div className="flex gap-2">
                  <Button size="sm" className="bg-purple-600 hover:bg-purple-700">
                    <Target className="w-4 h-4 mr-1" />
                    Coordonner Achat
                  </Button>
                  <Button size="sm" variant="outline">
                    <Users className="w-4 h-4 mr-1" />
                    Voir Pharmacies
                  </Button>
                  <Button size="sm" variant="outline">
                    <Calendar className="w-4 h-4 mr-1" />
                    Planifier
                  </Button>
                </div>
              </Card>
            ))}
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}
