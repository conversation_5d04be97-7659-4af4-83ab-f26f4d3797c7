"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Slider } from "@/components/ui/slider";
import {
  MapPin,
  Users,
  Package,
  Clock,
  Phone,
  MessageSquare,
  Navigation,
  Filter,
  Search,
  AlertTriangle,
  CheckCircle,
  Truck,
} from "lucide-react";
import { BaseMap } from "./base-map";

interface NearbyPharmacy {
  id: string;
  name: string;
  owner: string;
  address: string;
  city: string;
  latitude: number;
  longitude: number;
  distance: number; // in km
  phone: string;
  email: string;
  partnershipStatus: 'partner' | 'contacted' | 'new' | 'blocked';
  stockStatus: 'high' | 'medium' | 'low' | 'critical';
  urgentRequests: number;
  availableCategories: string[];
  lastActivity: string;
  responseTime: number; // in hours
  reliabilityScore: number; // 0-100
  estimatedDeliveryTime: number; // in hours
}

interface UrgentRequest {
  id: string;
  pharmacyId: string;
  pharmacyName: string;
  medicationName: string;
  category: string;
  quantity: number;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
  maxDistance: number;
  postedTime: string;
  expiresIn: number; // hours
  latitude: number;
  longitude: number;
}

export function PharmacistNetworkMap() {
  const [activeTab, setActiveTab] = useState("network");
  const [searchRadius, setSearchRadius] = useState([25]); // km
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [partnershipFilter, setPartnershipFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedPharmacy, setSelectedPharmacy] = useState<NearbyPharmacy | null>(null);

  const [nearbyPharmacies, setNearbyPharmacies] = useState<NearbyPharmacy[]>([]);
  const [urgentRequests, setUrgentRequests] = useState<UrgentRequest[]>([]);
  const [userLocation] = useState<[number, number]>([-7.6167, 33.5833]); // Mock user location

  useEffect(() => {
    loadNetworkData();
  }, [searchRadius, selectedCategory, partnershipFilter]);

  const loadNetworkData = () => {
    // Mock data - in real implementation, this would come from your API
    const mockPharmacies: NearbyPharmacy[] = [
      {
        id: '1',
        name: 'Pharmacie Al Andalous',
        owner: 'Dr. Amina Benali',
        address: '123 Rue Mohammed V',
        city: 'Casablanca',
        latitude: 33.5731,
        longitude: -7.5898,
        distance: 2.3,
        phone: '+212522123456',
        email: '<EMAIL>',
        partnershipStatus: 'partner',
        stockStatus: 'high',
        urgentRequests: 0,
        availableCategories: ['Antibiotiques', 'Antalgiques', 'Cardiovasculaire'],
        lastActivity: 'Il y a 2 heures',
        responseTime: 1.5,
        reliabilityScore: 95,
        estimatedDeliveryTime: 0.5
      },
      {
        id: '2',
        name: 'Pharmacie Centrale',
        owner: 'Dr. Mohamed Alami',
        address: '456 Avenue Hassan II',
        city: 'Casablanca',
        latitude: 33.5650,
        longitude: -7.6030,
        distance: 4.7,
        phone: '+212522987654',
        email: '<EMAIL>',
        partnershipStatus: 'contacted',
        stockStatus: 'medium',
        urgentRequests: 2,
        availableCategories: ['Diabète', 'Respiratoire', 'Dermatologie'],
        lastActivity: 'Il y a 1 jour',
        responseTime: 3.2,
        reliabilityScore: 87,
        estimatedDeliveryTime: 1.0
      },
      {
        id: '3',
        name: 'Pharmacie du Quartier',
        owner: 'Dr. Fatima Zahra',
        address: '789 Rue Zerktouni',
        city: 'Casablanca',
        latitude: 33.5500,
        longitude: -7.6200,
        distance: 8.1,
        phone: '+212522456789',
        email: '<EMAIL>',
        partnershipStatus: 'new',
        stockStatus: 'low',
        urgentRequests: 1,
        availableCategories: ['Généraliste', 'OTC'],
        lastActivity: 'Il y a 3 jours',
        responseTime: 6.0,
        reliabilityScore: 72,
        estimatedDeliveryTime: 2.0
      }
    ];

    const mockUrgentRequests: UrgentRequest[] = [
      {
        id: '1',
        pharmacyId: '2',
        pharmacyName: 'Pharmacie Centrale',
        medicationName: 'Insuline Lantus',
        category: 'Diabète',
        quantity: 5,
        urgencyLevel: 'critical',
        maxDistance: 10,
        postedTime: 'Il y a 30 min',
        expiresIn: 2,
        latitude: 33.5650,
        longitude: -7.6030
      },
      {
        id: '2',
        pharmacyId: '3',
        pharmacyName: 'Pharmacie du Quartier',
        medicationName: 'Ventoline',
        category: 'Respiratoire',
        quantity: 3,
        urgencyLevel: 'high',
        maxDistance: 15,
        postedTime: 'Il y a 1 heure',
        expiresIn: 4,
        latitude: 33.5500,
        longitude: -7.6200
      }
    ];

    setNearbyPharmacies(mockPharmacies.filter(p => p.distance <= searchRadius[0]));
    setUrgentRequests(mockUrgentRequests);
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'partner': return 'bg-green-100 text-green-800 border-green-200';
      case 'contacted': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'new': return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'blocked': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStockColor = (status: string) => {
    switch (status) {
      case 'high': return '#10b981'; // green
      case 'medium': return '#f59e0b'; // amber
      case 'low': return '#f97316'; // orange
      case 'critical': return '#ef4444'; // red
      default: return '#6b7280'; // gray
    }
  };

  const getUrgencyColor = (urgency: string) => {
    switch (urgency) {
      case 'critical': return 'bg-red-100 text-red-800 border-red-200';
      case 'high': return 'bg-orange-100 text-orange-800 border-orange-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-blue-100 text-blue-800 border-blue-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  // Convert pharmacies to map markers
  const pharmacyMarkers = nearbyPharmacies.map(pharmacy => ({
    id: pharmacy.id,
    latitude: pharmacy.latitude,
    longitude: pharmacy.longitude,
    type: 'pharmacy' as const,
    data: pharmacy,
    color: getStockColor(pharmacy.stockStatus),
    size: pharmacy.partnershipStatus === 'partner' ? 'large' as const : 'medium' as const,
    priority: pharmacy.urgentRequests > 0 ? 'high' as const : 'medium' as const
  }));

  // Convert urgent requests to map markers
  const urgentMarkers = urgentRequests.map(request => ({
    id: `urgent-${request.id}`,
    latitude: request.latitude,
    longitude: request.longitude,
    type: 'demand' as const,
    data: request,
    color: request.urgencyLevel === 'critical' ? '#ef4444' : '#f97316',
    size: 'large' as const,
    priority: request.urgencyLevel as 'low' | 'medium' | 'high' | 'critical'
  }));

  const allMarkers = [...pharmacyMarkers, ...urgentMarkers];

  const handleMarkerClick = (marker: any) => {
    if (marker.type === 'pharmacy') {
      setSelectedPharmacy(marker.data);
    }
  };

  const filteredPharmacies = nearbyPharmacies.filter(pharmacy => {
    const matchesPartnership = partnershipFilter === 'all' || pharmacy.partnershipStatus === partnershipFilter;
    const matchesSearch = searchTerm === '' || 
      pharmacy.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      pharmacy.owner.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === 'all' || 
      pharmacy.availableCategories.some(cat => cat.toLowerCase().includes(selectedCategory.toLowerCase()));
    
    return matchesPartnership && matchesSearch && matchesCategory;
  });

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <MapPin className="w-6 h-6 text-blue-600" />
            Réseau Pharmacies - Carte Interactive
          </h2>
          <p className="text-muted-foreground">
            Trouvez des pharmacies partenaires et gérez les demandes urgentes dans votre région
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Navigation className="w-4 h-4 mr-2" />
            Ma Position
          </Button>
          <Button className="bg-blue-600 hover:bg-blue-700">
            <Users className="w-4 h-4 mr-2" />
            Nouveau Partenaire
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-5 gap-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Recherche</label>
            <Input
              placeholder="Nom ou propriétaire..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">Rayon (km)</label>
            <div className="px-2">
              <Slider
                value={searchRadius}
                onValueChange={setSearchRadius}
                max={50}
                min={5}
                step={5}
                className="w-full"
              />
              <div className="text-xs text-center mt-1">{searchRadius[0]} km</div>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">Partenariat</label>
            <Select value={partnershipFilter} onValueChange={setPartnershipFilter}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Tous</SelectItem>
                <SelectItem value="partner">Partenaires</SelectItem>
                <SelectItem value="contacted">Contactés</SelectItem>
                <SelectItem value="new">Nouveaux</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">Catégorie</label>
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes</SelectItem>
                <SelectItem value="antibiotiques">Antibiotiques</SelectItem>
                <SelectItem value="diabète">Diabète</SelectItem>
                <SelectItem value="cardiovasculaire">Cardiovasculaire</SelectItem>
                <SelectItem value="respiratoire">Respiratoire</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end">
            <Button variant="outline" className="w-full">
              <Filter className="w-4 h-4 mr-2" />
              Filtrer
            </Button>
          </div>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map */}
        <div className="lg:col-span-2">
          <BaseMap
            markers={allMarkers}
            center={userLocation}
            zoom={12}
            onMarkerClick={handleMarkerClick}
            showControls={true}
            showFilters={true}
            height="600px"
          />
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="network">Réseau</TabsTrigger>
              <TabsTrigger value="urgent">Urgent</TabsTrigger>
            </TabsList>

            <TabsContent value="network" className="space-y-4">
              <div className="space-y-3 max-h-[500px] overflow-y-auto">
                {filteredPharmacies.map((pharmacy) => (
                  <Card 
                    key={pharmacy.id} 
                    className={`p-4 cursor-pointer transition-all hover:shadow-md ${
                      selectedPharmacy?.id === pharmacy.id ? 'ring-2 ring-blue-500' : ''
                    }`}
                    onClick={() => setSelectedPharmacy(pharmacy)}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-semibold">{pharmacy.name}</h4>
                        <p className="text-sm text-muted-foreground">{pharmacy.owner}</p>
                      </div>
                      <Badge className={getStatusColor(pharmacy.partnershipStatus)}>
                        {pharmacy.partnershipStatus}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                      <div className="flex items-center gap-1">
                        <Navigation className="w-3 h-3" />
                        <span>{pharmacy.distance} km</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Clock className="w-3 h-3" />
                        <span>{pharmacy.estimatedDeliveryTime}h</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Package className="w-3 h-3" />
                        <span className={`font-medium ${
                          pharmacy.stockStatus === 'high' ? 'text-green-600' :
                          pharmacy.stockStatus === 'medium' ? 'text-yellow-600' :
                          pharmacy.stockStatus === 'low' ? 'text-orange-600' : 'text-red-600'
                        }`}>
                          {pharmacy.stockStatus}
                        </span>
                      </div>
                      <div className="flex items-center gap-1">
                        <CheckCircle className="w-3 h-3" />
                        <span>{pharmacy.reliabilityScore}%</span>
                      </div>
                    </div>

                    {pharmacy.urgentRequests > 0 && (
                      <div className="flex items-center gap-1 text-red-600 text-sm mb-2">
                        <AlertTriangle className="w-3 h-3" />
                        <span>{pharmacy.urgentRequests} demande(s) urgente(s)</span>
                      </div>
                    )}

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Phone className="w-3 h-3 mr-1" />
                        Appeler
                      </Button>
                      <Button size="sm" className="flex-1 bg-blue-600 hover:bg-blue-700">
                        <MessageSquare className="w-3 h-3 mr-1" />
                        Message
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>

            <TabsContent value="urgent" className="space-y-4">
              <div className="space-y-3 max-h-[500px] overflow-y-auto">
                {urgentRequests.map((request) => (
                  <Card key={request.id} className="p-4 border-l-4 border-red-500">
                    <div className="flex items-start justify-between mb-2">
                      <div>
                        <h4 className="font-semibold">{request.medicationName}</h4>
                        <p className="text-sm text-muted-foreground">{request.pharmacyName}</p>
                      </div>
                      <Badge className={getUrgencyColor(request.urgencyLevel)}>
                        {request.urgencyLevel}
                      </Badge>
                    </div>

                    <div className="grid grid-cols-2 gap-2 text-sm mb-3">
                      <div>
                        <span className="text-muted-foreground">Quantité:</span>
                        <span className="ml-1 font-medium">{request.quantity}</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Expire dans:</span>
                        <span className="ml-1 font-medium text-red-600">{request.expiresIn}h</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Distance max:</span>
                        <span className="ml-1 font-medium">{request.maxDistance} km</span>
                      </div>
                      <div>
                        <span className="text-muted-foreground">Posté:</span>
                        <span className="ml-1 font-medium">{request.postedTime}</span>
                      </div>
                    </div>

                    <div className="flex gap-2">
                      <Button size="sm" variant="outline" className="flex-1">
                        <Navigation className="w-3 h-3 mr-1" />
                        Itinéraire
                      </Button>
                      <Button size="sm" className="flex-1 bg-green-600 hover:bg-green-700">
                        <Truck className="w-3 h-3 mr-1" />
                        Répondre
                      </Button>
                    </div>
                  </Card>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>

      {/* Selected Pharmacy Details */}
      {selectedPharmacy && (
        <Card className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h3 className="text-xl font-semibold">{selectedPharmacy.name}</h3>
              <p className="text-muted-foreground">{selectedPharmacy.address}, {selectedPharmacy.city}</p>
            </div>
            <Button variant="outline" onClick={() => setSelectedPharmacy(null)}>
              Fermer
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
            <div>
              <h4 className="font-medium mb-2">Informations Contact</h4>
              <div className="space-y-2 text-sm">
                <div>Propriétaire: {selectedPharmacy.owner}</div>
                <div>Téléphone: {selectedPharmacy.phone}</div>
                <div>Email: {selectedPharmacy.email}</div>
                <div>Dernière activité: {selectedPharmacy.lastActivity}</div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Performance</h4>
              <div className="space-y-2 text-sm">
                <div>Temps de réponse: {selectedPharmacy.responseTime}h</div>
                <div>Fiabilité: {selectedPharmacy.reliabilityScore}%</div>
                <div>Livraison estimée: {selectedPharmacy.estimatedDeliveryTime}h</div>
                <div>Distance: {selectedPharmacy.distance} km</div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Catégories Disponibles</h4>
              <div className="flex flex-wrap gap-1">
                {selectedPharmacy.availableCategories.map((category, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {category}
                  </Badge>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-2 mt-6">
            <Button className="bg-blue-600 hover:bg-blue-700">
              <Phone className="w-4 h-4 mr-2" />
              Appeler Maintenant
            </Button>
            <Button variant="outline">
              <MessageSquare className="w-4 h-4 mr-2" />
              Envoyer Message
            </Button>
            <Button variant="outline">
              <Navigation className="w-4 h-4 mr-2" />
              Voir Itinéraire
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
