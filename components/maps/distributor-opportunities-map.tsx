"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Slider } from "@/components/ui/slider";
import {
  MapPin,
  Package,
  TrendingUp,
  DollarSign,
  Clock,
  Users,
  Target,
  Zap,
  AlertTriangle,
  ShoppingCart,
  Phone,
  Eye,
  Filter,
  Layers,
} from "lucide-react";
import { BaseMap } from "./base-map";

interface OpportunityCluster {
  id: string;
  region: string;
  latitude: number;
  longitude: number;
  excessInventoryCount: number;
  totalValue: number;
  avgDiscount: number;
  urgentCount: number;
  categories: string[];
  pharmaciesCount: number;
  lastUpdated: string;
}

interface DemandHotspot {
  id: string;
  region: string;
  latitude: number;
  longitude: number;
  requestCount: number;
  avgQuantity: number;
  topCategory: string;
  trendDirection: 'up' | 'down' | 'stable';
  trendPercentage: number;
  unfulfilledRate: number;
  potentialRevenue: number;
}

interface BulkOpportunityZone {
  id: string;
  region: string;
  latitude: number;
  longitude: number;
  medicationName: string;
  totalQuantityNeeded: number;
  pharmaciesInvolved: number;
  avgPriceOffered: number;
  deadline: string;
  coordinationComplexity: 'low' | 'medium' | 'high';
  potentialProfit: number;
}

export function DistributorOpportunitiesMap() {
  const [activeTab, setActiveTab] = useState("excess");
  const [selectedRegion, setSelectedRegion] = useState("all");
  const [valueRange, setValueRange] = useState([10000]); // Minimum opportunity value
  const [timeFilter, setTimeFilter] = useState("24h");
  const [selectedOpportunity, setSelectedOpportunity] = useState<any>(null);

  const [opportunityClusters, setOpportunityClusters] = useState<OpportunityCluster[]>([]);
  const [demandHotspots, setDemandHotspots] = useState<DemandHotspot[]>([]);
  const [bulkZones, setBulkZones] = useState<BulkOpportunityZone[]>([]);

  useEffect(() => {
    loadOpportunityData();
  }, [selectedRegion, valueRange, timeFilter]);

  const loadOpportunityData = () => {
    // Mock data - in real implementation, this would come from marketplace analytics
    const mockClusters: OpportunityCluster[] = [
      {
        id: '1',
        region: 'Casablanca Centre',
        latitude: 33.5731,
        longitude: -7.5898,
        excessInventoryCount: 45,
        totalValue: 125000,
        avgDiscount: 35,
        urgentCount: 12,
        categories: ['Antibiotiques', 'Antalgiques', 'Cardiovasculaire'],
        pharmaciesCount: 8,
        lastUpdated: 'Il y a 15 min'
      },
      {
        id: '2',
        region: 'Rabat Agdal',
        latitude: 33.9716,
        longitude: -6.8498,
        excessInventoryCount: 28,
        totalValue: 78000,
        avgDiscount: 42,
        urgentCount: 8,
        categories: ['Diabète', 'Respiratoire', 'Dermatologie'],
        pharmaciesCount: 5,
        lastUpdated: 'Il y a 32 min'
      },
      {
        id: '3',
        region: 'Marrakech Gueliz',
        latitude: 31.6295,
        longitude: -7.9811,
        excessInventoryCount: 67,
        totalValue: 189000,
        avgDiscount: 28,
        urgentCount: 18,
        categories: ['Généraliste', 'OTC', 'Pédiatrie'],
        pharmaciesCount: 12,
        lastUpdated: 'Il y a 8 min'
      }
    ];

    const mockHotspots: DemandHotspot[] = [
      {
        id: '1',
        region: 'Casablanca Maarif',
        latitude: 33.5650,
        longitude: -7.6030,
        requestCount: 23,
        avgQuantity: 85,
        topCategory: 'Antibiotiques',
        trendDirection: 'up',
        trendPercentage: 34,
        unfulfilledRate: 65,
        potentialRevenue: 45000
      },
      {
        id: '2',
        region: 'Rabat Hassan',
        latitude: 34.0209,
        longitude: -6.8416,
        requestCount: 18,
        avgQuantity: 62,
        topCategory: 'Cardiovasculaire',
        trendDirection: 'up',
        trendPercentage: 28,
        unfulfilledRate: 72,
        potentialRevenue: 38000
      }
    ];

    const mockBulkZones: BulkOpportunityZone[] = [
      {
        id: '1',
        region: 'Zone Nord Casablanca',
        latitude: 33.6000,
        longitude: -7.6167,
        medicationName: 'Vaccin Grippe',
        totalQuantityNeeded: 500,
        pharmaciesInvolved: 12,
        avgPriceOffered: 85,
        deadline: '2024-02-20',
        coordinationComplexity: 'medium',
        potentialProfit: 15000
      },
      {
        id: '2',
        region: 'Axe Rabat-Salé',
        latitude: 34.0000,
        longitude: -6.8000,
        medicationName: 'Masques N95',
        totalQuantityNeeded: 2000,
        pharmaciesInvolved: 8,
        avgPriceOffered: 12,
        deadline: '2024-02-15',
        coordinationComplexity: 'low',
        potentialProfit: 8000
      }
    ];

    setOpportunityClusters(mockClusters);
    setDemandHotspots(mockHotspots);
    setBulkZones(mockBulkZones);
  };

  // Convert data to map markers
  const excessMarkers = opportunityClusters.map(cluster => ({
    id: cluster.id,
    latitude: cluster.latitude,
    longitude: cluster.longitude,
    type: 'opportunity' as const,
    data: cluster,
    color: cluster.urgentCount > 10 ? '#ef4444' : '#10b981',
    size: cluster.totalValue > 100000 ? 'large' as const : 'medium' as const,
    priority: cluster.urgentCount > 10 ? 'high' as const : 'medium' as const
  }));

  const demandMarkers = demandHotspots.map(hotspot => ({
    id: hotspot.id,
    latitude: hotspot.latitude,
    longitude: hotspot.longitude,
    type: 'demand' as const,
    data: hotspot,
    color: hotspot.unfulfilledRate > 60 ? '#f59e0b' : '#3b82f6',
    size: hotspot.potentialRevenue > 40000 ? 'large' as const : 'medium' as const,
    priority: hotspot.unfulfilledRate > 60 ? 'high' as const : 'medium' as const
  }));

  const bulkMarkers = bulkZones.map(zone => ({
    id: zone.id,
    latitude: zone.latitude,
    longitude: zone.longitude,
    type: 'distributor' as const,
    data: zone,
    color: '#8b5cf6',
    size: zone.potentialProfit > 10000 ? 'large' as const : 'medium' as const,
    priority: zone.coordinationComplexity === 'low' ? 'high' as const : 'medium' as const
  }));

  const getActiveMarkers = () => {
    switch (activeTab) {
      case 'excess': return excessMarkers;
      case 'demand': return demandMarkers;
      case 'bulk': return bulkMarkers;
      default: return [...excessMarkers, ...demandMarkers, ...bulkMarkers];
    }
  };

  const handleMarkerClick = (marker: any) => {
    setSelectedOpportunity(marker.data);
  };

  const getTrendIcon = (direction: string) => {
    switch (direction) {
      case 'up': return <TrendingUp className="w-4 h-4 text-green-600" />;
      case 'down': return <TrendingUp className="w-4 h-4 text-red-600 rotate-180" />;
      default: return <div className="w-4 h-4 bg-gray-400 rounded-full"></div>;
    }
  };

  const getComplexityColor = (complexity: string) => {
    switch (complexity) {
      case 'low': return 'text-green-600';
      case 'medium': return 'text-yellow-600';
      case 'high': return 'text-red-600';
      default: return 'text-gray-600';
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <Target className="w-6 h-6 text-purple-600" />
            Carte des Opportunités Distributeur
          </h2>
          <p className="text-muted-foreground">
            Visualisez les opportunités d'achat, zones de demande et coordination en gros
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Layers className="w-4 h-4 mr-2" />
            Couches
          </Button>
          <Button className="bg-purple-600 hover:bg-purple-700">
            <Zap className="w-4 h-4 mr-2" />
            Nouvelle Opportunité
          </Button>
        </div>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label className="text-sm font-medium mb-2 block">Région</label>
            <Select value={selectedRegion} onValueChange={setSelectedRegion}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">Toutes régions</SelectItem>
                <SelectItem value="casablanca">Casablanca-Settat</SelectItem>
                <SelectItem value="rabat">Rabat-Salé-Kénitra</SelectItem>
                <SelectItem value="marrakech">Marrakech-Safi</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">Valeur Min (MAD)</label>
            <div className="px-2">
              <Slider
                value={valueRange}
                onValueChange={setValueRange}
                max={200000}
                min={5000}
                step={5000}
                className="w-full"
              />
              <div className="text-xs text-center mt-1">{valueRange[0].toLocaleString()}</div>
            </div>
          </div>
          <div>
            <label className="text-sm font-medium mb-2 block">Période</label>
            <Select value={timeFilter} onValueChange={setTimeFilter}>
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="1h">Dernière heure</SelectItem>
                <SelectItem value="24h">24 heures</SelectItem>
                <SelectItem value="7d">7 jours</SelectItem>
                <SelectItem value="30d">30 jours</SelectItem>
              </SelectContent>
            </Select>
          </div>
          <div className="flex items-end">
            <Button variant="outline" className="w-full">
              <Filter className="w-4 h-4 mr-2" />
              Appliquer
            </Button>
          </div>
        </div>
      </Card>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <Package className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Stock Excédentaire</h3>
              <p className="text-2xl font-bold text-green-800">
                {opportunityClusters.reduce((sum, c) => sum + c.excessInventoryCount, 0)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <TrendingUp className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Demandes Actives</h3>
              <p className="text-2xl font-bold text-blue-800">
                {demandHotspots.reduce((sum, h) => sum + h.requestCount, 0)}
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <ShoppingCart className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Opportunités Gros</h3>
              <p className="text-2xl font-bold text-purple-800">{bulkZones.length}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Valeur Totale</h3>
              <p className="text-2xl font-bold text-orange-800">
                {(opportunityClusters.reduce((sum, c) => sum + c.totalValue, 0) / 1000).toFixed(0)}K
              </p>
            </div>
          </div>
        </Card>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="excess" className="flex items-center gap-2">
                <Package className="w-4 h-4" />
                Stock Excédentaire
              </TabsTrigger>
              <TabsTrigger value="demand" className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Zones de Demande
              </TabsTrigger>
              <TabsTrigger value="bulk" className="flex items-center gap-2">
                <ShoppingCart className="w-4 h-4" />
                Opportunités Gros
              </TabsTrigger>
            </TabsList>

            <BaseMap
              markers={getActiveMarkers()}
              center={[-7.6167, 33.5833]}
              zoom={8}
              onMarkerClick={handleMarkerClick}
              showControls={true}
              showFilters={false}
              height="600px"
            />
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          <Card className="p-4">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Eye className="w-4 h-4" />
              Opportunités Détectées
            </h3>

            <TabsContent value="excess" className="space-y-3 max-h-[400px] overflow-y-auto">
              {opportunityClusters.map((cluster) => (
                <div
                  key={cluster.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedOpportunity?.id === cluster.id ? 'ring-2 ring-green-500' : ''
                  }`}
                  onClick={() => setSelectedOpportunity(cluster)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{cluster.region}</h4>
                      <p className="text-sm text-muted-foreground">{cluster.pharmaciesCount} pharmacies</p>
                    </div>
                    <Badge variant="outline" className="bg-green-50 text-green-700">
                      {cluster.excessInventoryCount} items
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                    <div>
                      <span className="text-muted-foreground">Valeur:</span>
                      <span className="ml-1 font-medium">{(cluster.totalValue / 1000).toFixed(0)}K</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Remise:</span>
                      <span className="ml-1 font-medium text-green-600">{cluster.avgDiscount}%</span>
                    </div>
                  </div>

                  {cluster.urgentCount > 0 && (
                    <div className="flex items-center gap-1 text-red-600 text-sm">
                      <AlertTriangle className="w-3 h-3" />
                      <span>{cluster.urgentCount} urgent(s)</span>
                    </div>
                  )}
                </div>
              ))}
            </TabsContent>

            <TabsContent value="demand" className="space-y-3 max-h-[400px] overflow-y-auto">
              {demandHotspots.map((hotspot) => (
                <div
                  key={hotspot.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedOpportunity?.id === hotspot.id ? 'ring-2 ring-blue-500' : ''
                  }`}
                  onClick={() => setSelectedOpportunity(hotspot)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{hotspot.region}</h4>
                      <p className="text-sm text-muted-foreground">{hotspot.topCategory}</p>
                    </div>
                    <div className="flex items-center gap-1">
                      {getTrendIcon(hotspot.trendDirection)}
                      <span className="text-sm font-medium">{hotspot.trendPercentage}%</span>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                    <div>
                      <span className="text-muted-foreground">Demandes:</span>
                      <span className="ml-1 font-medium">{hotspot.requestCount}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Non satisfait:</span>
                      <span className="ml-1 font-medium text-orange-600">{hotspot.unfulfilledRate}%</span>
                    </div>
                  </div>

                  <div className="text-sm">
                    <span className="text-muted-foreground">Potentiel:</span>
                    <span className="ml-1 font-medium text-green-600">
                      {(hotspot.potentialRevenue / 1000).toFixed(0)}K MAD
                    </span>
                  </div>
                </div>
              ))}
            </TabsContent>

            <TabsContent value="bulk" className="space-y-3 max-h-[400px] overflow-y-auto">
              {bulkZones.map((zone) => (
                <div
                  key={zone.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedOpportunity?.id === zone.id ? 'ring-2 ring-purple-500' : ''
                  }`}
                  onClick={() => setSelectedOpportunity(zone)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{zone.medicationName}</h4>
                      <p className="text-sm text-muted-foreground">{zone.region}</p>
                    </div>
                    <Badge variant="outline" className="bg-purple-50 text-purple-700">
                      {zone.pharmaciesInvolved} pharmacies
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                    <div>
                      <span className="text-muted-foreground">Quantité:</span>
                      <span className="ml-1 font-medium">{zone.totalQuantityNeeded}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Prix moy:</span>
                      <span className="ml-1 font-medium">{zone.avgPriceOffered} MAD</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <div>
                      <span className="text-muted-foreground">Complexité:</span>
                      <span className={`ml-1 font-medium ${getComplexityColor(zone.coordinationComplexity)}`}>
                        {zone.coordinationComplexity}
                      </span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Profit:</span>
                      <span className="ml-1 font-medium text-green-600">
                        {(zone.potentialProfit / 1000).toFixed(0)}K
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </TabsContent>
          </Card>
        </div>
      </div>

      {/* Selected Opportunity Details */}
      {selectedOpportunity && (
        <Card className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h3 className="text-xl font-semibold">
                {selectedOpportunity.region || selectedOpportunity.medicationName}
              </h3>
              <p className="text-muted-foreground">Détails de l'opportunité</p>
            </div>
            <Button variant="outline" onClick={() => setSelectedOpportunity(null)}>
              Fermer
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {/* Content varies based on opportunity type */}
            {selectedOpportunity.excessInventoryCount && (
              <>
                <div>
                  <h4 className="font-medium mb-2">Stock Excédentaire</h4>
                  <div className="space-y-2 text-sm">
                    <div>Articles: {selectedOpportunity.excessInventoryCount}</div>
                    <div>Valeur totale: {selectedOpportunity.totalValue.toLocaleString()} MAD</div>
                    <div>Remise moyenne: {selectedOpportunity.avgDiscount}%</div>
                    <div>Urgent: {selectedOpportunity.urgentCount} articles</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Pharmacies</h4>
                  <div className="space-y-2 text-sm">
                    <div>Nombre: {selectedOpportunity.pharmaciesCount}</div>
                    <div>Dernière MAJ: {selectedOpportunity.lastUpdated}</div>
                  </div>
                </div>
                <div>
                  <h4 className="font-medium mb-2">Catégories</h4>
                  <div className="flex flex-wrap gap-1">
                    {selectedOpportunity.categories.map((cat: string, i: number) => (
                      <Badge key={i} variant="outline" className="text-xs">{cat}</Badge>
                    ))}
                  </div>
                </div>
              </>
            )}
          </div>

          <div className="flex gap-2">
            <Button className="bg-purple-600 hover:bg-purple-700">
              <Phone className="w-4 h-4 mr-2" />
              Contacter Pharmacies
            </Button>
            <Button variant="outline">
              <Target className="w-4 h-4 mr-2" />
              Analyser Opportunité
            </Button>
            <Button variant="outline">
              <MapPin className="w-4 h-4 mr-2" />
              Voir Itinéraire
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}