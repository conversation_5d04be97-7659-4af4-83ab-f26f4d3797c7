"use client";

import { useEffect, useRef, useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Layers,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Filter,
  Maximize2,
  Minimize2
} from "lucide-react";

// Note: In a real implementation, you would install mapbox-gl:
// npm install mapbox-gl @types/mapbox-gl
// For now, we'll create a mock implementation

interface MapMarker {
  id: string;
  latitude: number;
  longitude: number;
  type: 'pharmacy' | 'distributor' | 'opportunity' | 'demand' | 'admin';
  data: any;
  color: string;
  size: 'small' | 'medium' | 'large';
  priority: 'low' | 'medium' | 'high' | 'critical';
}

interface BaseMapProps {
  markers: MapMarker[];
  center: [number, number]; // [longitude, latitude]
  zoom: number;
  onMarkerClick?: (marker: MapMarker) => void;
  onMapClick?: (coordinates: [number, number]) => void;
  showControls?: boolean;
  showFilters?: boolean;
  height?: string;
  className?: string;
}

export function BaseMap({
  markers,
  center = [-7.6167, 33.5833], // Morocco center coordinates
  zoom = 6,
  onMarkerClick,
  onMapClick,
  showControls = true,
  showFilters = false,
  height = "500px",
  className = ""
}: BaseMapProps) {
  const mapContainer = useRef<HTMLDivElement>(null);
  const map = useRef<any>(null);
  const [isFullscreen, setIsFullscreen] = useState(false);
  const [activeFilters, setActiveFilters] = useState<string[]>([]);
  const [mapStyle, setMapStyle] = useState('streets');

  useEffect(() => {
    // Mock Mapbox implementation
    // In real implementation, this would initialize Mapbox GL JS
    if (mapContainer.current && !map.current) {
      // map.current = new mapboxgl.Map({
      //   container: mapContainer.current,
      //   style: 'mapbox://styles/mapbox/streets-v11',
      //   center: center,
      //   zoom: zoom,
      //   accessToken: process.env.NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN
      // });

      console.log('Map would be initialized here with Mapbox GL JS');
    }

    return () => {
      // Cleanup map instance
      if (map.current) {
        map.current.remove();
      }
    };
  }, []);

  useEffect(() => {
    // Update markers when they change
    if (map.current && markers) {
      // In real implementation, this would update map markers
      console.log('Updating markers:', markers.length);
    }
  }, [markers]);

  const handleZoomIn = () => {
    if (map.current) {
      map.current.zoomIn();
    }
  };

  const handleZoomOut = () => {
    if (map.current) {
      map.current.zoomOut();
    }
  };

  const handleResetView = () => {
    if (map.current) {
      map.current.flyTo({ center: center, zoom: zoom });
    }
  };

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen);
  };

  const getMarkerColor = (marker: MapMarker) => {
    if (marker.color) return marker.color;

    switch (marker.type) {
      case 'pharmacy': return '#3b82f6'; // blue
      case 'distributor': return '#8b5cf6'; // purple
      case 'opportunity': return '#10b981'; // green
      case 'demand': return '#f59e0b'; // amber
      case 'admin': return '#ef4444'; // red
      default: return '#6b7280'; // gray
    }
  };

  const getMarkerSize = (marker: MapMarker) => {
    switch (marker.size) {
      case 'small': return 8;
      case 'medium': return 12;
      case 'large': return 16;
      default: return 10;
    }
  };

  const filteredMarkers = markers.filter(marker => {
    if (activeFilters.length === 0) return true;
    return activeFilters.includes(marker.type);
  });

  return (
    <Card className={`relative overflow-hidden ${className}`}>
      {/* Map Container */}
      <div
        ref={mapContainer}
        className={`relative bg-gray-100 ${isFullscreen ? 'fixed inset-0 z-50' : ''}`}
        style={{ height: isFullscreen ? '100vh' : height }}
      >
        {/* Mock Map Content - Replace with actual Mapbox */}
        <div className="w-full h-full bg-gradient-to-br from-blue-50 to-green-50 flex items-center justify-center">
          <div className="text-center">
            <MapPin className="w-16 h-16 mx-auto text-blue-600 mb-4" />
            <h3 className="text-lg font-semibold text-gray-700 mb-2">
              Carte Interactive Mapbox
            </h3>
            <p className="text-sm text-gray-500 mb-4">
              {filteredMarkers.length} marqueurs • Centre: {center[1].toFixed(4)}, {center[0].toFixed(4)}
            </p>
            <div className="flex flex-wrap gap-2 justify-center">
              {filteredMarkers.slice(0, 6).map((marker) => (
                <div
                  key={marker.id}
                  className="flex items-center gap-1 px-2 py-1 bg-white rounded-full shadow-sm cursor-pointer hover:shadow-md transition-shadow"
                  onClick={() => onMarkerClick?.(marker)}
                >
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: getMarkerColor(marker) }}
                  />
                  <span className="text-xs font-medium">{marker.type}</span>
                </div>
              ))}
              {filteredMarkers.length > 6 && (
                <div className="px-2 py-1 bg-gray-100 rounded-full">
                  <span className="text-xs text-gray-600">+{filteredMarkers.length - 6}</span>
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Map Controls */}
        {showControls && (
          <div className="absolute top-4 right-4 flex flex-col gap-2">
            <Button
              size="sm"
              variant="outline"
              className="bg-white/90 backdrop-blur-sm"
              onClick={toggleFullscreen}
            >
              {isFullscreen ? <Minimize2 className="w-4 h-4" /> : <Maximize2 className="w-4 h-4" />}
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="bg-white/90 backdrop-blur-sm"
              onClick={handleZoomIn}
            >
              <ZoomIn className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="bg-white/90 backdrop-blur-sm"
              onClick={handleZoomOut}
            >
              <ZoomOut className="w-4 h-4" />
            </Button>
            <Button
              size="sm"
              variant="outline"
              className="bg-white/90 backdrop-blur-sm"
              onClick={handleResetView}
            >
              <RotateCcw className="w-4 h-4" />
            </Button>
          </div>
        )}

        {/* Layer Controls */}
        <div className="absolute top-4 left-4">
          <div className="bg-white/90 backdrop-blur-sm rounded-lg p-2 shadow-sm">
            <div className="flex gap-1">
              {['streets', 'satellite', 'terrain'].map((style) => (
                <Button
                  key={style}
                  size="sm"
                  variant={mapStyle === style ? "default" : "ghost"}
                  className="text-xs"
                  onClick={() => setMapStyle(style)}
                >
                  {style}
                </Button>
              ))}
            </div>
          </div>
        </div>

        {/* Filters */}
        {showFilters && (
          <div className="absolute bottom-4 left-4 right-4">
            <div className="bg-white/90 backdrop-blur-sm rounded-lg p-3 shadow-sm">
              <div className="flex items-center gap-2 mb-2">
                <Filter className="w-4 h-4 text-gray-600" />
                <span className="text-sm font-medium">Filtres</span>
              </div>
              <div className="flex flex-wrap gap-2">
                {['pharmacy', 'distributor', 'opportunity', 'demand'].map((type) => {
                  const isActive = activeFilters.includes(type);
                  const count = markers.filter(m => m.type === type).length;

                  return (
                    <Button
                      key={type}
                      size="sm"
                      variant={isActive ? "default" : "outline"}
                      className="text-xs"
                      onClick={() => {
                        if (isActive) {
                          setActiveFilters(activeFilters.filter(f => f !== type));
                        } else {
                          setActiveFilters([...activeFilters, type]);
                        }
                      }}
                    >
                      {type} ({count})
                    </Button>
                  );
                })}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Map Legend */}
      <div className="p-3 border-t bg-gray-50">
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <div className="flex items-center gap-2">
              <Layers className="w-4 h-4 text-gray-600" />
              <span className="text-sm font-medium">Légende</span>
            </div>
            <div className="flex gap-3">
              {[
                { type: 'pharmacy', label: 'Pharmacies', color: '#3b82f6' },
                { type: 'opportunity', label: 'Opportunités', color: '#10b981' },
                { type: 'demand', label: 'Demande', color: '#f59e0b' },
                { type: 'distributor', label: 'Distributeurs', color: '#8b5cf6' }
              ].map(({ type, label, color }) => (
                <div key={type} className="flex items-center gap-1">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: color }}
                  />
                  <span className="text-xs text-gray-600">{label}</span>
                </div>
              ))}
            </div>
          </div>
          <div className="text-xs text-gray-500">
            {filteredMarkers.length} éléments affichés
          </div>
        </div>
      </div>
    </Card>
  );
}