"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  BarChart3,
  Users,
  TrendingUp,
  AlertTriangle,
  MapPin,
  Activity,
  DollarSign,
  Package,
  Clock,
  Target,
  Download,
  RefreshCw,
  Layers,
  Filter,
} from "lucide-react";
import { BaseMap } from "./base-map";

interface RegionAnalytics {
  id: string;
  region: string;
  city: string;
  latitude: number;
  longitude: number;
  activePharmacies: number;
  totalTransactions: number;
  transactionVolume: number; // MAD
  avgResponseTime: number; // hours
  fulfillmentRate: number; // percentage
  growthRate: number; // percentage
  problemAreas: string[];
  topCategories: string[];
  lastUpdated: string;
}

interface PlatformUsage {
  id: string;
  region: string;
  latitude: number;
  longitude: number;
  dailyActiveUsers: number;
  weeklyActiveUsers: number;
  monthlyActiveUsers: number;
  usageIntensity: 'low' | 'medium' | 'high' | 'very_high';
  featureAdoption: {
    marketplace: number;
    sosNetwork: number;
    seasonalDemand: number;
    pharmacyRadar: number;
  };
  userSatisfaction: number;
}

interface PerformanceMetrics {
  id: string;
  region: string;
  latitude: number;
  longitude: number;
  avgTransactionTime: number;
  successRate: number;
  errorRate: number;
  serverResponseTime: number;
  userRetentionRate: number;
  churnRate: number;
  issueCount: number;
  criticalIssues: number;
}

export function AdminAnalyticsMap() {
  const [activeTab, setActiveTab] = useState("analytics");
  const [selectedMetric, setSelectedMetric] = useState("transactions");
  const [timeRange, setTimeRange] = useState("7d");
  const [selectedRegion, setSelectedRegion] = useState<any>(null);

  const [regionAnalytics, setRegionAnalytics] = useState<RegionAnalytics[]>([]);
  const [platformUsage, setPlatformUsage] = useState<PlatformUsage[]>([]);
  const [performanceMetrics, setPerformanceMetrics] = useState<PerformanceMetrics[]>([]);

  useEffect(() => {
    loadAnalyticsData();
  }, [timeRange, selectedMetric]);

  const loadAnalyticsData = () => {
    // Mock data - in real implementation, this would come from analytics API
    const mockRegionAnalytics: RegionAnalytics[] = [
      {
        id: '1',
        region: 'Casablanca-Settat',
        city: 'Casablanca',
        latitude: 33.5731,
        longitude: -7.5898,
        activePharmacies: 156,
        totalTransactions: 2847,
        transactionVolume: 1250000,
        avgResponseTime: 2.3,
        fulfillmentRate: 87,
        growthRate: 23,
        problemAreas: ['Délais de livraison', 'Stock insuffisant'],
        topCategories: ['Antibiotiques', 'Antalgiques', 'Cardiovasculaire'],
        lastUpdated: 'Il y a 5 min'
      },
      {
        id: '2',
        region: 'Rabat-Salé-Kénitra',
        city: 'Rabat',
        latitude: 34.0209,
        longitude: -6.8416,
        activePharmacies: 89,
        totalTransactions: 1654,
        transactionVolume: 780000,
        avgResponseTime: 1.8,
        fulfillmentRate: 92,
        growthRate: 18,
        problemAreas: ['Communication inter-pharmacies'],
        topCategories: ['Diabète', 'Respiratoire', 'Dermatologie'],
        lastUpdated: 'Il y a 3 min'
      },
      {
        id: '3',
        region: 'Marrakech-Safi',
        city: 'Marrakech',
        latitude: 31.6295,
        longitude: -7.9811,
        activePharmacies: 67,
        totalTransactions: 1234,
        transactionVolume: 560000,
        avgResponseTime: 3.1,
        fulfillmentRate: 78,
        growthRate: 31,
        problemAreas: ['Couverture géographique', 'Adoption technologique'],
        topCategories: ['Généraliste', 'OTC', 'Pédiatrie'],
        lastUpdated: 'Il y a 8 min'
      },
      {
        id: '4',
        region: 'Fès-Meknès',
        city: 'Fès',
        latitude: 34.0181,
        longitude: -5.0078,
        activePharmacies: 45,
        totalTransactions: 892,
        transactionVolume: 420000,
        avgResponseTime: 4.2,
        fulfillmentRate: 71,
        growthRate: 12,
        problemAreas: ['Résistance au changement', 'Formation utilisateurs'],
        topCategories: ['Traditionnel', 'Généraliste'],
        lastUpdated: 'Il y a 12 min'
      }
    ];

    const mockPlatformUsage: PlatformUsage[] = [
      {
        id: '1',
        region: 'Casablanca',
        latitude: 33.5731,
        longitude: -7.5898,
        dailyActiveUsers: 234,
        weeklyActiveUsers: 456,
        monthlyActiveUsers: 678,
        usageIntensity: 'very_high',
        featureAdoption: {
          marketplace: 89,
          sosNetwork: 76,
          seasonalDemand: 45,
          pharmacyRadar: 67
        },
        userSatisfaction: 4.2
      },
      {
        id: '2',
        region: 'Rabat',
        latitude: 34.0209,
        longitude: -6.8416,
        dailyActiveUsers: 145,
        weeklyActiveUsers: 289,
        monthlyActiveUsers: 423,
        usageIntensity: 'high',
        featureAdoption: {
          marketplace: 92,
          sosNetwork: 83,
          seasonalDemand: 52,
          pharmacyRadar: 71
        },
        userSatisfaction: 4.5
      }
    ];

    const mockPerformanceMetrics: PerformanceMetrics[] = [
      {
        id: '1',
        region: 'Casablanca',
        latitude: 33.5731,
        longitude: -7.5898,
        avgTransactionTime: 45,
        successRate: 96.8,
        errorRate: 3.2,
        serverResponseTime: 120,
        userRetentionRate: 87,
        churnRate: 13,
        issueCount: 23,
        criticalIssues: 2
      },
      {
        id: '2',
        region: 'Rabat',
        latitude: 34.0209,
        longitude: -6.8416,
        avgTransactionTime: 38,
        successRate: 98.1,
        errorRate: 1.9,
        serverResponseTime: 95,
        userRetentionRate: 91,
        churnRate: 9,
        issueCount: 12,
        criticalIssues: 0
      }
    ];

    setRegionAnalytics(mockRegionAnalytics);
    setPlatformUsage(mockPlatformUsage);
    setPerformanceMetrics(mockPerformanceMetrics);
  };

  // Convert data to map markers based on selected metric
  const getAnalyticsMarkers = () => {
    return regionAnalytics.map(region => {
      let color = '#3b82f6';
      let size: 'small' | 'medium' | 'large' = 'medium';
      let priority: 'low' | 'medium' | 'high' | 'critical' = 'medium';

      switch (selectedMetric) {
        case 'transactions':
          color = region.totalTransactions > 2000 ? '#10b981' : 
                 region.totalTransactions > 1000 ? '#f59e0b' : '#ef4444';
          size = region.totalTransactions > 2000 ? 'large' : 'medium';
          break;
        case 'fulfillment':
          color = region.fulfillmentRate > 85 ? '#10b981' : 
                 region.fulfillmentRate > 75 ? '#f59e0b' : '#ef4444';
          priority = region.fulfillmentRate < 75 ? 'high' : 'medium';
          break;
        case 'growth':
          color = region.growthRate > 25 ? '#10b981' : 
                 region.growthRate > 15 ? '#f59e0b' : '#ef4444';
          size = region.growthRate > 25 ? 'large' : 'medium';
          break;
        case 'pharmacies':
          color = region.activePharmacies > 100 ? '#10b981' : 
                 region.activePharmacies > 50 ? '#f59e0b' : '#ef4444';
          size = region.activePharmacies > 100 ? 'large' : 'medium';
          break;
      }

      return {
        id: region.id,
        latitude: region.latitude,
        longitude: region.longitude,
        type: 'admin' as const,
        data: region,
        color,
        size,
        priority
      };
    });
  };

  const getUsageMarkers = () => {
    return platformUsage.map(usage => ({
      id: usage.id,
      latitude: usage.latitude,
      longitude: usage.longitude,
      type: 'admin' as const,
      data: usage,
      color: usage.usageIntensity === 'very_high' ? '#10b981' :
             usage.usageIntensity === 'high' ? '#3b82f6' :
             usage.usageIntensity === 'medium' ? '#f59e0b' : '#ef4444',
      size: usage.usageIntensity === 'very_high' ? 'large' as const : 'medium' as const,
      priority: 'medium' as const
    }));
  };

  const getPerformanceMarkers = () => {
    return performanceMetrics.map(perf => ({
      id: perf.id,
      latitude: perf.latitude,
      longitude: perf.longitude,
      type: 'admin' as const,
      data: perf,
      color: perf.criticalIssues > 0 ? '#ef4444' :
             perf.errorRate > 5 ? '#f59e0b' : '#10b981',
      size: perf.criticalIssues > 0 ? 'large' as const : 'medium' as const,
      priority: perf.criticalIssues > 0 ? 'critical' as const : 'medium' as const
    }));
  };

  const getActiveMarkers = () => {
    switch (activeTab) {
      case 'analytics': return getAnalyticsMarkers();
      case 'usage': return getUsageMarkers();
      case 'performance': return getPerformanceMarkers();
      default: return getAnalyticsMarkers();
    }
  };

  const handleMarkerClick = (marker: any) => {
    setSelectedRegion(marker.data);
  };

  const getMetricValue = (region: RegionAnalytics, metric: string) => {
    switch (metric) {
      case 'transactions': return region.totalTransactions;
      case 'fulfillment': return `${region.fulfillmentRate}%`;
      case 'growth': return `+${region.growthRate}%`;
      case 'pharmacies': return region.activePharmacies;
      default: return region.totalTransactions;
    }
  };

  const totalPharmacies = regionAnalytics.reduce((sum, r) => sum + r.activePharmacies, 0);
  const totalTransactions = regionAnalytics.reduce((sum, r) => sum + r.totalTransactions, 0);
  const totalVolume = regionAnalytics.reduce((sum, r) => sum + r.transactionVolume, 0);
  const avgFulfillment = regionAnalytics.reduce((sum, r) => sum + r.fulfillmentRate, 0) / regionAnalytics.length;

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-2xl font-bold flex items-center gap-2">
            <BarChart3 className="w-6 h-6 text-red-600" />
            Tableau de Bord Analytics - Carte Nationale
          </h2>
          <p className="text-muted-foreground">
            Vue d'ensemble des performances de la plateforme par région
          </p>
        </div>
        <div className="flex gap-2">
          <Button variant="outline">
            <Download className="w-4 h-4 mr-2" />
            Exporter
          </Button>
          <Button variant="outline" onClick={loadAnalyticsData}>
            <RefreshCw className="w-4 h-4 mr-2" />
            Actualiser
          </Button>
        </div>
      </div>

      {/* Global KPIs */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card className="p-4 bg-gradient-to-br from-blue-50 to-blue-100 border-blue-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-blue-600 rounded-lg">
              <Users className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-blue-900">Pharmacies Actives</h3>
              <p className="text-2xl font-bold text-blue-800">{totalPharmacies}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-green-50 to-green-100 border-green-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-green-600 rounded-lg">
              <Activity className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-green-900">Transactions</h3>
              <p className="text-2xl font-bold text-green-800">{totalTransactions.toLocaleString()}</p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-purple-50 to-purple-100 border-purple-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-purple-600 rounded-lg">
              <DollarSign className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-purple-900">Volume Total</h3>
              <p className="text-2xl font-bold text-purple-800">
                {(totalVolume / 1000000).toFixed(1)}M MAD
              </p>
            </div>
          </div>
        </Card>

        <Card className="p-4 bg-gradient-to-br from-orange-50 to-orange-100 border-orange-200">
          <div className="flex items-center gap-3">
            <div className="p-2 bg-orange-600 rounded-lg">
              <Target className="w-5 h-5 text-white" />
            </div>
            <div>
              <h3 className="font-semibold text-orange-900">Taux Satisfaction</h3>
              <p className="text-2xl font-bold text-orange-800">{avgFulfillment.toFixed(0)}%</p>
            </div>
          </div>
        </Card>
      </div>

      {/* Filters */}
      <Card className="p-4">
        <div className="flex flex-wrap gap-4">
          <Select value={timeRange} onValueChange={setTimeRange}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="24h">24 heures</SelectItem>
              <SelectItem value="7d">7 jours</SelectItem>
              <SelectItem value="30d">30 jours</SelectItem>
              <SelectItem value="90d">90 jours</SelectItem>
            </SelectContent>
          </Select>

          {activeTab === 'analytics' && (
            <Select value={selectedMetric} onValueChange={setSelectedMetric}>
              <SelectTrigger className="w-40">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="transactions">Transactions</SelectItem>
                <SelectItem value="fulfillment">Satisfaction</SelectItem>
                <SelectItem value="growth">Croissance</SelectItem>
                <SelectItem value="pharmacies">Pharmacies</SelectItem>
              </SelectContent>
            </Select>
          )}

          <Button variant="outline">
            <Filter className="w-4 h-4 mr-2" />
            Filtres Avancés
          </Button>
        </div>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Map */}
        <div className="lg:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="analytics" className="flex items-center gap-2">
                <BarChart3 className="w-4 h-4" />
                Analytics Régionales
              </TabsTrigger>
              <TabsTrigger value="usage" className="flex items-center gap-2">
                <Users className="w-4 h-4" />
                Usage Plateforme
              </TabsTrigger>
              <TabsTrigger value="performance" className="flex items-center gap-2">
                <Activity className="w-4 h-4" />
                Performance Système
              </TabsTrigger>
            </TabsList>

            <BaseMap
              markers={getActiveMarkers()}
              center={[-7.6167, 33.5833]}
              zoom={6}
              onMarkerClick={handleMarkerClick}
              showControls={true}
              showFilters={false}
              height="600px"
            />
          </Tabs>
        </div>

        {/* Sidebar */}
        <div className="space-y-4">
          <Card className="p-4">
            <h3 className="font-semibold mb-3 flex items-center gap-2">
              <Layers className="w-4 h-4" />
              Données Régionales
            </h3>
            
            <div className="space-y-3 max-h-[400px] overflow-y-auto">
              {regionAnalytics.map((region) => (
                <div 
                  key={region.id}
                  className={`p-3 border rounded-lg cursor-pointer transition-all hover:shadow-md ${
                    selectedRegion?.id === region.id ? 'ring-2 ring-red-500' : ''
                  }`}
                  onClick={() => setSelectedRegion(region)}
                >
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h4 className="font-medium">{region.city}</h4>
                      <p className="text-sm text-muted-foreground">{region.region}</p>
                    </div>
                    <Badge variant="outline">
                      {getMetricValue(region, selectedMetric)}
                    </Badge>
                  </div>

                  <div className="grid grid-cols-2 gap-2 text-sm mb-2">
                    <div>
                      <span className="text-muted-foreground">Pharmacies:</span>
                      <span className="ml-1 font-medium">{region.activePharmacies}</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Satisfaction:</span>
                      <span className="ml-1 font-medium">{region.fulfillmentRate}%</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Croissance:</span>
                      <span className="ml-1 font-medium text-green-600">+{region.growthRate}%</span>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Réponse:</span>
                      <span className="ml-1 font-medium">{region.avgResponseTime}h</span>
                    </div>
                  </div>

                  {region.problemAreas.length > 0 && (
                    <div className="flex items-center gap-1 text-orange-600 text-sm">
                      <AlertTriangle className="w-3 h-3" />
                      <span>{region.problemAreas.length} problème(s)</span>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </Card>
        </div>
      </div>

      {/* Selected Region Details */}
      {selectedRegion && (
        <Card className="p-6">
          <div className="flex items-start justify-between mb-4">
            <div>
              <h3 className="text-xl font-semibold">{selectedRegion.city}</h3>
              <p className="text-muted-foreground">{selectedRegion.region}</p>
            </div>
            <Button variant="outline" onClick={() => setSelectedRegion(null)}>
              Fermer
            </Button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-6">
            <div>
              <h4 className="font-medium mb-2">Activité</h4>
              <div className="space-y-2 text-sm">
                <div>Pharmacies actives: {selectedRegion.activePharmacies}</div>
                <div>Transactions: {selectedRegion.totalTransactions?.toLocaleString()}</div>
                <div>Volume: {selectedRegion.transactionVolume?.toLocaleString()} MAD</div>
                <div>Temps réponse: {selectedRegion.avgResponseTime}h</div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Performance</h4>
              <div className="space-y-2 text-sm">
                <div>Satisfaction: {selectedRegion.fulfillmentRate}%</div>
                <div>Croissance: +{selectedRegion.growthRate}%</div>
                <div>Dernière MAJ: {selectedRegion.lastUpdated}</div>
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Catégories Populaires</h4>
              <div className="flex flex-wrap gap-1">
                {selectedRegion.topCategories?.map((cat: string, i: number) => (
                  <Badge key={i} variant="outline" className="text-xs">{cat}</Badge>
                ))}
              </div>
            </div>

            <div>
              <h4 className="font-medium mb-2">Problèmes Identifiés</h4>
              <div className="space-y-1">
                {selectedRegion.problemAreas?.map((problem: string, i: number) => (
                  <div key={i} className="flex items-center gap-1 text-sm text-orange-600">
                    <AlertTriangle className="w-3 h-3" />
                    <span>{problem}</span>
                  </div>
                ))}
              </div>
            </div>
          </div>

          <div className="flex gap-2">
            <Button className="bg-red-600 hover:bg-red-700">
              <BarChart3 className="w-4 h-4 mr-2" />
              Rapport Détaillé
            </Button>
            <Button variant="outline">
              <Download className="w-4 h-4 mr-2" />
              Exporter Données
            </Button>
            <Button variant="outline">
              <AlertTriangle className="w-4 h-4 mr-2" />
              Créer Alerte
            </Button>
          </div>
        </Card>
      )}
    </div>
  );
}
