"use client";

import { useState } from "react";
import { Search, Loader2, Package, Info, AlertCircle } from "lucide-react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import { toast } from "sonner";

interface MedicationInfo {
  name: string;
  nameAr?: string;
  manufacturer: string;
  dosage: string;
  form: string;
  activeIngredients: string;
  posology: string;
  indications: string;
  contraindications: string;
  reimbursementRate: string;
  packaging: string;
  cip13: string;
  ppv: number;
  amoCoverage: {
    cnops: number;
    cnss: number;
    ramed: boolean;
  };
  ammNumber: string;
  dmpCategory: string;
}

interface MedicationSearchProps {
  onMedicationSelected?: (medication: MedicationInfo) => void;
}

export function MedicationSearch({ onMedicationSelected }: MedicationSearchProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [medication, setMedication] = useState<MedicationInfo | null>(null);
  const [error, setError] = useState<string | null>(null);

  const searchMedication = async () => {
    if (!searchTerm.trim()) {
      toast.error("Veuillez entrer un nom de médicament ou un code-barres");
      return;
    }

    setIsSearching(true);
    setError(null);
    setMedication(null);

    try {
      const response = await fetch(`/api/medication-lookup?search=${encodeURIComponent(searchTerm)}`);
      
      if (!response.ok) {
        throw new Error("Médicament non trouvé");
      }

      const data = await response.json();
      setMedication(data);
      
      if (onMedicationSelected) {
        onMedicationSelected(data);
      }
      
      toast.success("Médicament trouvé!");
    } catch (err) {
      setError(err instanceof Error ? err.message : "Erreur lors de la recherche");
      toast.error("Médicament non trouvé dans la base de données");
    } finally {
      setIsSearching(false);
    }
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      searchMedication();
    }
  };

  return (
    <div className="space-y-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Package className="h-5 w-5" />
            Recherche de Médicament
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-2">
            <Input
              placeholder="Nom du médicament ou code-barres..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              onKeyPress={handleKeyPress}
              className="flex-1"
            />
            <Button 
              onClick={searchMedication} 
              disabled={isSearching}
              className="flex items-center gap-2"
            >
              {isSearching ? (
                <Loader2 className="h-4 w-4 animate-spin" />
              ) : (
                <Search className="h-4 w-4" />
              )}
              Rechercher
            </Button>
          </div>

          {error && (
            <Alert variant="destructive">
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          )}
        </CardContent>
      </Card>

      {medication && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              Informations du Médicament
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Nom</h4>
                <p className="font-medium">{medication.name}</p>
                {medication.nameAr && (
                  <p className="text-sm text-muted-foreground">{medication.nameAr}</p>
                )}
              </div>
              
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Fabricant</h4>
                <p>{medication.manufacturer}</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Dosage & Forme</h4>
                <p>{medication.dosage} - {medication.form}</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Prix Public</h4>
                <p className="font-semibold text-green-600">{medication.ppv} DH</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Code CIP13</h4>
                <p className="font-mono text-sm">{medication.cip13}</p>
              </div>
              
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Remboursement</h4>
                <p>{medication.reimbursementRate}</p>
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-sm text-muted-foreground mb-2">Couverture AMO</h4>
              <div className="flex gap-2">
                <Badge variant="outline">CNOPS: {medication.amoCoverage.cnops}%</Badge>
                <Badge variant="outline">CNSS: {medication.amoCoverage.cnss}%</Badge>
                {medication.amoCoverage.ramed && (
                  <Badge variant="secondary">RAMED</Badge>
                )}
              </div>
            </div>

            <div>
              <h4 className="font-semibold text-sm text-muted-foreground">Principes Actifs</h4>
              <p className="text-sm">{medication.activeIngredients}</p>
            </div>

            <div>
              <h4 className="font-semibold text-sm text-muted-foreground">Indications</h4>
              <p className="text-sm">{medication.indications}</p>
            </div>

            <div>
              <h4 className="font-semibold text-sm text-muted-foreground">Posologie</h4>
              <p className="text-sm">{medication.posology}</p>
            </div>

            {medication.contraindications && (
              <div>
                <h4 className="font-semibold text-sm text-muted-foreground">Contre-indications</h4>
                <p className="text-sm text-red-600">{medication.contraindications}</p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4 text-xs text-muted-foreground">
              <div>
                <span className="font-semibold">AMM:</span> {medication.ammNumber}
              </div>
              <div>
                <span className="font-semibold">Catégorie DMP:</span> {medication.dmpCategory}
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
