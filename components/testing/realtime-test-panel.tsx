"use client";

import { useState, useEffect } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Textarea } from "@/components/ui/textarea";
import { 
  TestTube, 
  Zap, 
  MapPin, 
  Clock, 
  CheckCircle, 
  XCircle, 
  AlertTriangle,
  Wifi,
  WifiOff
} from "lucide-react";
import { useUrgentRequestsRealtime } from "@/hooks/use-urgent-requests-realtime";
import { useNotification } from "@/contexts/notification-context";
import { createClientComponentClient } from "@supabase/auth-helpers-nextjs";

interface TestResult {
  id: string;
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  timestamp: Date;
}

export function RealtimeTestPanel() {
  const [isVisible, setIsVisible] = useState(false);
  const [testResults, setTestResults] = useState<TestResult[]>([]);
  const [isConnected, setIsConnected] = useState(false);
  const [testInProgress, setTestInProgress] = useState(false);
  
  // Test form data
  const [testData, setTestData] = useState({
    medication: "Test Médicament",
    quantity: "10",
    urgency: "critical",
    details: "Test de notification en temps réel"
  });

  const { showNotification } = useNotification();
  const { nearbyRequests, myRequests } = useUrgentRequestsRealtime();
  const supabase = createClientComponentClient();

  // Check connection status
  useEffect(() => {
    const checkConnection = async () => {
      try {
        const { data, error } = await supabase.from('urgent_requests').select('count').limit(1);
        setIsConnected(!error);
      } catch (error) {
        setIsConnected(false);
      }
    };

    checkConnection();
    const interval = setInterval(checkConnection, 10000); // Check every 10 seconds

    return () => clearInterval(interval);
  }, [supabase]);

  const addTestResult = (test: string, status: 'success' | 'error', message: string) => {
    const result: TestResult = {
      id: Date.now().toString(),
      test,
      status,
      message,
      timestamp: new Date()
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]); // Keep last 10 results
  };

  const runConnectivityTest = async () => {
    addTestResult("Connectivity Test", "pending", "Testing Supabase connection...");
    
    try {
      const { data, error } = await supabase
        .from('urgent_requests')
        .select('count')
        .limit(1);
      
      if (error) throw error;
      
      addTestResult("Connectivity Test", "success", "✅ Supabase connection successful");
      setIsConnected(true);
    } catch (error) {
      addTestResult("Connectivity Test", "error", `❌ Connection failed: ${error}`);
      setIsConnected(false);
    }
  };

  const runRealtimeSubscriptionTest = async () => {
    addTestResult("Realtime Subscription", "pending", "Testing realtime subscriptions...");
    
    try {
      // Create a test channel to verify realtime functionality
      const testChannel = supabase
        .channel('test-realtime')
        .on('postgres_changes', {
          event: 'INSERT',
          schema: 'public',
          table: 'urgent_requests'
        }, (payload) => {
          addTestResult("Realtime Subscription", "success", "✅ Realtime event received");
        })
        .subscribe();

      // Wait a moment for subscription to establish
      setTimeout(() => {
        supabase.removeChannel(testChannel);
        addTestResult("Realtime Subscription", "success", "✅ Realtime subscription established");
      }, 2000);

    } catch (error) {
      addTestResult("Realtime Subscription", "error", `❌ Realtime test failed: ${error}`);
    }
  };

  const runGeographicFilterTest = async () => {
    addTestResult("Geographic Filter", "pending", "Testing geographic filtering...");
    
    try {
      const response = await fetch('/api/urgent-requests/nearby?radius=20&limit=5', {
        credentials: 'include'
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const data = await response.json();
      
      if (data.error) throw new Error(data.error);
      
      const { requests, metadata } = data;
      addTestResult(
        "Geographic Filter", 
        "success", 
        `✅ Found ${requests.length} requests within ${metadata.radius}km`
      );
      
    } catch (error) {
      addTestResult("Geographic Filter", "error", `❌ Geographic filter failed: ${error}`);
    }
  };

  const runUrgencyNotificationTest = async () => {
    if (!testData.medication || !testData.quantity) {
      addTestResult("Urgency Test", "error", "❌ Please fill in test data");
      return;
    }

    setTestInProgress(true);
    addTestResult("Urgency Test", "pending", "Creating test urgent request...");
    
    try {
      const response = await fetch('/api/urgent-requests', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(testData)
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const data = await response.json();
      
      if (data.error) throw new Error(data.error);
      
      addTestResult(
        "Urgency Test", 
        "success", 
        `✅ Test request created: ${data.request.medication} (${data.request.urgency})`
      );
      
      // Wait for realtime notification
      setTimeout(() => {
        addTestResult(
          "Urgency Test", 
          "success", 
          "✅ Check for realtime notification in nearby pharmacies"
        );
      }, 2000);
      
    } catch (error) {
      addTestResult("Urgency Test", "error", `❌ Urgency test failed: ${error}`);
    } finally {
      setTestInProgress(false);
    }
  };

  const runFullTestSuite = async () => {
    setTestResults([]);
    await runConnectivityTest();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await runRealtimeSubscriptionTest();
    await new Promise(resolve => setTimeout(resolve, 1000));
    await runGeographicFilterTest();
    await new Promise(resolve => setTimeout(resolve, 1000));
    showNotification("🧪 Test suite completed", "info");
  };

  const clearTestResults = () => {
    setTestResults([]);
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-blue-50 border-blue-200 text-blue-700 hover:bg-blue-100"
        >
          <TestTube className="w-4 h-4 mr-2" />
          Tests
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 w-96 z-50">
      <Card className="p-4 bg-white shadow-lg border-2 border-blue-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <TestTube className="w-5 h-5 text-blue-600" />
            <h3 className="font-semibold text-blue-900">Tests Temps Réel</h3>
            {isConnected ? (
              <Wifi className="w-4 h-4 text-green-500" />
            ) : (
              <WifiOff className="w-4 h-4 text-red-500" />
            )}
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            ✕
          </Button>
        </div>

        <div className="space-y-3">
          {/* Connection Status */}
          <div className="flex items-center justify-between text-sm">
            <span>Connexion Supabase:</span>
            <Badge variant={isConnected ? "default" : "destructive"}>
              {isConnected ? "Connecté" : "Déconnecté"}
            </Badge>
          </div>

          {/* Real-time Stats */}
          <div className="grid grid-cols-2 gap-2 text-xs">
            <div className="bg-blue-50 p-2 rounded">
              <div className="font-medium">Mes Demandes</div>
              <div className="text-blue-600">{myRequests.length}</div>
            </div>
            <div className="bg-green-50 p-2 rounded">
              <div className="font-medium">Réseau Local</div>
              <div className="text-green-600">{nearbyRequests.length}</div>
            </div>
          </div>

          {/* Test Buttons */}
          <div className="grid grid-cols-2 gap-2">
            <Button size="sm" variant="outline" onClick={runConnectivityTest}>
              Connexion
            </Button>
            <Button size="sm" variant="outline" onClick={runRealtimeSubscriptionTest}>
              Temps Réel
            </Button>
            <Button size="sm" variant="outline" onClick={runGeographicFilterTest}>
              Géographie
            </Button>
            <Button size="sm" variant="outline" onClick={runFullTestSuite}>
              Suite Complète
            </Button>
          </div>

          {/* Test Data Form */}
          <div className="border-t pt-3 space-y-2">
            <Label className="text-xs font-medium">Test de Notification:</Label>
            <div className="grid grid-cols-2 gap-2">
              <Input
                placeholder="Médicament"
                value={testData.medication}
                onChange={(e) => setTestData(prev => ({ ...prev, medication: e.target.value }))}
                className="text-xs"
              />
              <Input
                placeholder="Quantité"
                value={testData.quantity}
                onChange={(e) => setTestData(prev => ({ ...prev, quantity: e.target.value }))}
                className="text-xs"
              />
            </div>
            <Select
              value={testData.urgency}
              onValueChange={(value) => setTestData(prev => ({ ...prev, urgency: value }))}
            >
              <SelectTrigger className="text-xs">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="critical">🚨 Critique</SelectItem>
                <SelectItem value="high">⚡ Urgent</SelectItem>
                <SelectItem value="normal">📋 Normal</SelectItem>
                <SelectItem value="low">📝 Faible</SelectItem>
              </SelectContent>
            </Select>
            <Button
              size="sm"
              onClick={runUrgencyNotificationTest}
              disabled={testInProgress}
              className="w-full"
            >
              {testInProgress ? "Test en cours..." : "Tester Notification"}
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="border-t pt-3">
              <div className="flex items-center justify-between mb-2">
                <Label className="text-xs font-medium">Résultats:</Label>
                <Button variant="ghost" size="sm" onClick={clearTestResults}>
                  Effacer
                </Button>
              </div>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {testResults.map((result) => (
                  <div
                    key={result.id}
                    className="text-xs p-2 rounded bg-gray-50 border-l-2"
                    style={{
                      borderLeftColor: 
                        result.status === 'success' ? '#10b981' :
                        result.status === 'error' ? '#ef4444' : '#f59e0b'
                    }}
                  >
                    <div className="font-medium">{result.test}</div>
                    <div className="text-gray-600">{result.message}</div>
                    <div className="text-gray-400">
                      {result.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}
        </div>
      </Card>
    </div>
  );
}
