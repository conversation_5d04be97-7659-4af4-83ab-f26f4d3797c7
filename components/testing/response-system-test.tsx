"use client";

import { useState } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  TestTube2, 
  Send, 
  CheckCircle, 
  XCircle, 
  Clock,
  User,
  Phone,
  Package
} from "lucide-react";

interface TestResponse {
  id: string;
  test: string;
  status: 'pending' | 'success' | 'error';
  message: string;
  timestamp: Date;
  data?: any;
}

export function ResponseSystemTest() {
  const [testResults, setTestResults] = useState<TestResponse[]>([]);
  const [isVisible, setIsVisible] = useState(false);
  const [testInProgress, setTestInProgress] = useState(false);

  const addTestResult = (test: string, status: 'success' | 'error', message: string, data?: any) => {
    const result: TestResponse = {
      id: Date.now().toString(),
      test,
      status,
      message,
      timestamp: new Date(),
      data
    };
    setTestResults(prev => [result, ...prev.slice(0, 9)]);
  };

  const testResponseAPI = async () => {
    addTestResult("Response API", "pending", "Testing response submission...");
    
    try {
      // First, get a nearby request to respond to
      const nearbyResponse = await fetch('/api/urgent-requests/nearby?radius=50&limit=1', {
        credentials: 'include'
      });
      
      if (!nearbyResponse.ok) throw new Error(`HTTP ${nearbyResponse.status}`);
      
      const nearbyData = await nearbyResponse.json();
      
      if (nearbyData.error) throw new Error(nearbyData.error);
      
      if (nearbyData.requests.length === 0) {
        addTestResult("Response API", "error", "❌ No nearby requests found to test response");
        return;
      }

      const testRequest = nearbyData.requests[0];
      
      // Test response submission
      const responseData = {
        urgentRequestId: testRequest.id,
        quantityAvailable: "5",
        pricePerUnit: "25.50",
        message: "Test response - medication available",
        contactName: "Test Pharmacist",
        contactPhone: "06 12 34 56 78",
        contactEmail: "<EMAIL>"
      };

      const response = await fetch('/api/urgent-requests/nearby', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(responseData)
      });
      
      if (!response.ok) throw new Error(`HTTP ${response.status}`);
      
      const data = await response.json();
      
      if (data.error) throw new Error(data.error);
      
      addTestResult(
        "Response API", 
        "success", 
        `✅ Response submitted successfully for ${testRequest.medication}`,
        { requestId: testRequest.id, responseId: data.response?.id }
      );
      
    } catch (error) {
      addTestResult("Response API", "error", `❌ Response test failed: ${error}`);
    }
  };

  const testResponseValidation = async () => {
    addTestResult("Response Validation", "pending", "Testing form validation...");
    
    try {
      // Test with invalid data
      const invalidData = {
        urgentRequestId: "invalid-id",
        quantityAvailable: "", // Empty quantity
        contactName: "", // Empty name
        contactPhone: "123" // Invalid phone
      };

      const response = await fetch('/api/urgent-requests/nearby', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(invalidData)
      });
      
      if (response.ok) {
        addTestResult("Response Validation", "error", "❌ Validation should have failed");
        return;
      }
      
      addTestResult("Response Validation", "success", "✅ Form validation working correctly");
      
    } catch (error) {
      addTestResult("Response Validation", "success", "✅ Validation caught invalid data");
    }
  };

  const testDuplicateResponse = async () => {
    addTestResult("Duplicate Response", "pending", "Testing duplicate response prevention...");
    
    try {
      // Get a nearby request
      const nearbyResponse = await fetch('/api/urgent-requests/nearby?radius=50&limit=1', {
        credentials: 'include'
      });
      
      if (!nearbyResponse.ok) throw new Error(`HTTP ${nearbyResponse.status}`);
      
      const nearbyData = await nearbyResponse.json();
      
      if (nearbyData.requests.length === 0) {
        addTestResult("Duplicate Response", "error", "❌ No requests available for duplicate test");
        return;
      }

      const testRequest = nearbyData.requests[0];
      
      const responseData = {
        urgentRequestId: testRequest.id,
        quantityAvailable: "3",
        contactName: "Test Pharmacist 2",
        contactPhone: "06 87 65 43 21"
      };

      // Try to respond twice
      const firstResponse = await fetch('/api/urgent-requests/nearby', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(responseData)
      });

      const secondResponse = await fetch('/api/urgent-requests/nearby', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        credentials: 'include',
        body: JSON.stringify(responseData)
      });

      if (secondResponse.ok) {
        addTestResult("Duplicate Response", "error", "❌ Duplicate response should be prevented");
      } else {
        addTestResult("Duplicate Response", "success", "✅ Duplicate response prevention working");
      }
      
    } catch (error) {
      addTestResult("Duplicate Response", "error", `❌ Duplicate test failed: ${error}`);
    }
  };

  const runFullResponseTest = async () => {
    setTestInProgress(true);
    setTestResults([]);
    
    try {
      await testResponseAPI();
      await new Promise(resolve => setTimeout(resolve, 1000));
      await testResponseValidation();
      await new Promise(resolve => setTimeout(resolve, 1000));
      await testDuplicateResponse();
    } finally {
      setTestInProgress(false);
    }
  };

  if (!isVisible) {
    return (
      <div className="fixed bottom-20 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-green-50 border-green-200 text-green-700 hover:bg-green-100"
        >
          <TestTube2 className="w-4 h-4 mr-2" />
          Test Réponses
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-20 right-4 w-96 z-50">
      <Card className="p-4 bg-white shadow-lg border-2 border-green-200">
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            <TestTube2 className="w-5 h-5 text-green-600" />
            <h3 className="font-semibold text-green-900">Tests Système de Réponse</h3>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsVisible(false)}
          >
            ✕
          </Button>
        </div>

        <div className="space-y-3">
          {/* Test Buttons */}
          <div className="grid grid-cols-2 gap-2">
            <Button 
              size="sm" 
              variant="outline" 
              onClick={testResponseAPI}
              disabled={testInProgress}
            >
              <Send className="w-3 h-3 mr-1" />
              API
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={testResponseValidation}
              disabled={testInProgress}
            >
              <CheckCircle className="w-3 h-3 mr-1" />
              Validation
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={testDuplicateResponse}
              disabled={testInProgress}
            >
              <XCircle className="w-3 h-3 mr-1" />
              Doublons
            </Button>
            <Button 
              size="sm" 
              onClick={runFullResponseTest}
              disabled={testInProgress}
              className="bg-green-600 text-white hover:bg-green-700"
            >
              {testInProgress ? "Test..." : "Tout Tester"}
            </Button>
          </div>

          {/* Test Results */}
          {testResults.length > 0 && (
            <div className="border-t pt-3">
              <div className="flex items-center justify-between mb-2">
                <span className="text-xs font-medium">Résultats:</span>
                <Button 
                  variant="ghost" 
                  size="sm" 
                  onClick={() => setTestResults([])}
                >
                  Effacer
                </Button>
              </div>
              <div className="max-h-40 overflow-y-auto space-y-1">
                {testResults.map((result) => (
                  <div
                    key={result.id}
                    className="text-xs p-2 rounded bg-gray-50 border-l-2"
                    style={{
                      borderLeftColor: 
                        result.status === 'success' ? '#10b981' :
                        result.status === 'error' ? '#ef4444' : '#f59e0b'
                    }}
                  >
                    <div className="font-medium flex items-center gap-1">
                      {result.status === 'success' && <CheckCircle className="w-3 h-3 text-green-500" />}
                      {result.status === 'error' && <XCircle className="w-3 h-3 text-red-500" />}
                      {result.status === 'pending' && <Clock className="w-3 h-3 text-yellow-500" />}
                      {result.test}
                    </div>
                    <div className="text-gray-600 mt-1">{result.message}</div>
                    {result.data && (
                      <div className="text-gray-500 text-xs mt-1">
                        Data: {JSON.stringify(result.data, null, 2).slice(0, 100)}...
                      </div>
                    )}
                    <div className="text-gray-400 text-xs">
                      {result.timestamp.toLocaleTimeString()}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Instructions */}
          <div className="bg-green-50 p-2 rounded text-xs">
            <div className="font-medium text-green-800">Instructions:</div>
            <div className="text-green-700 mt-1">
              • Testez l'API de réponse aux demandes urgentes<br/>
              • Vérifiez la validation des formulaires<br/>
              • Confirmez la prévention des doublons<br/>
              • Surveillez les notifications en temps réel
            </div>
          </div>
        </div>
      </Card>
    </div>
  );
}
