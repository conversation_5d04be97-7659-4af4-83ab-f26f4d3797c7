-- Migration: Database Schema Consolidation
-- Description: Consolidate redundant tables and optimize relationships for better performance

-- ============================================================================
-- PHASE 1: CONSOLIDATE USER MANAGEMENT
-- ============================================================================

-- Create unified user_profiles table that consolidates profiles and user data
CREATE TABLE IF NOT EXISTS user_profiles_consolidated (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  auth_user_id UUID UNIQUE REFERENCES auth.users(id) ON DELETE CASCADE,
  email TEXT UNIQUE NOT NULL,
  full_name TEXT,
  phone TEXT,
  role user_role DEFAULT 'staff',
  
  -- Subscription data (consolidated from multiple tables)
  subscription_tier subscription_tier DEFAULT 'free',
  subscription_status subscription_status DEFAULT 'active',
  subscription_expires_at TIMESTAMPTZ,
  trial_ends_at TIMESTAMPTZ,
  subscription_started_at TIMESTAMPTZ,
  
  -- Pharmacy association
  primary_pharmacy_id UUID REFERENCES pharmacies(id) ON DELETE SET NULL,
  
  -- Settings (consolidated from user_preferences)
  preferences JSONB DEFAULT '{
    "language": "fr",
    "timezone": "Africa/Casablanca",
    "notifications": {
      "email": true,
      "push": true,
      "sms": false
    },
    "dashboard": {
      "theme": "light",
      "layout": "default"
    }
  }'::jsonb,
  
  -- Metadata
  last_login_at TIMESTAMPTZ,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- PHASE 2: CONSOLIDATE SUBSCRIPTION & BILLING
-- ============================================================================

-- Create unified billing_and_subscriptions table
CREATE TABLE IF NOT EXISTS billing_and_subscriptions_consolidated (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES user_profiles_consolidated(id) ON DELETE CASCADE,
  
  -- Subscription details
  tier subscription_tier NOT NULL,
  status subscription_status NOT NULL,
  current_period_start TIMESTAMPTZ NOT NULL,
  current_period_end TIMESTAMPTZ NOT NULL,
  
  -- Billing details
  stripe_customer_id TEXT,
  stripe_subscription_id TEXT,
  payment_method_id TEXT,
  
  -- Usage tracking (consolidated from usage_limits and usage_tracking)
  usage_data JSONB DEFAULT '{
    "marketplace_listings": {"current": 0, "limit": 10},
    "urgent_requests": {"current": 0, "limit": 5},
    "team_members": {"current": 0, "limit": 3},
    "api_calls": {"current": 0, "limit": 1000}
  }'::jsonb,
  
  -- Transaction history (consolidated from billing_transactions)
  last_payment_at TIMESTAMPTZ,
  last_payment_amount DECIMAL(10,2),
  last_payment_status payment_status,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW(),
  
  UNIQUE(user_id, tier)
);

-- ============================================================================
-- PHASE 3: CONSOLIDATE NOTIFICATION SYSTEM
-- ============================================================================

-- Create unified notifications_consolidated table
CREATE TABLE IF NOT EXISTS notifications_consolidated (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  user_id UUID NOT NULL REFERENCES user_profiles_consolidated(id) ON DELETE CASCADE,
  
  -- Notification content
  type TEXT NOT NULL,
  title TEXT NOT NULL,
  message TEXT NOT NULL,
  data JSONB DEFAULT '{}'::jsonb,
  
  -- Delivery channels (consolidated from push_subscriptions)
  channels JSONB DEFAULT '{
    "email": {"enabled": true, "sent": false},
    "push": {"enabled": true, "sent": false, "endpoints": []},
    "sms": {"enabled": false, "sent": false}
  }'::jsonb,
  
  -- Status tracking
  is_read BOOLEAN DEFAULT false,
  read_at TIMESTAMPTZ,
  priority TEXT DEFAULT 'normal' CHECK (priority IN ('low', 'normal', 'high', 'urgent')),
  
  -- Scheduling
  scheduled_for TIMESTAMPTZ,
  sent_at TIMESTAMPTZ,
  expires_at TIMESTAMPTZ,
  
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- ============================================================================
-- PHASE 4: OPTIMIZE PRODUCT & LISTING RELATIONSHIPS
-- ============================================================================

-- Note: Generated columns with subqueries are not supported in PostgreSQL
-- Instead, we'll use views or application-level joins for product information

-- ============================================================================
-- PHASE 5: CREATE OPTIMIZED INDEXES
-- ============================================================================

-- Indexes for consolidated user profiles
CREATE INDEX IF NOT EXISTS idx_user_profiles_auth_user_id
ON user_profiles_consolidated(auth_user_id);

CREATE INDEX IF NOT EXISTS idx_user_profiles_pharmacy_id
ON user_profiles_consolidated(primary_pharmacy_id);

CREATE INDEX IF NOT EXISTS idx_user_profiles_subscription
ON user_profiles_consolidated(subscription_tier, subscription_status);

-- Indexes for consolidated billing
CREATE INDEX IF NOT EXISTS idx_billing_user_tier
ON billing_and_subscriptions_consolidated(user_id, tier);

CREATE INDEX IF NOT EXISTS idx_billing_status_period
ON billing_and_subscriptions_consolidated(status, current_period_end);

-- Indexes for consolidated notifications
CREATE INDEX IF NOT EXISTS idx_notifications_user_unread
ON notifications_consolidated(user_id, is_read, created_at DESC);

CREATE INDEX IF NOT EXISTS idx_notifications_scheduled
ON notifications_consolidated(scheduled_for) WHERE scheduled_for IS NOT NULL;

-- ============================================================================
-- PHASE 6: CREATE MIGRATION FUNCTIONS
-- ============================================================================

-- Function to migrate existing data to consolidated tables
CREATE OR REPLACE FUNCTION migrate_to_consolidated_schema()
RETURNS void
LANGUAGE plpgsql
SECURITY DEFINER
AS $$
BEGIN
  -- Migrate profiles to consolidated user_profiles
  INSERT INTO user_profiles_consolidated (
    auth_user_id, email, full_name, phone, role,
    subscription_tier, subscription_status, subscription_expires_at,
    primary_pharmacy_id, preferences, last_login_at, created_at, updated_at
  )
  SELECT 
    p.id, p.email, p.full_name, p.phone, 
    COALESCE(ptm.role, 'assistant'::user_role),
    COALESCE(p.subscription_tier, 'free'::subscription_tier),
    COALESCE(p.subscription_status, 'active'::subscription_status),
    p.subscription_expires_at,
    ptm.pharmacy_id,
    COALESCE(up.preferences, '{}'::jsonb),
    p.last_sign_in_at,
    p.created_at,
    p.updated_at
  FROM profiles p
  LEFT JOIN pharmacy_team_members ptm ON ptm.user_id = p.id
  LEFT JOIN user_preferences up ON up.user_id = p.id
  ON CONFLICT (auth_user_id) DO NOTHING;

  -- Migrate notifications to consolidated table
  INSERT INTO notifications_consolidated (
    user_id, type, title, message, data, is_read, created_at
  )
  SELECT 
    upc.id, n.type, n.title, n.message, 
    COALESCE(n.data, '{}'::jsonb), n.read, n.created_at
  FROM notifications n
  JOIN user_profiles_consolidated upc ON upc.auth_user_id = n.user_id
  ON CONFLICT DO NOTHING;

  RAISE NOTICE 'Data migration to consolidated schema completed successfully';
END;
$$;

-- Grant permissions
GRANT EXECUTE ON FUNCTION migrate_to_consolidated_schema() TO service_role;

-- ============================================================================
-- PHASE 7: ADD COMMENTS AND DOCUMENTATION
-- ============================================================================

COMMENT ON TABLE user_profiles_consolidated IS 'Consolidated user profiles combining auth, subscription, and preference data';
COMMENT ON TABLE billing_and_subscriptions_consolidated IS 'Unified billing and subscription management with usage tracking';
COMMENT ON TABLE notifications_consolidated IS 'Consolidated notification system with multi-channel delivery support';



-- Add triggers for updated_at
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

CREATE TRIGGER update_user_profiles_updated_at 
BEFORE UPDATE ON user_profiles_consolidated 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_billing_updated_at 
BEFORE UPDATE ON billing_and_subscriptions_consolidated 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER update_notifications_updated_at 
BEFORE UPDATE ON notifications_consolidated 
FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
