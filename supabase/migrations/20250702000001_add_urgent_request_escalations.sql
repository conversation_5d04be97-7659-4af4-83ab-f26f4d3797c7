-- Add escalation tracking for urgent requests
-- This enables the SOS Network Enhancement with urgency escalation system

-- Create urgent_request_escalations table
CREATE TABLE IF NOT EXISTS urgent_request_escalations (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    urgent_request_id UUID NOT NULL REFERENCES urgent_requests(id) ON DELETE CASCADE,
    urgency_level TEXT NOT NULL CHECK (urgency_level IN ('low', 'normal', 'high', 'critical')),
    current_radius INTEGER NOT NULL DEFAULT 20, -- Current notification radius in km
    current_step INTEGER NOT NULL DEFAULT 0, -- Current escalation step
    next_escalation_at TIMESTAMPTZ, -- When to escalate next
    notifications_sent INTEGER NOT NULL DEFAULT 0, -- Count of notifications sent
    last_notification_at TIMESTAMPTZ, -- Last notification timestamp
    escalation_data JSONB, -- Detailed escalation status and history
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX IF NOT EXISTS idx_urgent_request_escalations_request_id 
    ON urgent_request_escalations(urgent_request_id);

CREATE INDEX IF NOT EXISTS idx_urgent_request_escalations_next_escalation 
    ON urgent_request_escalations(next_escalation_at) 
    WHERE next_escalation_at IS NOT NULL;

CREATE INDEX IF NOT EXISTS idx_urgent_request_escalations_urgency 
    ON urgent_request_escalations(urgency_level);

-- Create response tracking and analytics tables
CREATE TABLE IF NOT EXISTS urgent_request_analytics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    urgent_request_id UUID NOT NULL REFERENCES urgent_requests(id) ON DELETE CASCADE,
    pharmacy_id UUID NOT NULL REFERENCES pharmacies(id) ON DELETE CASCADE,
    
    -- Response metrics
    response_time_minutes INTEGER, -- Time to first response
    fulfillment_time_minutes INTEGER, -- Time to fulfillment
    total_responses INTEGER NOT NULL DEFAULT 0,
    successful_responses INTEGER NOT NULL DEFAULT 0,
    
    -- Geographic metrics
    average_response_distance DECIMAL(5,2), -- Average distance of responding pharmacies
    closest_response_distance DECIMAL(5,2), -- Distance of closest responder
    furthest_response_distance DECIMAL(5,2), -- Distance of furthest responder
    
    -- Escalation metrics
    escalation_steps_triggered INTEGER NOT NULL DEFAULT 0,
    max_radius_reached INTEGER NOT NULL DEFAULT 20,
    notifications_sent INTEGER NOT NULL DEFAULT 0,
    
    -- Outcome
    final_status TEXT NOT NULL CHECK (final_status IN ('fulfilled', 'expired', 'cancelled')),
    fulfillment_source TEXT, -- 'network', 'direct', 'escalation'
    
    -- Timestamps
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    updated_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create indexes for analytics
CREATE INDEX IF NOT EXISTS idx_urgent_request_analytics_request_id 
    ON urgent_request_analytics(urgent_request_id);

CREATE INDEX IF NOT EXISTS idx_urgent_request_analytics_pharmacy_id 
    ON urgent_request_analytics(pharmacy_id);

CREATE INDEX IF NOT EXISTS idx_urgent_request_analytics_status 
    ON urgent_request_analytics(final_status);

CREATE INDEX IF NOT EXISTS idx_urgent_request_analytics_created_at 
    ON urgent_request_analytics(created_at);

-- Create pharmacy response performance tracking
CREATE TABLE IF NOT EXISTS pharmacy_response_metrics (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    pharmacy_id UUID NOT NULL REFERENCES pharmacies(id) ON DELETE CASCADE,
    
    -- Response statistics (last 30 days)
    total_requests_seen INTEGER NOT NULL DEFAULT 0,
    total_responses_sent INTEGER NOT NULL DEFAULT 0,
    successful_fulfillments INTEGER NOT NULL DEFAULT 0,
    response_rate DECIMAL(5,2) NOT NULL DEFAULT 0.0, -- Percentage
    fulfillment_rate DECIMAL(5,2) NOT NULL DEFAULT 0.0, -- Percentage
    
    -- Performance metrics
    average_response_time_minutes INTEGER,
    median_response_time_minutes INTEGER,
    fastest_response_time_minutes INTEGER,
    
    -- Geographic reach
    average_service_distance DECIMAL(5,2),
    max_service_distance DECIMAL(5,2),
    
    -- Reliability score (0-100)
    reliability_score INTEGER NOT NULL DEFAULT 50,
    
    -- Last updated
    calculated_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
    period_start TIMESTAMPTZ NOT NULL DEFAULT (NOW() - INTERVAL '30 days'),
    period_end TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create unique constraint for pharmacy metrics
CREATE UNIQUE INDEX IF NOT EXISTS idx_pharmacy_response_metrics_pharmacy_id 
    ON pharmacy_response_metrics(pharmacy_id);

-- Create network performance summary table
CREATE TABLE IF NOT EXISTS network_performance_summary (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    
    -- Time period
    period_start TIMESTAMPTZ NOT NULL,
    period_end TIMESTAMPTZ NOT NULL,
    
    -- Request volume
    total_requests INTEGER NOT NULL DEFAULT 0,
    critical_requests INTEGER NOT NULL DEFAULT 0,
    high_requests INTEGER NOT NULL DEFAULT 0,
    normal_requests INTEGER NOT NULL DEFAULT 0,
    low_requests INTEGER NOT NULL DEFAULT 0,
    
    -- Fulfillment metrics
    fulfilled_requests INTEGER NOT NULL DEFAULT 0,
    expired_requests INTEGER NOT NULL DEFAULT 0,
    cancelled_requests INTEGER NOT NULL DEFAULT 0,
    fulfillment_rate DECIMAL(5,2) NOT NULL DEFAULT 0.0,
    
    -- Response metrics
    average_response_time_minutes INTEGER,
    median_response_time_minutes INTEGER,
    average_responses_per_request DECIMAL(3,1),
    
    -- Geographic metrics
    average_request_radius DECIMAL(5,2),
    average_fulfillment_distance DECIMAL(5,2),
    
    -- Escalation metrics
    requests_escalated INTEGER NOT NULL DEFAULT 0,
    escalation_rate DECIMAL(5,2) NOT NULL DEFAULT 0.0,
    
    -- Network health score (0-100)
    network_health_score INTEGER NOT NULL DEFAULT 50,
    
    created_at TIMESTAMPTZ NOT NULL DEFAULT NOW()
);

-- Create index for network performance queries
CREATE INDEX IF NOT EXISTS idx_network_performance_summary_period 
    ON network_performance_summary(period_start, period_end);

-- Create function to update escalation timestamps
CREATE OR REPLACE FUNCTION update_escalation_timestamp()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create triggers for automatic timestamp updates
CREATE TRIGGER update_urgent_request_escalations_timestamp
    BEFORE UPDATE ON urgent_request_escalations
    FOR EACH ROW
    EXECUTE FUNCTION update_escalation_timestamp();

CREATE TRIGGER update_urgent_request_analytics_timestamp
    BEFORE UPDATE ON urgent_request_analytics
    FOR EACH ROW
    EXECUTE FUNCTION update_escalation_timestamp();

-- Create function to initialize escalation when urgent request is created
CREATE OR REPLACE FUNCTION initialize_urgent_request_escalation()
RETURNS TRIGGER AS $$
DECLARE
    initial_radius INTEGER;
    first_escalation_minutes INTEGER;
    next_escalation TIMESTAMPTZ;
BEGIN
    -- Set initial radius based on urgency level
    CASE NEW.urgency_level
        WHEN 'critical' THEN 
            initial_radius := 10;
            first_escalation_minutes := 15;
        WHEN 'high' THEN 
            initial_radius := 15;
            first_escalation_minutes := 30;
        WHEN 'normal' THEN 
            initial_radius := 20;
            first_escalation_minutes := 120;
        WHEN 'low' THEN 
            initial_radius := 25;
            first_escalation_minutes := 480;
        ELSE 
            initial_radius := 20;
            first_escalation_minutes := 120;
    END CASE;
    
    -- Calculate next escalation time
    next_escalation := NEW.created_at + (first_escalation_minutes || ' minutes')::INTERVAL;
    
    -- Insert escalation record
    INSERT INTO urgent_request_escalations (
        urgent_request_id,
        urgency_level,
        current_radius,
        current_step,
        next_escalation_at,
        escalation_data
    ) VALUES (
        NEW.id,
        NEW.urgency_level,
        initial_radius,
        0,
        next_escalation,
        jsonb_build_object(
            'currentRadius', initial_radius,
            'currentStep', 0,
            'nextEscalationAt', next_escalation,
            'notificationsSent', 0,
            'lastNotificationAt', null,
            'escalationHistory', jsonb_build_array(
                jsonb_build_object(
                    'timestamp', NEW.created_at,
                    'action', 'initialized',
                    'oldRadius', 0,
                    'newRadius', initial_radius,
                    'reason', 'Initial radius set for ' || NEW.urgency_level || ' urgency'
                )
            )
        )
    );
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to auto-initialize escalation
CREATE TRIGGER initialize_escalation_on_urgent_request
    AFTER INSERT ON urgent_requests
    FOR EACH ROW
    WHEN (NEW.status = 'active')
    EXECUTE FUNCTION initialize_urgent_request_escalation();

-- Create function to finalize analytics when request is completed
CREATE OR REPLACE FUNCTION finalize_urgent_request_analytics()
RETURNS TRIGGER AS $$
DECLARE
    escalation_record RECORD;
    response_count INTEGER;
    first_response_time INTEGER;
    fulfillment_time INTEGER;
BEGIN
    -- Only process when status changes to final state
    IF OLD.status = 'active' AND NEW.status IN ('fulfilled', 'expired', 'cancelled') THEN
        
        -- Get escalation data
        SELECT * INTO escalation_record
        FROM urgent_request_escalations
        WHERE urgent_request_id = NEW.id;
        
        -- Count responses
        SELECT COUNT(*) INTO response_count
        FROM urgent_request_responses
        WHERE urgent_request_id = NEW.id;
        
        -- Calculate response time (minutes to first response)
        SELECT EXTRACT(EPOCH FROM (MIN(created_at) - NEW.created_at)) / 60 INTO first_response_time
        FROM urgent_request_responses
        WHERE urgent_request_id = NEW.id;
        
        -- Calculate fulfillment time
        fulfillment_time := EXTRACT(EPOCH FROM (NEW.updated_at - NEW.created_at)) / 60;
        
        -- Insert analytics record
        INSERT INTO urgent_request_analytics (
            urgent_request_id,
            pharmacy_id,
            response_time_minutes,
            fulfillment_time_minutes,
            total_responses,
            successful_responses,
            escalation_steps_triggered,
            max_radius_reached,
            notifications_sent,
            final_status
        ) VALUES (
            NEW.id,
            NEW.pharmacy_id,
            first_response_time,
            fulfillment_time,
            response_count,
            CASE WHEN NEW.status = 'fulfilled' THEN 1 ELSE 0 END,
            COALESCE(escalation_record.current_step, 0),
            COALESCE(escalation_record.current_radius, 20),
            COALESCE(escalation_record.notifications_sent, 0),
            NEW.status
        );
        
    END IF;
    
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for analytics finalization
CREATE TRIGGER finalize_analytics_on_completion
    AFTER UPDATE ON urgent_requests
    FOR EACH ROW
    EXECUTE FUNCTION finalize_urgent_request_analytics();

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE ON urgent_request_escalations TO authenticated;
GRANT SELECT ON urgent_request_analytics TO authenticated;
GRANT SELECT ON pharmacy_response_metrics TO authenticated;
GRANT SELECT ON network_performance_summary TO authenticated;

-- Add RLS policies
ALTER TABLE urgent_request_escalations ENABLE ROW LEVEL SECURITY;
ALTER TABLE urgent_request_analytics ENABLE ROW LEVEL SECURITY;
ALTER TABLE pharmacy_response_metrics ENABLE ROW LEVEL SECURITY;
ALTER TABLE network_performance_summary ENABLE ROW LEVEL SECURITY;

-- Policy for escalations (pharmacy can see their own request escalations)
CREATE POLICY "Pharmacies can view their own request escalations" ON urgent_request_escalations
    FOR SELECT USING (
        urgent_request_id IN (
            SELECT id FROM urgent_requests WHERE pharmacy_id IN (
                SELECT pharmacy_id FROM pharmacy_team_members WHERE user_id = auth.uid()
            )
        )
    );

-- Policy for analytics (pharmacy can see their own analytics)
CREATE POLICY "Pharmacies can view their own analytics" ON urgent_request_analytics
    FOR SELECT USING (
        pharmacy_id IN (
            SELECT pharmacy_id FROM pharmacy_team_members WHERE user_id = auth.uid()
        )
    );

-- Policy for response metrics (pharmacy can see their own metrics)
CREATE POLICY "Pharmacies can view their own response metrics" ON pharmacy_response_metrics
    FOR SELECT USING (
        pharmacy_id IN (
            SELECT pharmacy_id FROM pharmacy_team_members WHERE user_id = auth.uid()
        )
    );

-- Policy for network summary (all authenticated users can view)
CREATE POLICY "Authenticated users can view network performance" ON network_performance_summary
    FOR SELECT TO authenticated USING (true);
