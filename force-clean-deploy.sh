#!/bin/bash

# Force clean deployment with correct environment variables
set -e

SERVER_IP="**************"
SSH_USER="deploy"
APP_DIR="/var/www/pharmastock-app"

echo "🚨 EMERGENCY: Force clean deployment with production environment..."

# Step 1: Upload source files
echo "📤 Uploading source files..."
rsync -avz --progress --delete \
  --exclude='.git' \
  --exclude='node_modules' \
  --exclude='.next' \
  --exclude='build' \
  --exclude='dist' \
  ./ $SSH_USER@$SERVER_IP:$APP_DIR/

# Step 2: Set up production environment FIRST
echo "🔧 Setting up production environment..."
scp .env.production $SSH_USER@$SERVER_IP:$APP_DIR/.env

# Step 3: Force complete clean rebuild on server
echo "🏗️ Force clean rebuild on server..."
ssh $SSH_USER@$SERVER_IP << 'EOF'
cd /var/www/pharmastock-app

echo "🧹 Cleaning all build artifacts..."
rm -rf .next
rm -rf node_modules
rm -rf build
rm -rf dist
rm -f .env.local
rm -f .env.development

echo "📋 Verifying production environment..."
cat .env | grep NEXT_PUBLIC_SUPABASE_URL
echo "Environment check complete."

echo "📦 Fresh install of dependencies..."
npm install --production=false

echo "🔧 Building with production environment..."
NODE_ENV=production npm run build

echo "⚙️ Restarting PM2..."
pm2 delete pharmastock-app || true
pm2 start npm --name "pharmastock-app" -- start

echo "💾 Saving PM2 config..."
pm2 save

echo "✅ Clean deployment completed!"
pm2 status
EOF

echo ""
echo "🎉 Force clean deployment completed!"
echo "🌐 App should be available at: https://pharmastock.ma"
echo "🔍 Check browser console - should now connect to production Supabase"
echo ""
