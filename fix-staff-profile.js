import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load production environment
dotenv.config({ path: '.env.production' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function fixStaffProfile() {
  try {
    console.log('🔧 Fixing demo staff profile...');

    // Update demo staff profile to have the correct pharmacy_id
    const { data, error } = await supabase
      .from('profiles')
      .update({ 
        pharmacy_id: '116732cb-882e-47d1-a17a-c0a28f70432b' 
      })
      .eq('id', 'a2b495df-fb7d-4942-90b0-98289b82529f')
      .select();

    if (error) {
      console.error('❌ Failed to update staff profile:', error.message);
    } else {
      console.log('✅ Staff profile updated successfully');
      console.log('Updated profile:', data[0]);
    }

  } catch (error) {
    console.error('💥 Fix error:', error.message);
  }
}

fixStaffProfile();
