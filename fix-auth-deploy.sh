#!/bin/bash

echo "🚀 Deploying critical authentication fix..."

# Deploy the fixed useAccessControl hook
echo "📁 Uploading useAccessControl.ts..."
scp hooks/useAccessControl.ts deploy@216.238.99.198:/var/www/pharmastock-app/hooks/

# Deploy the dashboard layout with AccessControl re-enabled
echo "📁 Uploading dashboard layout..."
scp app/dashboard/layout.tsx deploy@216.238.99.198:/var/www/pharmastock-app/app/dashboard/

# Restart PM2
echo "🔄 Restarting PM2..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && pm2 restart pharmastock-app"

echo "✅ Authentication fix deployed!"
echo "🧪 Test login now - should work without /unauthorized redirect!"
