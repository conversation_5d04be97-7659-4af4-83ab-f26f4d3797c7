import { createClient } from '@supabase/supabase-js';
import dotenv from 'dotenv';

// Load environment variables
dotenv.config({ path: '.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseServiceKey = process.env.SUPABASE_SERVICE_ROLE_KEY;

if (!supabaseUrl || !supabaseServiceKey) {
  console.error('❌ Missing Supabase environment variables');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseServiceKey, {
  auth: {
    autoRefreshToken: false,
    persistSession: false
  }
});

async function debugDemoAccounts() {
  try {
    console.log('🔍 Debugging demo accounts...\n');

    // Check demo users in auth
    console.log('1. Checking auth users...');
    const { data: authUsers, error: authError } = await supabase.auth.admin.listUsers();
    
    if (authError) {
      console.error('❌ Auth error:', authError.message);
      return;
    }

    const demoUsers = authUsers.users.filter(u => 
      u.email === '<EMAIL>' || 
      u.email === '<EMAIL>'
    );

    console.log(`Found ${demoUsers.length} demo users in auth:`);
    demoUsers.forEach(user => {
      console.log(`  - ${user.email}: ${user.id}`);
      console.log(`    Role: ${user.user_metadata?.role || 'none'}`);
      console.log(`    Confirmed: ${user.email_confirmed_at ? 'Yes' : 'No'}`);
    });

    // Check profiles
    console.log('\n2. Checking profiles...');
    for (const user of demoUsers) {
      const { data: profile, error: profileError } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', user.id)
        .single();

      console.log(`Profile for ${user.email}:`);
      if (profileError) {
        console.log(`  ❌ Error: ${profileError.message}`);
      } else if (profile) {
        console.log(`  ✅ Found: pharmacy_id=${profile.pharmacy_id}, role=${profile.role}`);
      } else {
        console.log(`  ⚠️  No profile found`);
      }
    }

    // Check pharmacy team members
    console.log('\n3. Checking pharmacy team members...');
    for (const user of demoUsers) {
      const { data: teamMembers, error: teamError } = await supabase
        .from('pharmacy_team_members')
        .select('*, pharmacies(name, email)')
        .eq('user_id', user.id);

      console.log(`Team membership for ${user.email}:`);
      if (teamError) {
        console.log(`  ❌ Error: ${teamError.message}`);
      } else if (teamMembers && teamMembers.length > 0) {
        teamMembers.forEach(member => {
          console.log(`  ✅ Pharmacy: ${member.pharmacies?.name} (${member.pharmacy_id})`);
          console.log(`     Role: ${member.role}, Status: ${member.status}`);
        });
      } else {
        console.log(`  ⚠️  No team memberships found`);
      }
    }

    // Check pharmacies
    console.log('\n4. Checking demo pharmacy...');
    const { data: demoPharmacy, error: pharmacyError } = await supabase
      .from('pharmacies')
      .select('*')
      .eq('email', '<EMAIL>')
      .single();

    if (pharmacyError) {
      console.log(`❌ Pharmacy error: ${pharmacyError.message}`);
    } else if (demoPharmacy) {
      console.log(`✅ Demo pharmacy found: ${demoPharmacy.name} (${demoPharmacy.id})`);
      console.log(`   Verified: ${demoPharmacy.is_verified}`);
      console.log(`   City: ${demoPharmacy.city}`);
    } else {
      console.log(`⚠️  No demo pharmacy found`);
    }

    // Test authentication
    console.log('\n5. Testing authentication...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'demo123!'
    });

    if (loginError) {
      console.log(`❌ Login failed: ${loginError.message}`);
    } else {
      console.log(`✅ Login successful for: ${loginData.user.email}`);
      console.log(`   User ID: ${loginData.user.id}`);
      console.log(`   Role: ${loginData.user.user_metadata?.role}`);
      
      // Test getting user with session
      const { data: sessionUser, error: sessionError } = await supabase.auth.getUser();
      if (sessionError) {
        console.log(`❌ Session error: ${sessionError.message}`);
      } else {
        console.log(`✅ Session valid for: ${sessionUser.user.email}`);
      }
    }

    console.log('\n🎯 Summary:');
    console.log('Demo accounts should have:');
    console.log('- Auth user with confirmed email');
    console.log('- Profile with pharmacy_id');
    console.log('- Team membership with active status');
    console.log('- Associated pharmacy that is verified');

  } catch (error) {
    console.error('💥 Debug error:', error.message);
  }
}

debugDemoAccounts();
