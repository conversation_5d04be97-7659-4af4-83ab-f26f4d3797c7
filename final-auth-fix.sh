#!/bin/bash

echo "🚀 FINAL AUTH FIX - Ensuring correct files are deployed..."

# Kill any hanging processes first
echo "🔄 Stopping PM2..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && pm2 stop pharmastock-app"

# Upload the correct useAccessControl file
echo "📁 Uploading fixed useAccessControl.ts..."
scp pharmastock-app/hooks/useAccessControl.ts deploy@216.238.99.198:/var/www/pharmastock-app/hooks/

# Upload the simplified login page
echo "📁 Uploading simplified login page..."
scp app/auth/login/page.tsx deploy@216.238.99.198:/var/www/pharmastock-app/app/auth/login/

# Clear Next.js cache
echo "🧹 Clearing Next.js cache..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && rm -rf .next/cache"

# Restart PM2
echo "🔄 Starting PM2..."
ssh deploy@216.238.99.198 "cd /var/www/pharmastock-app && pm2 start pharmastock-app"

# Check status
echo "📊 Checking PM2 status..."
ssh deploy@216.238.99.198 "pm2 status"

echo "✅ FINAL AUTH FIX DEPLOYED!"
echo ""
echo "🧪 Test now:"
echo "1. Clear browser cache completely"
echo "2. Go to https://pharmastock.ma/auth/login"
echo "3. <NAME_EMAIL> / demo123!"
echo "4. Should redirect to dashboard without /unauthorized"
