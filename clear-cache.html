<!DOCTYPE html>
<html>
<head>
    <title>Clear Cache & Redirect</title>
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate">
    <meta http-equiv="Pragma" content="no-cache">
    <meta http-equiv="Expires" content="0">
</head>
<body>
    <h1>Clearing Cache and Redirecting...</h1>
    <p id="status">Initializing...</p>
    
    <script>
        const statusEl = document.getElementById('status');
        
        async function clearCacheAndRedirect() {
            try {
                statusEl.textContent = 'Clearing service worker cache...';
                
                // Clear service worker cache
                if ('serviceWorker' in navigator && 'caches' in window) {
                    const cacheNames = await caches.keys();
                    console.log('Found caches:', cacheNames);
                    
                    await Promise.all(
                        cacheNames.map(cacheName => {
                            console.log('Deleting cache:', cacheName);
                            return caches.delete(cacheName);
                        })
                    );
                    
                    statusEl.textContent = 'Service worker cache cleared!';
                } else {
                    statusEl.textContent = 'No service worker cache found.';
                }
                
                // Clear browser cache
                statusEl.textContent = 'Clearing browser cache...';
                
                // Force reload from server
                setTimeout(() => {
                    statusEl.textContent = 'Redirecting to dashboard...';
                    window.location.href = '/dashboard?cb=' + Date.now();
                }, 1000);
                
            } catch (error) {
                console.error('Error clearing cache:', error);
                statusEl.textContent = 'Error: ' + error.message;
                
                // Fallback redirect
                setTimeout(() => {
                    window.location.href = '/dashboard?cb=' + Date.now();
                }, 2000);
            }
        }
        
        // Start the process
        clearCacheAndRedirect();
    </script>
</body>
</html>
