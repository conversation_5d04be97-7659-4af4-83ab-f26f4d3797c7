import { NextResponse } from "next/server";
import type { NextRequest } from "next/server";
import { logger } from "@/lib/logger";
import { UserRole } from "@/lib/types/auth";

// Simplified middleware - only handles authentication, not authorization
// Authorization is now handled by the AccessControl component

const PUBLIC_PATHS = [
  // Auth routes
  "/auth/login",
  "/auth/register",
  "/auth/forgot-password",
  "/api/auth/login",
  "/api/auth/logout",
  "/api/auth/session",
  "/api/auth/register",
  "/api/auth/refresh-session",
  "/api/auth/forgot-password",
  "/api/auth/create-demo-user",
  "/unauthorized",
  "/routes",
  // Public tools
  "/prescription-reader",
  // Static assets and public files
  "/_next",
  "/icons",
  "/favicon.ico",
  "/manifest.json",
  "/apple-icon.png",
  "/robots.txt",
  "/sw.js",
  // Service worker files
  "/workbox-",
  "/worker-",
] as const;

// Rate limiting configuration
const RATE_LIMIT = {
  MAX_REQUESTS: 500,
  WINDOW_MS: 5 * 60 * 1000, // 5 minutes
} as const;

// Use Map with string key for rate limiting
const rateLimitStore = new Map<string, { count: number; timestamp: number }>();

function isRateLimited(clientId: string): boolean {
  const now = Date.now();
  const windowStart = now - RATE_LIMIT.WINDOW_MS;

  // Clean up old entries
  Array.from(rateLimitStore.entries()).forEach(([key, value]) => {
    if (value.timestamp < windowStart) {
      rateLimitStore.delete(key);
    }
  });

  const current = rateLimitStore.get(clientId) || { count: 0, timestamp: now };

  if (current.timestamp < windowStart) {
    current.count = 1;
    current.timestamp = now;
  } else {
    current.count++;
  }

  rateLimitStore.set(clientId, current);
  return current.count > RATE_LIMIT.MAX_REQUESTS;
}

const validateSession = (sessionStr: string) => {
  try {
    const session = JSON.parse(sessionStr);
    const now = Math.floor(Date.now() / 1000);

    logger.debug("Validating session", {
      hasUser: !!session.user,
      hasRole: !!session.user?.role,
      hasExpiry: !!session.expires_at,
      expiresAt: session.expires_at,
      currentTime: now,
    });

    if (!session.user?.id || !session.user?.role || !session.expires_at) {
      logger.warn("Invalid session structure", {
        hasId: !!session.user?.id,
        hasRole: !!session.user?.role,
        hasExpiry: !!session.expires_at,
      });
      return null;
    }

    if (session.expires_at < now) {
      logger.warn("Session expired", {
        expiresAt: session.expires_at,
        currentTime: now,
      });
      return null;
    }

    // Basic role validation
    const role = session.user.role as UserRole;
    const validRoles = [
      "super_admin",
      "owner",
      "admin",
      "pharmacist",
      "staff",
      "supplier",
    ];
    if (!validRoles.includes(role)) {
      logger.warn("Invalid role in session", { role });
      return null;
    }

    logger.debug("Session validated successfully", {
      userId: session.user.id,
      role: session.user.role,
      expiresIn: session.expires_at - now,
    });

    return {
      user: session.user,
      expires_at: session.expires_at,
      has_access_token: !!session.access_token,
    };
  } catch (error) {
    logger.error({ err: error }, "Session validation failed");
    return null;
  }
};

// Authorization logic removed - now handled by AccessControl component

export async function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  logger.debug("🔍 Middleware entry point", {
    pathname,
    method: request.method,
    url: request.url,
    cookies: Object.fromEntries(request.cookies.getAll().map(c => [c.name, c.value ? '***' : 'empty'])),
  });

  // For local development, bypass auth checks entirely
  const isLocalhost = request.headers.get('host')?.includes('localhost') ||
                     request.headers.get('host')?.includes('127.0.0.1');

  if (isLocalhost && process.env.NODE_ENV === 'development') {
    logger.debug("🚀 Local development mode - bypassing auth checks", { pathname });
    return NextResponse.next();
  }

  // Production authentication handling
  if (process.env.NODE_ENV === 'production') {
    logger.debug("🏭 Production mode - checking authentication", { pathname });

    // For API routes, we need to pass through to let the API handle auth
    if (pathname.startsWith("/api/")) {
      logger.debug("✅ Allowing API route for server-side auth", { pathname });
      return NextResponse.next();
    }

    // For pages, check if user has session cookies
    const allCookies = request.cookies.getAll();
    const hasSupabaseAuth = allCookies.some(cookie =>
      cookie.name.startsWith('sb-') && cookie.value
    );

    if (!hasSupabaseAuth) {
      logger.info("🚫 No session in production - redirecting to login", {
        from: pathname,
        cookies: Object.fromEntries(allCookies.map(c => [c.name, !!c.value]))
      });
      return NextResponse.redirect(new URL("/auth/login", request.url));
    }

    // Has session cookies, allow through
    logger.debug("✅ Has session cookies in production", { pathname });
    return NextResponse.next();
  }

  // Check if the path is public
  const isPublicPath = PUBLIC_PATHS.some((path) =>
    pathname === path || pathname.startsWith(path + "/")
  );
  if (isPublicPath) {
    logger.debug("✅ Allowing public path access", { pathname });
    return NextResponse.next();
  }

  // Get client identifier for rate limiting
  const clientId =
    request.headers.get("x-forwarded-for") ||
    request.headers.get("x-real-ip") ||
    "unknown";

  // Check rate limit
  if (isRateLimited(clientId)) {
    logger.warn("⛔ Rate limit exceeded", { clientId, pathname });
    return new NextResponse("Too Many Requests", { status: 429 });
  }

  // Allow API routes except auth endpoints
  if (pathname.startsWith("/api/") && !pathname.startsWith("/api/auth/")) {
    logger.debug("✅ Allowing API route", { pathname });
    return NextResponse.next();
  }

  // Check session - Supabase uses these cookie names in production
  const accessToken = request.cookies.get("sb-access-token")?.value;
  const refreshToken = request.cookies.get("sb-refresh-token")?.value;

  // Also check for the standard Supabase session cookies
  const supabaseSession = request.cookies.get("sb-session")?.value;

  // Check for any Supabase-related cookies (they use different patterns)
  const allCookies = request.cookies.getAll();
  const hasSupabaseAuth = allCookies.some(cookie =>
    cookie.name.startsWith('sb-') && cookie.value
  );

  if (!hasSupabaseAuth && !accessToken && !refreshToken && !supabaseSession) {
    logger.info("🚫 No valid session - redirecting to login", {
      from: pathname,
      hasAccessToken: !!accessToken,
      hasRefreshToken: !!refreshToken,
      hasSupabaseSession: !!supabaseSession,
      allCookies: Object.fromEntries(allCookies.map(c => [c.name, !!c.value]))
    });
    const response = NextResponse.redirect(new URL("/auth/login", request.url));
    return response;
  }

  // For local testing, create a mock session
  const session = {
    user: {
      id: 'test-user',
      role: 'owner'
    }
  };

  // Authentication successful - authorization is handled by AccessControl component
  const userRole = session.user.role as UserRole;

  logger.debug("✅ Authentication successful", {
    userRole,
    pathname,
    userId: session.user.id,
  });

  // Add user info to headers for API routes
  const response = NextResponse.next();
  response.headers.set("X-User-Id", session.user.id);
  response.headers.set("X-User-Role", userRole);
  if (session.user.pharmacyId) {
    response.headers.set("X-Pharmacy-Id", session.user.pharmacyId);
  }

  return response;
}
