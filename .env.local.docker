# Local Docker Supabase Configuration
# Use this for testing optimizations locally before production deployment

# ============================================================================
# SUPABASE LOCAL DOCKER CONFIGURATION
# ============================================================================

# API Configuration
NEXT_PUBLIC_SUPABASE_URL=http://127.0.0.1:54351
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6ImFub24iLCJleHAiOjE5ODM4MTI5OTZ9.CRXP1A7WOeoJeXxjNni43kdQwgnWNReilDMblYTn_I0

# Service Role (for server-side operations)
SUPABASE_SERVICE_ROLE_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZS1kZW1vIiwicm9sZSI6InNlcnZpY2Vfcm9sZSIsImV4cCI6MTk4MzgxMjk5Nn0.EGIM96RAZx35lJzdJsyH-qQwv8Hdp7fsn3W0YpN81IU

# Direct Database Connection (for migrations and testing)
DATABASE_URL=postgresql://postgres:postgres@127.0.0.1:54352/postgres
DIRECT_URL=postgresql://postgres:postgres@127.0.0.1:54352/postgres

# ============================================================================
# DEVELOPMENT CONFIGURATION
# ============================================================================

# Environment
NODE_ENV=development
NEXT_PUBLIC_APP_ENV=local

# App Configuration
NEXT_PUBLIC_APP_URL=http://localhost:3006
NEXT_PUBLIC_API_URL=http://localhost:3006/api

# ============================================================================
# AUTHENTICATION CONFIGURATION
# ============================================================================

# NextAuth Configuration
NEXTAUTH_URL=http://localhost:3006
NEXTAUTH_SECRET=your-local-nextauth-secret-key-for-development-only

# JWT Configuration (matches Supabase local)
JWT_SECRET=super-secret-jwt-token-with-at-least-32-characters-long

# ============================================================================
# STORAGE CONFIGURATION
# ============================================================================

# Supabase Storage (local)
NEXT_PUBLIC_SUPABASE_STORAGE_URL=http://127.0.0.1:54351/storage/v1
SUPABASE_STORAGE_URL=http://127.0.0.1:54351/storage/v1

# S3 Configuration (local)
S3_ACCESS_KEY=625729a08b95bf1b7ff351a663f3a23c
S3_SECRET_KEY=850181e4652dd023b7a98c58ae0d2d34bd487ee0cc3254aed6eda37307425907
S3_REGION=local
S3_BUCKET=pharmastock-local

# ============================================================================
# TESTING AND DEBUGGING
# ============================================================================

# Enable detailed logging for testing
DEBUG=true
NEXT_PUBLIC_DEBUG=true
LOG_LEVEL=debug

# Performance monitoring
ENABLE_PERFORMANCE_MONITORING=true
NEXT_PUBLIC_ENABLE_PERFORMANCE_MONITORING=true

# Database query logging
LOG_DATABASE_QUERIES=true

# ============================================================================
# FEATURE FLAGS FOR TESTING
# ============================================================================

# Enable all optimizations for testing
ENABLE_CONSOLIDATED_API=true
ENABLE_DATABASE_FUNCTIONS=true
ENABLE_COMPONENT_MEMOIZATION=true
ENABLE_SCHEMA_CONSOLIDATION=true

# Testing flags
ENABLE_MOCK_DATA=false
ENABLE_PERFORMANCE_TESTING=true
ENABLE_LOAD_TESTING=false

# ============================================================================
# EXTERNAL SERVICES (MOCK/LOCAL)
# ============================================================================

# Email service (use local inbucket)
EMAIL_SERVICE_URL=http://127.0.0.1:54354
SMTP_HOST=127.0.0.1
SMTP_PORT=2500
SMTP_USER=test
SMTP_PASS=test

# Payment service (mock for testing)
STRIPE_PUBLISHABLE_KEY=pk_test_local_development_key
STRIPE_SECRET_KEY=sk_test_local_development_key
STRIPE_WEBHOOK_SECRET=whsec_local_development_secret

# ============================================================================
# MONITORING AND ANALYTICS (DISABLED FOR LOCAL)
# ============================================================================

# Disable external services for local testing
ENABLE_ANALYTICS=false
ENABLE_ERROR_REPORTING=false
ENABLE_PERFORMANCE_TRACKING=true

# Local monitoring
ENABLE_LOCAL_MONITORING=true

NEXT_PUBLIC_MAPBOX_ACCESS_TOKEN=pk.eyJ1Ijoid3dtcyIsImEiOiJjbHdrYWQ0eXAxNGM1MmptbTd4YXg2NGxqIn0.tAM-9pPFtZHoVAzuDuLkUg
