'use client';

import React, { createContext, useContext, useEffect, useState, ReactNode } from 'react';
import { User } from '@supabase/supabase-js';
import { getSupabase } from '@/lib/supabase/client';

export interface ClientAuthState {
  user: User | null;
  loading: boolean;
  error: string | null;
  pharmacyId: string | null;
  pharmacyName: string | null;
  role: string | null;
  isPharmacy: boolean;
}

export interface ClientAuthContextType extends ClientAuthState {
  signIn: (email: string, password: string) => Promise<{ success: boolean; error?: string }>;
  signUp: (email: string, password: string, userData: any) => Promise<{ success: boolean; error?: string }>;
  signOut: () => Promise<void>;
  resetPassword: (email: string) => Promise<{ success: boolean; error?: string }>;
  updatePharmacy: (pharmacyId: string, pharmacyName: string) => void;
  refreshAuth: () => Promise<void>;
}

const ClientAuthContext = createContext<ClientAuthContextType | undefined>(undefined);

interface ClientAuthProviderProps {
  children: ReactNode;
  initialSession?: any; // Server session passed from layout
}

export function ClientAuthProvider({ children, initialSession }: ClientAuthProviderProps) {
  const [state, setState] = useState<ClientAuthState>({
    user: null,
    loading: true,
    error: null,
    pharmacyId: initialSession?.pharmacyId || null,
    pharmacyName: null,
    role: initialSession?.role || null,
    isPharmacy: false,
  });

  const supabase = getSupabase();

  // Safe state updater
  const updateState = (updates: Partial<ClientAuthState>) => {
    setState(prev => ({ ...prev, ...updates }));
  };

  // Fetch user's pharmacy ID from profile
  const fetchUserPharmacyId = async (userId: string) => {
    try {
      const response = await fetch(`/api/debug/auth`);
      if (response.ok) {
        const data = await response.json();
        if (data.user?.pharmacyId) {
          updateState({
            pharmacyId: data.user.pharmacyId,
            isPharmacy: true
          });
          return data.user.pharmacyId;
        }
      }
    } catch (error) {
      console.error('Error fetching user pharmacy ID:', error);
    }
    return null;
  };

  // Fetch pharmacy data with error handling
  const fetchPharmacyData = async (pharmacyId: string) => {
    try {
      const response = await fetch(`/api/pharmacy/${pharmacyId}`);
      if (response.ok) {
        const data = await response.json();
        updateState({
          pharmacyName: data.name || null,
          error: null
        });
      } else {
        console.warn('Failed to fetch pharmacy data:', response.statusText);
      }
    } catch (error) {
      console.error('Error fetching pharmacy data:', error);
      updateState({ error: 'Failed to load pharmacy information' });
    }
  };

  // Initialize auth state
  useEffect(() => {
    let mounted = true;

    const initializeAuth = async () => {
      try {
        // For local development, provide mock data
        if (process.env.NODE_ENV === 'development' || process.env.NODE_ENV === 'test') {
          if (!mounted) return;

          updateState({
            user: {
              id: '550e8400-e29b-41d4-a716-446655440001',
              email: '<EMAIL>',
              user_metadata: { role: 'owner' },
              app_metadata: { role: 'owner' }
            } as any,
            loading: false,
            error: null,
            role: 'owner',
            pharmacyId: '550e8400-e29b-41d4-a716-446655440000',
            pharmacyName: 'Pharmacie Test Locale',
            isPharmacy: true
          });
          return;
        }

        const { data: { session }, error } = await supabase.auth.getSession();

        if (!mounted) return;

        if (error) {
          console.error('Auth initialization error:', error);
          updateState({
            loading: false,
            error: 'Authentication error',
            user: null,
            isPharmacy: false
          });
          return;
        }

        // Extract role from user_metadata
        const userRole = session?.user?.user_metadata?.role || session?.user?.role || null;

        updateState({
          user: session?.user || null,
          loading: false,
          error: null,
          role: userRole,
          isPharmacy: !!(state.pharmacyId || initialSession?.pharmacyId)
        });

        // Fetch pharmacy ID if we don't have one
        let pharmacyId = state.pharmacyId || initialSession?.pharmacyId;
        if (!pharmacyId && session?.user) {
          pharmacyId = await fetchUserPharmacyId(session.user.id);
        }

        // Fetch pharmacy data if we have a pharmacy ID
        if (pharmacyId && session?.user) {
          await fetchPharmacyData(pharmacyId);
        }
      } catch (error) {
        if (mounted) {
          console.error('Auth initialization failed:', error);
          updateState({
            loading: false,
            error: 'Failed to initialize authentication',
            user: null,
            isPharmacy: false
          });
        }
      }
    };

    initializeAuth();

    // Listen for auth changes
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        if (!mounted) return;

        // Extract role from user_metadata
        const userRole = session?.user?.user_metadata?.role || session?.user?.role || null;

        updateState({
          user: session?.user || null,
          loading: false,
          error: null,
          role: userRole,
          isPharmacy: !!(state.pharmacyId || initialSession?.pharmacyId)
        });

        if (event === 'SIGNED_OUT') {
          updateState({
            pharmacyId: null,
            pharmacyName: null,
            role: null,
            isPharmacy: false
          });
        }
      }
    );

    return () => {
      mounted = false;
      subscription.unsubscribe();
    };
  }, []);

  const signIn = async (email: string, password: string) => {
    try {
      updateState({ loading: true, error: null });
      
      const { data, error } = await supabase.auth.signInWithPassword({
        email,
        password,
      });

      if (error) {
        updateState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      updateState({ loading: false });
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign in failed';
      updateState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  const signUp = async (email: string, password: string, userData: any) => {
    try {
      updateState({ loading: true, error: null });
      
      const { data, error } = await supabase.auth.signUp({
        email,
        password,
        options: {
          data: userData,
        },
      });

      if (error) {
        updateState({ loading: false, error: error.message });
        return { success: false, error: error.message };
      }

      updateState({ loading: false });
      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Sign up failed';
      updateState({ loading: false, error: errorMessage });
      return { success: false, error: errorMessage };
    }
  };

  const signOut = async () => {
    try {
      updateState({ loading: true, error: null });
      await supabase.auth.signOut();
      updateState({ 
        loading: false,
        user: null,
        pharmacyId: null,
        pharmacyName: null,
        role: null 
      });
    } catch (error) {
      console.error('Sign out error:', error);
      updateState({ loading: false, error: 'Sign out failed' });
    }
  };

  const resetPassword = async (email: string) => {
    try {
      const { error } = await supabase.auth.resetPasswordForEmail(email);
      
      if (error) {
        return { success: false, error: error.message };
      }

      return { success: true };
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Password reset failed';
      return { success: false, error: errorMessage };
    }
  };

  const updatePharmacy = (pharmacyId: string, pharmacyName: string) => {
    updateState({ pharmacyId, pharmacyName });
  };

  const refreshAuth = async () => {
    try {
      updateState({ loading: true, error: null });
      const { data: { session }, error } = await supabase.auth.getSession();
      
      if (error) {
        updateState({ loading: false, error: error.message, isPharmacy: false });
        return;
      }

      // Extract role from user_metadata
      const userRole = session?.user?.user_metadata?.role || session?.user?.role || null;

      updateState({
        user: session?.user || null,
        loading: false,
        role: userRole,
        isPharmacy: !!state.pharmacyId
      });

      if (state.pharmacyId && session?.user) {
        await fetchPharmacyData(state.pharmacyId);
      }
    } catch (error) {
      console.error('Auth refresh failed:', error);
      updateState({ loading: false, error: 'Failed to refresh authentication', isPharmacy: false });
    }
  };

  const contextValue: ClientAuthContextType = {
    ...state,
    signIn,
    signUp,
    signOut,
    resetPassword,
    updatePharmacy,
    refreshAuth,
  };

  return (
    <ClientAuthContext.Provider value={contextValue}>
      {children}
    </ClientAuthContext.Provider>
  );
}

export function useClientAuth(): ClientAuthContextType {
  const context = useContext(ClientAuthContext);
  if (context === undefined) {
    throw new Error('useClientAuth must be used within a ClientAuthProvider');
  }
  return context;
}

// Utility hooks for common auth checks
export function useAuthUser() {
  const { user, loading } = useClientAuth();
  return { user, loading, isAuthenticated: !!user };
}

export function useAuthRole() {
  const { role, loading } = useClientAuth();
  return { role, loading, hasRole: (roles: string[]) => role ? roles.includes(role) : false };
}

export function usePharmacyInfo() {
  const { pharmacyId, pharmacyName, loading, isPharmacy } = useClientAuth();
  return { pharmacyId, pharmacyName, loading, hasPharmacy: !!pharmacyId, isPharmacy };
}